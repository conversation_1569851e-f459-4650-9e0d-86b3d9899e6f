﻿using Lrb.Domain.Entities.MasterData;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Lrb.Application.Property.Web.Dtos
{
    public class FilterDropdownDataDto : IDto
    {
        public List<string>? Locality { get; set; }
        public List<string>? SubLocality { get; set; }
        public List<string>? City { get; set; }
        public List<string>? State { get; set; }
        public List<string>? Country { get; set; }

    }
}
