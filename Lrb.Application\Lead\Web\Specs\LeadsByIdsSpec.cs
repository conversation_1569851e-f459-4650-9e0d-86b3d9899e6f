﻿namespace Lrb.Application.Lead.Web
{
    public class LeadsByIdsSpec : Specification<Domain.Entities.Lead>
    {
        public LeadsByIdsSpec(List<Guid> ids)
        {
            Query.Where(i => !i.IsDeleted && ids.Contains(i.Id)).Where(i => !i.IsArchived)
                .Include(i => i.TagInfo)
                .Include(i => i.CustomLeadStatus)
                .Include(i => i.Enquiries)
              .Include(i => i.Enquiries)
                  .ThenInclude(i => i.Addresses)
                      .ThenInclude(i => i.Location)
              .Include(i => i.Enquiries)
                  .ThenInclude(i => i.PropertyType)
              .Include(i => i.Appointments)
                  .ThenInclude(i => i.Location)
              .Include(i => i.Projects)
              .Include(i => i.Properties)
              .ThenInclude(i => i.Dimension)
              .Include(i => i.Address)
              .Include(i => i.ChannelPartners)
              .Include(i => i.CustomFlags)
              .ThenInclude(i => i.Flag)
              .Include(i => i.Agencies)
              .Include(i => i.BookedDetails)
                .ThenInclude(i => i.Properties)
                  .ThenInclude(i => i.Dimension)
              .Include(i => i.BookedDetails)
                 .ThenInclude(i => i.Documents)
               .Include(i => i.Campaigns)
                 .Include(i => i.Enquiries)
                .ThenInclude(i => i.PropertyTypes);
        }
        public LeadsByIdsSpec(string contactNo)
        {
            Query.Where(i => !i.IsDeleted && (i.ContactNo != null && i.ContactNo.Contains(contactNo))).Where(i => !i.IsArchived)
                .Include(i => i.TagInfo)
                .Include(i => i.CustomLeadStatus)
                .Include(i => i.Enquiries)
                    //.ThenInclude(i => i.Address)
                    .ThenInclude(i => i.Addresses)
                        .ThenInclude(i => i.Location)
                            .ThenInclude(i => i.Zone)
                .Include(i => i.Enquiries)
                    //.ThenInclude(i => i.Address)
                    .ThenInclude(i => i.Addresses)
                        .ThenInclude(i => i.Location)
                            .ThenInclude(i => i.City)
                .Include(i => i.Enquiries)
                .ThenInclude(i => i.PropertyType)
                .Include(i => i.Appointments)
                .ThenInclude(i => i.Location)
                .Include(i => i.Projects)
                .Include(i => i.Properties)
                .Include(i => i.CustomFlags)
                .ThenInclude(i => i.Flag)
                .Include(i => i.Agencies)
                .Include(i => i.Enquiries)
                .ThenInclude(i => i.PropertyTypes);
        }
    }
    public class GetLeadsByIdsSpec : Specification<Domain.Entities.Lead>
    {
        public GetLeadsByIdsSpec(List<Guid> ids)
        {
            Query.Where(i => !i.IsDeleted && ids.Contains(i.Id)).Where(i => !i.IsArchived)
                .Include(i => i.CustomLeadStatus)
                .Include(i => i.Enquiries)
                .Include(i => i.Address)
                .Include(i => i.ChannelPartners)
                .Include(i => i.Projects)
                .Include(i => i.CustomFlags)
                .ThenInclude(i => i.Flag);
        }
    }
    public class SourceLeadsByIdsSpec : Specification<Domain.Entities.Lead>
    {
        public SourceLeadsByIdsSpec(List<Guid> ids)
        {
            Query.Where(i => !i.IsDeleted && ids.Contains(i.Id))
                .Include(i => i.TagInfo)
                .Include(i => i.CustomLeadStatus)
                .Include(i => i.Enquiries)
              .Include(i => i.Enquiries)
                  .ThenInclude(i => i.Addresses)
                      .ThenInclude(i => i.Location)
              .Include(i => i.Enquiries)
                  .ThenInclude(i => i.PropertyType)
              .Include(i => i.Appointments)
                  .ThenInclude(i => i.Location)
              .Include(i => i.Projects)
              .Include(i => i.Properties)
              .ThenInclude(i => i.Dimension)
              .Include(i => i.Address)
              .Include(i => i.ChannelPartners)
              .Include(i => i.CustomFlags)
              .ThenInclude(i => i.Flag)
              .Include(i => i.Agencies)
              .Include(i => i.BookedDetails)
                .ThenInclude(i => i.Properties)
                  .ThenInclude(i => i.Dimension)
              .Include(i => i.BookedDetails)
                 .ThenInclude(i => i.Documents)
               .Include(i => i.Campaigns)
                 .Include(i => i.Enquiries)
                .ThenInclude(i => i.PropertyTypes);
        }
    }
    public class GetLatestLeadByContactNo : Specification<Domain.Entities.Lead>
    {
        public GetLatestLeadByContactNo(string contactNo)
        {
            Query.Where(i => !i.IsDeleted && (i.ContactNo != null && i.ContactNo.Contains(contactNo))).Where(i => !i.IsArchived)
                .Include(i => i.TagInfo)
                .Include(i => i.CustomLeadStatus)
                .Include(i => i.Enquiries)
                    .ThenInclude(i => i.Addresses)
                        .ThenInclude(i => i.Location)
                            .ThenInclude(i => i.Zone)
                .Include(i => i.Enquiries)
                    .ThenInclude(i => i.Addresses)
                        .ThenInclude(i => i.Location)
                            .ThenInclude(i => i.City)
                .Include(i => i.Enquiries)
                .ThenInclude(i => i.PropertyType)
                .Include(i => i.Appointments)
                .ThenInclude(i => i.Location)
                .Include(i => i.Projects)
                .Include(i => i.Properties)
                .Include(i => i.CustomFlags)
                .ThenInclude(i => i.Flag)
                .Include(i => i.Agencies)
                .Include(i => i.Enquiries)
                .ThenInclude(i => i.PropertyTypes);
        }
    }

}
