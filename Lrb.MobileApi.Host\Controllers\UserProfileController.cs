﻿using Lrb.Application.UserDetails.Mobile;
using Lrb.Application.UserDetails.Mobile.Request;
using static Lrb.Application.UserDetails.Mobile.Request.GetReporteesRequestHandler;

namespace Lrb.MobileApi.Host.Controllers
{
    [Authorize]
    public class UserProfileController : VersionedApiController
    {
        [HttpPut("{id:guid}")]
        [TenantIdHeader]
        //[MustHavePermission(LrbAction.Update, LrbResource.UserProfile)]
        [OpenApiOperation("Update a user.", "")]
        public async Task<ActionResult<Response<Guid>>> UpdateAsync(UpdateUserDetailsRequest request, Guid id)
        {
            return id != request.UserId
                ? BadRequest()
                : Ok(await Mediator.Send(request));
        }
        [HttpPut("image")]
        [TenantIdHeader]
        //[MustHavePermission(LrbAction.Update, LrbResource.UserProfile)]
        [OpenApiOperation("Update user image.", "")]
        public Task<Response<Guid>> UpdateAsync([FromBody] string? imgUrl)
        {
            return Mediator.Send(new UpdateUserImgRequest(imgUrl));
        }
        [AllowAnonymous]
        [HttpGet("{id:guid}")]
        [TenantIdHeader]
        //[MustHavePermission(LrbAction.View, LrbResource.UserProfile)]
        [OpenApiOperation("Get user details.", "")]
        public async Task<Response<UserDetailsDto>> GetAsync(Guid id)
        {
            return await Mediator.Send(new GetFullUserViewV2ByIdRequest(id));
        }
        [HttpPost("profileCompletion")]
        [TenantIdHeader]
        [OpenApiOperation("Get user profile completion.", "")]
        public async Task<Response<int>> GetAsync(GetProfileCompletionRequest request)
        {
            return await Mediator.Send(request);
        }
        [HttpDelete("document/{id:guid}")]
        [TenantIdHeader]
        //[MustHavePermission(LrbAction.Update, LrbResource.UserProfile)]
        [OpenApiOperation("Delete a document.", "")]
        public Task<Response<bool>> DeleteDocumentAsync(Guid id)
        {
            return Mediator.Send(new DeleteDocumentRequest(id));
        }

        [HttpPut("document/{userId:guid}")]
        [TenantIdHeader]
        //[MustHavePermission(LrbAction.Update, LrbResource.UserProfile)]
        [OpenApiOperation("Update a document.", "")]
        public async Task<ActionResult<Response<Guid>>> UpdateAsync(UpdateDocumentRequest request, Guid userId)
        {
            return userId != request.UserId
                ? BadRequest()
                : Ok(await Mediator.Send(request));
        }
        [HttpGet("document/{userId:guid}")]
        [TenantIdHeader]
        //[MustHavePermission(LrbAction.Update, LrbResource.UserProfile)]
        [OpenApiOperation("Get All a documents.", "")]
        public async Task<ActionResult<PagedResponse<UserDocumentDto, string>>> GetDocumentsAsync(Guid userId)
        {
            return Ok(await Mediator.Send(new GetAllDocumentsByUserIdRequest(userId)));
        }
        [HttpGet("getAllReportees")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Users)]
        [OpenApiOperation("Get All Reportees.", "")]
        public async Task<PagedResponse<ReportUserDto, string>> GetReportees()
        {
            return await Mediator.Send(new GetReporteesRequest());
        }
        [HttpGet("settings")]
        [TenantIdHeader]
        [OpenApiOperation("Get user settings.", "")]
        public async Task<Response<ViewUserSettingsDto>> GetAsync([FromQuery] GetSettingsRequest request)
        {
            return await Mediator.Send(request);
        }
        [HttpPut("settings")]
        [TenantIdHeader]
        [OpenApiOperation("Get user settings.", "")]
        public async Task<Response<ViewUserSettingsDto>> UpdateAsync(UpdateSettingsRequest request)
        {
            return await Mediator.Send(request);
        }
    }
}
