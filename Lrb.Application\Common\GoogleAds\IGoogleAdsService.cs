﻿using Lrb.Application.Common.Facebook;
using Lrb.Application.Common.GoogleAd;
using Lrb.Application.Integration.Web.Dtos;
using Lrb.Application.Integration.Web.Requests;

namespace Lrb.Application.Common.GoogleAds
{
    public interface IGoogleAdsService
    {
        public string? ClientId { get; set; }
        public string? ClientSecret { get; set; }
        public string? DeveloperToken { get; set; }
        public string? LoginCustomerId { get; set; }
        Task<bool> IsGoogleAdsAccountExists(string googleUserId, string? connectionString = null);
        Task<string> GetTenantIdByGoogleAdsAccountId(string googleUserId, string connectionString = null);
        Task<GoogleAdsAccountResponse?> GetCustomerDetailsAsync(string customerId, string token);
        Task<GoogleAdsUserAccessTokenResponse?> GetLongLivedUserAccessTokenAsync(string token);
        Task<GoogleAdsAdAccountsResponse?> GetAllGoogleAdAccountsAsync(string? customerId, string user_access_token);
        Task<GoogleAdResponse> GetAllGoogleAdsAsync(string customerId, string user_access_token);
        Task<List<GoogleAdOrFormWithUserOrPageTokenDto>> GetAdsAndFormsWithUserOrAccessTokenAsync(string? tenantId);
        Task<GoogleAdsBulkLeadDto> GetBulkLeadInfoAsync(string formOrAdOrAdGroupId, string userOrPageAccessToken, DateTime? fromDateTime = null, DateTime? toDateTime = null);
    }
}
