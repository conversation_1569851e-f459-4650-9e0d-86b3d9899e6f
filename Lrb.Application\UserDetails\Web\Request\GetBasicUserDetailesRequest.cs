﻿using Lrb.Application.Identity.Users;
using Lrb.Application.OrgProfile.Web;

namespace Lrb.Application.UserDetails.Web.Request
{
    public class GetBasicUserDetailesRequest : IRequest<Response<UserBasicDetailsDto>>
    {
        public GetBasicUserDetailesRequest(Guid id,string tenantId)
        {
            Id = id;
            TenantId = tenantId;
        }
        public Guid Id { get; set; }
        public string TenantId { get; set; }
    }
    public class GetBasicUserDetailesRequestHandler : IRequestHandler<GetBasicUserDetailesRequest, Response<UserBasicDetailsDto>>
    {
        private readonly IUserService _userService;
        private readonly IRepositoryWithEvents<Domain.Entities.UserDetails> _userDetailsrepository;
        private readonly IRepositoryWithEvents<Domain.Entities.Profile> _profileRepo; private readonly IDapperRepository _dapperRepository;


        public GetBasicUserDetailesRequestHandler(IUserService userService,
            IRepositoryWithEvents<Domain.Entities.UserDetails> userDetailsrepository, IRepositoryWithEvents<Profile> profileRepo, IDapperRepository dapperRepository)
        {
            _userService = userService;
            _userDetailsrepository = userDetailsrepository;
            _profileRepo = profileRepo;
            _dapperRepository = dapperRepository;
        }
        public async Task<Response<UserBasicDetailsDto>> Handle(GetBasicUserDetailesRequest request, CancellationToken cancellationToken)
        {
            try
            {
                var result = (await _dapperRepository.QueryStoredProcedureFromMainDbAsync<SubscriptionDetails>(
                    "LeadratBlack",
                    "GetSubscriptionAndAdminStatus",
                    new
                    {
                        uid = request.Id,
                        tenant = request.TenantId
                    })).FirstOrDefault();
                var profile = await _profileRepo.FirstOrDefaultAsync(new GetOnlyProfileSpec());
                var user = await _userService.GetAsync(request.Id.ToString(), cancellationToken);
                var existingUsers = await _userDetailsrepository.FirstOrDefaultAsync(new GetUserDetailsByIdSpec(request.Id), cancellationToken);
                user.TimeZoneInfo = existingUsers?.TimeZoneInfo ?? default;
                var basicUserDetails = user.Adapt<UserBasicDetailsDto>();
                basicUserDetails.IsGeoFenceActive = existingUsers?.IsGeoFenceActive ?? false;
                var userDto = user.Adapt<UserDetailsDto>();
                basicUserDetails.ShouldShowTimeZone = existingUsers?.ShouldShowTimeZone ?? default;
                basicUserDetails.ProfileCompletion = UserHelper.GetProfileCompletion(userDto);
                basicUserDetails.OrganizationName = profile?.DisplayName ?? "";
                basicUserDetails.SubscriptionDetails = result ?? new();
                return new(basicUserDetails);
            }
            catch (Exception ex)
            {
                return new() { Message = ex.Message };
            }
        }
    }
}
