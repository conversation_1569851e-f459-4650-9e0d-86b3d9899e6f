﻿
using Lrb.Application.Dashboard.Web;
using Lrb.Application.Dashboard.Web.Dtos;
using Lrb.Application.Dashboard.Web.Requests;
using Lrb.Domain.Enums;
using MediatR;
using Newtonsoft.Json;
using static Lrb.Application.Dashboard.Web.Requests.GetDashboardLeadsByAgeRequestHandler;
using Lrb.Application.UserDetails.Web;
using Lrb.Application.UserDetails.Web.Dtos;
using Lrb.Domain.Entities;
using Lrb.Application.MasterData;
using Lrb.Domain.Entities.MasterData;
using Lrb.Application.Reports.Web;
using Lrb.Application.Dashboard.Web.Requests.Custom;
using Lrb.Application.Reports.Web.Data.Dtos.User;
using Lrb.Application.Reports.Web.Data.Requests.User;
using Lrb.Application.Integration.Web.Dtos;
using Lrb.Application.Integration.Web.Requests.GoogleAds;
using Google.Ads.GoogleAds.V18.Services;

namespace Lrb.WebApi.Host.Controllers
{
    [Authorize]
    public class DashboardController : VersionedApiController
    {
        private readonly IMediator _mediator;
        public DashboardController(IMediator mediator)
        {
            _mediator = mediator;
        }
        //[HttpGet]
        //[TenantIdHeader]
        //[MustHavePermission(LrbAction.Search, LrbResource.Leads)]
        //[OpenApiOperation("Get dashboard basic details.", "")]
        //public async Task<Response<DashboardDto>> GetDashboardBasicDetailsAsync([FromQuery] GetDashboardBasicDetailsRequest request)
        //{
        //    return new(await _mediator.Send(request));
        //}
        //[HttpGet("default")]
        //[TenantIdHeader]
        //[MustHavePermission(LrbAction.Search, LrbResource.Leads)]
        //[OpenApiOperation("Get dashboard with default filters.", "")]
        //public async Task<Response<NewDashboardDto>> GetDashboardWithDefaultFilterAsync([FromQuery] GetDashboardWithDefaultFilterRequest request)
        //{
        //    return await _mediator.Send(request);
        //}
        //todo
        [HttpGet("source")]
        [TenantIdHeader]
        //[MustHavePermission(LrbAction.Search, LrbResource.Leads)]
        [OpenApiOperation("Get Lead Count By Source.", "")]
        public async Task<Response<List<DashboardBySourceWithTotalCountDto>>> GetDashboardBySourceAsync([FromQuery] GetDashboardBySourceRequest request)
        {
            return await _mediator.Send(request);
        }
        //todo
        [HttpGet("leadsInContactWith")]
        [TenantIdHeader]
        //[MustHavePermission(LrbAction.Search, LrbResource.Leads)]
        [OpenApiOperation("Get Leads In Contact With.", "")]
        public async Task<Response<LeadsInContactWithDto>> GetLeadsInContatWithAsync([FromQuery] GetLeadsInContactWithRequest request)
        {
            return await _mediator.Send(request);
        }


        [HttpGet("leadTracker")]
        [TenantIdHeader]
        //[MustHavePermission(LrbAction.Search, LrbResource.Leads)]
        [OpenApiOperation("Get Lead Tracker.", "")]
        public async Task<Response<Application.Dashboard.Web.LeadtrackerDto>> GetLeadTrackerAsync([FromQuery] GetLeadTrackerRequest request)
        {
            return await _mediator.Send(request);
        }

        //[HttpGet("siteVisitAndMeetingStatus")]
        //[TenantIdHeader]
        //[MustHavePermission(LrbAction.Search, LrbResource.Leads)]
        //[OpenApiOperation("Site Visit and Meeting Scheduled.", "")]
        //public async Task<Response<SiteVisitAndMeetingScheduledDto>> GetSiteVisitAndMeetingStatusAsync([FromQuery] GetSiteVisitStatusRequest request)
        //{
        //    return await _mediator.Send(request);
        //}
        //todo
        [HttpGet("upcomingEvents")]
        [TenantIdHeader]
        //[MustHavePermission(LrbAction.Search, LrbResource.Leads)]
        [OpenApiOperation("Get Upcoming Events.", "")]
        public async Task<Response<UpcomingEventsWrapperDto>> GetUpcomingEventsAsync([FromQuery] GetUpcomingEventsRequest request)
        {
            return await _mediator.Send(request);
        }
        //todo
        [HttpGet("upcomingEvents/month")]
        [TenantIdHeader]
        //[MustHavePermission(LrbAction.Search, LrbResource.Leads)]
        [OpenApiOperation("Get Upcoming Events Counts by Month.", "")]
        public async Task<Response<List<EventCountsByMonthDto>>> GetUpcomingEventsCountsAsync([FromQuery] GetDashboardEventsByMonthRequest request)
        {
            return await _mediator.Send(request);
        }
        //todo
        [HttpGet("leadReport")]
        [TenantIdHeader]
        //[MustHavePermission(LrbAction.Search, LrbResource.Leads)]
        [OpenApiOperation("Get Lead Report.", "")]
        public async Task<Response<List<LeadReportDto>>> GetLeadReportAsync([FromQuery] GetLeadReportRequest request)
        {
            return await _mediator.Send(request);
        }

        //todo
        [HttpGet("countByStatus")]
        [TenantIdHeader]
        //[MustHavePermission(LrbAction.Search, LrbResource.Leads)]
        [OpenApiOperation("Get Count By Status.", "")]
        public async Task<Response<LeadCountByStatusDto>> GetLeadCountByStatusAsync([FromQuery] GetDashboardByStatusRequest request)
        {
            return await _mediator.Send(request);
        }
        /**/


        #region New Endpoints
        //New lead Tracker

        [HttpGet("newLeadReport")]
        [TenantIdHeader]
        //[MustHavePermission(LrbAction.Search, LrbResource.Leads)]
        [OpenApiOperation("Get Lead Report.", "")]
        public async Task<Response<IEnumerable<DashboardLeadReportDto>>> GetNewLeadReportAsync([FromQuery] GetNewLeadReportRequest request)
        {
            return await _mediator.Send(request);
        }


        [HttpGet("newLeadTracker")]
        [TenantIdHeader]
        //[MustHavePermission(LrbAction.Search, LrbResource.Leads)]
        [OpenApiOperation("Get Lead Tracker.", "")]
        public async Task<Response<Dictionary<string, grouppedLeadtrackerDto>>> GetNewLeadTrackerAsync([FromQuery] GetNewLeadTrackerRequest request)
        {
            return await _mediator.Send(request);
        }
        [HttpGet("leadSource")]
        [TenantIdHeader]
        //[MustHavePermission(LrbAction.Search, LrbResource.Leads)]
        [OpenApiOperation("Get lead sources.", "")]
        public async Task<PagedResponse<DashboardNewLeadSourceDto, int>> GetCountLeadSourceByUser([FromQuery] GetDashboardNewLeadSourceRequest request)
        {
            return await (_mediator.Send(request));
        }


        [HttpGet("leadsPipeline")]
        [TenantIdHeader]
        //[MustHavePermission(LrbAction.Search, LrbResource.Leads)]
        [OpenApiOperation("Get lead pipelines details count.", "")]
        public async Task<Response<DashboardLeadsPipelineWebDto>> GetCountLeadLeadPipeline([FromQuery] GetDashboardPipelineDetailsWebRequest request)
        {
            return await (_mediator.Send(request));
        }



        [HttpGet("leadReceived")]
        [TenantIdHeader]
        //[MustHavePermission(LrbAction.Search, LrbResource.Leads)]
        [OpenApiOperation("Get Received Leads .", "")]
        public async Task<Response<Dictionary<string, Dictionary<string, int>>>> GeLeadsByYear([FromQuery] GetLeadReceivedCountByYearRequest request)
        {
            return await (_mediator.Send(request));
        }



        [HttpGet("teamsPerformance/Overview")]
        [TenantIdHeader]
        //[MustHavePermission(LrbAction.Search, LrbResource.Leads)]
        [OpenApiOperation("Get Teams Performance Overview.", "")]
        //  public async Task<Response<List<GroupedPerformanceDetails>>> GetTeamsPerformanceOverview([FromQuery] GetTeamPerformanceRequest request)
        public async Task<Response<IEnumerable<DashboardTeamPerformanceDto>>> GetTeamsPerformanceOverview([FromQuery] GetTeamPerformanceRequest request)
        {
            return await (_mediator.Send(request));
        }


        [HttpGet("sourceDetails")]
        [TenantIdHeader]
        //[MustHavePermission(LrbAction.Search, LrbResource.Leads)]
        [OpenApiOperation("Get lead All Source details.", "")]
        public async Task<List<object>> GetAllSourceDetails([FromQuery] GetDashboradLeadsDetailsRequest request)
        {
            return await (_mediator.Send(request));
        }

        #endregion

        /* [HttpGet("leadsByAge")]
         [TenantIdHeader]
         [MustHavePermission(LrbAction.Search, LrbResource.Leads)]
         [OpenApiOperation("Get Leads By Leads Age.", "")]
         public async Task<Response<List<LeadDetailsByAge>>> GetTeamsPerformanceOverview([FromQuery] GetDashboardLeadsByAgeRequest request)
         {
             return await (_mediator.Send(request));
         }
 */



        #region Custom
        [HttpGet("status/custom")]
        [TenantIdHeader]
        //[MustHavePermission(LrbAction.Search, LrbResource.Leads)]
        [OpenApiOperation("Get leads count by status.", "")]
        public async Task<Response<List<Lrb.Application.Dashboard.Web.ViewStatusDto>>> GetAsync([FromQuery] GetLeadStatusDataRequest request)
        {
            return await (_mediator.Send(request));
        }

        [HttpGet("toplevel/custom")]
        [TenantIdHeader]
        //[MustHavePermission(LrbAction.Search, LrbResource.Leads)]
        [OpenApiOperation("Get top level data.", "")]
        public async Task<Response<ViewTopLevelDto>> GetAsync([FromQuery] GetTopLevelDataRequest request)
        {
            return await (_mediator.Send(request));
        }
        #endregion
        [HttpGet("siteVisitDetails")]
        [TenantIdHeader]
        //[MustHavePermission(LrbAction.Search, LrbResource.Leads)]
        [OpenApiOperation("Get Leads Site Visit Details", "")]
        public async Task<Response<Dictionary<string, SiteVisitMeetingScheduledDto>>> GetSiteVisitDetails([FromQuery] GetDashboardLeadsSiteVisitDetailsRequest request)
        {
            return await (_mediator.Send(request));
        }


        [HttpGet("meetingDetails")]
        [TenantIdHeader]
        //[MustHavePermission(LrbAction.Search, LrbResource.Leads)]
        [OpenApiOperation("Get Leads meeting Details", "")]
        public async Task<Response<Dictionary<string, SiteVisitMeetingScheduledDto>>> GetMeetingDetails([FromQuery] GetDashboardLeadsMeetingDetailsRequest request)
        {
            return await (_mediator.Send(request));
        }
        [HttpGet("callReport")]
        [TenantIdHeader]
        //[MustHavePermission(LrbAction.Search, LrbResource.Leads)]
        [OpenApiOperation("Get Leads Call Report.", "")]
        public async Task<Response<Dictionary<string, DashboardCallGroupedReportDto>>> GetLeadActivityAsync([FromQuery] GetDashboardCallDetailsRequest request)
        {
            return await _mediator.Send(request);
        }
      
        [HttpGet("leadSource/V1")]
        [TenantIdHeader]
        //[MustHavePermission(LrbAction.Search, LrbResource.Leads)]
        [OpenApiOperation("Get lead sources.", "")]
        public async Task<PagedResponse<DashboardNewLeadSourceDto, int>> GetLeadSourceByUser([FromQuery] GetDashboardNewLeadSourceRequestV1 request)
        {
            return await (_mediator.Send(request));
        }
        [HttpGet("leadstatus/custom")]
        [TenantIdHeader]
        //[MustHavePermission(LrbAction.Search, LrbResource.Leads)]
        [OpenApiOperation("Get leads count by status.", "")]
        public async Task<PagedResponse<ViewUsersWithStatusDto, string>> GetleadstatusAsync([FromQuery] GetLeadStatusDataRequestV1 request)
        {
            return await (_mediator.Send(request));
        }
       
        [HttpGet("Datastatus")]
        [TenantIdHeader]
        //[MustHavePermission(LrbAction.View, LrbResource.Prospects)]
        [OpenApiOperation("Get data Count  report based on users", "")]
        public async Task<PagedResponse<DataByUserDto, string>> GetDataAsync([FromQuery] DataStatusByUsersRequest request)
        {
            return await Mediator.Send(request);
        }
        [HttpGet("Count/all/status")]
        [TenantIdHeader]
        //[MustHavePermission(LrbAction.Search, LrbResource.Leads)]
        [OpenApiOperation("Get all leads count by status.", "")]
        public async Task<Dictionary<string, string>> GetAllCountstatusAsync([FromQuery] GetAllLeadStatusCountRequest request)
        {
            return await (_mediator.Send(request));
        }
        [HttpGet("WhatsApp/Chats")]
        [TenantIdHeader]
        //[MustHavePermission(LrbAction.Search, LrbResource.Leads)]
        [OpenApiOperation("Get all leads with meassages.", "")]
        public async Task<PagedResponse<WhatsAppChatDto, string>> GetAsync([FromQuery] GetWhatsAppChatRequest request)
        {
            return await (_mediator.Send(request));
        }
        [HttpGet("calls")]
        [TenantIdHeader]
        //[MustHavePermission(LrbAction.Search, LrbResource.Leads)]
        [OpenApiOperation("Get all leads count with callrecordings.", "")]
        public async Task<PagedResponse<CallRecordsDto, string>> GetAsync([FromQuery] GetCallCountRequest request)
        {
            return await (_mediator.Send(request));
        }
        [HttpGet("lead/status/custom-count")]
        [TenantIdHeader]
        [OpenApiOperation("Get lead status report based on users", "")]
        public async Task<int> GetReportAsync([FromQuery] GeLeadStatusCountRequest request)
        {
            return await Mediator.Send(request);
        }
        [HttpGet("user/marketingfinances")]
        [TenantIdHeader]
        [OpenApiOperation("Get user Marketing finance report", "")]
        public async Task<PagedResponse<UserPerformanceDto, string>> GetMarketingRepFinanceortAsync([FromQuery] GetMarketingFinanceDetailsRequest request)
        {
            try
            {
                return await Mediator.Send(request);
            }
            catch (Exception ex)
            {
                return new PagedResponse<UserPerformanceDto, string>();
            }
        }

        [HttpGet("user/googleads/marketingfinances")]
        [TenantIdHeader]
        [OpenApiOperation("Get user GoogleAds Marketing finance report", "")]
        public async Task<PagedResponse<GoogleUserPerformanceDto, string>> GetGoogleAdsMarketingFinancReportAsync([FromQuery] GetGoogleAdsMarketingFinanceDetailsRequest request)
        {
            try
            {
                return await Mediator.Send(request);
            }
            catch (Exception ex)
            {
                return new PagedResponse<GoogleUserPerformanceDto, string>();
            }
        }

        [HttpGet("user/googleads/marketingfinances/campaigns")]
        [TenantIdHeader]
        [AllowAnonymous]
        [OpenApiOperation("Get user GoogleAds Marketing finance report", "")]
        public async Task<Response<List<GoogleCampaignAdMetricsDto>>> GetGoogleAdsMarketingFinancReportAsyncget([FromQuery] GetGoogleAdsCampaignAdMetricsRequest request)
        {
                return await Mediator.Send(request);
        }
    }
}
