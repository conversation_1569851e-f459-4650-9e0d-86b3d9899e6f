﻿using Lrb.Application.Common.BlobStorage;
using System.Web;

namespace Lrb.MobileApi.Host.Controllers
{
    [Authorize]
    public class BlobStorageController : VersionNeutralApiController
    {
        private readonly IBlobStorageService _blobStorageService;

        public BlobStorageController(IBlobStorageService blobStorageService)
        {
            _blobStorageService = blobStorageService;
        }
        [HttpPost("image/base64/{bucketName}/{folderName}/{fileName}")]
        public async Task<IActionResult> PostImage(string bucketName, string folderName, [FromBody] List<string> base64encodedFiles, string fileName)
        {
            List<string> keys = new();
            if (string.IsNullOrEmpty(bucketName) || string.IsNullOrEmpty(folderName))
            {
                return BadRequest(nameof(ArgumentNullException));
            }
            bucketName = HttpUtility.UrlDecode(bucketName);
            folderName = "Images/" + HttpUtility.UrlDecode(folderName);
            if (base64encodedFiles.Any(i => i != null))
            {
                foreach (var file in base64encodedFiles)
                {
                    string key = await _blobStorageService.UploadMobileObjectAsync(bucketName, folderName, file, fileName);
                    keys.Add(key);
                }
            }
            return Ok(new Response<List<string>>(keys));
        }
        [HttpPost("image/grouped_base64/{bucketName}/{folderName}/{fileName}")]
        public async Task<IActionResult> PostImage(string bucketName, string folderName, [FromBody] Dictionary<string, List<string>> groupedBase64encodedFiles, string fileName)
        {
            Dictionary<string, List<string>> groupedKeys = new();
            if (string.IsNullOrEmpty(bucketName) || string.IsNullOrEmpty(folderName))
            {
                return BadRequest(nameof(ArgumentNullException));
            }
            bucketName = HttpUtility.UrlDecode(bucketName);
            folderName = "Images/" + HttpUtility.UrlDecode(folderName);
            foreach (var group in groupedBase64encodedFiles)
            {
                groupedKeys.Add(group.Key, new());
                foreach (var image in group.Value)
                {
                    string key = await _blobStorageService.UploadMobileObjectAsync(bucketName, folderName, image, fileName);
                    groupedKeys[group.Key].Add(key);
                }
            }
            return Ok(new Response<Dictionary<string, List<string>>>(groupedKeys));
        }

        [HttpPost("doc/base64/{bucketName}/{folderName}/{fileName}")]
        public async Task<IActionResult> PostDocs(string bucketName, string folderName, [FromBody] List<string> base64encodedFiles, string fileName)
        {
            List<string> keys = new();
            if (string.IsNullOrEmpty(bucketName) || string.IsNullOrEmpty(folderName))
            {
                return BadRequest(nameof(ArgumentNullException));
            }
            bucketName = HttpUtility.UrlDecode(bucketName);
            folderName = "Documents/" + HttpUtility.UrlDecode(folderName);
            if (base64encodedFiles.Any(i => i != null))
            {
                foreach (var file in base64encodedFiles)
                {
                    string key = await _blobStorageService.UploadMobileObjectAsync(bucketName, folderName, file, fileName);
                    keys.Add(key);
                }
            }
            return Ok(new Response<List<string>>(keys));
        }
        [HttpPost("doc/grouped_base64/{bucketName}/{folderName}/{fileName}")]
        public async Task<IActionResult> PostDoc(string bucketName, string folderName, [FromBody] Dictionary<string, List<string>> groupedBase64encodedFiles, string fileName)
        {
            Dictionary<string, List<string>> groupedKeys = new();
            if (string.IsNullOrEmpty(bucketName) || string.IsNullOrEmpty(folderName))
            {
                return BadRequest(nameof(ArgumentNullException));
            }
            bucketName = HttpUtility.UrlDecode(bucketName);
            folderName = "Documents/" + HttpUtility.UrlDecode(folderName);
            foreach (var group in groupedBase64encodedFiles)
            {
                groupedKeys.Add(group.Key, new());
                foreach (var image in group.Value)
                {
                    string key = await _blobStorageService.UploadMobileObjectAsync(bucketName, folderName, image, fileName);
                    groupedKeys[group.Key].Add(key);
                }
            }
            return Ok(new Response<Dictionary<string, List<string>>>(groupedKeys));
        }
        [AllowAnonymous]
        [HttpPost("image/formFile/{bucketName}/{folderName}")]
        public async Task<IActionResult> PostImage(string bucketName, string folderName, [FromForm] IFormFileCollection files)
        {
            List<string> keys = new();
            if (string.IsNullOrEmpty(bucketName) || string.IsNullOrEmpty(folderName))
            {
                return BadRequest(nameof(ArgumentNullException));
            }
            bucketName = HttpUtility.UrlDecode(bucketName);
            folderName = "Images/" + HttpUtility.UrlDecode(folderName);
            if (files.Any(i => i != null))
            {
                foreach (var file in files)
                {
                    string key = await _blobStorageService.UploadObjectAsync(bucketName, folderName, file);
                    keys.Add(key);
                }
            }
            return Ok(new Response<List<string>>(keys));
        }
        [HttpPost("image/grouped_formFile/{bucketName}/{folderName}")]
        public async Task<IActionResult> PostImage(string bucketName, string folderName, [FromForm] Dictionary<string, IFormFileCollection> groupedFiles)
        {
            Dictionary<string, List<string>> groupedKeys = new();
            if (string.IsNullOrEmpty(bucketName) || string.IsNullOrEmpty(folderName))
            {
                return BadRequest(nameof(ArgumentNullException));
            }
            bucketName = HttpUtility.UrlDecode(bucketName);
            folderName = "Images/" + HttpUtility.UrlDecode(folderName);
            foreach (var group in groupedFiles)
            {
                groupedKeys.Add(group.Key, new());
                foreach (var image in group.Value)
                {
                    string key = await _blobStorageService.UploadObjectAsync(bucketName, folderName, image);
                    groupedKeys[group.Key].Add(key);
                }
            }
            return Ok(new Response<Dictionary<string, List<string>>>(groupedKeys));
        }
        [HttpPost("doc/formFile/{bucketName}/{folderName}")]
        public async Task<IActionResult> PostDoc(string bucketName, string folderName, [FromForm] IFormFileCollection files)
        {
            List<string> keys = new();
            if (string.IsNullOrEmpty(bucketName) || string.IsNullOrEmpty(folderName))
            {
                return BadRequest(nameof(ArgumentNullException));
            }
            bucketName = HttpUtility.UrlDecode(bucketName);
            folderName = "Documents/" + HttpUtility.UrlDecode(folderName);
            if (files.Any(i => i != null))
            {
                foreach (var file in files)
                {
                    string key = await _blobStorageService.UploadObjectAsync(bucketName, folderName, file);
                    keys.Add(key);
                }
            }
            return Ok(new Response<List<string>>(keys));
        }
        [HttpPost("doc/grouped_formFile/{bucketName}/{folderName}")]
        public async Task<IActionResult> PostDoc(string bucketName, string folderName, [FromForm] Dictionary<string, IFormFileCollection> groupedFiles)
        {
            Dictionary<string, List<string>> groupedKeys = new();
            if (string.IsNullOrEmpty(bucketName) || string.IsNullOrEmpty(folderName))
            {
                return BadRequest(nameof(ArgumentNullException));
            }
            bucketName = HttpUtility.UrlDecode(bucketName);
            folderName = "Documents/" + HttpUtility.UrlDecode(folderName);
            foreach (var group in groupedFiles)
            {
                groupedKeys.Add(group.Key, new());
                foreach (var file in group.Value)
                {
                    string key = await _blobStorageService.UploadObjectAsync(bucketName, folderName, file);
                    groupedKeys[group.Key].Add(key);
                }
            }
            return Ok(new Response<Dictionary<string, List<string>>>(groupedKeys));
        }
        [HttpGet("{bucketName}/{key}")]
        public async Task<IActionResult> Get(string bucketName, string key)
        {
            if (string.IsNullOrEmpty(bucketName) || string.IsNullOrEmpty(key))
            {
                return BadRequest(nameof(ArgumentNullException));
            }
            bucketName = HttpUtility.UrlDecode(bucketName);
            key = HttpUtility.UrlDecode(key);
            Stream file = await _blobStorageService.GetObjectAsync(bucketName, key);
            MemoryStream memoryStream = new();
            await file.CopyToAsync(memoryStream);
            return Ok(new Response<byte[]>(memoryStream.ToArray()));
        }
        [HttpGet("preSignedURL/{bucketName}/{key}")]
        public async Task<IActionResult> GetPresignedUrl(string bucketName, string key, [FromQuery] int validityInSeconds = 3600)
        {
            if (string.IsNullOrEmpty(bucketName) || string.IsNullOrEmpty(key))
            {
                return BadRequest(nameof(ArgumentNullException));
            }
            bucketName = HttpUtility.UrlDecode(bucketName);
            key = HttpUtility.UrlDecode(key);

            var url = await _blobStorageService.GetPreSignedURL(bucketName, key, validityInSeconds);
            return Ok(new Response<string>(url));
        }
        [AllowAnonymous]
        [HttpPost("image/grouped_base64/{bucketName}/{folderName}")]
        public async Task<IActionResult> PostImage(string bucketName, string folderName, [FromBody] Dictionary<string, List<string>> groupedBase64encodedFiles)
        {
            Dictionary<string, List<string>> groupedKeys = new();
            if (string.IsNullOrEmpty(bucketName) || string.IsNullOrEmpty(folderName))
            {
                return BadRequest(nameof(ArgumentNullException));
            }
            bucketName = HttpUtility.UrlDecode(bucketName);
            folderName = "Images/" + HttpUtility.UrlDecode(folderName);
            foreach (var group in groupedBase64encodedFiles)
            {
                groupedKeys.Add(group.Key, new());
                foreach (var image in group.Value)
                {
                    string key = await _blobStorageService.UploadObjectAsync(bucketName, folderName, image);
                    groupedKeys[group.Key].Add(key);
                }
            }
            return Ok(new Response<Dictionary<string, List<string>>>(groupedKeys));
        }
        [AllowAnonymous]
        [HttpPost("CallRecording/anonymous/{bucketName}/{id}/{moduleName}/{tenantId}/{fileName}")]
        public async Task<IActionResult> PostCallRecording(Guid id, string moduleName, string tenantId, string bucketName, [FromBody] List<string> base64encodedFiles, string fileName)
        {
            string folderName = $"Tenant_Date/{tenantId}/{moduleName}/{id}/";
            List<string> keys = new();
            if (string.IsNullOrEmpty(bucketName) || string.IsNullOrEmpty(folderName))
            {
                return BadRequest(nameof(ArgumentNullException));
            }
            bucketName = HttpUtility.UrlDecode(bucketName);
            folderName = "Documents/" + HttpUtility.UrlDecode(folderName);
            if (base64encodedFiles.Any(i => i != null))
            {
                foreach (var file in base64encodedFiles)
                {
                    string key = await _blobStorageService.UploadWebObjectAsync(bucketName, folderName, file, fileName);
                    keys.Add(key);
                }
            }
            return Ok(new Response<List<string>>(keys));
        }

        [RequestSizeLimit(500 * 1024 * 1024)] // 500MB
        [RequestFormLimits(MultipartBodyLengthLimit = 500 * 1024 * 1024)] // 500MB
        [HttpPost("doc/formFile")]
        public async Task<IActionResult> PostDocuments([FromForm] BlobData data)
        {
            List<string> keys = new();
            if (string.IsNullOrEmpty(data.BucketName) || string.IsNullOrEmpty(data.FolderName))
            {
                return BadRequest(nameof(ArgumentNullException));
            }
            data.BucketName = HttpUtility.UrlDecode(data.BucketName);
            data.FolderName = "Documents/" + HttpUtility.UrlDecode(data.FolderName);
            if (data.Files?.Any(i => i != null) ?? false)
            {
                foreach (var file in data.Files)
                {
                    string key = await _blobStorageService.UploadObjectAsync(data.BucketName, data.FolderName, file);
                    keys.Add(key);
                }
            }
            return Ok(new Response<List<string>>(keys));
        }
        public class BlobData
        {
            public string BucketName { get; set; } = default!;
            public string FolderName { get; set; } = default!;
            public List<IFormFile> Files { get; set; } = default!;
        }
    }
}
