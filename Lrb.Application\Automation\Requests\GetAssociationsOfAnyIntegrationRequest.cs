﻿using Lrb.Application.Agency.Web;
using Lrb.Application.Automation.Dtos;
using Lrb.Application.Integration.Web;
using Lrb.Application.Integration.Web.Specs;
using Lrb.Application.TempProject.Dtos;
using Mapster;

namespace Lrb.Application.Automation.Requests
{
    public class GetAssociationsOfAnyIntegrationRequest : IRequest<Response<IntegrationAssignmentDto?>>
    {
        public Guid Id { get; set; }
        public LeadSource Source { get; set; }
    }
    public class GetAssociationsOfAnyIntegrationRequestHandler : IRequestHandler<GetAssociationsOfAnyIntegrationRequest, Response<IntegrationAssignmentDto?>>
    {
        private readonly IReadRepository<FacebookAdsInfo> _fbAdsRepo;
        private readonly IReadRepository<FacebookLeadGenForm> _fbFormRepo;
        private readonly IReadRepository<IntegrationAccountInfo> _integrationAccRepo;
        private readonly IRepositoryWithEvents<Lrb.Domain.Entities.Agency> _agencyRepo;


        public GetAssociationsOfAnyIntegrationRequestHandler(
            IReadRepository<FacebookAdsInfo> fbAdsRepo,
            IReadRepository<FacebookLeadGenForm> fbFormRepo,
            IReadRepository<IntegrationAccountInfo> integrationAccRepo,
            IRepositoryWithEvents<Domain.Entities.Agency> agencyRepo)
        {
            _fbAdsRepo = fbAdsRepo;
            _fbFormRepo = fbFormRepo;
            _integrationAccRepo = integrationAccRepo;
            _agencyRepo = agencyRepo;
        }
        public async Task<Response<IntegrationAssignmentDto?>> Handle(GetAssociationsOfAnyIntegrationRequest request, CancellationToken cancellationToken)
        {
            var integrationAcc = await GetIntegrationAccountInfoAsync(request.Source, request.Id);

            if (integrationAcc == null)
            {
                var fbAd = await _fbAdsRepo.FirstOrDefaultAsync(new FacebookAdsInfoByIdSpec(request.Id), cancellationToken);
                if (fbAd == null)
                {
                    var fbForm = await _fbFormRepo.FirstOrDefaultAsync(new FacebookFormByIdSpec(request.Id), cancellationToken);

                    if (fbForm == null)
                    {
                        return new("Please provide a valid Id.");
                    }
                    var assignments = fbForm.Assignment?.Adapt<IntegrationAssignmentDto>();
                    if (assignments != null)
                    {
                        assignments.CountryCode = fbForm.CountryCode;
                        if (assignments.Agency == null)
                        {
                            var agency = await _agencyRepo.FirstOrDefaultAsync(new GetAgencyByNameSpec(fbForm?.AgencyName), cancellationToken);
                            assignments.Agency = agency?.Adapt<AgencyAssignmentDto>();
                        }
                        return new(assignments);
                    }
                    else
                    {
                        var agency = await _agencyRepo.FirstOrDefaultAsync(new GetAgencyByNameSpec(fbForm?.AgencyName), cancellationToken);
                        return new Response<IntegrationAssignmentDto?>(new IntegrationAssignmentDto
                        {
                            Project = null,
                            Location = null,
                            CountryCode = fbForm.CountryCode,
                            Agency = agency?.Adapt<AgencyAssignmentDto>(),
                            ChannelPartner = null,
                            Property = null,
                            Campaign = null,
                        });
                    }
                }
                else
                {
                    var assignments = fbAd.Assignment?.Adapt<IntegrationAssignmentDto>();
                    if (assignments != null)
                    {
                        assignments.CountryCode = fbAd.CountryCode;
                        if (assignments.Agency == null)
                        {
                            var agency = await _agencyRepo.FirstOrDefaultAsync(new GetAgencyByNameSpec(fbAd?.AgencyName), cancellationToken);
                            assignments.Agency = agency?.Adapt<AgencyAssignmentDto>();
                        }
                        return new(assignments);
                    }
                    else
                    {
                        var agency = await _agencyRepo.FirstOrDefaultAsync(new GetAgencyByNameSpec(fbAd?.AgencyName), cancellationToken);

                        return new Response<IntegrationAssignmentDto?>(new IntegrationAssignmentDto
                        {
                            Project = null,
                            Location = null,
                            CountryCode = fbAd.CountryCode,
                            Agency = agency?.Adapt<AgencyAssignmentDto>(),
                            ChannelPartner = null,
                            Property = null,
                            Campaign = null,
                        });
                    }
                }
            }
            else
            {
                try
                {

                    var module = integrationAcc?.Assignment?.Project?.UserAssignment?.Module;
                    var result = integrationAcc.Assignment?.Adapt<IntegrationAssignmentDto>();
                    if (result != null)
                    {
                        result.CountryCode = integrationAcc.CountryCode;
                        if (result.Agency == null)
                        {
                            var agency = await _agencyRepo.FirstOrDefaultAsync(new GetAgencyByNameSpec(integrationAcc?.AgencyName), cancellationToken);
                            result.Agency = agency?.Adapt<AgencyAssignmentDto>();
                        }
                        if (result?.Project?.UserAssignment != null)
                        {
                            result.Project.UserAssignment.Module = module;
                        }

                        return new(result);
                    }
                    else
                    {
                        var agency = await _agencyRepo.FirstOrDefaultAsync(new GetAgencyByNameSpec(integrationAcc?.AgencyName), cancellationToken);
                        return new Response<IntegrationAssignmentDto?>(new IntegrationAssignmentDto
                        {
                            Project = null,
                            Location = null,
                            CountryCode = integrationAcc.CountryCode,
                            Agency = agency?.Adapt<AgencyAssignmentDto>(),
                            ChannelPartner = null,
                            Property = null,
                            Campaign = null,
                        });
                    }
                }
                catch (Exception ex)
                {
                    throw;
                }
            }
        }
        private async Task<IntegrationAccountInfo?> GetIntegrationAccountInfoAsync(LeadSource source, Guid id)
        {
            IntegrationAccountInfo? integrationAccountInfo = null;
            switch (source)
            {
                case LeadSource.Facebook:
                    integrationAccountInfo = await _integrationAccRepo.FirstOrDefaultAsync(new IntegrationAccInfoByFacebookIdOrIdSpec(id), CancellationToken.None);
                    break;
                case LeadSource.Gmail:
                    integrationAccountInfo = await _integrationAccRepo.FirstOrDefaultAsync(new IntegrationAccByGmailIdOrIdSpec(id), CancellationToken.None);
                    break;
                case LeadSource.GoogleAds:
                    integrationAccountInfo = await _integrationAccRepo.FirstOrDefaultAsync(new IntegrationAccInfoByGoogleAdIdOrId(id), CancellationToken.None);
                    break;
                default:
                    integrationAccountInfo = await _integrationAccRepo.FirstOrDefaultAsync(new IntegrationAccountByIdSpec(id), CancellationToken.None);
                    break;
            }
            return integrationAccountInfo;
        }
    }
}
