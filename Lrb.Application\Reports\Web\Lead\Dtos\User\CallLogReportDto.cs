﻿namespace Lrb.Application.Reports.Web
{
    public class ViewCallLogReportDto 
    {
        public Guid? UserId { get; set; }
        private string? _firstName;
        private string? _lastName;
        private string? _userName;

        public string? FirstName
        {
            get => _firstName;
            set
            {
                _firstName = value;
                UpdateUserName();
            }
        }

        public string? LastName
        {
            get => _lastName;
            set
            {
                _lastName = value;
                UpdateUserName();
            }
        }

        public string? UserName
        {
            get => _userName;
            set => _userName = value;
        }
        private void UpdateUserName()
        {
            _userName = $"{_firstName} {_lastName}".Trim();
        }
        public long IncomingAnswered { get; set; }
        public long IncomingMissed { get; set; }
        public long TotalIncomingCalls { get; set; }
        public long OutgoingAnswered { get; set; }
        public long OutgoingNotConnected { get; set; }
        public long TotalOutgoingCalls { get; set; }
        public string? TotalTalkTime { get; set; }
        public string? AverageTalkTime { get; set; }
        public string? MaxTalkTime { get; set; }
        public string? MinTalkTime { get; set; }
        public long TotalCalls { get; set; }
    }
    public class CallLogReportDto : IDto
    {
        public Guid? UserId { get; set; }
        public string? FirstName { get; set; }
        public string? LastName { get; set; }
        public long IncomingAnswered { get; set; }
        public long IncomingMissed { get; set; }
        public long TotalIncomingCalls { get; set; }
        public long OutgoingAnswered { get; set; }
        public long OutgoingNotConnected { get; set; }
        public long TotalOutgoingCalls { get; set; }
        public double TotalTalkTime { get; set; }
        public double AverageTalkTime { get; set; }
        public double MaxTalkTime { get; set; }
        public double MinTalkTime { get; set; }
        public long TotalCalls { get; set; }
    }
    public class CallLogReportFormattedDto : IDto
    {
        public string SlNo { get; set; } = default!;
        public string? UserName { get; set; }
        //public string? LastName { get; set; }
        public IncomingDto? Incoming { get; set; }
        public OutgoingDto? Outgoing { get; set; }
        public string? TotalTalkTime { get; set; }
        public string? AverageTalkTime { get; set; }
        public string? MaxTalkTime { get; set; }
        public string? MinTalkTime { get; set; }
        public long TotalCalls { get; set; }
    }
    public class IncomingDto
    {
        public long Answered { get; set; }
        public long Missed { get; set; }
        public long Total { get; set; }
    }
    public class OutgoingDto
    {
        public long Answered { get; set; }
        public long NotConnected { get; set; }
        public long Total { get; set; }
    }
}
