﻿using Lrb.Application.Common.GoogleAds;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;

namespace Lrb.Infrastructure.GoogleAds
{
    public static class Startup
    {
        public static IServiceCollection AddGoogleAds(this IServiceCollection services, IConfiguration config)
        {
            services.Configure<GoogleAdsSettings>(config.GetSection("GoogleAdsSettings"));
            services.AddScoped<IGoogleAdsService, GoogleAdsService>();

            return services;
        }
    }
}
