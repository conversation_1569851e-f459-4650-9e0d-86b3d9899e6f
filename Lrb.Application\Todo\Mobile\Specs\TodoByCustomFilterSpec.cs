﻿using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;

namespace Lrb.Application.Todo.Mobile
{
    //public class TodoByCustomFilterSpec : EntitiesByPaginationFilterSpec<Domain.Entities.Todo>
    //{
    //    public TodoByCustomFilterSpec(GetAllTodoRequest filter, Guid userId) : base(filter)
    //    {
    //        Query.Where(i => !i.IsDeleted && (JsonConvert.DeserializeObject<List<Guid>>(i.AssignedUserIds.ToString()).Equals(userId) || i.CreatedBy == userId))
    //            .OrderBy(i => i.IsMarkedDone)
    //            .ThenBy(i => i.ScheduledDateTime);

    //        //if (!string.IsNullOrWhiteSpace(filter.SearchTitleOrNotes))
    //        //{
    //        //    Query.Where(i => i.Title.ToLower().Contains(filter.SearchTitleOrNotes.ToLower()) || i.Notes.ToLower().Contains(filter.SearchTitleOrNotes.ToLower()));
    //        //}


    //        //if (filter.TodoFilterType != default)
    //        //{
    //        //    switch (filter.TodoFilterType)
    //        //    {
    //        //        case TodoFilterType.Today:
    //        //            Query.Where(i => i.ScheduledDateTime != null && i.ScheduledDateTime.Value >= DateTime.UtcNow.Date && i.ScheduledDateTime < DateTime.UtcNow.Date.AddDays(1));
    //        //            break;
    //        //        case TodoFilterType.Upcoming:
    //        //            Query.Where(i => i.ScheduledDateTime != null && i.ScheduledDateTime >= DateTime.UtcNow.Date.AddDays(1) && !i.IsMarkedDone);
    //        //            break;
    //        //        case TodoFilterType.Overdue:
    //        //            Query.Where(i => i.ScheduledDateTime != null && i.ScheduledDateTime < DateTime.UtcNow && !i.IsMarkedDone);
    //        //            break;
    //        //        case TodoFilterType.Completed:
    //        //            Query.Where(i => i.ScheduledDateTime != null && i.IsMarkedDone);
    //        //            break;
    //        //        case TodoFilterType.All:
    //        //        default:
    //        //            break;

    //        //    }
    //        //}
    //    }
    //}
    public class TodoCountByCustomFilterSpec : Specification<Domain.Entities.Todo>
    {
        //public TodoCountByCustomFilterSpec(GetAllTodoRequest filter)
        //{
        //    Query.Where(i => !i.IsDeleted);

        //    if (!string.IsNullOrWhiteSpace(filter.SearchTitleOrNotes))
        //    {
        //        Query.Where(i => i.Title.ToLower().Contains(filter.SearchTitleOrNotes.ToLower()) || i.Notes.ToLower().Contains(filter.SearchTitleOrNotes.ToLower()));
        //    }
        //}
    }
    public class TodoFilterCountSpec : Specification<Domain.Entities.Todo>
    {
        //public TodoFilterCountSpec(GetAllTodoRequest filter, Guid userId)
        //{
        //  //  Query.Where(i => !i.IsDeleted && (i.AssignedToUserId == userId || i.CreatedBy == userId));

        //    if (!string.IsNullOrWhiteSpace(filter.SearchTitleOrNotes))
        //    {
        //        Query.Where(i => i.Title.ToLower().Contains(filter.SearchTitleOrNotes.ToLower()) || i.Notes.ToLower().Contains(filter.SearchTitleOrNotes.ToLower()));
        //    }

        //    if (filter.TodoFilterType != default)
        //    {
        //        switch (filter.TodoFilterType)
        //        {
        //            case TodoFilterType.Today:
        //                Query.Where(i => i.ScheduledDateTime != null && i.ScheduledDateTime.Value >= DateTime.UtcNow.Date && i.ScheduledDateTime < DateTime.UtcNow.Date.AddDays(1));
        //                break;
        //            case TodoFilterType.Upcoming:
        //                Query.Where(i => i.ScheduledDateTime != null && i.ScheduledDateTime >= DateTime.UtcNow.Date.AddDays(1) && !i.IsMarkedDone);
        //                break;
        //            case TodoFilterType.Overdue:
        //                Query.Where(i => i.ScheduledDateTime != null && i.ScheduledDateTime < DateTime.UtcNow && !i.IsMarkedDone);
        //                break;
        //            case TodoFilterType.Completed:
        //                Query.Where(i => i.ScheduledDateTime != null && i.IsMarkedDone);
        //                break;
        //            case TodoFilterType.All:
        //            default:
        //                break;

        //        }
        //    }
        //}

    }
    public class GetTodoBySheduleDateSpec : Specification<Lrb.Domain.Entities.Todo>
    {
        public GetTodoBySheduleDateSpec(GetTodobySheduledDateRequest filter, ICurrentUser currentUser, bool applyPagination = false)
        {
            var userId = currentUser.GetUserId();
            var subIds = currentUser.GetSubordinateIds() ?? new();
            var reporteeIds = subIds.Where(i => i != currentUser.GetUserId() && i != Guid.Empty);
            var selfWithReporteeIds = subIds.Where(i => i != Guid.Empty);

            Query.Where(i => !i.IsDeleted && (i.CreatedBy == userId || EF.Functions.JsonContains(i.AssignedUserIds, JsonConvert.SerializeObject(new[] { userId.ToString()}))));

            if (filter.Fromdate != default && filter.Todate != default)
            {
                try
                {
                    var timeZoneId = !string.IsNullOrWhiteSpace(filter.TimeZoneId)? filter.TimeZoneId : "Asia/Kolkata";

                    var fromUtc = TimeZoneInfo.ConvertTimeToUtc(DateTime.SpecifyKind(filter.Fromdate.Value.Date, DateTimeKind.Unspecified), TimeZoneInfo.FindSystemTimeZoneById(timeZoneId));
                    var toUtc = TimeZoneInfo.ConvertTimeToUtc(DateTime.SpecifyKind(filter.Todate.Value.Date.AddDays(1), DateTimeKind.Unspecified), TimeZoneInfo.FindSystemTimeZoneById(timeZoneId));

                    Query.Where(i =>i.ScheduledDateTime != null && i.ScheduledDateTime >= fromUtc && i.ScheduledDateTime < toUtc);
                }
                catch (Exception ex)
                {
                    throw new Exception($"Invalid TimeZoneId provided: {filter.TimeZoneId}", ex);
                }
            }

            if (filter.TodoFilterType != default)
            {
                var today = DateTime.UtcNow.Date;
                switch (filter.TodoFilterType)
                {
                    case TodoFilterType.Today:
                        Query.Where(i => i.ScheduledDateTime >= today && i.ScheduledDateTime < today.AddDays(1));
                        break;
                    case TodoFilterType.Upcoming:
                        Query.Where(i => i.ScheduledDateTime >= today.AddDays(1) && !i.IsMarkedDone);
                        break;
                    case TodoFilterType.Overdue:
                        Query.Where(i => i.ScheduledDateTime < DateTime.UtcNow && !i.IsMarkedDone);
                        break;
                    case TodoFilterType.Completed:
                        Query.Where(i => i.IsMarkedDone);
                        break;
                }
            }

            Query.OrderBy(i => i.IsMarkedDone).ThenBy(i => i.ScheduledDateTime);
            if (applyPagination)
            {
                var pageNumber = filter.PageNumber > 0 ? filter.PageNumber : 1;
                var pageSize = filter.PageSize > 0 ? filter.PageSize : int.MaxValue;

                Query.Skip((pageNumber - 1) * pageSize).Take(pageSize);
            }
        }
    }
}
