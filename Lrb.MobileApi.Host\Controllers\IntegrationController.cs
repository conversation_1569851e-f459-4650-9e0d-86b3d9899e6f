﻿using Lrb.Application.Common.Gmail;
using Lrb.Application.Common.IVR;
using Lrb.Application.Common.IVR.Common.Dtos;
using Lrb.Application.Common.Servetel.ResponseDtos;
using Lrb.Application.Integration.Mobile;
using Lrb.Application.Integration.Mobile.Dtos;
using Lrb.Application.Integration.Mobile.Requests;
using Lrb.Domain.Enums;
using Lrb.Shared.Multitenancy;
using Mapster;
using Microsoft.Extensions.Primitives;
using Newtonsoft.Json;

namespace Lrb.MobileApi.Host.Controllers
{
    [Authorize]
    public class IntegrationController : VersionedApiController
    {
        private readonly IServetelService _servetelService;

        public IntegrationController(IServetelService servetelService)
        {
            _servetelService = servetelService;
        }

        [HttpPost]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.Create, LrbResource.Integration)]
        [OpenApiOperation("Create a new Integration.", "")]
        public Task<Response<string>> Create([FromBody] CreateIntegrationRequest request)
        {
            return Mediator.Send(request);
        }

        [HttpPost("Servetel")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.Create, LrbResource.Integration)]
        [OpenApiOperation("Create a new Servetel IVR Integration.", "")]
        public Task<Response<string>> CreateServetelIntegration([FromBody] CreateServetelIntegrationRequest request)
        {
            return Mediator.Send(request);
        }

        [HttpPost("Servetel/ClickToCall")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.IVRCall, LrbResource.Integration)]
        [OpenApiOperation("Click to Call Servetel IVR", "")]
        public async Task<Response<ClickToCallResponseDto>> ClickToCall(ServetelClickToCallRequest request)
        {
            return await Mediator.Send(request);
        }
        

        [HttpPut("MakePrimary/{accountId:guid}")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.Update, LrbResource.Integration)]
        [OpenApiOperation("Make an IVR account as primary account", "")]
        public async Task<Response<bool>> MakeIVRPrimary(Guid accountId)
        {
            MakeIntegrationAccountPrimaryRequest request = new(accountId);
            return await Mediator.Send(request);
        }

        [HttpGet("Servetel/agents")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.IVRCall, LrbResource.Integration)]
        [OpenApiOperation("Get a list of all agents")]
        public async Task<PagedResponse<ServetelIVRAgentDto, string>> GetAllAgents([FromQuery] GetAllAgentsRequest request)
        {
            return await Mediator.Send(request);
        }

        [HttpPost("Servetel/agent")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.IVRCall, LrbResource.Integration)]
        [OpenApiOperation("Create a new agent.")]
        public async Task<Response<AgentCreationResponseDto>> CreateAgent(CreateAgentRequest request)
        {
            return await Mediator.Send(request);
        }

        [HttpGet]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Integration)]
        [OpenApiOperation("Get all Integrations.", "")]
        public Task<PagedResponse<IntegrationDto, string>> GetAllAsync([FromQuery] GetAllIntegrationAccountRequest request)
        {
            return Mediator.Send(request);
        }
        [HttpGet("totalCount")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Integration)]
        [OpenApiOperation("Get totalCount of Integrations.", "")]
        public Task<Response<Dictionary<LeadSource, IntegrationCountDto>>> GetAllAsync()
        {
            return Mediator.Send(new GetIntegrationTotalCountRequest());
        }
        [HttpPost("Gmail")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.Create, LrbResource.Integration)]
        [OpenApiOperation("Integrate a new Gmail Account.", "")]
        public Task<Response<Guid>> IntegrateGmail([FromBody] CreateMobileClientGmailDataRequest request)
        {
            return Mediator.Send(request);
        }
        [HttpPost("Gmail/messages")]
        [AllowAnonymous]
        //[TenantIdHeader]
        //[OpenApiOperation("Read the messages from google PubSub", "")]
        public Task<Response<bool>> ReadMessages([FromBody] object value)
        {
            CreateLeadFromPubSubMessageRequest request = new();
            request.IsMobileClient = true;
            request.PubSubMessage = JsonConvert.DeserializeObject<Root>(value.ToString());
            return Mediator.Send(request);
        }

        [HttpGet("IntegrationAccountInfo/{id:guid}")]
        [TenantIdHeader]
        [OpenApiOperation("Get by IntegrationAccountId.", "")]
        public Task<Response<string>> GetById(Guid id)
        {
            return Mediator.Send(new GetIntegrationAccountInfoRequest(id));
        }

        [ApiKey]
        [AllowAnonymous]
        [HttpPost("Housing")]
        [TenantIdHeader]
        [OpenApiOperation("Push endpoint for Housing.com integration", "")]
        public Task<Response<bool>> PostHousing([FromBody] ListingSitesIntegrationRequest lead)
        {
            var accountId = Guid.Parse(this.HttpContext.Request.Headers["API-Key"]);
            lead.LeadSource = Domain.Enums.LeadSource.Housing;
            lead.AccountId = accountId;
            var res = Mediator.Send(lead);
            return res;
        }
        [ApiKey]
        [AllowAnonymous]
        [HttpPost("NinetyNineAcres")]
        [TenantIdHeader]
        [OpenApiOperation("Push endpoint for NinetyNineAcres integration", "")]
        public Task<Response<bool>> PostNinetynineAcres([FromBody] ListingSitesIntegrationRequest lead)
        {
            var accountId = Guid.Parse(this.HttpContext.Request.Headers["API-Key"]);
            lead.LeadSource = Domain.Enums.LeadSource.NinetyNineAcres;
            lead.AccountId = accountId;
            var res = Mediator.Send(lead);
            return res;
        }

        [ApiKey]
        [AllowAnonymous]
        [HttpPost("MagicBricks")]
        [TenantIdHeader]
        [OpenApiOperation("Push endpoint for MagicBricks integration", "")]
        public Task<Response<bool>> PostMagicBricks([FromBody] ListingSitesIntegrationRequest lead)
        {
            var accountId = Guid.Parse(this.HttpContext.Request.Headers["API-Key"]);
            lead.LeadSource = Domain.Enums.LeadSource.MagicBricks;
            lead.AccountId = accountId;
            var res = Mediator.Send(lead);
            return res;
        }


        [ApiKey]
        [AllowAnonymous]
        [HttpPost("Website")]
        [TenantIdHeader]
        [OpenApiOperation("Push endpoint for any Website integration", "")]
        public Task<Response<bool>> PostWebsite([FromBody] List<WebsiteIntegrationDto>? Leads)
        {
            WebsiteIntegrationRequest command = new()
            {
                Leads = Leads
            };
            var accountId = Guid.Parse(this.HttpContext.Request.Headers["API-Key"]);
            command?.Leads?.ForEach(i => i.LeadSource = Domain.Enums.LeadSource.Website);
            command.AccountId = accountId;
            return Mediator.Send(command);
        }
        [ApiKey]
        [AllowAnonymous]
        [HttpPost("IVR")]
        [TenantIdHeader]
        [OpenApiOperation("Push endpoint for IVR integration", "")]
        public Task<Response<bool>> PostIVR([FromBody] IVRIntegrationDto dto)
        {
            var accountId = Guid.Parse(this.HttpContext.Request.Headers["API-Key"]);
            IVRIntegrationRequest command = dto.Adapt<IVRIntegrationRequest>();
            command.AccountId = accountId;
            return Mediator.Send(command);
        }

        [HttpDelete("{id:guid}")]
        [MustHavePermission(LrbAction.Delete, LrbResource.Integration)]
        [TenantIdHeader]
        [OpenApiOperation("Delete an integration account.", "")]
        public Task<Response<Guid>> DeleteAsync(Guid id)
        {
            return Mediator.Send(new DeleteIntegrationAccountRequest(id));
        }

        [HttpGet("Servetel/History")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Integration)]
        [OpenApiOperation("Get ServetelCallLogHistory by Customer Number", "")]
        public async Task<Response<Dictionary<DateTime, Dictionary<DateTime, List<ServetelCallLogHistoryDto>>>>> GetServetelCallLogHistory([FromQuery] string customerNo)
        {
            GetServetelCallLogHistoryByCustomerNoRequest request = new() { CustomerNo = customerNo };
            var res = await Mediator.Send(request);
            return res;
        }
        [HttpGet("agencynames")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Integration)]
        [OpenApiOperation("Get all agency names")]
        public async Task<Response<List<string>>> GetAsync()
        {
            return await Mediator.Send(new GetIntegrationAgencyNamesRequest());
        }
        [HttpGet("subsource")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Integration)]
        [OpenApiOperation("Get all integration subsources.", "")]
        public async Task<Response<Dictionary<LeadSource, List<string?>?>>> GetAllSubSourceAsync()
        {
            return await Mediator.Send(new GetAllIntegrationSubSourceRequest());
        }
        [AllowAnonymous]
        [TenantIdHeader]
        [HttpGet("IVR/count")]
        [OpenApiOperation("Get IVR Primary Account Count", "")]
        public async Task<Response<int>> GetIVRPrimaryCount()
        {
            return await Mediator.Send(new GetIVRPrimaryAccountRequest());
        }

        //Newly Added
        [HttpPost("common/ClickToCall")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.IVRCall, LrbResource.Integration)]
        [OpenApiOperation("Click to Call Common IVR", "")]
        public async Task<Response<ClickToCallCommonResponseDto>> ClickToCalla(CommonClickToCallRequest request)
        {
            return await Mediator.Send(request);
        }
        //Newly Added
        [HttpGet("IVR/virtual-numbers")]
        [OpenApiOperation("Get All Virtual Numbers", " ")]
        [TenantIdHeader]
        public async Task<Response<List<string>>> GetAllIVRVirtualNumbersAsync()
        {
            return await Mediator.Send(new GetAllVirtualNumbersRequest());
        }
        //Newly Added
        [HttpGet("IVR/virtual-number/assignment-check")]
        [OpenApiOperation("To check if any Virtual Number has been assigned to a user", " ")]
        [TenantIdHeader]
        public async Task<Response<VirtualNumberAssignmentResponseDto>> CheckVirtualNumberAssignmentAsync([FromQuery] CheckVirtualNumberAssignmentRequest request)
        {
            return await Mediator.Send(request);
        }

        [HttpPost("common/ClickToCall/config")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.IVRCall, LrbResource.Integration)]
        [OpenApiOperation("Click to Call Common IVR using stored config", "")]
        public async Task<Response<ClickToCallCommonResponseDto>> ClickToCallFromConfig(ClickToCallWithConfigRequest request)
        {
            return await Mediator.Send(request);
        }

        [HttpPut("status/{id:guid}")]
        [AllowAnonymous]
        [ApiKey]
        [TenantIdHeader]
        [OpenApiOperation("Update status of a lead.", "")]
        public async Task<ActionResult<Response<bool>>> UpdateStatusAsync(UpdateLeadStatusIntegrationRequest request, Guid id)
        {
            var apiKey = this.HttpContext.Request.Headers["API-Key"];
            StringValues tenantIds;
            this.HttpContext.Request.Headers.TryGetValue(MultitenancyConstants.TenantIdName, out tenantIds);

            if (tenantIds.Count > 0)
            {
                request.TenantId ??= tenantIds[0];
            }
            request.ApiKey ??= apiKey;
            return id != request.Id
                ? BadRequest()
                : Ok(await Mediator.Send(request));
        }
    }
}
