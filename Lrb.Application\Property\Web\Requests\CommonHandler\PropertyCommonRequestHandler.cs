﻿using Lrb.Application.Common.BlobStorage;
using Lrb.Domain.Entities.AmenitiesAttributes;
using Lrb.Domain.Entities.ErrorModule;
using Lrb.Domain.Entities.MasterData;
using Microsoft.Extensions.DependencyInjection;
using Newtonsoft.Json;

namespace Lrb.Application.Property.Web.Requests.CommonHandler
{
    public class PropertyCommonRequestHandler
    {
        protected readonly IServiceProvider _serviceProvider;
        protected readonly IRepositoryWithEvents<Domain.Entities.Property> _propertyRepo;
        protected readonly IDapperRepository _dapperRepository;
        protected readonly ILeadRepositoryAsync _leadRepositoryAsync;
        protected readonly IReadRepository<MasterAreaUnit> _masterAreaUnitRepo;
        protected readonly IReadRepository<PropertyDimension> _propertyDimensionRepo;
        protected readonly ICurrentUser _currentUser;
        protected readonly IRepositoryWithEvents<Domain.Entities.PropertyAssignment> _propertyAssignmentRepository;
        protected readonly IRepositoryWithEvents<PropertyDimension> _propertyDimensionInfoRepository;
        protected readonly IRepositoryWithEvents<PropertyAmenity> _propertyAmenitiesRepository;
        protected readonly IRepositoryWithEvents<PropertyAttribute> _propertyAttributesRepository;
        protected readonly IRepositoryWithEvents<PropertyOwnerDetails> _propertyOwnerDetailsRepository;
        protected readonly IRepositoryWithEvents<PropertyMonetaryInfo> _propertyMonetaryInfoRepository;
        protected readonly IRepositoryWithEvents<PropertyGallery> _propertyGallleryRepository;
        protected readonly IRepositoryWithEvents<Address> _addressRepo;
        protected readonly IRepositoryWithEvents<CustomMasterAmenity> _propertyAmenityListRepository;
        protected readonly IRepositoryWithEvents<CustomMasterAttribute> _propertyAttributeListRepository;
        protected readonly IRepositoryWithEvents<MasterPropertyType> _masterPropertyTypeRepo;
        protected readonly IRepositoryWithEvents<Lrb.Domain.Entities.Project> _projectsRepo;
        protected readonly IBlobStorageService _blobStorageService;
        protected readonly IRepositoryWithEvents<UserView> _userViewRepo;
        public PropertyCommonRequestHandler(IServiceProvider serviceProvider)
        {
            _serviceProvider = serviceProvider;
            _propertyRepo = _serviceProvider.GetRequiredService<IRepositoryWithEvents<Domain.Entities.Property>>();
            _dapperRepository = _serviceProvider.GetRequiredService<IDapperRepository>();
            _leadRepositoryAsync = _serviceProvider.GetRequiredService<ILeadRepositoryAsync>();
            _masterAreaUnitRepo = _serviceProvider.GetRequiredService<IReadRepository<MasterAreaUnit>>();
            _propertyDimensionRepo = _serviceProvider.GetRequiredService<IReadRepository<PropertyDimension>>();
            _currentUser = _serviceProvider.GetRequiredService<ICurrentUser>();
            _propertyAssignmentRepository = _serviceProvider.GetRequiredService<IRepositoryWithEvents<Domain.Entities.PropertyAssignment>>();
            _propertyDimensionInfoRepository = _serviceProvider.GetRequiredService<IRepositoryWithEvents<PropertyDimension>>();
            _propertyAmenitiesRepository = _serviceProvider.GetRequiredService<IRepositoryWithEvents<PropertyAmenity>>();
            _propertyAttributesRepository = _serviceProvider.GetRequiredService<IRepositoryWithEvents<PropertyAttribute>>();
            _propertyOwnerDetailsRepository = _serviceProvider.GetRequiredService<IRepositoryWithEvents<PropertyOwnerDetails>>();
            _propertyMonetaryInfoRepository = _serviceProvider.GetRequiredService<IRepositoryWithEvents<PropertyMonetaryInfo>>();
            _propertyGallleryRepository = _serviceProvider.GetRequiredService<IRepositoryWithEvents<PropertyGallery>>();
            _addressRepo = _serviceProvider.GetRequiredService<IRepositoryWithEvents<Address>>();
            _propertyAmenityListRepository = _serviceProvider.GetRequiredService<IRepositoryWithEvents<CustomMasterAmenity>>();
            _propertyAttributeListRepository = _serviceProvider.GetRequiredService<IRepositoryWithEvents<CustomMasterAttribute>>();
            _masterPropertyTypeRepo = _serviceProvider.GetRequiredService<IRepositoryWithEvents<MasterPropertyType>>();
            _projectsRepo = _serviceProvider.GetRequiredService<IRepositoryWithEvents<Lrb.Domain.Entities.Project>>();
            _blobStorageService = _serviceProvider.GetRequiredService<IBlobStorageService>();
            _userViewRepo = _serviceProvider.GetRequiredService<IRepositoryWithEvents<UserView>>();
        }

        #region Common
        // Get All Users Based on permission
        protected async Task<(List<Guid>, bool)> GetUserIdsByPermissionAsync(GetAllPropertyForListingManagementRequest request, string? tenantId, Guid currentUserId)
        {
            List<Guid> userIds = new();
            List<Guid> filterIds = new();
            List<Guid> teamUserIds = new();
            bool showAllProperties = false;

            try
            {
                switch (request?.Permission)
                {
                    case ViewAssignmentsPermission.View:
                        if (request.UserIds?.Any() ?? false)
                        {
                            filterIds.AddRange(request.UserIds);
                            if (request.IsWithTeam ?? false)
                            {
                                teamUserIds = (await _dapperRepository.GetSubordinateIdsWithOnlyReporteesAsync(
                                    request.UserIds,
                                    tenantId ?? string.Empty))?.ToList() ?? new();
                                filterIds.AddRange(teamUserIds);
                            }
                            userIds.AddRange(filterIds);
                        }
                        else
                        {
                            userIds = (await _dapperRepository.GetAllUserIdsAsync(tenantId ?? string.Empty)).ToList();
                            showAllProperties = true;
                        }
                        break;

                    case ViewAssignmentsPermission.ViewAssigned:
                        userIds.Add(currentUserId);
                        break;

                    default:
                        if (request?.UserIds?.Any() ?? false)
                        {
                            filterIds.AddRange(request.UserIds);
                            if (request.IsWithTeam ?? false)
                            {
                                teamUserIds = (await _dapperRepository.GetSubordinateIdsWithOnlyReporteesAsync(
                                    request.UserIds,
                                    tenantId ?? string.Empty))?.ToList() ?? new();
                                filterIds.AddRange(teamUserIds);
                            }
                            userIds.AddRange(filterIds);
                        }
                        else
                        {
                            userIds = (await _dapperRepository.GetAllUserIdsAsync(tenantId ?? string.Empty)).ToList();
                            showAllProperties = true;
                        }
                        break;
                }
            }
            catch (Exception ex)
            {
                var error = new LrbError()
                {
                    ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                    ErrorSource = ex?.Source,
                    StackTrace = ex?.StackTrace,
                    InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                    ErrorModule = "GetAllPropertyRequestHandler -> Handle()"
                };
                await _leadRepositoryAsync.AddErrorAsync(error);
            }


            return (userIds, showAllProperties);
        }

        protected async Task<List<Guid>> GetPropertyDimensionIdsAsync(GetAllPropertyForListingManagementRequest request)
        {

            if (request == null || request.PropertySize == null) return new List<Guid>();
            var propertyDimensionIds = new List<Guid>();
            var propertySize = request.PropertySize;

            try
            {
                var areaConversionFactor = await GetConversionFactorAsync(propertySize?.AreaUnitId ?? Guid.Empty);
                var carpetAreaConversionFactor = await GetConversionFactorAsync(propertySize?.CarpetAreaId ?? Guid.Empty);
                var buildUpAreaConversionFactor = await GetConversionFactorAsync(propertySize?.BuildUpAreaId ?? Guid.Empty);
                var saleableAreaConversionFactor = await GetConversionFactorAsync(propertySize?.SaleableAreaId ?? Guid.Empty);
                var netAreaConversionFactor = await GetConversionFactorAsync(propertySize?.NetAreaUnitId ?? Guid.Empty);

                if (propertySize?.Area != default && areaConversionFactor != default)
                {
                    propertySize.ConversionFactor = areaConversionFactor;
                    var area = propertySize?.Area * propertySize.ConversionFactor;
                    propertyDimensionIds.AddRange(await FilterPropertyDimensionsAsync(area ?? default, "Area"));
                }

                if (propertySize?.CarpetArea != default && carpetAreaConversionFactor != default)
                {
                    propertySize.ConversionFactor = carpetAreaConversionFactor;
                    var carpetArea = propertySize?.CarpetArea * propertySize.ConversionFactor;
                    propertyDimensionIds.AddRange(await FilterPropertyDimensionsAsync(carpetArea ?? default, "CarpetArea"));
                }

                if (propertySize?.BuildUpArea != default && buildUpAreaConversionFactor != default)
                {
                    propertySize.ConversionFactor = buildUpAreaConversionFactor;
                    var buildUpArea = propertySize?.BuildUpArea * propertySize.ConversionFactor;
                    propertyDimensionIds.AddRange(await FilterPropertyDimensionsAsync(buildUpArea ?? default, "BuildUpArea"));
                }

                if (propertySize?.SaleableArea != default && saleableAreaConversionFactor != default)
                {
                    propertySize.ConversionFactor = saleableAreaConversionFactor;
                    var saleableArea = propertySize?.SaleableArea * propertySize.ConversionFactor;
                    propertyDimensionIds.AddRange(await FilterPropertyDimensionsAsync(saleableArea ?? default, "SaleableArea"));
                }
                if (propertySize?.NetArea != default && netAreaConversionFactor != default)
                {
                    propertySize.ConversionFactor = netAreaConversionFactor;
                    var netarea = propertySize?.NetArea * propertySize.ConversionFactor;
                    propertyDimensionIds.AddRange(await FilterPropertyDimensionsAsync(netarea ?? default, "SaleableArea"));
                }
            }
            catch (Exception ex)
            {
                var error = new LrbError()
                {
                    ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                    ErrorSource = ex?.Source,
                    StackTrace = ex?.StackTrace,
                    InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                    ErrorModule = "GetAllPropertyRequestHandler -> Handle()"
                };
                await _leadRepositoryAsync.AddErrorAsync(error);
            }
            return propertyDimensionIds;
        }

        protected async Task<float> GetConversionFactorAsync(Guid areaUnitId)
        {
            var masterAreaUnit = await _masterAreaUnitRepo.GetByIdAsync(areaUnitId);
            return masterAreaUnit?.ConversionFactor ?? default;
        }

        protected async Task<List<Guid>> FilterPropertyDimensionsAsync(double areaValue, string areaType)
        {
            var propertyDimensions = await _propertyDimensionRepo.ListAsync();
            List<Guid>? propertyDimensionIds = new();
            if (areaType == "Area")
            {
                propertyDimensionIds = propertyDimensions
                   .Where(i => i.Area * PropertySearchHelper.GetConversionFactor(i.AreaUnitId, _masterAreaUnitRepo).Result == areaValue)
                      .Select(i => i.Id)
                      .ToList();
            }
            if (areaType == "CarpetArea")
            {
                propertyDimensionIds = propertyDimensions
                   .Where(i => i.CarpetArea * PropertySearchHelper.GetConversionFactor(i.CarpetAreaId ?? Guid.Empty, _masterAreaUnitRepo).Result == areaValue)
                      .Select(i => i.Id)
                      .ToList();
            }
            if (areaType == "BuildUpArea")
            {
                propertyDimensionIds = propertyDimensions
                   .Where(i => i.BuildUpArea * PropertySearchHelper.GetConversionFactor(i.BuildUpAreaId ?? Guid.Empty, _masterAreaUnitRepo).Result == areaValue)
                      .Select(i => i.Id)
                      .ToList();
            }
            if (areaType == "SaleableArea")
            {
                propertyDimensionIds = propertyDimensions
                   .Where(i => i.SaleableArea * PropertySearchHelper.GetConversionFactor(i.SaleableAreaId ?? Guid.Empty, _masterAreaUnitRepo).Result == areaValue)
                      .Select(i => i.Id)
                      .ToList();
            }
            if (areaType == "NetArea")
            {
                propertyDimensionIds = propertyDimensions
                   .Where(i => i.NetArea * PropertySearchHelper.GetConversionFactor(i.NetAreaUnitId ?? Guid.Empty, _masterAreaUnitRepo).Result == areaValue)
                      .Select(i => i.Id)
                      .ToList();
            }
            return propertyDimensionIds;

        }

        protected async Task<NumericAttributesDto> InitializeNumericAttributes(List<int> noOfAttributes, GetAllPropertyForListingManagementRequest request)
        {
            return new NumericAttributesDto
            {
                NoOfFloor = FilterNumericAttributes(request.NoOfFloor, noOfAttributes),
                NoOfBathrooms = FilterNumericAttributes(request.NoOfBathrooms, noOfAttributes),
                NoOfBedrooms = FilterNumericAttributes(request.NoOfBedrooms, noOfAttributes),
                NoOfKitchens = FilterNumericAttributes(request.NoOfKitchens, noOfAttributes),
                NoOfUtilites = FilterNumericAttributes(request.NoOfUtilites, noOfAttributes),
                NoOfLivingrooms = FilterNumericAttributes(request.NoOfLivingrooms, noOfAttributes),
                NoOfBalconies = FilterNumericAttributes(request.NoOfBalconies, noOfAttributes),
                NoOfFloors = FilterNumericAttributesV1(request.NoOfFloors, noOfAttributes),
                Parking = FilterNumericAttributes(request.Parking, noOfAttributes),
            };
        }

        protected NoOfAttributeFilterDto FilterNumericAttributes(List<int>? requestValues, List<int> noOfAttributes)
        {
            if (requestValues == null) return new NoOfAttributeFilterDto();

            var noOfAttributesDto = new NoOfAttributeFilterDto();
            var filterList = requestValues.Contains(5)
                ? noOfAttributes.Except(requestValues).Select(i => i.ToString()).ToList()
                : noOfAttributes.Intersect(requestValues).Select(i => i.ToString()).ToList();

            noOfAttributesDto.NoOfAttributes = filterList;
            noOfAttributesDto.IsMaxValueIncluded = requestValues.Contains(5);
            return noOfAttributesDto;
        }
        protected NoOfAttributeFilterDto FilterNumericAttributesV1(List<string>? requestValues, List<int> noOfAttributes)
        {
            NoOfAttributeFilterDto noOfAttributesDto = new NoOfAttributeFilterDto();
            var noOfFloorFilterList = new List<int>();
            var selectedAttributes = new List<string>();
            if (requestValues != null)
            {
                if (requestValues.Contains("Ground Floor"))
                {
                    selectedAttributes.Add("Ground Floor");
                }
                if (requestValues.Contains("5"))
                {
                    noOfFloorFilterList = noOfAttributes.Where(i => !requestValues.Contains(i.ToString())).ToList();
                    selectedAttributes.AddRange(noOfFloorFilterList.Select(i => i.ToString()));
                    noOfAttributesDto.IsMaxValueIncluded = true;
                }
                else
                {
                    noOfFloorFilterList = noOfAttributes.Where(i => requestValues.Contains(i.ToString())).ToList();
                    selectedAttributes.AddRange(noOfFloorFilterList.Select(i => i.ToString()));
                    noOfAttributesDto.IsMaxValueIncluded = false;
                }
                noOfAttributesDto.NoOfAttributes = selectedAttributes;
            }

            return noOfAttributesDto;
        }

        #endregion

    }
}
