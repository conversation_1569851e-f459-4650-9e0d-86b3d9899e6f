﻿using Lrb.Application.Common.Persistence.New_Implementation;
using Lrb.Application.Identity.Users;
using Lrb.Application.Property.Web.Dtos;
using Lrb.Application.Property.Web.Specs;
using Lrb.Domain.Entities.AmenitiesAttributes;
using Lrb.Domain.Entities.ErrorModule;
using Lrb.Domain.Entities.MasterData;
using Newtonsoft.Json;
using System.Linq;

namespace Lrb.Application.Property.Web.Requests
{
    public class GetAllPropertyParameters : PaginationFilter
    {
        public PropertyDimensionDto? PropertySize { get; set; }
        public List<string>? Locations { get; set; }
        public List<string>? Cities { get; set; }
        public List<string>? States { get; set; }
        public EnquiryType? EnquiredFor { get; set; }
        public double? NoOfBHK { get; set; }
        public List<double>? BHKs { get; set; }
        public string? Ratings { get; set; }
        public PropertyStatus? PropertyStatus { get; set; }
        public List<Guid>? PropertyTypes { get; set; }
        public List<Guid>? PropertySubTypes { get; set; }
        public Guid? BasePropertyTypeId { get; set; }
        public string? PropertySearch { get; set; }
        public PropertyDateType? DateType { get; set; }
        public DateTime? FromDate { get; set; }
        public DateTime? ToDate { get; set; }
        public long? MinPrice { get; set; }
        public long? MaxPrice { get; set; }
        public List<Guid>? Amenities { get; set; }
        public List<BHKType>? BHKTypes { get; set; }
        public List<string>? Projects { get; set; }
        public List<FurnishStatus>? FurnishStatuses { get; set; }
        public List<SaleType>? SaleTypes { get; set; }
        public string? OwnerName { get; set; }
        public string? PropertyTitle { get; set; }
        public Facing? Facing { get; set; }
        public List<int>? NoOfBathrooms { get; set; }
        public List<int>? NoOfLivingrooms { get; set; }
        public List<int>? NoOfBedrooms { get; set; }
        public List<int>? NoOfUtilites { get; set; }
        public List<int>? NoOfKitchens { get; set; }
        public List<int>? NoOfBalconies { get; set; }
        public List<int>? NoOfFloor { get; set; }
        public int? FloorNumber { get; set; }
        public long? MaxBudget { get; set; }
        public long? MinBudget { get; set; }
        public List<string>? OwnerNames { get; set; }
        public bool? IsWithTeam { get; set; }
        public List<Guid>? UserIds { get; set; }
        public ViewAssignmentsPermission? Permission { get; set; }
        public string? Currency { get; set; }
        public List<Guid>? ListingOnBehalf { get; set; }

        public int? MinLeadCount { get; set; } 
        public int? MaxLeadCount { get; set; } 
        public int? MinProspectCount { get; set; } 
        public int? MaxProspectCount { get; set; } 
        public PossesionType? PossesionType { get; set; }
        public DateTime? FromPossesionDate { get; set; }
        public DateTime? ToPossesionDate { get; set; }
        public List<string>? NoOfFloors { get; set; }
        public List<int>? Parking { get; set; }
        public List<string>? Countries { get; set; }



    }

    public class V2GetAllPropertyRequest : GetAllPropertyParameters, IRequest<PagedResponse<ViewPropertyDto, string>>
    {
    }

    public class V2GetAllPropertyRequestHandler : IRequestHandler<V2GetAllPropertyRequest, PagedResponse<ViewPropertyDto, string>>
    {
        private readonly IUserService _userService;
        private readonly IReadRepository<Domain.Entities.Property> _propertyRepo;
        private readonly IDapperRepository _dapperRepository;
        private readonly ICurrentUser _currentUser;
        private readonly ILeadRepositoryAsync _leadRepositoryAsync;
        private readonly IReadRepository<MasterAreaUnit> _masterAreaUnitRepo;
        private readonly IReadRepository<PropertyDimension> _propertyDimensionRepo;
        private readonly IPropertyRepository _efPropertyRepository;
        private readonly IReadRepository<CustomMasterAttribute> _masterAttributeRepo;
        public V2GetAllPropertyRequestHandler(
            IUserService userService,
            IReadRepository<Domain.Entities.Property> propertyRepo,
            IDapperRepository dapperRepo,
            ICurrentUser currentUser,
            ILeadRepositoryAsync leadRepositoryAsync,
             IReadRepository<MasterAreaUnit> masterAreaUnitRepo,
             IReadRepository<PropertyDimension> propertyDimensionRepo,
             IPropertyRepository efPropertyRepository,
             IReadRepository<CustomMasterAttribute> masterAttributeRepo)
        {
            _userService = userService;
            _propertyRepo = propertyRepo;
            _dapperRepository = dapperRepo;
            _currentUser = currentUser;
            _leadRepositoryAsync = leadRepositoryAsync;
            _masterAreaUnitRepo = masterAreaUnitRepo;
            _propertyDimensionRepo = propertyDimensionRepo;
            _efPropertyRepository = efPropertyRepository;
            _masterAttributeRepo = masterAttributeRepo;
        }

        public async Task<PagedResponse<ViewPropertyDto, string>> Handle(V2GetAllPropertyRequest request, CancellationToken cancellationToken)
        {
            if (request != null && request.Permission == ViewAssignmentsPermission.None)
            {
                return new(null, 0);
            }

            List<Guid>? userIds = new();
            List<Guid>? filterIds = new();
            List<Guid>? teamUserIds = new();
            List<Guid>? propertyDimensionIds = new();
            var tenantId = _currentUser.GetTenant();
            var currentUserId = _currentUser.GetUserId();
            bool showAllProperties = false;

            try
            {
                switch (request?.Permission)
                {
                    case ViewAssignmentsPermission.View:
                        if (request.UserIds?.Any() ?? false)
                        {
                            filterIds.AddRange(request.UserIds);
                            if (request.IsWithTeam ?? false)
                            {
                                teamUserIds = (await _dapperRepository.GetSubordinateIdsWithOnlyReporteesAsync(request.UserIds, tenantId ?? string.Empty))?.ToList() ?? new();
                                filterIds.AddRange(teamUserIds);
                            }
                            userIds.AddRange(filterIds);
                        }
                        else
                        {
                            userIds = (await _dapperRepository.GetAllUserIdsAsync(tenantId ?? string.Empty)).ToList();
                            showAllProperties = true;
                        }
                        break;
                    case ViewAssignmentsPermission.ViewAssigned:
                        userIds.Add(currentUserId);
                        break;
                    default:
                        if (request?.UserIds?.Any() ?? false)
                        {
                            filterIds.AddRange(request.UserIds);
                            if (request.IsWithTeam ?? false)
                            {
                                teamUserIds = (await _dapperRepository.GetSubordinateIdsWithOnlyReporteesAsync(request.UserIds, tenantId ?? string.Empty))?.ToList() ?? new();
                                filterIds.AddRange(teamUserIds);
                            }
                            userIds.AddRange(filterIds);
                        }
                        else
                        {
                            userIds = (await _dapperRepository.GetAllUserIdsAsync(tenantId ?? string.Empty)).ToList();
                            showAllProperties = true;
                        }
                        break;
                }
            }
            catch (Exception ex)
            {
            }
            NumericAttributesDto numericAttributeDto = new();
            List<int> noOfAttributes = Enumerable.Range(1, 5).ToList();


            try
            {
                var tasks = new Task[]
                {
                //Task.Run(async () => propertyDimensionIds = await GetPropertyDimensionIdsAsync(request ?? new())),
                Task.Run(async () => numericAttributeDto = await InitializeNumericAttributes(noOfAttributes, request))
                };
                await Task.WhenAll(tasks);
            }
            catch (Exception ex)
            {
            }
            int count = 0;
            List<Guid> propertyIds = new();
            if (request.MinLeadCount != null || request.MaxLeadCount != null ||
                request.MinProspectCount != null || request.MaxProspectCount != null)
            {
                var property = (await _dapperRepository.QueryStoredProcedureFromReadReplicaAsync<PropertyIdsDto>("LeadratBlack","Lead&Prospects_PropertiesAssociatedCountFilter",
                    new
                    {
                        tenantid = tenantId,
                        minprospectcount = request.MinProspectCount,
                        maxprospectcount = request.MaxProspectCount,
                        minleadcount = request.MinLeadCount,
                        maxleadcount = request.MaxLeadCount
                    })).FirstOrDefault()?.PropertyIds ?? new List<Guid>();
                propertyIds = property.ToList();
            }
            List<CustomPropertyAttributeDto> attributes = new();
            if (request?.NoOfFloors != null ||request?.NoOfKitchens != null || request?.NoOfUtilites != null || request?.NoOfBedrooms != null || request?.NoOfLivingrooms != null || request?.NoOfBalconies != null || request?.NoOfBathrooms != null || request?.Parking != null)
            {
                attributes = await _dapperRepository.GetAttributeDetails(tenantId ?? string.Empty);
            }
            var propertiesResult = await _efPropertyRepository.GetAllPropertiesForWebNewAsync(request, propertyDimensionIds, numericAttributeDto, userIds, showAllProperties, tenantId, propertyIds, attributes);
            var properties = propertiesResult.Item1?.Adapt<List<ViewPropertyDto>>() ?? new List<ViewPropertyDto>();
            count = propertiesResult.Item2;
            List<Guid> allAttributeIds = properties.Where(p => p.Attributes != null).SelectMany(p => p.Attributes.Select(a => a.MasterPropertyAttributeId)).Distinct().ToList();
            var masterAttributes = await _masterAttributeRepo.ListAsync(new CustomMasterAttributesByIdsSpec(allAttributeIds), cancellationToken);
            foreach (var property in properties)
            {
                if (property?.Attributes?.Any() ?? false)
                {
                    var relevantMasterAttributes = masterAttributes.Where(ma => property.Attributes.Select(a => a.MasterPropertyAttributeId).Contains(ma.Id)).ToList();
                    await UpdateMasterAttributes(property, relevantMasterAttributes);
                }
            }
            return new(properties.Adapt<List<ViewPropertyDto>>(), count);
        }
        #region Filter
        private async Task<(List<Guid>, bool)> GetUserIdsByPermissionAsync(V2GetAllPropertyRequest request, string? tenantId, Guid currentUserId)
        {
            List<Guid> userIds = new();
            List<Guid> filterIds = new();
            List<Guid> teamUserIds = new();
            bool showAllProperties = false;

            try
            {
                switch (request?.Permission)
                {
                    case ViewAssignmentsPermission.View:
                        if (request.UserIds?.Any() ?? false)
                        {
                            filterIds.AddRange(request.UserIds);
                            if (request.IsWithTeam ?? false)
                            {
                                teamUserIds = (await _dapperRepository.GetSubordinateIdsWithOnlyReporteesAsync(
                                    request.UserIds,
                                    tenantId ?? string.Empty))?.ToList() ?? new();
                                filterIds.AddRange(teamUserIds);
                            }
                            userIds.AddRange(filterIds);
                        }
                        else
                        {
                            userIds = (await _dapperRepository.GetAllUserIdsAsync(tenantId ?? string.Empty)).ToList();
                            showAllProperties = true;
                        }
                        break;

                    case ViewAssignmentsPermission.ViewAssigned:
                        userIds.Add(currentUserId);
                        break;

                    default:
                        if (request?.UserIds?.Any() ?? false)
                        {
                            filterIds.AddRange(request.UserIds);
                            if (request.IsWithTeam ?? false)
                            {
                                teamUserIds = (await _dapperRepository.GetSubordinateIdsWithOnlyReporteesAsync(
                                    request.UserIds,
                                    tenantId ?? string.Empty))?.ToList() ?? new();
                                filterIds.AddRange(teamUserIds);
                            }
                            userIds.AddRange(filterIds);
                        }
                        else
                        {
                            userIds = (await _dapperRepository.GetAllUserIdsAsync(tenantId ?? string.Empty)).ToList();
                            showAllProperties = true;
                        }
                        break;
                }
            }
            catch (Exception ex)
            {
                var error = new LrbError()
                {
                    ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                    ErrorSource = ex?.Source,
                    StackTrace = ex?.StackTrace,
                    InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                    ErrorModule = "GetAllPropertyRequestHandler -> Handle()"
                };
                await _leadRepositoryAsync.AddErrorAsync(error);
            }

           
            return (userIds, showAllProperties);
        }

        private async Task<List<Guid>> GetPropertyDimensionIdsAsync(V2GetAllPropertyRequest request)
        {

            if (request == null || request.PropertySize == null) return new List<Guid>();
            var propertyDimensionIds = new List<Guid>();
            var propertySize = request.PropertySize;

            try
            {
                var areaConversionFactor = await GetConversionFactorAsync(propertySize?.AreaUnitId ?? Guid.Empty);
                var carpetAreaConversionFactor = await GetConversionFactorAsync(propertySize?.CarpetAreaId ?? Guid.Empty);
                var buildUpAreaConversionFactor = await GetConversionFactorAsync(propertySize?.BuildUpAreaId ?? Guid.Empty);
                var saleableAreaConversionFactor = await GetConversionFactorAsync(propertySize?.SaleableAreaId ?? Guid.Empty);
                var netAreaConversionFactor = await GetConversionFactorAsync(propertySize?.NetAreaUnitId ?? Guid.Empty);

                if (propertySize?.Area != default && areaConversionFactor != default)
                {
                    propertySize.ConversionFactor = areaConversionFactor;
                    var area = propertySize?.Area * propertySize.ConversionFactor;
                    propertyDimensionIds.AddRange(await FilterPropertyDimensionsAsync(area ?? default, "Area"));
                }

                if (propertySize?.CarpetArea != default && carpetAreaConversionFactor != default)
                {
                    propertySize.ConversionFactor = carpetAreaConversionFactor;
                    var carpetArea = propertySize?.CarpetArea * propertySize.ConversionFactor;
                    propertyDimensionIds.AddRange(await FilterPropertyDimensionsAsync(carpetArea ?? default, "CarpetArea"));
                }

                if (propertySize?.BuildUpArea != default && buildUpAreaConversionFactor != default)
                {
                    propertySize.ConversionFactor = buildUpAreaConversionFactor;
                    var buildUpArea = propertySize?.BuildUpArea * propertySize.ConversionFactor;
                    propertyDimensionIds.AddRange(await FilterPropertyDimensionsAsync(buildUpArea ?? default, "BuildUpArea"));
                }

                if (propertySize?.SaleableArea != default && saleableAreaConversionFactor != default)
                {
                    propertySize.ConversionFactor = saleableAreaConversionFactor;
                    var saleableArea = propertySize?.SaleableArea * propertySize.ConversionFactor;
                    propertyDimensionIds.AddRange(await FilterPropertyDimensionsAsync(saleableArea ?? default, "SaleableArea"));
                }
                if (propertySize?.NetArea != default && netAreaConversionFactor != default)
                {
                    propertySize.ConversionFactor = netAreaConversionFactor;
                    var netarea = propertySize?.NetArea * propertySize.ConversionFactor;
                    propertyDimensionIds.AddRange(await FilterPropertyDimensionsAsync(netarea ?? default, "SaleableArea"));
                }
            }
            catch (Exception ex)
            {
                var error = new LrbError()
                {
                    ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                    ErrorSource = ex?.Source,
                    StackTrace = ex?.StackTrace,
                    InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                    ErrorModule = "GetAllPropertyRequestHandler -> Handle()"
                };
                await _leadRepositoryAsync.AddErrorAsync(error);
            }
            return propertyDimensionIds;
        }

        private async Task<float> GetConversionFactorAsync(Guid areaUnitId)
        {
            var masterAreaUnit = await _masterAreaUnitRepo.GetByIdAsync(areaUnitId);
            return masterAreaUnit?.ConversionFactor ?? default;
        }

        private async Task<List<Guid>> FilterPropertyDimensionsAsync(double areaValue, string areaType)
        {
            var propertyDimensions = await _propertyDimensionRepo.ListAsync();
            List<Guid>? propertyDimensionIds = new();
            if (areaType == "Area")
            {
                propertyDimensionIds = propertyDimensions
                   .Where(i => i.Area * PropertySearchHelper.GetConversionFactor(i.AreaUnitId, _masterAreaUnitRepo).Result == areaValue)
                      .Select(i => i.Id)
                      .ToList();
            }
            if (areaType == "CarpetArea")
            {
                propertyDimensionIds = propertyDimensions
                   .Where(i => i.CarpetArea * PropertySearchHelper.GetConversionFactor(i.CarpetAreaId ?? Guid.Empty, _masterAreaUnitRepo).Result == areaValue)
                      .Select(i => i.Id)
                      .ToList();
            }
            if (areaType == "BuildUpArea")
            {
                propertyDimensionIds = propertyDimensions
                   .Where(i => i.BuildUpArea * PropertySearchHelper.GetConversionFactor(i.BuildUpAreaId ?? Guid.Empty, _masterAreaUnitRepo).Result == areaValue)
                      .Select(i => i.Id)
                      .ToList();
            }
            if (areaType == "SaleableArea")
            {
                propertyDimensionIds = propertyDimensions
                   .Where(i => i.SaleableArea * PropertySearchHelper.GetConversionFactor(i.SaleableAreaId ?? Guid.Empty, _masterAreaUnitRepo).Result == areaValue)
                      .Select(i => i.Id)
                      .ToList();
            }
            if (areaType == "NetArea")
            {
                propertyDimensionIds = propertyDimensions
                   .Where(i => i.NetArea * PropertySearchHelper.GetConversionFactor(i.NetAreaUnitId ?? Guid.Empty, _masterAreaUnitRepo).Result == areaValue)
                      .Select(i => i.Id)
                      .ToList();
            }
            return propertyDimensionIds;

        }

        private async Task<NumericAttributesDto> InitializeNumericAttributes(List<int> noOfAttributes, V2GetAllPropertyRequest request)
        {
            return new NumericAttributesDto
            {
                NoOfFloor = FilterNumericAttributes(request.NoOfFloor, noOfAttributes),
                NoOfBathrooms = FilterNumericAttributes(request.NoOfBathrooms, noOfAttributes),
                NoOfBedrooms = FilterNumericAttributes(request.NoOfBedrooms, noOfAttributes),
                NoOfKitchens = FilterNumericAttributes(request.NoOfKitchens, noOfAttributes),
                NoOfUtilites = FilterNumericAttributes(request.NoOfUtilites, noOfAttributes),
                NoOfLivingrooms = FilterNumericAttributes(request.NoOfLivingrooms, noOfAttributes),
                NoOfBalconies = FilterNumericAttributes(request.NoOfBalconies, noOfAttributes),
                NoOfFloors = FilterNumericAttributesV1(request.NoOfFloors, noOfAttributes),
                Parking = FilterNumericAttributes(request.Parking, noOfAttributes),
            };
        }

        private NoOfAttributeFilterDto FilterNumericAttributes(List<int>? requestValues, List<int> noOfAttributes)
        {
            NoOfAttributeFilterDto noOfAttributesDto = new NoOfAttributeFilterDto();
            var noOfFloorFilterList = new List<int>();

            if (requestValues != null)
            {
                if (requestValues.Contains(5))
                {
                    noOfFloorFilterList = noOfAttributes.Where(i => !requestValues.Contains(i)).ToList();
                    noOfAttributesDto.NoOfAttributes = noOfFloorFilterList.Select(i => i.ToString()).ToList();
                    noOfAttributesDto.IsMaxValueIncluded = true;
                }
                else
                {
                    noOfFloorFilterList = noOfAttributes.Where(i => requestValues.Contains(i)).ToList();
                    noOfAttributesDto.NoOfAttributes = noOfFloorFilterList.Select(i => i.ToString()).ToList();
                    noOfAttributesDto.IsMaxValueIncluded = false;
                }
            }

            return noOfAttributesDto;
        }
        private NoOfAttributeFilterDto FilterNumericAttributesV1(List<string>? requestValues, List<int> noOfAttributes)
        {
            NoOfAttributeFilterDto noOfAttributesDto = new NoOfAttributeFilterDto();
            var noOfFloorFilterList = new List<int>();
            var selectedAttributes = new List<string>();
            if (requestValues != null)
            {
                if (requestValues.Contains("Ground Floor"))
                {
                    selectedAttributes.Add("Ground Floor");
                }
                if (requestValues.Contains("5"))
                {
                    noOfFloorFilterList = noOfAttributes.Where(i => !requestValues.Contains(i.ToString())).ToList();
                    selectedAttributes.AddRange(noOfFloorFilterList.Select(i => i.ToString()));
                    noOfAttributesDto.IsMaxValueIncluded = true;
                }
                else
                {
                    noOfFloorFilterList = noOfAttributes.Where(i => requestValues.Contains(i.ToString())).ToList();
                    selectedAttributes.AddRange(noOfFloorFilterList.Select(i => i.ToString()));
                    noOfAttributesDto.IsMaxValueIncluded = false;
                }
                noOfAttributesDto.NoOfAttributes = selectedAttributes;
            }

            return noOfAttributesDto;
        }

        #endregion
        private async static Task<ViewPropertyDto> UpdateMasterAttributes(ViewPropertyDto propertyDto, List<CustomMasterAttribute> masterAttributes)
        {
            try
            {
                var attributes = propertyDto.Attributes?.ToList();
                attributes?.ForEach(i =>
                {
                    var matchingAttribute = masterAttributes.FirstOrDefault(j => j.Id == i.MasterPropertyAttributeId);
                    i.AttributeDisplayName = masterAttributes.Where(j => j.Id == i.MasterPropertyAttributeId)?.FirstOrDefault()?.AttributeDisplayName;
                    i.AttributeName = masterAttributes.Where(j => j.Id == i.MasterPropertyAttributeId)?.FirstOrDefault()?.AttributeName;
                    i.AttributeType = masterAttributes.Where(j => j.Id == i.MasterPropertyAttributeId)?.FirstOrDefault()?.AttributeType;
                    i.ActiveImageURL = masterAttributes.Where(j => j.Id == i.MasterPropertyAttributeId)?.FirstOrDefault()?.ActiveImageURL;
                    i.IsActive = masterAttributes.Where(j => j.Id == i.MasterPropertyAttributeId)?.FirstOrDefault()?.IsActive;

                });

                propertyDto.Attributes = attributes;
                return propertyDto;
            }
            catch
            {
                return propertyDto;
            }
        }
    }
}
