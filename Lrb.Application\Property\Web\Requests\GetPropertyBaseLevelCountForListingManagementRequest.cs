﻿using Lrb.Application.Common.Persistence.New_Implementation;
using Lrb.Application.Property.Web.Dtos;
using Lrb.Application.Property.Web.Requests.CommonHandler;
using Lrb.Application.Property.Web.Specs;
using Lrb.Domain.Entities.ErrorModule;
using Newtonsoft.Json;

namespace Lrb.Application.Property.Web.Requests
{
    public class GetPropertyBaseLevelCountForListingManagementRequest : GetAllListingManagementParameter, IRequest<Response<GetPropertyCountForListingManagementDto>>
    {
    }

    public class GetPropertyBaseLevelCountForListingManagementRequestHandler : PropertyCommonRequestHandler,
        IRequestHandler<GetPropertyBaseLevelCountForListingManagementRequest, Response<GetPropertyCountForListingManagementDto>>
    {
        private readonly IPropertyRepository _efPropertyRepository;
        public GetPropertyBaseLevelCountForListingManagementRequestHandler(
            IServiceProvider serviceProvider,
            IPropertyRepository efPropertyRepository
            ) : base( serviceProvider ) 
        {
            _efPropertyRepository = efPropertyRepository;
        }

        public async Task<Response<GetPropertyCountForListingManagementDto>> Handle(GetPropertyBaseLevelCountForListingManagementRequest request, CancellationToken cancellationToken)
        {
            if (request != null && request.Permission == ViewAssignmentsPermission.None)
            {
                return new(null);
            }

            var tenantId = _currentUser.GetTenant();
            var currentUserId = _currentUser.GetUserId();
            List<Guid>? propertyDimensionIds = new();

            var userWithPermission = await GetUserIdsByPermissionAsync(request.Adapt<GetAllPropertyForListingManagementRequest>(), tenantId, currentUserId);

            NumericAttributesDto numericAttributeDto = new();
            List<int> noOfAttributes = Enumerable.Range(1, 5).ToList();
            PropertyTypeBaseId propertyTypeIds = new();

            try
            {
                var tasks = new Task[]
                {
                //Task.Run(async () => propertyDimensionIds = await GetPropertyDimensionIdsAsync(request.Adapt < GetAllPropertyForListingManagementRequest >() ?? new())),
                Task.Run(async () => numericAttributeDto = await InitializeNumericAttributes(noOfAttributes, request.Adapt < GetAllPropertyForListingManagementRequest >()))
                };
                await Task.WhenAll(tasks);

                propertyTypeIds.ResidentialBaseId = (await _masterPropertyTypeRepo.FirstOrDefaultAsync(new GetMasterPropertyTypeByTypeSpec("residential")))?.Id;
                propertyTypeIds.AgricultureBaseId = (await _masterPropertyTypeRepo.FirstOrDefaultAsync(new GetMasterPropertyTypeByTypeSpec("agricultural")))?.Id;
                propertyTypeIds.CommercialBaseId = (await _masterPropertyTypeRepo.FirstOrDefaultAsync(new GetMasterPropertyTypeByTypeSpec("commercial")))?.Id;
            }
            catch (Exception ex)
            {
                var error = new LrbError()
                {
                    ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                    ErrorSource = ex?.Source,
                    StackTrace = ex?.StackTrace,
                    InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                    ErrorModule = "GetPropertyBaseLevelCountForListingManagementRequest -> Handle()"
                };
                await _leadRepositoryAsync.AddErrorAsync(error);
            }
            List<Guid> propertyIds = new();
            if (request.MinLeadCount != null || request.MaxLeadCount != null || request.MinProspectCount != null || request.MaxProspectCount != null)
            {
                var property = (await _dapperRepository.QueryStoredProcedureFromReadReplicaAsync<PropertyIdsDto>("LeadratBlack", "Lead&Prospects_PropertiesAssociatedCountFilter", new
                {
                    tenantid = tenantId,
                    minprospectcount = request.MinProspectCount,
                    maxprospectcount = request.MaxProspectCount,
                    minleadcount = request.MinLeadCount,
                    maxleadcount = request.MaxLeadCount
                })).FirstOrDefault()?.PropertyIds ?? new List<Guid>();
                propertyIds = property.ToList();
            }
            List<CustomPropertyAttributeDto> attributes = new();
            if (request?.NoOfFloors != null || request?.NoOfKitchens != null || request?.NoOfUtilites != null || request?.NoOfBedrooms != null || request?.NoOfLivingrooms != null || request?.NoOfBalconies != null || request?.NoOfBathrooms != null || request?.Parking != null)
            {
                attributes = await _dapperRepository.GetAttributeDetails(tenantId ?? string.Empty);
            }
            var propertiesResult = await _efPropertyRepository.GetAllPropertiesCountListingAsync(request.Adapt<GetAllPropertyForListingManagementRequest>(), propertyDimensionIds, numericAttributeDto, userWithPermission.Item1, userWithPermission.Item2, tenantId, propertyIds, propertyTypeIds,attributes);

            return new(propertiesResult);
        }
    }
}
