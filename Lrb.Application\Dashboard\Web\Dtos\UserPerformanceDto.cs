﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Lrb.Application.Dashboard.Web
{
    public class UserPerformanceDto
    {
        public Guid? User { get; set; }
        public string? UserName { get; set; }
        public int? FbLeads { get; set; }
        public decimal? CPLPerUser { get; set; }
        public int? NoOfMeetingDone { get; set; }
        public int? MeetingDoneUniqueCount { get; set; }        
        public int? NoOfSiteVisitDone { get; set; }
        public int? SiteVisitDoneUniqueCount { get; set; }  
        public int? NoOfInvoiced { get; set; }
        public decimal? TotalRevenue { get; set; }
        public decimal? ROI { get; set; }
    }

    public class FacebookLeadDto
    {
        public Guid? LeadId { get; set; }
        public Guid? UserId { get; set; }
        public string? UserName { get; set; } = null!;
        public string? AdId { get; set; } = null!;
        public string? CampaignId { get; set; } = null!;
        public double SoldPrice { get; set; }
    }

    public class GoogleUserPerformanceDto
    {
        public Guid? UserId { get; set; }
        public string? UserName { get; set; }
        public int? GoogleAdLeads { get; set; }
        public decimal? CPLPerUser { get; set; }
        public int? NoOfMeetingDone { get; set; }
        public int? MeetingDoneUniqueCount { get; set; }
        public int? NoOfSiteVisitDone { get; set; }
        public int? SiteVisitDoneUniqueCount { get; set; }
        public int? NoOfInvoiced { get; set; }
        public decimal? TotalRevenue { get; set; }
        public decimal? ROI { get; set; }
    }

    public class GoogleAdLeadDto
    {
        public Guid? LeadId { get; set; }
        public Guid? UserId { get; set; }
        public string? UserName { get; set; } = null!;
        public long CampaignId { get; set; }
        public double SoldPrice { get; set; }
    }
}
