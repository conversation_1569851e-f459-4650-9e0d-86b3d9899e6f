﻿using Amazon.Runtime.Internal.Transform;
using Lrb.Application.Utils;
using Lrb.Domain.Entities.MasterData;
using Lrb.Shared.Extensions;
using Newtonsoft.Json;

namespace Lrb.Application.Reports.Web
{
    public class GetSubStatusReportRequest : IRequest<PagedResponse<ModifiedSubStatusReportDto, string>>
    {
        public DateTime? FromDate { get; set; }
        public DateTime? ToDate { get; set; }
        public DateType? DateType { get; set; }
        public bool IsWithTeam { get; set; }
        public List<Guid>? UserIds { get; set; }
        public string? SearchText { get; set; }
        public List<LeadSource>? Sources { get; set; }
        public List<string>? Localites { get; set; }
        public List<string>? States { get; set; }
        public List<string>? Cities { get; set; }
        public List<string>? Projects { get; set; }
        public List<string>? SubSources { get; set; }
        public UserStatus? UserStatus { get; set; }
        public ReportPermission? ReportPermission { get; set; }
        public int PageNumber { get; set; }
        public int PageSize { get; set; } = int.MaxValue;
        public List<string>? Countries { get; set; }

    }
    public class GetSubStatusReportRequestHandler : IRequestHandler<GetSubStatusReportRequest, PagedResponse<ModifiedSubStatusReportDto, string>>
    {
        private readonly IDapperRepository _dapperRepository;
        private readonly ICurrentUser _currentUser;
        private readonly IRepositoryWithEvents<CustomMasterLeadStatus> _customMastereadStatus;
        public GetSubStatusReportRequestHandler(IDapperRepository dapperRepository, ICurrentUser currentUser, 
            IRepositoryWithEvents<CustomMasterLeadStatus> customMastereadStatus)
        {
            _dapperRepository = dapperRepository;
            _currentUser = currentUser;
            _customMastereadStatus = customMastereadStatus;
        }
        public async Task<PagedResponse<ModifiedSubStatusReportDto, string>> Handle(GetSubStatusReportRequest request, CancellationToken cancellationToken)
        {
            var tenantId = _currentUser.GetTenant();
            var userId = _currentUser.GetUserId();
            List<Guid> teamUserIds = new();
            List<Guid> permittedUserIds = new();
            var isAdmin = await _dapperRepository.IsAdminAsync(userId, tenantId ?? string.Empty);
            if (isAdmin)
            {
                permittedUserIds = (await _dapperRepository.GetAllUserIdsAsync(tenantId ?? string.Empty)).ToList();
            }
            else if (request.ReportPermission != null)
            {
                switch (request.ReportPermission)
                {
                    case ReportPermission.All:
                        permittedUserIds = (await _dapperRepository.GetAllUserIdsAsync(tenantId ?? string.Empty)).ToList();
                        break;
                    case ReportPermission.Reportees:
                        permittedUserIds = (await _dapperRepository.GetSubordinateIdsWithOnlyReporteesAsync(new List<Guid>() { userId }, tenantId ?? string.Empty)).ToList();
                        break;
                }
            }
            if (request?.UserIds?.Any() ?? false)
            {
                if (request?.IsWithTeam ?? false)
                {
                    teamUserIds = (await _dapperRepository.GetSubordinateIdsWithOnlyReporteesAsync(request.UserIds, tenantId ?? string.Empty)).ToList();
                }
                else
                {
                    teamUserIds = request?.UserIds ?? new List<Guid>();
                }
            }
            else
            {
                if (!isAdmin)
                {
                    //request.UserIds = new List<Guid>() { userId };
                    //teamUserIds = (await _dapperRepository.GetSubordinateIdsAsync(request.UserIds, tenantId ?? string.Empty)).ToList();

                    teamUserIds = permittedUserIds;
                }
            }
            if (teamUserIds.Any())
            {
                teamUserIds = teamUserIds.Where(userId => permittedUserIds.Contains(userId)).ToList();
            }
            else
            {
                teamUserIds = permittedUserIds;
            }
            request.FromDate = request.FromDate.HasValue ? request.FromDate.Value.ConvertFromDateToUtc() : null;
            request.ToDate = request.ToDate.HasValue ? request.ToDate.Value.ConvertToDateToUtc() : null;
            var res = (await _dapperRepository.QueryStoredProcedureFromReadReplicaAsync<LeadsReportDto>("LeadratBlack", "GetLeadSubStatusReportByUser", new
            {
                fromdate = request.FromDate,
                todate = request.ToDate,
                datetype = request.DateType,
                tenantid = tenantId,
                userids = teamUserIds,
                searchtext = string.IsNullOrEmpty(request.SearchText) ? null : request.SearchText.Replace(" ", "").ToLower(),
                sources = request?.Sources?.ConvertAll(i => (int)i),
                projects = request?.Projects?.ConvertAll<string>(i => i.Replace(" ", "").ToLower()),
                subsources = request?.SubSources?.ConvertAll<string>(i => i.Replace(" ", "").ToLower()),
                userstatus = (request?.UserStatus ?? 0),
                pagesize = request.PageSize,
                pagenumber = request.PageNumber,
                localites = request?.Localites?.ConvertAll<string>(i => i.LrbNormalize()),
                cities = request?.Cities?.ConvertAll<string>(i => i.LrbNormalize()),
                states = request?.States?.ConvertAll<string>(i => i.LrbNormalize()),
                countries = request?.Countries?.ConvertAll<string>(i => i.Replace(" ", "").ToLower())

            })).ToList();
            List<SubStatusReportDto> subStatusDtos = new List<SubStatusReportDto>();
            res.ForEach(i => subStatusDtos.Add(JsonConvert.DeserializeObject<SubStatusReportDto>(i.Report ?? string.Empty) ?? new()));
            var groupedResult = subStatusDtos.GroupBy(i => i.User ?? new Web.User()).ToDictionary(i => i.Key, j => j.SelectMany(i => i.Status ?? new()).ToList());
            var customStatuses =  await _customMastereadStatus.ListAsync();
            List<ModifiedSubStatusReportDto> modifiedSubStatusReportDtos = new();
            foreach (var group in groupedResult)
            {
                ModifiedSubStatusReportDto reportDto = new();
                
                reportDto.UserId = group.Key.UserId;
                reportDto.FirstName = group.Key.FirstName;
                reportDto.LastName = group.Key.LastName;

                var baseStatusWithSubStatusCount = await GetGroupedStatusAsync(group, customStatuses);

                reportDto.BaseStatusWithSubStatusCount = baseStatusWithSubStatusCount ?? new();
                reportDto.BaseStatusWithSubStatusCount.Add("All", group.Value?.Sum(i => i.Count) ?? 0);
                reportDto.BaseStatusWithSubStatusCount.Add("Active", group.Value?.Where(i => i.BaseStatus != "dropped" && i.BaseStatus != "not_interested" && i.BaseStatus != "booked" && i.BaseStatus != "booking_cancel" && i.BaseStatus != "invoiced").Sum(i => i.Count) ?? 0);
                reportDto.BaseStatusWithSubStatusCount.Add("overdue", group.Value?.Sum(i => i.OverdueCount) ?? 0);
                modifiedSubStatusReportDtos.Add(reportDto);
                //subStatusByUserDtos.Add(new 
                //{
                //    UserId = group.Key.UserId,
                //    FirstName = group.Key.FirstName,
                //    LastName = group.Key.LastName,
                //    All = group.Value?.Sum(i => i.Count) ?? 0,
                //    Active = group.Value?.Where(i => i.BaseStatus != "dropped" && i.BaseStatus != "not_interested" && i.BaseStatus != "booked").Sum(i => i.Count) ?? 0,
                //    Overdue = group.Value?.Sum(i => i.OverdueCount) ?? 0,
                //    Callback = group.Value?.Where(i => i.BaseStatus == "callback" && i.SubStatus != "callback")?.Sum(i => i.Count) ?? 0,
                //    Busy = group.Value?.FirstOrDefault(i => i.SubStatus == "busy")?.Count ?? 0,
                //    ToScheduleAMeeting = group.Value?.FirstOrDefault(i => i.SubStatus == "to_schedule_a_meeting")?.Count ?? 0,
                //    FollowUp = group.Value?.FirstOrDefault(i => i.SubStatus == "follow_up")?.Count ?? 0,
                //    ToScheduleSiteVisit = group.Value?.FirstOrDefault(i => i.SubStatus == "to_schedule_site_visit")?.Count ?? 0,
                //    PlanPostponed = group.Value?.FirstOrDefault(i => i.SubStatus == "plan_postponed")?.Count ?? 0,
                //    NeedMoreInfo = group.Value?.FirstOrDefault(i => i.SubStatus == "need_more_info")?.Count ?? 0,
                //    NotAnswered = group.Value?.FirstOrDefault(i => i.SubStatus == "not_answered")?.Count ?? 0,
                //    NotReachable = group.Value?.FirstOrDefault(i => i.SubStatus == "not_reachable")?.Count ?? 0,
                //    Dropped = group.Value?.Where(i => i.BaseStatus == "dropped" && i.SubStatus != "dropped")?.Sum(i => i.Count) ?? 0,
                //    NotLooking = group.Value?.Where(i => i.SubStatus == "not_looking")?.Sum(i => i.Count) ?? 0,
                //    RingingNotReceived = group.Value?.Where(i => i.SubStatus == "ringing_not_received")?.Sum(i => i.Count) ?? 0,
                //    WrongOrInvalidNo = group.Value?.Where(i => i.SubStatus == "wrong/invalid_no.")?.Sum(i => i.Count) ?? 0,
                //    PurchasedFromOthers = group.Value?.Where(i => i.SubStatus == "purchased_from_others")?.Sum(i => i.Count) ?? 0,
                //    MeetingScheduled = group.Value?.Where(i => i.BaseStatus == "meeting_scheduled" && i.SubStatus != "meeting_scheduled")?.Sum(i => i.Count) ?? 0,
                //    OnCall = group.Value?.FirstOrDefault(i => i.SubStatus == "on_call")?.Count ?? 0,
                //    Online = group.Value?.FirstOrDefault(i => i.SubStatus == "online")?.Count ?? 0,
                //    InPerson = group.Value?.FirstOrDefault(i => i.SubStatus == "in_person")?.Count ?? 0,
                //    Others = group.Value?.FirstOrDefault(i => i.SubStatus == "others")?.Count ?? 0,
                //    NotInterested = group.Value?.Where(i => i.BaseStatus == "not_interested" && i.SubStatus != "not_interested")?.Sum(i => i.Count) ?? 0,
                //    DifferentLocation = group.Value?.Where(i => i.SubStatus == "different_location")?.Sum(i => i.Count) ?? 0,
                //    DifferentRequirements = group.Value?.Where(i => i.SubStatus == "different_requirements")?.Sum(i => i.Count) ?? 0,
                //    UnmatchedBudget = group.Value?.Where(i => i.SubStatus == "unmatched_budget")?.Sum(i => i.Count) ?? 0,
                //    SiteVisitScheduled = group.Value?.Where(i => i.BaseStatus == "site_visit_scheduled" && i.SubStatus != "site_visit_scheduled")?.Sum(i => i.Count) ?? 0,
                //    FirstVisit = group.Value?.Where(i => i.SubStatus == "first_visit")?.Sum(i => i.Count) ?? 0,
                //    ReVisit = group.Value?.Where(i => i.SubStatus == "revisit")?.Sum(i => i.Count) ?? 0,
                //    Pending = group.Value?.FirstOrDefault(i => i.BaseStatus == "pending")?.Count ?? 0,
                //    Booked = group.Value?.FirstOrDefault(i => i.BaseStatus == "booked")?.Count ?? 0,
                //    //New = group.Value?.FirstOrDefault(i => i.BaseStatus == "new")?.Count ?? 0,
                //    jhjkhkkkh = 9,
                //    custom1dropped = 5,
                //    custom1 = 2
                //});
            }
            //var totalCount = (await _dapperRepository.QueryStoredProcedureCountAsync("LeadratBlack", "GetLeadSubStatusReportCountByUser", new
            //{
            //    fromdate = request.FromDate,
            //    todate = request.ToDate,
            //    datetype = request.DateType,
            //    tenantid = tenantId,
            //    userids = teamUserIds,
            //    searchtext = string.IsNullOrEmpty(request.SearchText) ? null : request.SearchText.Replace(" ", "").ToLower(),
            //    sources = request?.Sources?.ConvertAll(i => (int)i),
            //    projects = request?.Projects?.ConvertAll<string>(i => i.Replace(" ", "").ToLower()),
            //    subsources = request?.SubSources?.ConvertAll<string>(i => i.Replace(" ", "").ToLower()),
            //    userstatus = (request?.UserStatus ?? 0),
            //}));
            //return new(subStatusByUserDtos, totalCount);
            return new(modifiedSubStatusReportDtos, 0);
        }
        private async Task<Dictionary<string, object>?> GetGroupedStatusAsync(KeyValuePair<User, List<StatusDto>> group, List<CustomMasterLeadStatus> customStatuses)
        {
            var groupedValues = group.Value?.GroupBy(i => i.BaseStatus)?.ToDictionary(i => i.Key, j => j.ToList());
            Dictionary<string, object> baseStatusWithSubStatusCount = new();
            if(groupedValues == null)
            {
                return null;
            }
            foreach (var baseStatus in groupedValues)
            {
                Dictionary<string, int> subStatus = new();
                foreach (var status in baseStatus.Value)
                {
                    var customStatus = customStatuses.FirstOrDefault(i => i.Id == status.Id);
                    if (customStatus?.DisplayName != null)
                    {
                        string subStatusDisplayName = customStatus.DisplayName.ToLower();
                        subStatus.Add(subStatusDisplayName.Replace(" ", ""), status.Count);
                    }
                }
                if(baseStatus.Key != null)
                {
                    string baseKey = baseStatus.Key.Replace(" ", "").Replace("_", "").ToLower();
                    if (baseKey == subStatus.FirstOrDefault().Key)
                    {
                        baseStatusWithSubStatusCount.Add(baseStatus.Key, subStatus.FirstOrDefault().Value);
                    }
                    else
                    {
                        baseStatusWithSubStatusCount.Add(baseStatus.Key, subStatus);
                    }
                }
            }
            return baseStatusWithSubStatusCount;
        }
    }

    //public class Dto
    //{
    //    Lis Dictionary<string, Dto> Substatus { get; set; }
    //}

}
