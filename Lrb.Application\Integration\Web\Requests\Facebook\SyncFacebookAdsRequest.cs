﻿using Lrb.Application.Common.Facebook;
using Lrb.Application.Common.GoogleAds;
using Lrb.Domain.Entities.Integration;
using Microsoft.Identity.Client;

namespace Lrb.Application.Integration.Web.Requests.Facebook
{
    public class SyncFacebookAdsRequest : IRequest<PagedResponse<FacebookAdsInfo, string>>
    {
        public Guid AccountId { get; set; }
    }
    public class SyncFacebookAdsRequesthandler : <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, IRequestHandler<SyncFacebookAdsRequest, PagedResponse<FacebookAdsInfo, string>>
    {
        public SyncFacebookAdsRequesthandler(
           IRepositoryWithEvents<FacebookAuthResponse> facebookAuthResponseRepo,
           IRepositoryWithEvents<FacebookConnectedPageAccount> facebookConnectedPageAccountRepo,
           IRepositoryWithEvents<FacebookLeadGenForm> facebookLeadGenFormRepo,
           IFacebookService facebookService,
           IJobService hangfireService,
           ITenantIndependentRepository repository,
           ICurrentUser currentUser,
           IRepositoryWithEvents<IntegrationAccountInfo> integrationAccInfoRepo,
           IRepositoryWithEvents<FacebookAdsInfo> fbAdsRepo, ILeadRepositoryAsync leadRepositoryAsync,
           IGoogleAdsService googleAdsService,
           IRepositoryWithEvents<GoogleAdsAuthResponse> googleAdsAuthResponseRepo,
           IRepositoryWithEvents<GoogleAdsInfo> googleAdsRepo,
           IRepositoryWithEvents<GoogleCampaign> googleCampaignsRepo
           )
           : base(facebookAuthResponseRepo,
                 facebookConnectedPageAccountRepo,
                 facebookLeadGenFormRepo,
                 facebookService,
                 hangfireService,
                 repository,
                 currentUser,
                 integrationAccInfoRepo,
                 fbAdsRepo, leadRepositoryAsync,
                 googleAdsService, googleAdsAuthResponseRepo, googleAdsRepo, googleCampaignsRepo)
        { }
        public async Task<PagedResponse<FacebookAdsInfo, string>> Handle(SyncFacebookAdsRequest request, CancellationToken cancellationToken)
        {
            var fbAuthResponse = await _facebookAuthResponseRepo.GetByIdAsync(request.AccountId);
            if (fbAuthResponse == null) { throw new NotFoundException("No facebook account found by this id."); }

            var activeAds = new List<FacebookAdDto?>();
            var adAccounts = await _facebookService.GetAllFacebookAdAccountsAsync(fbAuthResponse.LongLivedUserAccessToken);
            if (adAccounts?.data.Any() ?? false)
            {
                foreach (var account in adAccounts.data)
                {
                    var ads = await _facebookService.GetAllFacebookAdsAsync(account.id, fbAuthResponse.LongLivedUserAccessToken);
                    activeAds.AddRange(ads?.data ?? new());
                }
            }
            var existingAds = await _fbAdsRepo.ListAsync(new FacebookAdsByFbAccountIdSpec(request.AccountId), CancellationToken.None);
            var activeAdIds = activeAds?.Select(i => i.id) ?? new List<string>();
            var existingAdIds = existingAds?.Select(i => i.AdId) ?? new List<string>();
            var adsToRemove = existingAds?.Where(i => !(activeAdIds).Contains(i.AdId))?.ToList() ?? new();
            var adsToAdd = activeAds?.Where(i => !existingAdIds.Contains(i.id))?.ToList() ?? new();
            if (adsToRemove.Any())
            {
                await _fbAdsRepo.DeleteRangeAsync(adsToRemove);
            }
            if (adsToAdd.Any())
            {
                IEnumerable<FacebookAdsInfo> adEntities = new List<FacebookAdsInfo>();
                var campaignIds = adsToAdd.Select(i => i.campaign_id).Distinct();
                List<FacebookEntityDto> campaigns = campaignIds.Select(i => _facebookService.GetFBEntityByIdAsync(i, fbAuthResponse.LongLivedUserAccessToken).Result).ToList();
                var adsetIds = adsToAdd.Select(i => i.adset_id).Distinct();
                List<FacebookEntityDto> adsets = adsetIds.Select(i => _facebookService.GetFBEntityByIdAsync(i, fbAuthResponse.LongLivedUserAccessToken).Result).ToList();
                adEntities = adsToAdd.Select(i => new FacebookAdsInfo()
                {
                    AdId = i?.id,
                    AdName = i?.name,
                    AdAccountId = i?.ad_account_id,
                    AdAccountName = i?.ad_account_name,
                    AdSetId = i?.adset_id,
                    AdSetName = i?.adset?.name,
                    CampaignId = i?.campaign_id,
                    CampaignName = i?.campaign?.name,
                    Status = i?.effective_status,
                    FacebookAuthResponseId = fbAuthResponse.Id,
                    IsSubscribed = true,
                    PageId = i?.creative?.object_story_spec?.page_id
                });
                adEntities = await _fbAdsRepo.AddRangeAsync(adEntities);
            }
            existingAds = await _fbAdsRepo.ListAsync(new FacebookAdsByFbAccountIdSpec(request.AccountId), CancellationToken.None);
            return new(existingAds, existingAds.Count);
        }
    }
}
