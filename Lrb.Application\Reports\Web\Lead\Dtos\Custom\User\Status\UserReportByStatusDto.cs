﻿namespace Lrb.Application.Reports.Web
{
    public class UserReportByStatusDto : IDto
    {
        public Guid Id { get; set; }
        public string? FirstName { get; set; }
        public string? LastName { get; set; }
        public string? ReportingManager { get; set; }
        public string? GeneralManager { get; set; }
        public Guid? StatusId { get; set; }
        public Guid? BaseStatusId { get; set; }
        public string? StatusDisplayName { get; set; }
        public string? BaseStatusDisplayName { get; set; }
        public long? Count { get; set; }
        public long? MeetingDoneCount { get; set; }
        public long? MeetingDoneUniqueCount { get; set; }
        public long? MeetingNotDoneCount { get; set; }
        public long? MeetingNotDoneUniqueCount { get; set; }
        public long? SiteVisitDoneCount { get; set; }
        public long? SiteVisitDoneUniqueCount { get; set; }
        public long? SiteVisitNotDoneCount { get; set; }
        public long? SiteVisitNotDoneUniqueCount { get; set; }
        public long? OverdueCount { get; set; }
        public long AllCount { get; set; }
        public long ActiveCount { get; set; }
    }

    public class ViewUserReportDto : IDto
    {
        public Guid Id { get; set; }
        private string? _firstName;
        private string? _lastName;
        private string? _userName;

        public string? FirstName
        {
            get => _firstName;
            set
            {
                _firstName = value;
                UpdateUserName();
            }
        }

        public string? LastName
        {
            get => _lastName;
            set
            {
                _lastName = value;
                UpdateUserName();
            }
        }

        public string? UserName
        {
            get => _userName;
            set => _userName = value;
        }
        private void UpdateUserName()
        {
            _userName = $"{_firstName} {_lastName}".Trim();
        }

        public string? ReportingManager { get; set; }
        public string? GeneralManager { get; set; }
        public List<ViewStatusDto>? Status { get; set; }
        public long MeetingDoneCount { get; set; }
        public long MeetingDoneUniqueCount { get; set; }
        public string? MeetingDoneUniqueCountPercentage { get; set; }
        public long MeetingNotDoneCount { get; set; }
        public long MeetingNotDoneUniqueCount { get; set; }
        public string? MeetingNotDoneUniqueCountPercentage { get; set; }
        public long SiteVisitDoneCount { get; set; }
        public long SiteVisitDoneUniqueCount { get; set; }
        public string? SiteVisitDoneUniqueCountPercentage { get; set; }
        public long SiteVisitNotDoneCount { get; set; }
        public long SiteVisitNotDoneUniqueCount { get; set; }
        public string? SiteVisitNotDoneUniqueCountPercentage { get; set; }
        public long OverdueCount { get; set; }
        public string? OverdueCountPercentage { get; set; }
        public long AllCount { get; set; }
        public long ActiveCount { get; set; }
    }

    public class FormattedUserReportDto : IDto
    {
        public string SlNo { get; set; } = default!;
        public string? FirstName { get; set; }
        public string? LastName { get; set; }
        public string? GeneralManager { get; set; }
        public string? ReportingManager { get; set; }
        public long All { get; set; }
        public long Active { get; set; }
        public long MeetingDone { get; set; }
        public long MeetingDoneUnique { get; set; }
        public string? MeetingDoneUniquePercentage { get; set; }
        public long MeetingNotDone { get; set; }
        public long MeetingNotDoneUnique { get; set; }
        public string? MeetingNotDoneUniquePercentage { get; set; }
        public long SiteVisitDone { get; set; }
        public long SiteVisitDoneUnique { get; set; }
        public string? SiteVisitDoneUniquePercentage { get; set; }
        public long SiteVisitNotDone { get; set; }
        public long SiteVisitNotDoneUnique { get; set; }
        public string? SiteVisitNotDoneUniquePercentage { get; set; }
        public long Overdue { get; set; }
        public string? OverduePercentage { get; set; }
        public List<ViewUserStatusDto>? Status { get; set; }

    }
    public class ViewUserStatusDto : IDto
    {
        public Guid StatusId { get; set; }
        public Guid? BaseStatusId { get; set; }
        public string? StatusDisplayName { get; set; }
        public string? BaseStatusDisplayName { get; set; }
        public string? Count { get; set; }
        public string? Percentage { get; set; }
    }
}
