﻿using Lrb.Application.Integration.Web.Dtos;
using Lrb.Application.LeadGenRequests;
using Lrb.Shared.Extensions;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.WebUtilities;
using Newtonsoft.Json;

namespace Lrb.Application.Integration.Web.Requests.Bayut
{
    public class PropertyFinderPushIntegrationRequestV2 : IRequest<Response<bool>>
    {
        public string ApiKey { get; set; }
        public HttpRequest HttpRequest { get; set; }
        public string TenantId { get; set; }

        public PropertyFinderPushIntegrationRequestV2(
            HttpRequest httpRequest, 
            string tenantId, 
            string base64)
        {
            HttpRequest = httpRequest;
            TenantId = tenantId;
            ApiKey = base64;
        }
    }

    public class PropertyFinderPushIntegrationRequestV2Handler : IRequestHandler<PropertyFinderPushIntegrationRequestV2, Response<bool>>
    {
        private readonly IMediator _mediator;
        private readonly Serilog.ILogger _logger;
        private readonly IDapperRepository _repository;

        public PropertyFinderPushIntegrationRequestV2Handler(
            IMediator mediator,
            Serilog.ILogger logger,
            IDapperRepository repository)
        {
            _mediator = mediator;
            _logger = logger;
            _repository = repository;
        }

        public async Task<Response<bool>> Handle(PropertyFinderPushIntegrationRequestV2 request, CancellationToken cancellationToken)
        {
            var httpRequest = request.HttpRequest;
            var bodyInString = "";
            PFRoot? requestBody = null;
            _logger.Information("PropertyFinderPushIntegrationRequestHandlerV2 -> POST(PropertyFinder) -> HttpRequest: " + httpRequest);

            if (request.HttpRequest.HasFormContentType)
            {
                var form = await httpRequest.ReadFormAsync();
                var formData = form.ToDictionary(x => x.Key, x => x.Value.ToString());
                bodyInString = JsonConvert.SerializeObject(formData);
                _logger.Information("PropertyFinderWAPushIntegrationRequestHandler -> POST(PropertyFinder) -> called, Dto: " + bodyInString);
                if (string.IsNullOrWhiteSpace(bodyInString))
                {
                    throw new ArgumentNullException("Payload Cannot be empty");
                }
                requestBody = JsonConvert.DeserializeObject<PFRoot>(bodyInString);
            }
            else if (httpRequest.QueryString.HasValue)
            {
                var queryParamsData = httpRequest.Query.ToDictionary(x => x.Key, x => x.Value.ToString());
                bodyInString = JsonConvert.SerializeObject(queryParamsData);
                _logger.Information("PropertyFinderWAPushIntegrationRequestHandler -> POST(PropertyFinder) -> called, Dto: " + bodyInString);
                if (string.IsNullOrWhiteSpace(bodyInString))
                {
                    throw new ArgumentNullException("Payload Cannot be empty");
                }
                requestBody = JsonConvert.DeserializeObject<PFRoot>(bodyInString);
            }
            else
            {
                Stream stream = httpRequest.Body;
                HttpRequestStreamReader reader = new(stream, System.Text.Encoding.UTF8);
                bodyInString = await reader.ReadToEndAsync();
                _logger.Information("PropertyFinderWAPushIntegrationRequestHandler -> POST(PropertyFinder) -> called, Dto: " + bodyInString);
                if (string.IsNullOrWhiteSpace(bodyInString))
                {
                    throw new ArgumentNullException("Payload Cannot be empty");
                }
                requestBody = JsonConvert.DeserializeObject<PFRoot>(bodyInString);
            }

            if (requestBody != null)
            {
                _logger.Information("PropertyFinderWAPushIntegrationRequestHandler -> POST(PropertyFinder) -> called, Dto: " + requestBody);
                CreateLeadGenRequest leadGenRequest = new(LeadSource.PropertyFinder, requestBody);

                await _mediator.Send(leadGenRequest);
                var userName = string.Empty;
                if (!string.IsNullOrEmpty(requestBody?.Payload?.PublicProfile?.Id))
                {
                    var isConverted = int.TryParse(requestBody?.Payload?.PublicProfile?.Id, out int thirdPartyUserId);
                    if (isConverted)
                    {
                        userName = await _repository.GetUserNameByThirdPartyId(thirdPartyUserId, request.TenantId);
                    }
                }
                var responseLink = requestBody?.Payload?.ResponseLink ?? string.Empty;
                var lead = new ListingSitesIntegrationRequest()
                {
                    Name = requestBody?.Payload?.Sender?.Name ?? string.Empty,
                    LeadSource = LeadSource.PropertyFinder,
                    Subsource = requestBody?.Payload?.Channel ?? string.Empty,
                    ApiKey = request.ApiKey,
                    Mobile = requestBody?.Payload?.Sender?.Contacts?.Where(i => i.Type == "phone").FirstOrDefault()?.Value,
                    Email = requestBody?.Payload?.Sender?.Contacts?.Where(i => i.Type == "email").FirstOrDefault()?.Value,
                    PrimaryUser = !string.IsNullOrEmpty(userName) ? userName : string.Empty,
                    ListingId = requestBody?.Payload?.Listing?.Id?.ToString() ?? string.Empty,
                    Link = requestBody?.Payload?.ResponseLink ?? string.Empty,
                    Notes = !string.IsNullOrEmpty(responseLink) ? $"Tracking Url: {responseLink}" : string.Empty,
                };
                CheckDuplicateLeadRequest checkDuplicateLeadRequest = lead.Adapt<CheckDuplicateLeadRequest>();
                checkDuplicateLeadRequest.SerializedData = lead.Serialize();
                var shouldCreateNewLead = await _mediator.Send(checkDuplicateLeadRequest);
                if (shouldCreateNewLead.Data)
                {
                    await _mediator.Send(lead);
                }
            }
            return new(true);
        }
    }
}
