﻿using Dapper;
using Npgsql;

namespace PlayGround
{
    public class CustomFiltersMigration
    {
        public string TenantId { get; set; }

        #region Lead Custom Filter Migration
        public async Task<List<CustomMasterLeadStatusDto>> GetCustomMasterLeadStatuses(string connectionString, string tenantId)
        {
            var query = $"SELECT * FROM \"LeadratBlack\".\"CustomMasterLeadStatuses\" Where \"IsDeleted\" = false and \"TenantId\" = '{tenantId}'";
            var conn = new NpgsqlConnection(connectionString);
            var result = await conn.QueryAsync<CustomMasterLeadStatusDto>(query);
            return result.ToList();
        }


        public async Task<bool> MigrateAsync(string connectionString, string tenantId)
        {
            TenantId = tenantId;
            List<CustomMasterLeadStatusDto> statuses = await GetCustomMasterLeadStatuses(connectionString, tenantId);
            var data = GetCustomFilters(statuses);
            var conn = new NpgsqlConnection(connectionString);
            await conn.OpenAsync();
            string insertquery = @"INSERT INTO ""LeadratBlack"".""CustomFilters"" 
                                       (
                                           ""Id"", 
                                           ""Name"", 
                                           ""BaseId"", 
                                           ""Level"", 
                                           ""OrderRank"", 
                                           ""TenantId"", 
                                           ""IsDeleted"", 
                                           ""CreatedBy"", 
                                           ""CreatedOn"", 
                                           ""LastModifiedBy"", 
                                           ""LastModifiedOn"", 
                                           ""DeletedOn"", 
                                           ""DeletedBy"", 
                                           ""UserId"", 
                                           ""ToNoOfDays"", 
                                           ""FromNoOfDays"", 
                                           ""IsForward"",
                                           ""IsDefault"",
                                           ""Module"",
                                           ""IsMobileFilter""
                                       ) 
                                       VALUES 
                                       (
                                           @Id, 
                                           @Name, 
                                           @BaseId, 
                                           @Level, 
                                           @OrderRank, 
                                           @TenantId, 
                                           @IsDeleted, 
                                           @CreatedBy, 
                                           @CreatedOn, 
                                           @LastModifiedBy, 
                                           @LastModifiedOn, 
                                           @DeletedOn, 
                                           @DeletedBy, 
                                           @UserId, 
                                           @ToNoOfDays, 
                                           @FromNoOfDays, 
                                           @IsForward,
                                           @IsDefault,
                                           @Module,
                                           @IsMobileFilter
                                       )";



            try
            {
                var res = await conn.ExecuteAsync(insertquery, data);
                Console.WriteLine(res);
                await conn.CloseAsync();
                await MigrateFiltersWithStatusAsync(connectionString, data);
                var fields = await MigrateFieldsAsync(connectionString);
                await MigrateCustomFieldsAsync(connectionString, statuses, fields);
            }
            catch (Exception ex)
            {
            }
            return true;
        }
        public async Task<bool> MigrateFiltersWithStatusAsync(string connectionString, List<CustomFilters> filters)
        {
            var data = GetCustomFilterCustomMasterLeadStatus(filters);
            var conn = new NpgsqlConnection(connectionString);
            await conn.OpenAsync();
            string insertquery = @"INSERT INTO ""LeadratBlack"".""CustomFilterCustomMasterLeadStatus"" 
                                       (
                                           ""CustomFiltersId"", 
                                           ""StatusesId""
                                       ) 
                                       VALUES 
                                       (
                                           @CustomFiltersId, 
                                           @StatusesId
                                       )";

            try
            {
                var res = await conn.ExecuteAsync(insertquery, data);
                Console.WriteLine(res);
                await conn.CloseAsync();
            }
            catch (Exception ex)
            {
            }
            return true;
        }

        public async Task<bool> MigrateCustomFieldsAsync(string connectionString, List<CustomMasterLeadStatusDto> statusDtos, List<Field> fields)
        {
            var data = GetCustomFieldsDto(statusDtos, fields);
            var conn = new NpgsqlConnection(connectionString);
            await conn.OpenAsync();
            string insertquery = @"INSERT INTO ""LeadratBlack"".""CustomFields"" 
                                       (
                                           ""Id"", 
                                           ""IsRequired"",
                                           ""Value"",
                                           ""FieldId"",
                                           ""StatusId"",
                                           ""TenantId"",
                                           ""IsDeleted"",
                                           ""CreatedBy"",
                                           ""CreatedOn"",
                                           ""LastModifiedBy"",
                                           ""LastModifiedOn"",
                                           ""DeletedOn"",
                                           ""DeletedBy"",
                                           ""Validators""
                                       ) 
                                       VALUES 
                                       (
                                           @Id, 
                                           @IsRequired,
                                           @Value,
                                           @FieldId,
                                           @StatusId,
                                           @TenantId,
                                           @IsDeleted,
                                           @CreatedBy,
                                           @CreatedOn,
                                           @LastModifiedBy,
                                           @LastModifiedOn,
                                           @DeletedOn,
                                           @DeletedBy,
                                           @Validators
                                       )";
            try
            {
                var res = await conn.ExecuteAsync(insertquery, data);
                Console.WriteLine(res);
                await conn.CloseAsync();
            }
            catch (Exception ex)
            {

            }
            return true;
        }
        public async Task<List<Field>> MigrateFieldsAsync(string connectionString)
        {
            var data = GetFieldsDto();
            var conn = new NpgsqlConnection(connectionString);
            await conn.OpenAsync();
            string insertquery = @"INSERT INTO ""LeadratBlack"".""Fields"" 
                                       (
                                           ""Id"", 
                                           ""Name"",
                                           ""OrderRank"",
                                           ""Module"",
                                           ""Notes"",
                                           ""TenantId"",
                                           ""IsDeleted"",
                                           ""CreatedBy"",
                                           ""CreatedOn"",
                                           ""LastModifiedBy"",
                                           ""LastModifiedOn"",
                                           ""DeletedOn"",
                                           ""DeletedBy""
                                       )
                                       VALUES 
                                       (
                                           @Id, 
                                           @Name,
                                           @OrderRank,
                                           @Module,
                                           @Notes,
                                           @TenantId,
                                           @IsDeleted,
                                           @CreatedBy,
                                           @CreatedOn,
                                           @LastModifiedBy,
                                           @LastModifiedOn,
                                           @DeletedOn,
                                           @DeletedBy
                                       )";
            try
            {
                var res = await conn.ExecuteAsync(insertquery, data);
                Console.WriteLine(res);
                await conn.CloseAsync();
            }
            catch (Exception ex)
            {

            }
            return data;
        }



        public List<Field> GetFieldsDto()
        {
            var field1 = GetField("ScheduledDate", 1);
            var field2 = GetField("Projects", 2);
            var field3 = GetField("Document", 3);
            return new List<Field>() { field1, field2 , field3};
        }
        public Field GetField(string field, int orderRank)
        {
            return new Field()
            {
                Id = Guid.NewGuid(),
                Name = field,
                OrderRank = orderRank,
                LastModifiedOn = DateTime.UtcNow,
                CreatedOn = DateTime.UtcNow,
                CreatedBy = Guid.Empty,
                LastModifiedBy = Guid.Empty,
                TenantId = TenantId,
                IsDeleted = false,
                Notes = string.Empty,
                DeletedBy = null,
                DeletedOn = null,
                Module = "leads",
            };
        }
        public List<CustomField> GetCustomFieldsDto(List<CustomMasterLeadStatusDto> statusDtos, List<Field> fields)
        {
            var data = new List<CustomField>();
            if (statusDtos?.Any() ?? false)
            {
                var baseStatuses = statusDtos.Where(i => i.BaseId == null || i.BaseId == Guid.Empty).ToList();
                foreach (var item in baseStatuses)
                {
                    if (item.Status == Status.meeting_scheduled)
                    {
                        var subStatus = statusDtos.Where(i => i.BaseId == item.Id).ToList();
                        var field1 = fields.FirstOrDefault(i => i.Name == "ScheduledDate");
                        if (subStatus?.Any() ?? false)
                        {
                            foreach (var status in subStatus)
                            {
                                var result = GetCustomField(true, status.Id, field1?.Id ?? Guid.Empty);
                                data.Add(result);
                            }
                        }
                    }
                    else if (item.Status == Status.site_visit_scheduled)
                    {
                        var subStatus = statusDtos.Where(i => i.BaseId == item.Id).ToList();
                        var field2 = fields.FirstOrDefault(i => i.Name == "Projects");
                        var field1 = fields.FirstOrDefault(i => i.Name == "ScheduledDate"); 
                        if (subStatus?.Any() ?? false)
                        {
                            foreach (var status in subStatus)
                            {
                                var result = GetCustomField(true, status.Id, field1?.Id ?? Guid.Empty);
                                var result1 = GetCustomField(false, status.Id, field2?.Id ?? Guid.Empty);
                                data.Add(result);
                                data.Add(result1);
                            }
                        }
                    }
                    else if (item.Status == Status.callback)
                    {
                        var subStatus = statusDtos.Where(i => i.BaseId == item.Id).ToList();
                        var field1 = fields.FirstOrDefault(i => i.Name == "ScheduledDate");
                        if (subStatus?.Any() ?? false)
                        {
                            foreach (var status in subStatus)
                            {
                                var result = GetCustomField(true, status.Id, field1?.Id ?? Guid.Empty);
                                data.Add(result);
                            }
                        }
                    }
                }
            }
            return data;
        }
        public CustomField GetCustomField(bool isRequired, Guid statusId, Guid fieldId)
        {
            return new CustomField()
            {
                Id = Guid.NewGuid(),
                IsRequired = isRequired,
                Value = string.Empty,
                StatusId = statusId,
                FieldId = fieldId,
                LastModifiedOn = DateTime.UtcNow,
                CreatedOn = DateTime.UtcNow,
                CreatedBy = Guid.Empty,
                LastModifiedBy = Guid.Empty,
                TenantId = TenantId,
                IsDeleted = false,
                DeletedBy = null,
                DeletedOn = null,
                Validators = isRequired ? new List<string>() { "required" } : null,
            };
        }

        public List<CustomFilterCustomMasterLeadStatus> GetCustomFilterCustomMasterLeadStatus(List<CustomFilters> filters)
        {
            var data = new List<CustomFilterCustomMasterLeadStatus>();
            foreach (var filter in filters)
            {
                if (filter?.CustomMasterLeadStatuses?.Any() ?? false)
                {
                    foreach (var item in filter.CustomMasterLeadStatuses)
                    {
                        var cfcms = MapCustomFilterCustomMasterLeadStatus(filter.Id, item.Id);
                        data.Add(cfcms);
                    }
                }
            }
            return data;
        }
        public List<CustomFilters> GetCustomFilters(List<CustomMasterLeadStatusDto> statuses)
        {
            #region Web
            var notInterestedStatus = statuses.FirstOrDefault(i => Status.not_interested.Contains(i?.Status ?? string.Empty) && (i.BaseId == null || i.BaseId == Guid.Empty));
            var notInterestedSubStatus = statuses.Where(i => i.BaseId == notInterestedStatus?.Id).ToList();


            var droppedStatus = statuses.FirstOrDefault(i => Status.dropped.Contains(i?.Status ?? string.Empty) && (i.BaseId == null || i.BaseId == Guid.Empty));
            var droppedSubStatus = statuses.Where(i => i.BaseId == droppedStatus?.Id).ToList();


            var bookedStatus = statuses.FirstOrDefault(i => Status.booked.Contains(i?.Status ?? string.Empty) && (i.BaseId == null || i.BaseId == Guid.Empty));
            var bookedSubStatus = statuses.Where(i => i.BaseId == bookedStatus?.Id).ToList();


            var bookingCancelStatus = statuses.FirstOrDefault(i => Status.booking_cancel.Contains(i?.Status ?? string.Empty) && (i.BaseId == null || i.BaseId == Guid.Empty));
            var bookingCancelSubStatus = statuses.Where(i => i.BaseId == bookingCancelStatus?.Id).ToList();


            var site_visit_scheduled = statuses.FirstOrDefault(i => Status.site_visit_scheduled.Contains(i?.Status ?? string.Empty) && (i.BaseId == null || i.BaseId == Guid.Empty));
            var site_visit_scheduledSubStatus = statuses.Where(i => (site_visit_scheduled?.Id ?? Guid.Empty) == i.BaseId).ToList();


            var meeting_scheduled = statuses.FirstOrDefault(i => Status.meeting_scheduled.Contains(i?.Status ?? string.Empty) && (i.BaseId == null || i.BaseId == Guid.Empty));
            var meeting_scheduledSubStatus = statuses.Where(i => (meeting_scheduled?.Id ?? Guid.Empty) == i.BaseId).ToList();


            var callbackStatus = statuses.FirstOrDefault(i => Status.callback.Contains(i?.Status ?? string.Empty) && (i.BaseId == null || i.BaseId == Guid.Empty));
            var callbackSubStatus = statuses.Where(i => (callbackStatus?.Id ?? Guid.Empty) == i.BaseId).ToList();

            var invoicedStatus = statuses.FirstOrDefault(i => Status.invoiced.Contains(i?.Status ?? string.Empty) && (i.BaseId == null || i.BaseId == Guid.Empty));
            var invoicedSubStatus = statuses.Where(i => (invoicedStatus?.Id ?? Guid.Empty) == i.BaseId).ToList();

            var newStatus = statuses.FirstOrDefault(i => Status.New.Contains(i?.Status ?? string.Empty) && (i.BaseId == null || i.BaseId == Guid.Empty));


            var pendingStatus = statuses.FirstOrDefault(i => Status.pending.Contains(i?.Status ?? string.Empty) && (i.BaseId == null || i.BaseId == Guid.Empty));


            var expression_of_interest = statuses.FirstOrDefault(i => Status.expression_of_interest.Contains(i?.Status ?? string.Empty) && (i.BaseId == null || i.BaseId == Guid.Empty));


            #region NotInclude
            var notIncludeIds = new List<Guid>();
            notIncludeIds.Add(notInterestedStatus?.Id ?? Guid.Empty);
            notIncludeIds.Add(droppedStatus?.Id ?? Guid.Empty);
            notIncludeIds.Add(bookedStatus?.Id ?? Guid.Empty);
            notIncludeIds.Add(bookingCancelStatus?.Id ?? Guid.Empty);
            notIncludeIds.Add(invoicedStatus?.Id ?? Guid.Empty);
            notIncludeIds.AddRange(notInterestedSubStatus.Select(i => i.Id).ToList());
            notIncludeIds.AddRange(droppedSubStatus.Select(i => i.Id).ToList());
            notIncludeIds.AddRange(bookedSubStatus.Select(i => i.Id).ToList());
            notIncludeIds.AddRange(bookingCancelSubStatus.Select(i => i.Id).ToList());
            notIncludeIds.AddRange(invoicedSubStatus.Select(i => i.Id).ToList());
            #endregion

            #region ScheduledIds
            var ScheduledIds = new List<Guid>();
            ScheduledIds.Add(callbackStatus?.Id ?? Guid.Empty);
            ScheduledIds.Add(site_visit_scheduled?.Id ?? Guid.Empty);
            ScheduledIds.Add(meeting_scheduled?.Id ?? Guid.Empty);
            ScheduledIds.AddRange(callbackSubStatus.Select(i => i.Id).ToList());
            ScheduledIds.AddRange(site_visit_scheduledSubStatus.Select(i => i.Id).ToList());
            ScheduledIds.AddRange(meeting_scheduledSubStatus.Select(i => i.Id).ToList());
            #endregion

            var data = new List<CustomFilters>();
            var allLeads = MapData("All Leads", null, 1, 1, null, null);
            var activeLeads = MapData("Active Leads", null, 1, 2, null, null, statuses.Where(i => !notIncludeIds.Contains(i.Id)).ToList(), null, true);
            var notInterested = MapData("Not Interested", null, 1, 3, null, null, statuses.Where(i => (notInterestedStatus?.Id ?? Guid.Empty) == i.Id || (notInterestedStatus?.Id ?? Guid.Empty) == i.BaseId).ToList());
            var dropped = MapData("Dropped", null, 1, 4, null, null, statuses.Where(i => (droppedStatus?.Id ?? Guid.Empty) == i.Id || (droppedStatus?.Id ?? Guid.Empty) == i.BaseId).ToList());
            var booked = MapData("Booked", null, 1, 5, null, null, statuses.Where(i => (bookedStatus?.Id ?? Guid.Empty) == i.Id || (bookedStatus?.Id ?? Guid.Empty) == i.BaseId).ToList());
            var bookingCancel = MapData("Booking Cancel", null, 1, 6, null, null, statuses.Where(i => (bookingCancelStatus?.Id ?? Guid.Empty) == i.Id || (bookingCancelStatus?.Id ?? Guid.Empty) == i.BaseId).ToList());
            var invoiced = MapData("Invoiced", null, 1, 7, null, null, statuses.Where(i => (invoicedStatus?.Id ?? Guid.Empty) == i.Id || (invoicedStatus?.Id ?? Guid.Empty) == i.BaseId).ToList());

            #region Not Interested
            if (notInterestedSubStatus?.Any() ?? false)
            {
                var i = 1;
                foreach (var status in notInterestedSubStatus)
                {
                    if (!string.IsNullOrEmpty(status.DisplayName))
                    {
                        var filter = MapData(status.DisplayName, notInterested.Id, 2, i, null, null, new List<CustomMasterLeadStatusDto>() { status });
                        data.Add(filter);
                        i++;
                    }
                }
            }
            #endregion
            #region DroppedSubStatus
            if (droppedSubStatus?.Any() ?? false)
            {
                var i = 1;
                foreach (var status in droppedSubStatus)
                {
                    if (!string.IsNullOrEmpty(status.DisplayName))
                    {
                        var filter = MapData(status.DisplayName, dropped.Id, 2, i, null, null, new List<CustomMasterLeadStatusDto>() { status });
                        data.Add(filter);
                        i++;
                    }
                }
            }
            #endregion
            #region BookedSubStatus
            if (bookedSubStatus?.Any() ?? false)
            {
                var i = 1;
                foreach (var status in bookedSubStatus)
                {
                    if (!string.IsNullOrEmpty(status.DisplayName))
                    {
                        var filter = MapData(status.DisplayName, booked.Id, 2, i, null, null, new List<CustomMasterLeadStatusDto>() { status });
                        data.Add(filter);
                        i++;
                    }
                }
            }
            #endregion
            #region BookingCancelSubStatus
            if (bookingCancelSubStatus?.Any() ?? false)
            {
                var i = 1;
                foreach (var status in bookingCancelSubStatus)
                {
                    if (!string.IsNullOrEmpty(status.DisplayName))
                    {
                        var filter = MapData(status.DisplayName, bookingCancel.Id, 2, i, null, null, new List<CustomMasterLeadStatusDto>() { status });
                        data.Add(filter);
                        i++;
                    }
                }
            }
            #endregion
            #region InvoicedStatus
            if (invoicedSubStatus?.Any() ?? false)
            {
                var i = 1;
                foreach (var status in invoicedSubStatus)
                {
                    if (!string.IsNullOrEmpty(status.DisplayName))
                    {
                        var filter = MapData(status.DisplayName, invoiced.Id, 2, i, null, null, new List<CustomMasterLeadStatusDto>() { status });
                        data.Add(filter);
                        i++;
                    }
                }
            }
            #endregion

            data.Add(allLeads);
            data.Add(activeLeads);
            data.Add(notInterested);
            data.Add(dropped);
            data.Add(booked);
            data.Add(bookingCancel);
            data.Add(invoiced);


            #region ActiveLeads
            var New = MapData("New", activeLeads.Id, 2, 1, null, null, new List<CustomMasterLeadStatusDto>() { newStatus }.ToList());
            var Pending = MapData("Pending", activeLeads.Id, 2, 2, null, null, new List<CustomMasterLeadStatusDto>() { pendingStatus }.ToList());
            var Scheduled = MapData("Scheduled", activeLeads.Id, 2, 3, true, null, statuses.Where(i => ScheduledIds.Contains(i.Id)).ToList());
            data.Add(Scheduled);
            data.Add(Pending);
            data.Add(New);


            #region Scheduled
            var ScheduledAll = MapData("All", Scheduled.Id, 3, 1, true, null, statuses.Where(i => ScheduledIds.Contains(i.Id)).ToList());
            var ScheduledToday = MapData("Scheduled Today", Scheduled.Id, 3, 2, true, 0, statuses.Where(i => ScheduledIds.Contains(i.Id)).ToList(), 1);
            ScheduledToday.IsDefault = true;
            var ScheduledTomorrow = MapData("Scheduled Tomorrow", Scheduled.Id, 3, 3, true, 1, statuses.Where(i => ScheduledIds.Contains(i.Id)).ToList(), 2);
            var Schedulednext = MapData("Scheduled next 2 days", Scheduled.Id, 3, 4, true, 1, statuses.Where(i => ScheduledIds.Contains(i.Id)).ToList(), 3);
            var Scheduledup = MapData("Upcoming Schedules", Scheduled.Id, 3, 5, true, 1, statuses.Where(i => ScheduledIds.Contains(i.Id)).ToList());
            data.Add(ScheduledAll);
            data.Add(ScheduledToday);
            data.Add(ScheduledTomorrow);
            data.Add(Schedulednext);
            data.Add(Scheduledup);
            #endregion

            #region NextLevelFilters 
            var firstNextLevelFilters = GetNextLevelFilters(ScheduledAll.Id, 4, true, statuses.Where(i => (site_visit_scheduled?.Id ?? Guid.Empty) == i.Id || site_visit_scheduledSubStatus.Select(j => j.Id).Contains(i.Id)).ToList(),
                statuses.Where(i => (meeting_scheduled?.Id ?? Guid.Empty) == i.Id || meeting_scheduledSubStatus.Select(j => j.Id).Contains(i.Id)).ToList(),
                statuses.Where(i => (callbackStatus?.Id ?? Guid.Empty) == i.Id || callbackSubStatus.Select(j => j.Id).Contains(i.Id)).ToList(), null, null);
            var secondNextLevelFilters = GetNextLevelFilters(ScheduledToday.Id, 4, true, statuses.Where(i => (site_visit_scheduled?.Id ?? Guid.Empty) == i.Id || site_visit_scheduledSubStatus.Select(j => j.Id).Contains(i.Id)).ToList(),
                statuses.Where(i => (meeting_scheduled?.Id ?? Guid.Empty) == i.Id || meeting_scheduledSubStatus.Select(j => j.Id).Contains(i.Id)).ToList(),
                statuses.Where(i => (callbackStatus?.Id ?? Guid.Empty) == i.Id || callbackSubStatus.Select(j => j.Id).Contains(i.Id)).ToList(), 0, 1);
            var thirdNextLevelFilters = GetNextLevelFilters(ScheduledTomorrow.Id, 4, true, statuses.Where(i => (site_visit_scheduled?.Id ?? Guid.Empty) == i.Id || site_visit_scheduledSubStatus.Select(j => j.Id).Contains(i.Id)).ToList(),
                statuses.Where(i => (meeting_scheduled?.Id ?? Guid.Empty) == i.Id || meeting_scheduledSubStatus.Select(j => j.Id).Contains(i.Id)).ToList(),
                statuses.Where(i => (callbackStatus?.Id ?? Guid.Empty) == i.Id || callbackSubStatus.Select(j => j.Id).Contains(i.Id)).ToList(), 1, 2);
            var fourthNextLevelFilters = GetNextLevelFilters(Schedulednext.Id, 4, true, statuses.Where(i => (site_visit_scheduled?.Id ?? Guid.Empty) == i.Id || site_visit_scheduledSubStatus.Select(j => j.Id).Contains(i.Id)).ToList(),
                statuses.Where(i => (meeting_scheduled?.Id ?? Guid.Empty) == i.Id || meeting_scheduledSubStatus.Select(j => j.Id).Contains(i.Id)).ToList(),
                statuses.Where(i => (callbackStatus?.Id ?? Guid.Empty) == i.Id || callbackSubStatus.Select(j => j.Id).Contains(i.Id)).ToList(), 1, 3);
            var fifthNextLevelFilters = GetNextLevelFilters(Scheduledup.Id, 4, true, statuses.Where(i => (site_visit_scheduled?.Id ?? Guid.Empty) == i.Id || site_visit_scheduledSubStatus.Select(j => j.Id).Contains(i.Id)).ToList(),
                statuses.Where(i => (meeting_scheduled?.Id ?? Guid.Empty) == i.Id || meeting_scheduledSubStatus.Select(j => j.Id).Contains(i.Id)).ToList(),
                statuses.Where(i => (callbackStatus?.Id ?? Guid.Empty) == i.Id || callbackSubStatus.Select(j => j.Id).Contains(i.Id)).ToList(), 1, null);

            data.AddRange(firstNextLevelFilters);
            data.AddRange(secondNextLevelFilters);
            data.AddRange(thirdNextLevelFilters);
            data.AddRange(fourthNextLevelFilters);
            data.AddRange(fifthNextLevelFilters);
            #endregion
            #endregion


            #region Overdue
            var overdue = MapData("Overdue", activeLeads.Id, 2, 4, false, 0, statuses.Where(i => ScheduledIds.Contains(i.Id)).ToList());
            var overdueNxtFilters = GetNextLevelFilters(overdue.Id, 3, false, statuses.Where(i => (site_visit_scheduled?.Id ?? Guid.Empty) == i.Id || site_visit_scheduledSubStatus.Select(j => j.Id).Contains(i.Id)).ToList(),
                statuses.Where(i => (meeting_scheduled?.Id ?? Guid.Empty) == i.Id || meeting_scheduledSubStatus.Select(j => j.Id).Contains(i.Id)).ToList(),
                statuses.Where(i => (callbackStatus?.Id ?? Guid.Empty) == i.Id || callbackSubStatus.Select(j => j.Id).Contains(i.Id)).ToList(), 0, null);
            data.Add(overdue);
            overdueNxtFilters.FirstOrDefault(i => i.IsDefault = true);
            data.AddRange(overdueNxtFilters);
            #endregion


            #region Expression Of Interest 
            var expressionOfInterest = MapData("Expression Of Interest", activeLeads.Id, 2, 5, null, null, statuses.Where(i => expression_of_interest.Id == i.Id || i.BaseId == expression_of_interest.Id).ToList());
            data.Add(expressionOfInterest);
            #endregion
            #endregion

            #region Mobile
            var scheduledTodayMob = MapData("Scheduled Today", null, 1, 1, true, 0, statuses.Where(i => ScheduledIds.Contains(i.Id)).ToList(), 1, true, true);
            var freshLeadsMob = MapData("Fresh Leads", null, 1, 2, null, null, new List<CustomMasterLeadStatusDto>() { newStatus }.ToList(), null, true, true);
            var pendingLeadsMob = MapData("Pending", null, 1, 3, null, null, new List<CustomMasterLeadStatusDto>() { pendingStatus }.ToList(), null, true, true);
            var overdueLeadsMob = MapData("Overdue", null, 1, 4, false, 0, statuses.Where(i => ScheduledIds.Contains(i.Id)).ToList(), null, true, true);
            var allLeadsMob = MapData("All Leads", null, 1, 5, null, null, isDefault: true, isMobileFilter: true);
            var activeLeadsMob = MapData("Active Leads", null, 1, 6, null, null, statuses.Where(i => !notIncludeIds.Contains(i.Id)).ToList(), null, false, true);
            var bookedMob = MapData("Booked", null, 1, 7, null, null, statuses.Where(i => (bookedStatus?.Id ?? Guid.Empty) == i.Id || (bookedStatus?.Id ?? Guid.Empty) == i.BaseId).ToList(), null, false, true);
            var bookingCancelMob = MapData("Booking Cancel", null, 1, 8, null, null, statuses.Where(i => (bookingCancelStatus?.Id ?? Guid.Empty) == i.Id || (bookingCancelStatus?.Id ?? Guid.Empty) == i.BaseId).ToList(), null, false, true);
            var callbacksMob = MapData("Callbacks", null, 1, 9, true, null, statuses.Where(i => ScheduledIds.Contains(i.Id)).ToList(), null, false, true);
            var notInterestedMob = MapData("Not Interested", null, 1, 10, null, null, statuses.Where(i => (notInterestedStatus?.Id ?? Guid.Empty) == i.Id || (notInterestedStatus?.Id ?? Guid.Empty) == i.BaseId).ToList(), null, false, true);
            var droppedMob = MapData("Dropped", null, 1, 11, null, null, statuses.Where(i => (droppedStatus?.Id ?? Guid.Empty) == i.Id || (droppedStatus?.Id ?? Guid.Empty) == i.BaseId).ToList(), null, false, true);
            var expressionOfInterestMob = MapData("Expression Of Interest", null, 1, 12, null, null, statuses.Where(i => expression_of_interest.Id == i.Id || i.BaseId == expression_of_interest.Id).ToList(), null, false, true);
            var meetingScheduledMob = MapData("Meeting Scheduled", null, 1, 13, null, null, statuses.Where(i => meeting_scheduled.Id == i.Id || i.BaseId == meeting_scheduled.Id).ToList(), null, false, true);
            var siteVisitScheduledMob = MapData("Site Visit Scheduled", null, 1, 14, null, null, statuses.Where(i => site_visit_scheduled.Id == i.Id || i.BaseId == site_visit_scheduled.Id).ToList(), null, false, true);
            var scheduledTomorrowMob = MapData("Scheduled Tomorrow", null, 1, 15, true, 1, statuses.Where(i => ScheduledIds.Contains(i.Id)).ToList(), 2, false, true);
            var schedulednextMob = MapData("Scheduled next 2 days", null, 1, 16, true, 1, statuses.Where(i => ScheduledIds.Contains(i.Id)).ToList(), 3, false, true);
            var scheduledupMOb = MapData("Upcoming Schedules", null, 1, 17, true, 1, statuses.Where(i => ScheduledIds.Contains(i.Id)).ToList(), null, false, true);
            data.Add(scheduledTodayMob);
            data.Add(freshLeadsMob);
            data.Add(pendingLeadsMob);
            data.Add(overdueLeadsMob);
            data.Add(allLeadsMob);
            data.Add(activeLeadsMob);
            data.Add(bookedMob);
            data.Add(bookingCancelMob);
            data.Add(callbacksMob);
            data.Add(notInterestedMob);
            data.Add(droppedMob);
            data.Add(expressionOfInterestMob);
            data.Add(meetingScheduledMob);
            data.Add(siteVisitScheduledMob);
            data.Add(scheduledTomorrowMob);
            data.Add(schedulednextMob);
            data.Add(scheduledupMOb);
            #endregion

            return data;
        }
        public List<CustomFilters> GetNextLevelFilters(Guid baseId, int level, bool? isForward, List<CustomMasterLeadStatusDto>? SitevisitsstatusDtos = default,
            List<CustomMasterLeadStatusDto>? MeetingsstatusDtos = default, List<CustomMasterLeadStatusDto>? CallbacksstatusDtos = default, long? fromNoOfDays = default, long? toNoOfDays = default)
        {
            var data = new List<CustomFilters>();
            var Sitevisits = MapData("Site visits", baseId, level, 1, isForward, fromNoOfDays, SitevisitsstatusDtos, toNoOfDays);
            var Meetings = MapData("Meetings", baseId, level, 2, isForward, fromNoOfDays, MeetingsstatusDtos, toNoOfDays);
            var Callbacks = MapData("Callbacks", baseId, level, 3, isForward, fromNoOfDays, CallbacksstatusDtos, toNoOfDays);
            data.Add(Sitevisits);
            data.Add(Meetings);
            data.Add(Callbacks);
            return data;
        }
        public CustomFilters MapData(string name, Guid? baseId, int level, int orderRank, bool? isForward, long? fromNoOfDays, List<CustomMasterLeadStatusDto>? statusDtos = default, long? toNoOfDays = default, bool isDefault = false, bool isMobileFilter = false)
        {
            return new CustomFilters()
            {
                Id = Guid.NewGuid(),
                Name = name,
                BaseId = baseId,
                Level = level,
                OrderRank = orderRank,
                IsForward = isForward,
                FromNoOfDays = fromNoOfDays,
                ToNoOfDays = toNoOfDays,
                LastModifiedOn = DateTime.UtcNow,
                CreatedOn = DateTime.UtcNow,
                CreatedBy = Guid.Empty,
                LastModifiedBy = Guid.Empty,
                TenantId = TenantId,
                CustomMasterLeadStatuses = statusDtos,
                IsDeleted = false,
                UserId = Guid.Empty,
                IsDefault = isDefault,
                Module = "leads",
                IsMobileFilter = isMobileFilter,
            };
        }
        public CustomFilterCustomMasterLeadStatus MapCustomFilterCustomMasterLeadStatus(Guid customFiltersId, Guid statusesId)
        {
            return new CustomFilterCustomMasterLeadStatus()
            {
                CustomFiltersId = customFiltersId,
                StatusesId = statusesId
            };
        }
        public class CustomFilterCustomMasterLeadStatus
        {
            public Guid CustomFiltersId { get; set; }
            public Guid StatusesId { get; set; }
        }
        public class CustomFilters
        {
            public Guid Id { get; set; }
            public string Name { get; set; } = default!;
            public Guid? BaseId { get; set; }
            public int Level { get; set; }
            public int OrderRank { get; set; }
            public bool? IsForward { get; set; }
            public long? FromNoOfDays { get; set; }
            public long? ToNoOfDays { get; set; }
            public DateTime? LastModifiedOn { get; set; }
            public DateTime CreatedOn { get; set; }
            public Guid CreatedBy { get; set; }
            public Guid LastModifiedBy { get; set; }
            public string TenantId { get; set; }
            public bool IsDeleted { get; set; }
            public DateTime? DeletedOn { get; set; }
            public Guid? DeletedBy { get; set; }
            public Guid UserId { get; set; }
            public bool IsDefault { get; set; }
            public string Module { get; set; } = default!;
            public bool IsMobileFilter { get; set; }
            public List<CustomMasterLeadStatusDto>? CustomMasterLeadStatuses { get; set; }
            public List<CustomProspectStatusDto>? CustomProspectStatuses { get; set; }
            public bool? IsConvertedFilter { get; set; }
        }
        public static class Status
        {
            public static string booked = "booked";
            public static string expression_of_interest = "expression_of_interest";
            public static string New = "new";
            public static string pending = "pending";
            public static string dropped = "dropped";
            public static string booking_cancel = "booking_cancel";
            public static string meeting_scheduled = "meeting_scheduled";
            public static string not_interested = "not_interested";
            public static string site_visit_scheduled = "site_visit_scheduled";
            public static string callback = "callback";
            public static string invoiced = "invoiced";
        }

        public class Field
        {
            public Guid Id { get; set; }
            public string Name { get; set; } = default!;
            public int OrderRank { get; set; }
            public string Module { get; set; } = default!;
            public string? Notes { get; set; }
            public DateTime? LastModifiedOn { get; set; }
            public DateTime CreatedOn { get; set; }
            public Guid CreatedBy { get; set; }
            public Guid LastModifiedBy { get; set; }
            public string TenantId { get; set; }
            public bool IsDeleted { get; set; }
            public DateTime? DeletedOn { get; set; }
            public Guid? DeletedBy { get; set; }
        }
        public class CustomField
        {
            public Guid Id { get; set; }
            public Guid? StatusId { get; set; }
            public Guid FieldId { get; set; }
            public bool IsRequired { get; set; }
            public string? Value { get; set; }
            public DateTime? LastModifiedOn { get; set; }
            public DateTime CreatedOn { get; set; }
            public Guid CreatedBy { get; set; }
            public Guid LastModifiedBy { get; set; }
            public string TenantId { get; set; }
            public bool IsDeleted { get; set; }
            public DateTime? DeletedOn { get; set; }
            public Guid? DeletedBy { get; set; }
            public List<string>? Validators { get; set; }
            public Guid? ProspectStatusId { get; set; }
        }

        #endregion

        #region Data Custom Filter Migration

        public async Task<List<CustomProspectStatusDto>> GetCustomProspectStatuses(string connectionString, string tenantId)
        {
            var query = $"SELECT * FROM \"LeadratBlack\".\"CustomProspectStatuses\" Where \"IsDeleted\" = false and \"TenantId\" = '{tenantId}'";
            var conn = new NpgsqlConnection(connectionString);
            var result = await conn.QueryAsync<CustomProspectStatusDto>(query);
            return result.ToList();
        }

        public async Task<bool> DataMigrateAsync(string connectionString, string tenantId)
        {
            TenantId = tenantId;
            List<CustomProspectStatusDto> statuses = await GetCustomProspectStatuses(connectionString, tenantId);
            var data = GetProspectCustomFilters(statuses);
            var conn = new NpgsqlConnection(connectionString);
            await conn.OpenAsync();
            string insertquery = @"INSERT INTO ""LeadratBlack"".""CustomFilters"" 
                                       (
                                           ""Id"", 
                                           ""Name"", 
                                           ""BaseId"", 
                                           ""Level"", 
                                           ""OrderRank"", 
                                           ""TenantId"", 
                                           ""IsDeleted"", 
                                           ""CreatedBy"", 
                                           ""CreatedOn"", 
                                           ""LastModifiedBy"", 
                                           ""LastModifiedOn"", 
                                           ""DeletedOn"", 
                                           ""DeletedBy"", 
                                           ""UserId"", 
                                           ""ToNoOfDays"", 
                                           ""FromNoOfDays"", 
                                           ""IsForward"",
                                           ""IsDefault"",
                                           ""Module"",
                                           ""IsMobileFilter"",
                                           ""IsConvertedFilter""
                                       ) 
                                       VALUES 
                                       (
                                           @Id, 
                                           @Name, 
                                           @BaseId, 
                                           @Level, 
                                           @OrderRank, 
                                           @TenantId, 
                                           @IsDeleted, 
                                           @CreatedBy, 
                                           @CreatedOn, 
                                           @LastModifiedBy, 
                                           @LastModifiedOn, 
                                           @DeletedOn, 
                                           @DeletedBy, 
                                           @UserId, 
                                           @ToNoOfDays, 
                                           @FromNoOfDays, 
                                           @IsForward,
                                           @IsDefault,
                                           @Module,
                                           @IsMobileFilter,
                                           @IsConvertedFilter
                                       )";



            try
            {
                var res = await conn.ExecuteAsync(insertquery, data);
                Console.WriteLine(res);
                await conn.CloseAsync();
                await MigrateDataFiltersWithStatusAsync(connectionString, data);
                var fields = await MigrateDataFieldsAsync(connectionString);
                await MigrateDataCustomFieldsAsync(connectionString, statuses, fields);
            }
            catch (Exception ex)
            {
            }
            return true;
        }

        public async Task<List<Field>> MigrateDataFieldsAsync(string connectionString)
        {
            var data = GetDataFieldsDto();
            var conn = new NpgsqlConnection(connectionString);
            await conn.OpenAsync();
            string insertquery = @"INSERT INTO ""LeadratBlack"".""Fields"" 
                                       (
                                           ""Id"", 
                                           ""Name"",
                                           ""OrderRank"",
                                           ""Module"",
                                           ""Notes"",
                                           ""TenantId"",
                                           ""IsDeleted"",
                                           ""CreatedBy"",
                                           ""CreatedOn"",
                                           ""LastModifiedBy"",
                                           ""LastModifiedOn"",
                                           ""DeletedOn"",
                                           ""DeletedBy""
                                       )
                                       VALUES 
                                       (
                                           @Id, 
                                           @Name,
                                           @OrderRank,
                                           @Module,
                                           @Notes,
                                           @TenantId,
                                           @IsDeleted,
                                           @CreatedBy,
                                           @CreatedOn,
                                           @LastModifiedBy,
                                           @LastModifiedOn,
                                           @DeletedOn,
                                           @DeletedBy
                                       )";
            try
            {
                var res = await conn.ExecuteAsync(insertquery, data);
                Console.WriteLine(res);
                await conn.CloseAsync();
            }
            catch (Exception ex)
            {

            }
            return data;
        }

        public List<Field> GetDataFieldsDto()
        {
            var field1 = GetDataField("ScheduledDate", 1);
            return new List<Field>() { field1 };
        }
        public Field GetDataField(string field, int orderRank)
        {
            return new Field()
            {
                Id = Guid.NewGuid(),
                Name = field,
                OrderRank = orderRank,
                LastModifiedOn = DateTime.UtcNow,
                CreatedOn = DateTime.UtcNow,
                CreatedBy = Guid.Empty,
                LastModifiedBy = Guid.Empty,
                TenantId = TenantId,
                IsDeleted = false,
                Notes = string.Empty,
                DeletedBy = null,
                DeletedOn = null,
                Module = "prospects",
            };
        }
        public async Task<bool> MigrateDataFiltersWithStatusAsync(string connectionString, List<CustomFilters> filters)
        {
            var data = GetCustomFilterCustomProspectStatus(filters);
            var conn = new NpgsqlConnection(connectionString);
            await conn.OpenAsync();
            string insertquery = @"INSERT INTO ""LeadratBlack"".""CustomFilterCustomProspectStatus"" 
                                       (
                                           ""CustomFiltersId"", 
                                           ""CustomProspectStatusesId""
                                       ) 
                                       VALUES 
                                       (
                                           @CustomFiltersId, 
                                           @StatusesId
                                       )";

            try
            {
                var res = await conn.ExecuteAsync(insertquery, data);
                Console.WriteLine(res);
                await conn.CloseAsync();
            }
            catch (Exception ex)
            {
            }
            return true;
        }
        public class CustomFilterCustomProspectStatus
        {
            public Guid CustomFiltersId { get; set; }
            public Guid StatusesId { get; set; }
        }
        public List<CustomFilterCustomProspectStatus> GetCustomFilterCustomProspectStatus(List<CustomFilters> filters)
        {
            var data = new List<CustomFilterCustomProspectStatus>();
            foreach (var filter in filters)
            {
                if (filter?.CustomProspectStatuses?.Any() ?? false)
                {
                    foreach (var item in filter.CustomProspectStatuses)
                    {
                        var cfcps = MapCustomFilterCustomProspectStatus(filter.Id, item.Id);
                        data.Add(cfcps);
                    }
                }
            }
            return data;
        }
        public CustomFilterCustomProspectStatus MapCustomFilterCustomProspectStatus(Guid customFiltersId, Guid statusesId)
        {
            return new CustomFilterCustomProspectStatus()
            {
                CustomFiltersId = customFiltersId,
                StatusesId = statusesId
            };
        }
        public async Task<bool> MigrateDataCustomFieldsAsync(string connectionString, List<CustomProspectStatusDto> statusDtos, List<Field> fields)
        {
            var data = GetDataCustomFieldsDto(statusDtos, fields);
            var conn = new NpgsqlConnection(connectionString);
            await conn.OpenAsync();
            string insertquery = @"INSERT INTO ""LeadratBlack"".""CustomFields"" 
                                       (
                                           ""Id"", 
                                           ""IsRequired"",
                                           ""Value"",
                                           ""FieldId"",
                                           ""ProspectStatusId"",
                                           ""TenantId"",
                                           ""IsDeleted"",
                                           ""CreatedBy"",
                                           ""CreatedOn"",
                                           ""LastModifiedBy"",
                                           ""LastModifiedOn"",
                                           ""DeletedOn"",
                                           ""DeletedBy"",
                                           ""Validators""
                                       ) 
                                       VALUES 
                                       (
                                           @Id, 
                                           @IsRequired,
                                           @Value,
                                           @FieldId,
                                           @ProspectStatusId,
                                           @TenantId,
                                           @IsDeleted,
                                           @CreatedBy,
                                           @CreatedOn,
                                           @LastModifiedBy,
                                           @LastModifiedOn,
                                           @DeletedOn,
                                           @DeletedBy,
                                           @Validators
                                       )";
            try
            {
                var res = await conn.ExecuteAsync(insertquery, data);
                Console.WriteLine(res);
                await conn.CloseAsync();
            }
            catch (Exception ex)
            {

            }
            return true;
        }
        public List<CustomField> GetDataCustomFieldsDto(List<CustomProspectStatusDto> statusDtos, List<Field> fields)
        {
            var data = new List<CustomField>();
            if (statusDtos?.Any() ?? false)
            {
                var baseStatuses = statusDtos.Where(i => i.BaseId == null || i.BaseId == Guid.Empty).ToList();
                foreach (var item in baseStatuses)
                {
                    if (item.Status == ProspectStatus.not_reachable || item.Status == ProspectStatus.follow_ups || item.Status == ProspectStatus.not_answered)
                    {
                        var field1 = fields.FirstOrDefault(i => i.Name == "ScheduledDate");
                        var result = GetDataCustomField(true, item.Id, field1?.Id ?? Guid.Empty);
                        data.Add(result);

                    }
                }
            }
            return data;
        }
        public CustomField GetDataCustomField(bool isRequired, Guid statusId, Guid fieldId)
        {
            return new CustomField()
            {
                Id = Guid.NewGuid(),
                IsRequired = isRequired,
                Value = string.Empty,
                ProspectStatusId = statusId,
                FieldId = fieldId,
                LastModifiedOn = DateTime.UtcNow,
                CreatedOn = DateTime.UtcNow,
                CreatedBy = Guid.Empty,
                LastModifiedBy = Guid.Empty,
                TenantId = TenantId,
                IsDeleted = false,
                DeletedBy = null,
                DeletedOn = null,
                Validators = isRequired ? new List<string>() { "required" } : null,
            };
        }
        public List<CustomFilters> GetProspectCustomFilters(List<CustomProspectStatusDto> statuses)
        {
            #region Web
            var notInterestedStatus = statuses.FirstOrDefault(i => ProspectStatus.not_interested.Contains(i?.Status ?? string.Empty) && (i.BaseId == null || i.BaseId == Guid.Empty));
            var newStatus = statuses.FirstOrDefault(i => ProspectStatus.New.Contains(i?.Status ?? string.Empty) && (i.BaseId == null || i.BaseId == Guid.Empty));
            var qualifiedStatus = statuses.FirstOrDefault(i => ProspectStatus.qualified.Contains(i?.Status ?? string.Empty) && (i.BaseId == null || i.BaseId == Guid.Empty));
            var follow_upsStatus = statuses.FirstOrDefault(i => ProspectStatus.follow_ups.Contains(i?.Status ?? string.Empty) && (i.BaseId == null || i.BaseId == Guid.Empty));
            var not_connectedStatus = statuses.FirstOrDefault(i => ProspectStatus.not_connected.Contains(i?.Status ?? string.Empty) && (i.BaseId == null || i.BaseId == Guid.Empty));
            var not_answeredStatus = statuses.FirstOrDefault(i => ProspectStatus.not_answered.Contains(i?.Status ?? string.Empty) && (i.BaseId == null || i.BaseId == Guid.Empty));
            var not_reachableStatus = statuses.FirstOrDefault(i => ProspectStatus.not_reachable.Contains(i?.Status ?? string.Empty) && (i.BaseId == null || i.BaseId == Guid.Empty));
            var invalidStatus = statuses.FirstOrDefault(i => ProspectStatus.invalid.Contains(i?.Status ?? string.Empty) && (i.BaseId == null || i.BaseId == Guid.Empty));

            var activeDataIds = new List<Guid>();
            activeDataIds.Add(follow_upsStatus?.Id ?? Guid.Empty);
            activeDataIds.Add(newStatus?.Id ?? Guid.Empty);
            activeDataIds.Add(not_reachableStatus?.Id ?? Guid.Empty);
            activeDataIds.Add(not_answeredStatus?.Id ?? Guid.Empty);


            var data = new List<CustomFilters>();
            var allData = MapProspectData("All Data", null, 1, 1, null, null);
            var activeData = MapProspectData("Active Data", null, 1, 2, null, null, statuses.Where(i => activeDataIds.Contains(i.Id)).ToList(), null, true);
            var qualified = MapProspectData("Qualified", null, 1, 3, null, null, statuses.Where(i => (qualifiedStatus?.Id ?? Guid.Empty) == i.Id).ToList());
            var inactiveData = MapProspectData("Inactive Data", null, 1, 4, null, null, statuses.Where(i => (notInterestedStatus?.Id ?? Guid.Empty) == i.Id || (invalidStatus?.Id ?? Guid.Empty) == i.Id).ToList());

            data.Add(allData);
            data.Add(activeData);
            data.Add(qualified);
            data.Add(inactiveData);


            #region ActiveData
            var follow_ups = MapProspectData("Follow Ups", activeData.Id, 2, 1, true, 0, statuses.Where(i => (follow_upsStatus?.Id ?? Guid.Empty) == i.Id).ToList());
            var New = MapProspectData("New", activeData.Id, 2, 3, null, null, statuses.Where(i => (newStatus?.Id ?? Guid.Empty) == i.Id).ToList());
            var notReachable = MapProspectData("Not Reachable", activeData.Id, 2, 4, true, 0, statuses.Where(i => (not_reachableStatus?.Id ?? Guid.Empty) == i.Id).ToList());
            var notAnswered = MapProspectData("Not Answered", activeData.Id, 2, 5, true, 0, statuses.Where(i => (not_answeredStatus?.Id ?? Guid.Empty) == i.Id).ToList());

            data.Add(follow_ups);
            data.Add(New);
            data.Add(notReachable);
            data.Add(notAnswered);

            #region Backlog
            var backlog = MapProspectData("Backlog", activeData.Id, 2, 2, false, 0, statuses.Where(i => (not_reachableStatus?.Id ?? Guid.Empty) == i.Id || (not_answeredStatus?.Id ?? Guid.Empty) == i.Id || (follow_upsStatus?.Id ?? Guid.Empty) == i.Id).ToList());
            data.Add(backlog);
            #endregion

            #endregion

            #region InactiveData

            var notInterested = MapProspectData("Not Interested", inactiveData.Id, 2, 1, null, null, statuses.Where(i => (notInterestedStatus?.Id ?? Guid.Empty) == i.Id).ToList());
            var invalid = MapProspectData("Invalid", inactiveData.Id, 2, 2, null, null, statuses.Where(i => (invalidStatus?.Id ?? Guid.Empty) == i.Id).ToList());

            data.Add(notInterested);
            data.Add(invalid);
            #endregion

            #endregion

            #region Mobile

            var allMob = MapProspectData("All", null, 1, 1, null, null, isDefault: true, isMobileFilter: true);
            var NewMob = MapProspectData("New", null, 1, 2, null, null, statuses.Where(i => (newStatus?.Id ?? Guid.Empty) == i.Id).ToList(), null, isDefault: true, true, false);
            var qualifiedMob = MapProspectData("Qualified", null, 1, 3, null, null, statuses.Where(i => (qualifiedStatus?.Id ?? Guid.Empty) == i.Id).ToList(), null, isDefault: true, true, false);
            var backlogMob = MapProspectData("Backlog", null, 1, 4, false, 0, statuses.Where(i => (not_reachableStatus?.Id ?? Guid.Empty) == i.Id || (not_answeredStatus?.Id ?? Guid.Empty) == i.Id || (follow_upsStatus?.Id ?? Guid.Empty) == i.Id).ToList(), null, isDefault: true, true, false);
            var follow_upsMob = MapProspectData("Follow Ups", null, 1, 5, true, 0, statuses.Where(i => (follow_upsStatus?.Id ?? Guid.Empty) == i.Id).ToList(), null, isDefault: true, true, false);
            var notAnsweredMob = MapProspectData("Not Answered", null, 1, 6, true, 0, statuses.Where(i => (not_answeredStatus?.Id ?? Guid.Empty) == i.Id).ToList(), null, isDefault: true, true, false);
            var notInterestedMob = MapProspectData("Not Interested", null, 1, 7, null, null, statuses.Where(i => (notInterestedStatus?.Id ?? Guid.Empty) == i.Id).ToList(), null, false, true, false);
            var notReachableMob = MapProspectData("Not Reachable", null, 1, 8, true, 0, statuses.Where(i => (not_reachableStatus?.Id ?? Guid.Empty) == i.Id).ToList(), null, false, true, false);
            var invalidMob = MapProspectData("Invalid", null, 1, 9, null, null, statuses.Where(i => (invalidStatus?.Id ?? Guid.Empty) == i.Id).ToList(), null, false, true, false);
            var convertedMob = MapProspectData("Converted", null, 1, 10, null, null, null, isDefault: true, isMobileFilter: true, isConvertedFilter: true);

            data.Add(allMob);
            data.Add(NewMob);
            data.Add(qualifiedMob);
            data.Add(backlogMob);
            data.Add(follow_upsMob);
            data.Add(notAnsweredMob);
            data.Add(notInterestedMob);
            data.Add(notReachableMob);
            data.Add(invalidMob);
            data.Add(convertedMob);

            #endregion

            return data;
        }
        public static class ProspectStatus
        {
            public static string not_interested = "not_interested";
            public static string New = "new";
            public static string qualified = "qualified";
            public static string follow_ups = "follow_ups";
            public static string not_connected = "not_connected";
            public static string not_answered = "not_answered";
            public static string not_reachable = "not_reachable";
            public static string invalid = "invalid/wrong_number";
        }
        public CustomFilters MapProspectData(string name, Guid? baseId, int level, int orderRank, bool? isForward, long? fromNoOfDays, List<CustomProspectStatusDto>? statusDtos = default, long? toNoOfDays = default, bool isDefault = false, bool isMobileFilter = false, bool? isConvertedFilter = false)
        {
            return new CustomFilters()
            {
                Id = Guid.NewGuid(),
                Name = name,
                BaseId = baseId,
                Level = level,
                OrderRank = orderRank,
                IsForward = isForward,
                FromNoOfDays = fromNoOfDays,
                ToNoOfDays = toNoOfDays,
                LastModifiedOn = DateTime.UtcNow,
                CreatedOn = DateTime.UtcNow,
                CreatedBy = Guid.Empty,
                LastModifiedBy = Guid.Empty,
                TenantId = TenantId,
                CustomProspectStatuses = statusDtos,
                IsDeleted = false,
                UserId = Guid.Empty,
                IsDefault = isDefault,
                Module = "prospects",
                IsMobileFilter = isMobileFilter,
                IsConvertedFilter = isConvertedFilter
            };
        }

        #endregion
    }
}
