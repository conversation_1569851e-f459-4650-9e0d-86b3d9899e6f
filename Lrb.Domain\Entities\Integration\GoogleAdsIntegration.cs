﻿using Lrb.Domain.Entities.MasterData;
using Newtonsoft.Json;

namespace Lrb.Domain.Entities.Integration
{
    public class GoogleAdsAuthResponse : AuditableEntity, IAggregateRoot
    {
        public string AccessToken { get; set; } = default!;
        public string RefreshToken { get; set; } = default!;
        public string AccountName { get; set; } = default!;
        public string LongLivedUserAccessToken { get; set; } = default!;
        public string? CustomerId { get; set; }
        public IList<GoogleAdsAccount>? GoogleAdsAccounts { get; set; }
        public bool IsActive { get; set; }
    }

    public class GoogleAdsAccount : AuditableEntity, IAggregateRoot
    {
        public string AccountId { get; set; } = default!;
        public string AccountType { get; set; } = default!;

        [JsonProperty("access_token")]
        public string AccessToken { get; set; } = default!;

        [JsonProperty("refresh_token")]
        public string RefreshToken { get; set; } = default!;

        // Foreign key references
        public Guid GoogleAuthResponseId { get; set; }

        [System.Text.Json.Serialization.JsonIgnore]
        [Newtonsoft.Json.JsonIgnore]
        public GoogleAdsAuthResponse GoogleAuthResponse { get; set; } = default!;

        public IList<GoogleAdsInfo>? Campaigns { get; set; }
        public UserAssignment? UserAssignment { get; set; }
    }
    public class GoogleCampaign : AuditableEntity, IAggregateRoot
    {
        public string CampaignId { get; set; } = default!;
        public string CampaignName { get; set; } = default!;
        public bool IsActive { get; set; }
        public Guid GoogleAuthResponseId { get; set; }

    }
    public class GoogleAdsInfo : AuditableEntity, IAggregateRoot
    {
        public string? AdId { get; set; }
        public string? AdName { get; set; }
        public string? Status { get; set; }
        public string? CampaignName { get; set; }
        public string? CampaignId { get; set; }
        public string? AdSetName { get; set; }
        public string? AdSetId { get; set; }
        public string? AdAccountName { get; set; }
        public string? AdAccountId { get; set; }
        public string? CustomerId { get; set; } // Google Ads account ID
        public Guid GoogleAuthResponseId { get; set; }
        public bool IsSubscribed { get; set; }
        public int LeadsCount { get; set; }
        public string? CountryCode { get; set; }
        public string? CurrencyCode { get; set; }
    }

}
