﻿using Finbuckle.MultiTenant.EntityFrameworkCore;
using Lrb.Domain.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Lrb.Infrastructure.Persistence.Configuration.Application
{
    public class LeadConfig : IEntityTypeConfiguration<Lrb.Domain.Entities.Lead>
    {
        public void Configure(EntityTypeBuilder<Lrb.Domain.Entities.Lead> builder)
        {
            builder.IsMultiTenant();
            builder.Property(i => i.Name)
                .IsRequired();
            builder.Property(i => i.ContactNo)
                .IsRequired()
                .IsFixedLength(true);
            builder.HasOne(i => i.TagInfo)
                .WithOne(i => i.Lead)
                .HasForeignKey<LeadTag>(i => i.LeadId);
            builder.HasMany(i => i.Projects)
                .WithMany(i => i.Leads);
            builder.HasMany(i => i.Properties)
                .WithMany(i => i.Leads);
            builder.HasMany(i => i.Appointments);
            builder.Property(i => i.CallRecordingUrls).Metadata.SetProviderClrType(null);
            builder.Property(i => i.Documents).Metadata.SetProviderClrType(null);
            builder.Property(i => i.ContactRecords).Metadata.SetProviderClrType(null);
            builder.HasOne(i => i.Address);
            builder.HasMany(i => i.ChannelPartners)
                .WithMany(i => i.Leads);
            builder.HasMany(l => l.BookedDetails);
            builder.HasMany(i => i.CustomFlags)
                .WithOne(i => i.Lead);
            builder.HasMany(i => i.TempProjects)
                .WithMany(i => i.Leads);
            builder.HasMany(i => i.Assignments);
            builder.Property(i => i.AdditionalProperties).Metadata.SetProviderClrType(null);
            builder.Property(i => i.GoogleAdsProperties).Metadata.SetProviderClrType(null);
        }

    }
}
