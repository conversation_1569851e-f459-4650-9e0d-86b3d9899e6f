﻿using Finbuckle.MultiTenant.EntityFrameworkCore;
using Lrb.Domain.Entities.Integration;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Lrb.Infrastructure.Persistence.Configuration.Application.Integration
{
    public class GoogleAdsBulkLeadFetchTrackerConfig : IEntityTypeConfiguration<GoogleAdsBulkLeadFetchTracker>
    {
        public void Configure(EntityTypeBuilder<GoogleAdsBulkLeadFetchTracker> builder)
        {
            builder.IsMultiTenant();
        }
    }
}
