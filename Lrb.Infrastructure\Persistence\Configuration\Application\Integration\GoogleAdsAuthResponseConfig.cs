﻿using Lrb.Domain.Entities;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Lrb.Domain.Entities.Integration;
using Finbuckle.MultiTenant.EntityFrameworkCore;

namespace Lrb.Infrastructure.Persistence.Configuration.Application.Integration
{
    public class GoogleAdsAuthResponseConfig : IEntityTypeConfiguration<GoogleAdsAuthResponse>
    {
        public void Configure(EntityTypeBuilder<GoogleAdsAuthResponse> builder)
        {
            builder.IsMultiTenant();
            builder.HasMany(a => a.GoogleAdsAccounts)
                .WithOne(p => p.GoogleAuthResponse)
                .HasForeignKey(p => p.GoogleAuthResponseId)
                .OnDelete(DeleteBehavior.Cascade);
        }
    }
    public class GoogleAdsAccountConfig : IEntityTypeConfiguration<GoogleAdsAccount>
    {
        public void Configure(EntityTypeBuilder<GoogleAdsAccount> builder)
        {
            builder.IsMultiTenant();
        }
    }
    public class GoogleAdsInfoConfig : IEntityTypeConfiguration<GoogleAdsInfo>
    {
        public void Configure(EntityTypeBuilder<GoogleAdsInfo> builder)
        {
            builder.IsMultiTenant();
        }
    }

    public class GoogleCampaignConfig : IEntityTypeConfiguration<GoogleCampaign>
    {
        public void Configure(EntityTypeBuilder<GoogleCampaign> builder)
        {
            builder.IsMultiTenant();
        }
    }
}
