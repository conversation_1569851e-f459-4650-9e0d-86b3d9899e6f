﻿using Lrb.Application.Common.Persistence.New_Implementation;
using Lrb.Application.Lead.Mobile.Mappings.v2;
using Lrb.Application.Property.Mobile.Dtos;
using Lrb.Application.Utils;
using Lrb.Domain.Entities.ErrorModule;
using Lrb.Domain.Entities.MasterData;
using Newtonsoft.Json;

namespace Lrb.Application.Lead.Mobile.v2
{
    public class V2GetAllLeadsRequest : PaginationFilter, IRequest<Response<GetAllLeadsWrapperDto>>
    {
        public List<LeadSource>? Source { get; set; } = new();
        public List<EnquiryType>? EnquiredFor { get; set; }
        public Guid? AssignTo { get; set; }
        public DateType? DateType { get; set; }
        public DateTime? FromDate { get; set; }
        public DateTime? ToDate { get; set; }
        public string? SearchByNameOrNumber { get; set; }
        public long? MinBudget { get; set; }
        public long? MaxBudget { get; set; }
        public List<LeadFilterTypeMobile>? FilterTypes { get; set; }
        public BaseLeadVisibility LeadVisibility { get; set; }
        public List<Guid>? AssignToIds { get; set; }
        public List<Budget>? Budget { get; set; }
        public List<string>? Projects { get; set; }
        public List<string>? Properties { get; set; }
        public List<double>? NoOfBHKs { get; set; }
        public List<BHKType>? BHKTypes { get; set; }
        public List<Guid>? PropertyType { get; set; }
        public List<Guid>? PropertySubType { get; set; }
        public List<Guid>? StatusIds { get; set; }
        public List<Dtos.BudgetFilter>? BudgetFilters { get; set; }
        public List<string>? Locations { get; set; }
        public List<MeetingOrVisitCompletionStatus>? MeetingOrVisitStatuses { get; set; }
        public DateTime? ToDateForMeetingOrVisit { get; set; }
        public DateTime? FromDateForMeetingOrVisit { get; set; }
        public List<Guid>? AppointmentDoneByUserIds { get; set; }
        public bool? IsWithTeam { get; set; }
        public List<string>? SubSources { get; set; }
        public List<Guid>? IntegrationAccountIds { get; set; }
        public List<string>? AgencyNames { get; set; }
        public List<Guid>? LeadIds { get; set; }
        public List<string>? SerialNumbers { get; set; }
        public string? ReferralName { get; set; }
        public string? ReferralContactNo { get; set; }
        public string? ReferralEmail { get; set; }

        public double? CarpetArea { get; set; }
        public double? MinCarpetArea { get; set; }
        public double? MaxCarpetArea { get; set; }
        public Guid CarpetAreaUnitId { get; set; }
        public float? ConversionFactor { get; set; }
        public List<string>? Designations { get; set; }
        public string? Designation { get; set; }
        public bool? IsPicked { get; set; }
        public List<Guid>? SecondaryUsers { get; set; }
        public bool? IsDualOwnershipEnabled { get; set; }
        public List<Guid>? BookedByIds { get; set; }
        public List<Date>? Dates { get; set; }
        public string? DatesJsonFormattedString { get; set; }
        public DateTime? BookedDate { get; set; }
        public Guid? LeadBrokerageInfoId { get; set; }
        public List<Guid>? SecondaryIds { get; set; }
        public Guid? TeamHead { get; set; }
        public int? UpperAgreementLimit { get; set; }
        public int? LowerAgreementLimit { get; set; }
        public TokenType? PaymentMode { get; set; }
        public int? UpperDiscountLimit { get; set; }
        public int? LowerDiscountLimit { get; set; }
        public DiscountType? DiscountMode { get; set; }
        public bool? ShouldShowBookedDetails { get; set; }
        public bool? ShouldShowBrokerageInfo { get; set; }
        public List<string>? CustomFlags { get; set; }
        public string? Currency { get; set; }
        public string? BookedUnderName { get; set; }
        public double? TotalBrokerage { get; set; }
        public double? SoldPrice { get; set; }
        public double? BrokerageCharges { get; set; }
        public double? NetBrokerageAmount { get; set; }
        public double? GST { get; set; }
        public string? ReferralNumber { get; set; }
        public Guid? ReferredBy { get; set; }
        public double? Commission { get; set; }
        public string? CommissionUnit { get; set; }
        public double? EarnedBrokerage { get; set; }
        public BrokerageType? BrokerageType { get; set; }
        public string? GSTUnit { get; set; }
        public string? BrokerageUnit { get; set; }
        public string? DiscountUnit { get; set; }
        public double? UpperRemainingAmountLimit { get; set; }
        public double? LowerRemainingAmountLimit { get; set; }
        public PaymentType? PaymentType { get; set; }
        public double? CarParkingCharges { get; set; }
        public double? RemainingAmount { get; set; }
        public double? AdditionalCharges { get; set; }
        public DateTime? CallLogFromDate { get; set; }
        public DateTime? CallLogToDate { get; set; }
        public CallStatus? CallStatus { get; set; }
        public CallDirection? CallDirection { get; set; }
        public bool? IsUntouched { get; set; }
        public string? Longitude { get; set; }
        public string? Latitude { get; set; }
        public double? RadiusInKm { get; set; }
        public bool? CanAccessAllLeads { get; set; }
        public List<int>? Beds { get; set; }
        public List<int>? Baths { get; set; }
        public List<string>? Floors { get; set; }
        public List<OfferType>? OfferTypes { get; set; }
        public List<FurnishStatus>? Furnished { get; set; }
        public List<string>? Communities { get; set; }
        public List<string>? SubCommunities { get; set; }
        public List<string>? TowerNames { get; set; }
        public List<string>? Countries { get; set; }
        public List<string>? PostalCodes { get; set; }
        public List<string>? Cities { get; set; }
        public List<string>? States { get; set; }
        public double? BuiltUpArea { get; set; }
        public double? MinBuiltUpArea { get; set; }
        public double? MaxBuiltUpArea { get; set; }
        public Guid BuiltUpAreaUnitId { get; set; } = Guid.Empty;
        public double? SaleableArea { get; set; }
        public double? MinSaleableArea { get; set; }
        public double? MaxSaleableArea { get; set; }
        public Guid SaleableAreaUnitId { get; set; } = Guid.Empty;
        public string? ConfidentialNotes { get; set; }
        public double? NetArea { get; set; }
        public double? MinNetArea { get; set; }
        public double? MaxNetArea { get; set; }
        public Guid NetAreaUnitId { get; set; } = Guid.Empty;
        public double? PropertyArea { get; set; }
        public double? MinPropertyArea { get; set; }
        public double? MaxPropertyArea { get; set; }
        public Guid PropertyAreaUnitId { get; set; } = Guid.Empty;
        public string? UnitName { get; set; }
        public List<string>? ClusterName { get; set; }
        public List<string>? Nationality { get; set; }
        public List<string>? UnitNames { get; set; }
        public List<string>? CampaignNames { get; set; }
        public List<Purpose>? Purposes { get; set; }
        public int? ChildLeadsCount { get; set; }
        public List<Guid>? OriginalOwnerIds { get; set; }
        public List<string>? CountryCode { get; set; }
        public List<string>? AltCountryCode { get; set; }

        public PossesionType? PossesionType { get; set; }
        public DateTime? FromPossesionDate { get; set; }
        public DateTime? ToPossesionDate { get; set; }
        public List<string>? LandLine { get; set; }


        public List<string>? PropertyToSearch { get; set; }
        public bool? ShowOnlyParentLeads { get; set; }
        public List<LeadType>? LeadType { get; set; }
        public OwnerSelectionType? OwnerSelection { get; set; }

        public List<Gender>? GenderTypes { get; set; }
        public List<MaritalStatusType>? MaritalStatuses { get; set; }
        public DateTime? DateOfBirth { get; set; }
        public List<Guid>? UserIds { get; set; }
        public List<CallStatus>? CallStatuses { get; set; }
        public List<CallDirection>? CallDirections { get; set; }
        public string? TimeZoneId { get; set; }
        public TimeSpan? BaseUTcOffset { get; set; }
    }
    public class V2GetAllLeadsRequestHandler : IRequestHandler<V2GetAllLeadsRequest, Response<GetAllLeadsWrapperDto>>
    {
        private readonly IRepositoryWithEvents<Domain.Entities.Lead> _leadRepo;
        private readonly ICurrentUser _currentUser;
        private readonly ILeadRepository _efLeadRepository;
        private readonly IDapperRepository _dapperRepository;
        private readonly ILeadRepositoryAsync _leadRepositoryAsync;
        private readonly IRepositoryWithEvents<CustomMasterLeadStatus> _customMasterLeadStatus;

        public V2GetAllLeadsRequestHandler(
            IRepositoryWithEvents<Domain.Entities.Lead> leadRepo,
            ICurrentUser currentUser,
            ILeadRepository efLeadRepository,
            IDapperRepository dapperRepository,
            ILeadRepositoryAsync leadRepositoryAsync,
            IRepositoryWithEvents<CustomMasterLeadStatus> customMasterLeadStatus)
        {
            _leadRepo = leadRepo;
            _currentUser = currentUser;
            _efLeadRepository = efLeadRepository;
            _dapperRepository = dapperRepository;
            _leadRepositoryAsync = leadRepositoryAsync;
            _customMasterLeadStatus = customMasterLeadStatus;
        }
        #region implemention using EF Core Repo
        public async Task<Response<GetAllLeadsWrapperDto>> Handle(V2GetAllLeadsRequest request, CancellationToken cancellationToken)
        {
            System.Diagnostics.Stopwatch stopwatch = new System.Diagnostics.Stopwatch();
            stopwatch.Start();
            var userId = _currentUser.GetUserId();
            var tenantId = _currentUser.GetTenant();
            GetAllLeadsWrapperDto getAllLeadsWrapperDto = new() { Leads = new() };
            List<Guid> leadHistoryIds = new();
            List<Guid> subIds = new();
            var isAdmin = (await _dapperRepository.IsAdminAsync(userId, tenantId ?? string.Empty));
            try
            {
                if (request?.AssignToIds?.Any() ?? false)
                {
                    if (request?.IsWithTeam ?? false)
                    {
                        //subIds = (await _dapperRepository.GetSubordinateIdsWithOnlyReporteesPOCAsync(request.AssignToIds, tenantId ?? string.Empty)).ToList();
                        subIds = (await _dapperRepository.GetSubordinateIdsWithOnlyReporteesAsync(request.AssignToIds, tenantId ?? string.Empty)).ToList();
                    }
                    else
                    {
                        subIds = request?.AssignToIds ?? new List<Guid>();
                    }
                }
                else
                {
                    //subIds = (await _dapperRepository.GetSubordinateIdsPOCAsync(userId, tenantId ?? string.Empty))?.ToList() ?? new();
                    subIds = (await _dapperRepository.GetSubordinateIdsAsync(userId, tenantId ?? string.Empty, request?.CanAccessAllLeads, isAdmin))?.ToList() ?? new();
                }
            }
            catch (Exception ex) 
            {
                var error = new LrbError()
                {
                    ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                    ErrorSource = ex?.Source,
                    StackTrace = ex?.StackTrace,
                    InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                    ErrorModule = "V2GetAllLeadsRequestHandler -> Handle()"
                };
                await _leadRepositoryAsync.AddErrorAsync(error);
                throw;
            }

            List<Guid> leadHistoryIdsForScheduledMeeting = new();
            List<Guid> leadHistoryIdsForScheduledSiteVisit = new();

            if (request?.FilterTypes?.Any(i => i == LeadFilterTypeMobile.ScheduledMeeting || i == LeadFilterTypeMobile.SiteVisitScheduled) ?? false)
            {
                var leadHistoryTasks = new List<Task<IEnumerable<Guid>>>
                {
                    _dapperRepository.GetLeadHistoryIdsByBaseLeadStatusPOC(tenantId ?? string.Empty, "Meeting Scheduled"),
                    _dapperRepository.GetLeadHistoryIdsByBaseLeadStatusPOC(tenantId ?? string.Empty, "Site Visit Scheduled")
                };

                var leadHistoryResults = await Task.WhenAll(leadHistoryTasks);
                leadHistoryIdsForScheduledMeeting = leadHistoryResults[0].ToList();
                leadHistoryIdsForScheduledSiteVisit = leadHistoryResults[1].ToList();
            }
            var customMasterStatus = await _customMasterLeadStatus.ListAsync();
            var filterTypes = request.FilterTypes?.Any() ?? false
                ? request.FilterTypes
                : Enum.GetValues(typeof(LeadFilterTypeMobile)).Cast<LeadFilterTypeMobile>();

            if(request.IsDualOwnershipEnabled == null)
            {
                request.IsDualOwnershipEnabled = await _dapperRepository.GetDualOwnershipDetails(tenantId ?? string.Empty);
            }
            if (request.RadiusInKm != null)
            {
                List<LeadIdWithLatLongDto> leadIdsWithLatLong = (await _dapperRepository.QueryStoredProcedureFromReadReplicaAsync<LeadIdWithLatLongDto>("LeadratBlack", "get_leads_with_lat_long", new
                {
                    user_ids = subIds,
                    tenant_id = tenantId
                })).ToList();
                List<Guid> leadIdsWithinRange = new();

                if (!string.IsNullOrEmpty(request.Latitude) && !string.IsNullOrEmpty(request.Longitude))
                {
                    leadIdsWithinRange = leadIdsWithLatLong.Where(i =>
                                         (i != null && i.Latitude != null && i.Longitude != null) &&
                                         (request.Latitude != null && request.Longitude != null) &&
                    GeoLocationHelper.IsLocationWithinRange(double.TryParse(request.Latitude, out var requestLat) ? requestLat : 0.0,
                                                                                double.TryParse(request.Longitude, out var requestLong) ? requestLong : 0.0,
                                                                                 double.TryParse(i.Latitude, out var leadLat) ? leadLat : 0.0,
                                                                                 double.TryParse(i.Longitude, out var leadLong) ? leadLong : 0.0, request.RadiusInKm * 1000 ?? 0.0)).Select(i => i.Id).ToList();

                    request.LeadIds = leadIdsWithinRange;
                }
            }
            var leadTasks = filterTypes.Select(async filterType =>
            {
                var leads = (await _efLeadRepository.GetAllCategoryLeadsForMobileAsync(request.Adapt<V2GetAllLeadsRequest>(), userId, subIds, filterType, leadHistoryIds, leadHistoryIdsForScheduledMeeting, leadHistoryIdsForScheduledSiteVisit, customMasterStatus)).ToList();
                var count = await _efLeadRepository.GetAllCategoryLeadsCountForMobileAsync(request.Adapt<V2GetAllLeadsRequest>(), userId, subIds, filterType, leadHistoryIds, leadHistoryIdsForScheduledMeeting, leadHistoryIdsForScheduledSiteVisit, customMasterStatus);
                (List<AppointmentType> appTypes, List<bool> appDoneStatuses) = _efLeadRepository.GetAppointmentTypes(request);
                if (request.AppointmentDoneByUserIds?.Any() ?? false)
                {
                    leads.ForEach(lead =>
                    {
                        if (lead?.Appointments?.Any() ?? false)
                        {
                            var uniqueAppointments = lead.Appointments.Where(i => i.UniqueKey != null && i.UniqueKey != Guid.Empty).DistinctBy(i => i.UniqueKey).ToList();
                            uniqueAppointments.AddRange(lead.Appointments.Where(i => i.UniqueKey == null || i.UniqueKey == Guid.Empty).ToList());
                            lead.Appointments = uniqueAppointments.Where(i => request.AppointmentDoneByUserIds.Contains(i.CreatedBy) && appTypes.Contains(i.Type) && appDoneStatuses.Contains(i.IsDone)).ToList();
                        }
                    });
                }
                else if (subIds != null && !(isAdmin))
                {
                    leads.ForEach(lead =>
                    {
                        if ((lead?.Appointments?.Any() ?? false) && lead.Appointments.Any(i => subIds.Contains(i.UserId)))
                        {
                            var appointmentsWithoutUniqueKey = lead.Appointments?.Where(i => i.UniqueKey == null || i.UniqueKey == default).ToList() ?? new();

                            var appointmentsWithUniqueKey = lead.Appointments?.Where(i => i.UniqueKey != null && i.UniqueKey != default && subIds.Contains(i.UserId))?.DistinctBy(i => i.UniqueKey)?.OrderBy(i => i.LastModifiedOn)?.ToList() ?? new();

                            appointmentsWithoutUniqueKey.AddRange(appointmentsWithUniqueKey);
                            lead.Appointments = appointmentsWithoutUniqueKey;
                        }
                        else
                        {
                            lead.Appointments = null;
                        }
                    });
                }
                else
                {
                    leads.ForEach(lead =>
                    {
                        if (lead?.Appointments?.Any() ?? false)
                        {
                            var appointmentsWithoutUniqueKey = lead.Appointments?.Where(i => i.UniqueKey == null && i.UniqueKey == default)?.ToList() ?? new();
                            var appointmentsWithUniqueKey = lead.Appointments?.Where(i => i.UniqueKey != null && i.UniqueKey != default)?.DistinctBy(i => i.UniqueKey).ToList() ?? new();
                            
                            appointmentsWithoutUniqueKey.AddRange(appointmentsWithUniqueKey);
                            lead.Appointments = appointmentsWithoutUniqueKey;
                        }
                    });
                }
                var leadDtos = leads.Adapt<List<V2GetAllLeadDto>>();

                return new LeadCategoryDto
                {
                    LeadFilter = filterType,
                    Leads = leadDtos,
                    TotalCount = count
                };
            });

            var leadCategoryDtos = await Task.WhenAll(leadTasks);

            foreach (var leadCategoryDto in leadCategoryDtos)
            {
                if (leadCategoryDto.Leads.Any())
                {
                    getAllLeadsWrapperDto.Leads.TryAdd(leadCategoryDto.LeadFilter, leadCategoryDto);
                }
            }
            getAllLeadsWrapperDto.TotalLeadsCount = await _efLeadRepository.GetAllCategoryLeadsCountForMobileAsync(request.Adapt<V2GetAllLeadsRequest>(), userId, subIds, LeadFilterTypeMobile.All, leadHistoryIds, leadHistoryIdsForScheduledMeeting, leadHistoryIdsForScheduledSiteVisit);
            getAllLeadsWrapperDto.ShowAllLeadsCount = await _leadRepo.CountAsync(new GetAllLeadsCountSpec(), cancellationToken);

            stopwatch.Stop();
            Console.WriteLine($"Query execution and data retrieval took: {stopwatch.ElapsedMilliseconds} ms User: {userId.ToString() ?? string.Empty} Tenant: {tenantId ?? string.Empty}");
            return new(getAllLeadsWrapperDto);
        }
        #endregion
    }
    public class Date
    {
        public DateType? MultiDateType { get; set; }
        public DateTime? MultiFromDate { get; set; }
        public DateTime? MultiToDate { get; set; }
    }
}
