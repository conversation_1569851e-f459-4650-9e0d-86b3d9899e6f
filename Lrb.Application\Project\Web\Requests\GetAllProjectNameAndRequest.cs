﻿using Lrb.Application.Project.Web.Dtos;
using Lrb.Application.Project.Web.Specs;

namespace Lrb.Application.Project.Web.Requests
{
    public class GetAllProjectNameAndRequest : IRequest<Response<List<ProjectInfoDto>>>
    {
        public string? TenantId { set; get; }
    }

    public class GetAllProjectNameAndRequestHandler : IRequestHandler<GetAllProjectNameAndRequest, Response<List<ProjectInfoDto>>>
    {
        private readonly IDapperRepository _dapperRepository;
        public GetAllProjectNameAndRequestHandler(IDapperRepository dapperRepository)
        {
           _dapperRepository = dapperRepository;
        }
        public async Task<Response<List<ProjectInfoDto>>> Handle(GetAllProjectNameAndRequest request, CancellationToken cancellationToken)
        {
            try
            {
                return new((await _dapperRepository.GetProjectsWithIdsAndNames<ProjectInfoDto>(request.TenantId ?? string.Empty)).ToList());
            }
            catch (Exception ex)
            {
                return new() { Message = ex.Message };
            }
        }
    }
}
