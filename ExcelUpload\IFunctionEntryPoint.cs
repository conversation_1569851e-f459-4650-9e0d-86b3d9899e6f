﻿using Lrb.Application.GlobalSettings.Web.Dto;
using Lrb.Domain.Entities;

namespace ExcelUpload
{
    public interface IFunctionEntryPoint
    {
        Task ImportLeadHandler(InputPayload input);
        Task ImportLeadHandlerV2(InputPayload input);
        Task MigrateLeadHandler(InputPayload input);
        Task MigrateLeadHandlerV2(InputPayload input);
        Task MigrateDataHandler(InputPayload input);
        Task MigrateDataHandlerV2(InputPayload input);
        Task PropertyHandler(InputPayload input);
        Task ExportLeadsHandler(InputPayload input);
        Task ExportLeadsByNewFiltersHandler(InputPayload input);
        #region reports 
        Task ExportLeadMeetingAndVisitReportByUserHandler(InputPayload input, ReportsConfigurationDto? reportConfiguration = null);
        Task ExportLeadStatusReportByProjectHandler(InputPayload input, ReportsConfigurationDto? reportConfiguration = null);
        Task ExportLeadStatusReportBySourceHandler(InputPayload input, ReportsConfigurationDto? reportConfiguration = null);
        Task ExportLeadStatusReportByUserHandler(InputPayload input, ReportsConfigurationDto? reportConfiguration = null);
        Task ExportLeadStatusReportBySubSourceHandler(InputPayload input, ReportsConfigurationDto? reportConfiguration = null);
        Task ExportLeadStatusReportByAgencyHandler(InputPayload input, ReportsConfigurationDto? reportConfiguration = null);
        Task ExportLeadSubStatusReportBySubSourceHandler(InputPayload input, ReportsConfigurationDto? reportConfiguration = null);
        Task ExportLeadProjectReportBySubStatusHandler(InputPayload input, ReportsConfigurationDto? reportConfiguration = null);
        Task ExportCallLogReportsHandler(InputPayload input, ReportsConfigurationDto? reportConfiguration = null);
        Task ExportUserVsSourceReportHandler(InputPayload input, ReportsConfigurationDto? reportConfiguration = null);
        Task ExportUserVsSubSourceReportHandler(InputPayload input, ReportsConfigurationDto? reportConfiguration = null);

        #endregion
        Task FacebookSyncHandler(InputPayload input);
        Task FacebookLoginHandler(InputPayload input);
        Task FacebookBulkLeadsFetchHandler(InputPayload input);
        Task ValidateExcelHandler(InputPayload input);
        Task ExportFacebookBulkLeadsHandler(InputPayload input);
        Task ExportUserActivityReportHandler(InputPayload input, ReportsConfigurationDto? reportConfiguration = null);
        Task GoogleSheetHandler(InputPayload input);

        Task ChannelPartnerHandler(InputPayload input);
        Task ExportLeadSubStatusReportByUserHandler(InputPayload input, ReportsConfigurationDto? reportConfiguration = null);
        Task ExportAttendanceHandler(InputPayload input);
        Task SendWhatsAppBulkTemplateHandler(InputPayload input);
        Task ProspectHandler(InputPayload input);
        Task ProspectHandlerV2(InputPayload input);
        Task ExportPropertiesHandler(InputPayload input);

        Task ExportProspectsHandler(InputPayload input);
        Task ExportLeadDatewiseSourceCountHandler(InputPayload input, ReportsConfigurationDto? reportConfiguration = null);

        #region dataresport
        Task ExportDataStatusReportByUserHandler(InputPayload input, ReportsConfigurationDto? reportConfiguration = null);
        Task ExportDataStatusReportBySubSourceHandler(InputPayload input, ReportsConfigurationDto? reportConfiguration = null);
        Task ExportDataStatusReportBySourceHandler(InputPayload input, ReportsConfigurationDto? reportConfiguration = null);
        Task ExportDataStatusReportByProjectHandler(InputPayload input, ReportsConfigurationDto? reportConfiguration = null);
        Task ExportDataCallLogReportsHandler(InputPayload input, ReportsConfigurationDto? reportConfiguration = null);
        
        Task ExportDataUserActivityReportHandler(InputPayload input, ReportsConfigurationDto? reportConfiguration = null);
        Task ExportUserReportHandler(InputPayload input);

        #endregion
        #region Custom Filters
        Task ExportLeadsByCustomFiltersHandler(InputPayload input);
        Task ExportAgencyReportByLeadStatusHandler(InputPayload input, ReportsConfigurationDto? reportConfiguration = null);
        Task ExportProjectReportByLeadStatusHandler(InputPayload input, ReportsConfigurationDto? reportConfiguration = null);
        Task ExportSourceReportByLeadStatusHandler(InputPayload input, ReportsConfigurationDto? reportConfiguration = null);
        Task ExportSubsourceReportByLeadStatusHandler(InputPayload input, ReportsConfigurationDto? reportConfiguration = null);
        Task ExportUserReportByLeadStatusHandler(InputPayload input, ReportsConfigurationDto? reportConfiguration = null);
        Task ExportProspectsByCustomFiltersHandler(InputPayload input);
        #endregion
        Task UnitHandler(InputPayload input);
        Task UserDeleteHandler(InputPayload input);
        Task MarketingAgencyHandler(InputPayload input);
        Task MarketingChannelPartnerHandler(InputPayload output);
        Task ExportMarketingAgencyHandler(InputPayload output);
        Task ExportMarketingChannelPartnerHandler(InputPayload output);

        Task ExportTeamReportHandler(InputPayload input);
        Task BulkUserImportHandler(InputPayload input);
        Task ListingSourceAddressHandler(InputPayload input);
        Task ExportPropertiesListingHandler(InputPayload input);

        Task V3AddBulkLocations(List<Address> addresses, string connectionString, string tenantId);
        Task ImportCustomAddressFromExcelHandler(InputPayload input);
        Task ExportProjectsHandler(InputPayload input);
        Task ProjectHandler(InputPayload input);
        Task BulkImportReferenceIdHandler(InputPayload payload);

        Task MarketingCampaignHandler(InputPayload input);
        Task ExportMarketingCampaignHandler(InputPayload input);
        Task GoogleAdsLoginHandler(InputPayload input);
        Task GoogleAdsBulkLeadsFetchHandler(InputPayload input);
    }
}
