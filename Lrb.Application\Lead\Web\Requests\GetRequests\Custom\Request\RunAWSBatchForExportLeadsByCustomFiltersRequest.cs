﻿using Lrb.Application.Common.AWS_Batch;
using Lrb.Application.Common.ServiceBus;
using Newtonsoft.Json;

namespace Lrb.Application.Lead.Web.Export
{
    public class RunAWSBatchForExportLeadsByCustomFiltersRequest : GetAllLeadsParametersNewFilters, IRequest<Response<Guid>>
    {
        public List<string>? ToRecipients { get; set; } = new();
        public List<string>? CcRecipients { get; set; } = new();
        public List<string>? BccRecipients { get; set; } = new();
        public Guid? ExportTemplateId { get; set; }
        public string? FileName { get; set; }
        public bool? IsWithNotes {  get; set; }
        public int? NotesCount { get;set; }
        public bool? IsWithFacebookProperties { get; set; }

    }
    public class RunAWSBatchForExportLeadsByCustomFiltersRequestHandler : IRequestHandler<RunAWSBatchForExportLeadsByCustomFiltersRequest, Response<Guid>>
    {
        private readonly IAWSBatchService _batchService;
        private readonly ICurrentUser _currentUser;
        private readonly IRepositoryWithEvents<ExportLeadTracker> _exportLeadRepo;
        public const string TYPE = "exportleadsbycustomfilters";
        private readonly IServiceBus _serviceBus;

        public RunAWSBatchForExportLeadsByCustomFiltersRequestHandler(IAWSBatchService batchService,
            ICurrentUser currentUser, IRepositoryWithEvents<ExportLeadTracker> exportLeadRepo,
            IServiceBus serviceBus)
        {
            _batchService = batchService;
            _currentUser = currentUser;
            _exportLeadRepo = exportLeadRepo;
            _serviceBus = serviceBus;
        }

        public async Task<Response<Guid>> Handle(RunAWSBatchForExportLeadsByCustomFiltersRequest request, CancellationToken cancellationToken)
        {
            try
            {
                ExportLeadTracker exportTracker = new();
                exportTracker.Request = JsonConvert.SerializeObject(request);
                exportTracker.TemplateId = request.ExportTemplateId;
                exportTracker.ToRecipients = request.ToRecipients;
                exportTracker.CcRecipients = request.CcRecipients;
                exportTracker.BccRecipients = request.BccRecipients;
                var tenantId = _currentUser.GetTenant();
                var currentUserId = _currentUser.GetUserId();
                await _exportLeadRepo.AddAsync(exportTracker, cancellationToken);
                //Submit a job in AWS Batch
                InputPayload input = new(exportTracker.Id, tenantId ?? string.Empty, currentUserId, TYPE, null);
                var stringArgument = JsonConvert.SerializeObject(input);
                var cmdArgs = new List<string>() { stringArgument };
                await _serviceBus.RunExcelExportJobAsync(cmdArgs);
                return new(exportTracker.Id);
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException("Something went wrong.");
            }
        }
    }
}
