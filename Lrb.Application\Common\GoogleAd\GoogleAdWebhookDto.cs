﻿using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Lrb.Application.Common.GoogleAd
{
    public class GoogleAdWebhookDto
    {
        public string? lead_id { get; set; }
        public string? api_version { get; set; }
        public List<UserLeadColumnData>? user_column_data { get; set; } = new();
        public long? form_id { get; set; }
        public long? campaign_id { get; set; }
        public string? google_key { get; set; }
        public bool is_test { get; set; }
        public string? gcl_id { get; set; }
        public long? adgroup_id { get; set; }
        public long? creative_id { get; set; }

    }

    public class UserLeadColumnData
    {
        public string? column_name { get; set; }
        public string? string_value { get; set; }
        public string? column_id { get; set; }

    }
    public class GoogleAdsAccountResponse
    {
        [JsonProperty("resourceName")]
        public string? ResourceName { get; set; }

        [JsonProperty("id")]
        public string? Id { get; set; }

        [JsonProperty("descriptiveName")]
        public string? DescriptiveName { get; set; }
    }
}
