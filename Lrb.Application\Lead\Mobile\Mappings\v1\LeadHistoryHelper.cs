﻿using Lrb.Application.DailyStatusUpdates.Dtos;
using Lrb.Application.Identity.Users;
using Lrb.Application.Lead.Mobile.Dtos.v1;
using Lrb.Application.Lead.Mobile.Dtos.v2;
using Lrb.Application.Utils;
using Lrb.Application.WhatsAppCloudApi.Mobile;
using Newtonsoft.Json;
using System.Globalization;
using System.Reflection;
using System.Text.RegularExpressions;
using static Lrb.Application.Lead.Mobile.LeadHistoryDto;

namespace Lrb.Application.Lead.Mobile
{
    public static class LeadHistoryHelper
    {
        public static string[] formats = {
    // Month-Day-Year formats
    "MM/dd/yyyy HH:mm:ss",       // 10/24/2024 14:30:00
    "MM/dd/yyyy hh:mm:ss tt",    // 10/24/2024 02:30:00 PM
    "MM-dd-yyyy HH:mm:ss",       // 10-24-2024 14:30:00
    "MM-dd-yyyy hh:mm:ss tt",    // 10-24-2024 02:30:00 PM

    // Day-Month-Year formats
    "dd-MM-yyyy HH:mm:ss",       // 24-10-2024 14:30:00
    "dd-MM-yyyy hh:mm:ss tt",    // 24-10-2024 02:30:00 PM
    "dd/MM/yyyy HH:mm:ss",       // 24/10/2024 14:30:00
    "dd/MM/yyyy hh:mm:ss tt",    // 24/10/2024 02:30:00 PM
    "dd.MM.yyyy HH:mm:ss",       // 24.10.2024 14:30:00
    "dd.MM.yyyy hh:mm:ss tt",    // 24.10.2024 02:30:00 PM

    // Year-Month-Day formats
    "yyyy/MM/dd HH:mm:ss",       // 2024/10/24 14:30:00
    "yyyy-MM-dd HH:mm:ss",       // 2024-10-24 14:30:00
    "yyyy.MM.dd HH:mm:ss",       // 2024.10.24 14:30:00
    "yyyy/MM/dd hh:mm:ss tt",    // 2024/10/24 02:30:00 PM
    "yyyy-MM-dd hh:mm:ss tt",    // 2024-10-24 02:30:00 PM
    "yyyy.MM.dd hh:mm:ss tt",    // 2024.10.24 02:30:00 PM

    // ISO 8601 formats
    "yyyy-MM-ddTHH:mm:ss.fffffffZ",  // 2024-10-24T14:30:00.0000000Z
    "yyyy-MM-ddTHH:mm:ssZ",          // 2024-10-24T14:30:00Z
    "yyyy-MM-ddTHH:mm:ss.fffZ",      // 2024-10-24T14:30:00.000Z
    "yyyy-MM-ddTHH:mm:ss",           // 2024-10-24T14:30:00
    "yyyy-MM-ddTHH:mm:ssK",          // 2024-10-24T14:30:00+00:00

    // Custom formats
    "yyyyMMddHHmmss",             // 20241024143000
    "ddMMyyyyHHmmss",             // 24102024143000
    "yyyyMMdd",                   // 20241024

    // Optional seconds
    "MM/dd/yyyy HH:mm",           // 10/24/2024 14:30
    "dd/MM/yyyy HH:mm",           // 24/10/2024 14:30
    "yyyy-MM-ddTHH:mm",           // 2024-10-24T14:30
    "yyyyMMddHHmm",               // 202410241430

    // Optional milliseconds
    "MM/dd/yyyy HH:mm:ss.fff",    // 10/24/2024 14:30:00.000
    "dd/MM/yyyy HH:mm:ss.fff",    // 24/10/2024 14:30:00.000
    "yyyy-MM-dd HH:mm:ss.fff",    // 2024-10-24 14:30:00.000
    "yyyy-MM-ddTHH:mm:ss.fff",    // 2024-10-24T14:30:00.000
};
        public static List<LeadHistoryDto> GetListOfUpdatedItems(LeadHistory leadHistory, int historyCount, IReadRepository<Address>? addressRepo = null, List<AppointmentDataDto>? leadAppointments = null, List<LeadCommunicationDto>? leadCommunicationDetails = null, List<WhatsAppCommunicationDto>? whatsAppCommunications = null)
        {

            if (leadHistory == null) return null;
            List<LeadHistoryDto> historyItems = new List<LeadHistoryDto>();
            List<string> propertiesToSkip = new List<string>()
                {
                    "CreatedDate",
                    "CreatedBy",
                    "ModifiedDate",
                    "LastModifiedBy",
                    "LocationId",
                    "EnquiredLocationId",
                    "SourcingManager",
                    "ClosingManager",
                     "BookedBy"
                };
            List<string> propertiesToKeep = new List<string>()
            {
                "Name",
                "ContactNo",
                "AlternateContactNo",
                "AssignedToUser",
                "AssignedFromUser",
                "Documents",
                "LeadSource",
                "BaseLeadStatus",
                "SubLeadStatus",
                "ScheduledDate",
                "RevertDate",
                "ChosenProject",
                "ChosenProperty",
                "BookedUnderName",
                //"EnquiredFor",
                "Rating",
                "UpperBudget",
                "LowerBudget",
                "Notes",
                "ConfidentialNotes",
                "BasePropertyType",
                "SubPropertyType",
                //"IsHighlighted",
                //"IsEscalated",
                //"IsAboutToConvert",
                //"IsIntegrationLead",
                //"IsHotLead",
                //"IsColdLead",
                //"IsWarmLead",
                "ContactRecords",
                "SaleType",
                //"BHKType",
                //"NoOfBHK",
                "Email",
                "Area",
                "SoldPrice",
                "ShareCount",
                "IsMeetingDone",
                //"MeetingLocation",
                "IsSiteVisitDone",
                //"SiteLocation",
                "IsArchived",
                "Projects",
                "Properties",
                "ReferralName",
                "ReferralContactNo",
                "SubSource",
                //"AgencyName",
                "CompanyName",
                "CarpetArea",
                "PossessionDate",
                "CarpetAreaUnit",
                "ConversionFactor",
                //"DuplicateLeadVersion",
               // "ChildLeadsCount",
                "SourcingManagerUser",
                "ClosingManagerUser",
                "Profession",
                "CustomerCity",
                "CustomerState",
                "CustomerLocation",
                "ChannelPartnerName",
                "ChannelPartnerExecutiveName",
                "ChannelPartnerContactNo",
                "ChannelPartners",
                //"EnquiredCity",
                //"EnquiredState",
                //"EnquiredLocation",
                "PickedDate",
                "CustomFlags",
                //"IsPicked",
                "SecondaryUser",
                "BookedByUser",
                "BookedDate",
                "EnquiryTypes",
                "BHKTypes",
                "BHKs",
                "EnquiredCities",
                "EnquiredStates",
                "EnquiredLocations",
                "Agencies",
                "Designation",
                "BulkCategory",
                //"SecondaryFromUser",
                "Links",
                "Beds",
                "Baths",
                "Floors",
                "OfferType",
                "Furnished",
                "EnquiredCommunity",
                "EnquiredSubCommunity",
                "EnquiredTowerName",
                "ReferralEmail",
                "CustomerCommunity",
                "CustomerSubCommunity",
                "CustomerTowerName",
                "CustomerCountry",
                "EnquiredCountry",
                "SaleableArea",
                "BuiltUpArea",
                "SaleableAreaUnit",
                "BuiltUpAreaUnit",
                "BuiltUpAreaConversionFactor",
                "SaleableAreaConversionFactor",
                "Currency",
                "NetArea",
                "PropertyArea",
                "PropertyAreaUnit",
                "NetAreaUnit",
                "NetAreaConversionFactor",
                "PropertyAreaConversionFactor",
                "UnitName",
                "ClusterName",
                "Nationality",
                "Campaigns",
                "Purpose",
                "PossesionType",
                "LandLine",
                "Gender",
                "DateOfBirth",
                "MaritalStatus",
                "AppointmentDoneOn",
                "LeadAssignmentType"

            };


            for (int version = leadHistory.CurrentVersion; version >= 1; version--)
            {
                PropertyInfo[] properties = typeof(LeadHistory).GetProperties();
                foreach (PropertyInfo property in properties)
                {
                    var type = Nullable.GetUnderlyingType(property.PropertyType) ?? property.PropertyType;

                    if (propertiesToKeep.Contains(property.Name) && property.GetValue(leadHistory) != null
                            && type != typeof(Guid) && type != typeof(bool) && type != typeof(DateTime)
                            && IsVersionExists(version, type, property, leadHistory))
                    {
                        LeadHistoryDto result = null;
                        if (version == 1)
                        {
                            if (historyCount == 0)
                            {
                                //  var v1Vms = BuildAuditModelForV1(leadHistory);
                                if ((type.FullName == typeof(IDictionary<int, EnquiryType>).FullName))
                                {
                                    var enquiryType = (Dictionary<int, EnquiryType>)property.GetValue(leadHistory);
                                    if (enquiryType?.Values.FirstOrDefault() != EnquiryType.None)
                                    {
                                        result = BuildAuditModelForEnquiryType(version, enquiryType, property, leadHistory);
                                        result.OldValue = null;
                                    }
                                }
                                else if ((type.FullName == typeof(IDictionary<int, double>).FullName))
                                {
                                    var doubleValue = (Dictionary<int, double>)property.GetValue(leadHistory);
                                    if (doubleValue.Values.FirstOrDefault() != default)
                                    {
                                        result = BuildAuditModelForV1(version, doubleValue, property, leadHistory);
                                    }
                                }
                                else if ((type.FullName == typeof(IDictionary<int, int>).FullName))
                                {
                                    var intValue = (Dictionary<int, int>)property.GetValue(leadHistory);
                                    if (intValue.Values.FirstOrDefault() != default)
                                    {
                                        result = BuildAuditModelForV1(version, intValue, property, leadHistory);
                                    }
                                }
                                else if ((type.FullName == typeof(IDictionary<int, long>).FullName))
                                {
                                    var longValue = (Dictionary<int, long>)property.GetValue(leadHistory);
                                    if (longValue.Values.FirstOrDefault() != default)
                                    {

                                        result = BuildAuditModelForV1(version, longValue, property, leadHistory);
                                    }
                                }
                                else if ((type.FullName == typeof(IDictionary<int, BHKType>).FullName))
                                {
                                    var bhkType = (Dictionary<int, BHKType>)property.GetValue(leadHistory);
                                    if (bhkType?.Values.FirstOrDefault() != BHKType.None)
                                    {
                                        result = BuildAuditModelForBHKType(version, bhkType, property, leadHistory);
                                        result.OldValue = null;
                                    }
                                }
                                else if ((type.FullName == typeof(IDictionary<int, string>).FullName))
                                {
                                    var stringValue = (Dictionary<int, string>)property.GetValue(leadHistory);
                                    if ((property.Name != "NoOfBHK" && stringValue?.Values.FirstOrDefault() != "0") && !string.IsNullOrEmpty(stringValue?.Values.FirstOrDefault()))
                                    {
                                        result = BuildAuditModelForV1(version, (Dictionary<int, string>)property.GetValue(leadHistory), property, leadHistory);
                                    }
                                    else if ((property.Name == "Beds" && stringValue?.Values.FirstOrDefault() == "0") && !string.IsNullOrEmpty(stringValue?.Values.FirstOrDefault()))
                                    {
                                        result = BuildAuditModelForV1(version, (Dictionary<int, string>)property.GetValue(leadHistory), property, leadHistory);
                                    }
                                }
                                else if ((type.FullName == typeof(IDictionary<int, LeadSource>).FullName))
                                {
                                    result = BuildAuditModelForLeadSource(version, (Dictionary<int, LeadSource>)property.GetValue(leadHistory), property, leadHistory);
                                    result.OldValue = null;
                                }
                                else if ((type.FullName == typeof(IDictionary<int, DateTime>).FullName))
                                {
                                    result = BuildAuditModelForV1(version, (Dictionary<int, DateTime>)property.GetValue(leadHistory), property, leadHistory);
                                }
                                else if ((type.FullName == typeof(IDictionary<int, DateTime?>).FullName))
                                {
                                    result = BuildAuditModelForV1(version, (Dictionary<int, DateTime?>)property.GetValue(leadHistory), property, leadHistory);
                                }
                                else if ((type.FullName == typeof(IDictionary<int, OfferType>).FullName))
                                {
                                    var enquiryType = (Dictionary<int, OfferType>)property.GetValue(leadHistory);
                                    if (enquiryType?.Values.FirstOrDefault() != OfferType.None)
                                    {
                                        result = BuildAuditModelForOfferType(version, enquiryType, property, leadHistory);
                                        result.OldValue = null;
                                    }
                                }
                                else if ((type.FullName == typeof(IDictionary<int, Purpose>).FullName))
                                {
                                    var enquiryType = (Dictionary<int, Purpose>)property.GetValue(leadHistory);
                                    if (enquiryType?.Values.FirstOrDefault() != Purpose.None)
                                    {
                                        result = BuildAuditModelForPurpose(version, enquiryType, property, leadHistory);
                                        result.OldValue = null;
                                    }
                                }
                                else if ((type.FullName == typeof(IDictionary<int, PossesionType>).FullName))
                                {
                                    var enquiryType = (Dictionary<int, PossesionType>)property.GetValue(leadHistory);
                                    if (enquiryType?.Values.FirstOrDefault() != PossesionType.None)
                                    {
                                        result = BuildAuditModelForPosseionType(version, enquiryType, property, leadHistory);
                                        result.OldValue = null;
                                    }
                                }
                                else if ((type.FullName == typeof(IDictionary<int, Gender>).FullName))
                                {
                                    var gender = (Dictionary<int, Gender>)property.GetValue(leadHistory);
                                    if (gender?.Values.FirstOrDefault() != Gender.NotMentioned)
                                    {
                                        result = BuildAuditModelForGender(version, gender, property, leadHistory);
                                        result.OldValue = null;
                                    }
                                }
                                else if ((type.FullName == typeof(IDictionary<int, MaritalStatusType>).FullName))
                                {
                                    var maritalStatus = (Dictionary<int, MaritalStatusType>)property.GetValue(leadHistory);
                                    if (maritalStatus?.Values.FirstOrDefault() != MaritalStatusType.NotMentioned)
                                    {
                                        result = BuildAuditModelForMaritalStatus(version, maritalStatus, property, leadHistory);
                                        result.OldValue = null;
                                    }
                                }
                                else if (property.PropertyType.IsGenericType &&
                                    property.PropertyType.GetGenericArguments()[1].IsEnum && !historyItems.Any(i => i.FieldName?.Replace(" ", "").ToLower().Trim() == property.Name.Replace(" ", "").ToLower().Trim()))
                                {
                                    Type enumType = property.PropertyType.GetGenericArguments()[1];
                                    var method = typeof(LeadHistoryHelper).GetMethod(nameof(BuildAuditModelForEnum), BindingFlags.NonPublic | BindingFlags.Static);
                                    var genericMethod = method?.MakeGenericMethod(enumType);

                                    result = (LeadHistoryDto)genericMethod?.Invoke(null, new object[]
                                   {
                                    version,
                                    property.GetValue(leadHistory),
                                    property,
                                    leadHistory
                                   });


                                }
                                if (result != null)
                                {
                                    if (!string.IsNullOrEmpty(result.NewValue))
                                    {
                                        historyItems.Add(result);
                                    }
                                }
                            }
                        }
                        else
                        {
                            if ((type.FullName == typeof(IDictionary<int, EnquiryType>).FullName))
                                result = BuildAuditModelForEnquiryType(version, (Dictionary<int, EnquiryType>)property.GetValue(leadHistory), property, leadHistory);
                            else if ((type.FullName == typeof(IDictionary<int, LeadSource>).FullName))
                                result = BuildAuditModelForLeadSource(version, (Dictionary<int, LeadSource>)property.GetValue(leadHistory), property, leadHistory);
                            else if ((type.FullName == typeof(IDictionary<int, double>).FullName))
                                result = BuildAuditModel(version, (Dictionary<int, double>)property.GetValue(leadHistory), property, leadHistory);
                            else if ((type.FullName == typeof(IDictionary<int, DateTime>).FullName))
                                result = BuildAuditModel(version, (Dictionary<int, DateTime>)property.GetValue(leadHistory), property, leadHistory);
                            else if ((type.FullName == typeof(IDictionary<int, DateTime?>).FullName))
                                result = BuildAuditModel(version, (Dictionary<int, DateTime?>)property.GetValue(leadHistory), property, leadHistory);
                            else if ((type.FullName == typeof(IDictionary<int, int>).FullName))
                                result = BuildAuditModel(version, (Dictionary<int, int>)property.GetValue(leadHistory), property, leadHistory);
                            else if ((type.FullName == typeof(IDictionary<int, long>).FullName))
                                result = BuildAuditModel(version, (Dictionary<int, long>)property.GetValue(leadHistory), property, leadHistory);
                            else if ((type.FullName == typeof(IDictionary<int, Guid>).FullName))
                                result = BuildAuditModel(version, (Dictionary<int, Guid>)property.GetValue(leadHistory), property, leadHistory, addressRepo);
                            else if ((type.FullName == typeof(IDictionary<int, bool>).FullName))
                                result = BuildAuditModel(version, (Dictionary<int, bool>)property.GetValue(leadHistory), property, leadHistory, addressRepo);
                            else if ((type.FullName == typeof(IDictionary<int, string>).FullName))
                                result = BuildAuditModel(version, (Dictionary<int, string>)property.GetValue(leadHistory), property, leadHistory);
                            else if ((type.FullName == typeof(IDictionary<int, ContactType>).FullName))
                                result = BuildAuditModelForContactType(version, (Dictionary<int, ContactType>)property.GetValue(leadHistory), property, leadHistory);
                            else if ((type.FullName == typeof(IDictionary<int, SaleType>).FullName))
                                result = BuildAuditModelForSaleType(version, (Dictionary<int, SaleType>)property.GetValue(leadHistory), property, leadHistory);
                            else if ((type.FullName == typeof(IDictionary<int, BHKType>).FullName))
                                result = BuildAuditModelForBHKType(version, (Dictionary<int, BHKType>)property.GetValue(leadHistory), property, leadHistory);
                            else if ((type.FullName == typeof(IDictionary<int, Profession>).FullName))
                                result = BuildAuditModelForProfession(version, (Dictionary<int, Profession>)property.GetValue(leadHistory), property, leadHistory);
                            else if ((type.FullName == typeof(IDictionary<int, BulkType>).FullName))
                                result = BuildAuditModelForBulkType(version, (Dictionary<int, BulkType>)property.GetValue(leadHistory), property, leadHistory);
                            else if ((type.FullName == typeof(IDictionary<int, OfferType>).FullName))
                                result = BuildAuditModelForOfferType(version, (Dictionary<int, OfferType>)property.GetValue(leadHistory), property, leadHistory);
                            else if ((type.FullName == typeof(IDictionary<int, Purpose>).FullName))
                                result = BuildAuditModelForPurpose(version, (Dictionary<int, Purpose>)property.GetValue(leadHistory), property, leadHistory);
                            else if ((type.FullName == typeof(IDictionary<int, PossesionType>).FullName))
                                result = BuildAuditModelForPosseionType(version, (Dictionary<int, PossesionType>)property.GetValue(leadHistory), property, leadHistory);
                            else if ((type.FullName == typeof(IDictionary<int, Gender>).FullName))
                                result = BuildAuditModelForGender(version, (Dictionary<int, Gender>)property.GetValue(leadHistory), property, leadHistory);
                            else if ((type.FullName == typeof(IDictionary<int, MaritalStatusType>).FullName))
                                result = BuildAuditModelForMaritalStatus(version, (Dictionary<int, MaritalStatusType>)property.GetValue(leadHistory), property, leadHistory);
                            else if (property.PropertyType.IsGenericType && property.PropertyType.GetGenericArguments()[1].IsEnum && !historyItems.Any(i => i.FieldName?.Replace(" ", "").ToLower().Trim() == property.Name.Replace(" ", "").ToLower().Trim()))
                            {
                                Type enumType = property.PropertyType.GetGenericArguments()[1];
                                var method = typeof(LeadHistoryHelper).GetMethod(nameof(BuildAuditModelForEnum), BindingFlags.NonPublic | BindingFlags.Static);
                                var genericMethod = method?.MakeGenericMethod(enumType);

                                result = (LeadHistoryDto)genericMethod?.Invoke(null, new object[]
                               {
                                 version,
                                 property.GetValue(leadHistory),
                                 property,
                                 leadHistory
                               });

                            }
                            else if ((type.FullName == typeof(IDictionary<int, LeadAssignmentType>).FullName))
                                result = BuildAuditModelForLeadAssignmentTypeType(version, (Dictionary<int, LeadAssignmentType>)property.GetValue(leadHistory), property, leadHistory);
                            if (result != null)
                                historyItems.Add(result);
                        }

                    }

                }
            }
            //if (leadAppointments != null)
            //{
            //    var historyItemsWithAppointmentDetails = AddAppointmentDetails(leadAppointments);
            //    historyItems.AddRange(historyItemsWithAppointmentDetails);
            //    historyItems = historyItems.OrderByDescending(i => i.UpdatedOn).ToList();
            //}
            //if (leadCommunicationDetails != null)
            //{
            //    var historyItemsWithAppointmentDetails = AttachCommunicationDetails(leadCommunicationDetails);
            //    historyItems.AddRange(historyItemsWithAppointmentDetails);
            //    historyItems = historyItems.OrderByDescending(i => i.UpdatedOn).ToList();
            //}
            //if (whatsAppCommunications?.Any() ?? false)
            //{
            //    var historyItemsWithWhatsAppCommLogs = AddWhatsAppCommunications(whatsAppCommunications);
            //    historyItems.AddRange(historyItemsWithWhatsAppCommLogs);
            //    historyItems = historyItems.OrderByDescending(i => i.UpdatedOn).ToList();
            //}
            if (historyCount == 0)
            {
                var createdVm = new LeadHistoryDto()
                {
                    FieldName = "Lead got added",
                    FilterKey = LeadHistoryFilterKey.None,
                    NewValue = null,
                    OldValue = null,
                    UpdatedBy = GetAuditUpdateByValue(1, leadHistory),
                    UpdatedOn = GetAuditUpdatedOnValue(1, leadHistory),
                    AuditActionType = EnumHelper.GetEnumDescription(AuditActionType.Created)
                };
                historyItems.Add(createdVm);
            }
            historyItems = historyItems.RemoveDuplicateItems();
            historyItems = historyItems.RemoveDuplicateItemsV1();
            historyItems = historyItems.RemoveDuplicateItemsV2();
            historyItems = historyItems.RemoveDuplicateItemsV3();
            historyItems = historyItems.RemoveDuplicateItemsV4();

            return historyItems;
        }
        private static List<LeadHistoryDto> AddWhatsAppCommunications(List<WhatsAppCommunicationDto> whatsAppCommunications)
        {
            List<LeadHistoryDto> leadHistoryDtos = new();
            foreach (var waComm in whatsAppCommunications)
            {
                var newValue = JsonConvert.SerializeObject(waComm.Adapt<ViewWhatsAppCommunicationDto>());
                var leadHistoryDto = new LeadHistoryDto()
                {
                    FieldName = "WhatsApp Communication",
                    FilterKey = LeadHistoryFilterKey.None,
                    NewValue = newValue,
                    OldValue = null,
                    UpdatedBy = waComm.LastModifiedByUser,
                    UpdatedOn = waComm.CreatedOn,
                    AuditActionType = EnumHelper.GetEnumDescription(AuditActionType.Updated)
                };
                leadHistoryDtos.Add(leadHistoryDto);
            }
            return leadHistoryDtos;
        }

        private static List<LeadHistoryDto> AddAppointmentDetails(List<AppointmentDataDto> leadAppointments)
        {
            List<LeadHistoryDto> leadHistoryDtos = new();
            foreach (var appointment in leadAppointments)
            {
                var newValue = JsonConvert.SerializeObject(appointment.Adapt<ViewAppointmentDataDto>());
                var leadHistoryDto = new LeadHistoryDto()
                {
                    FieldName = "Lead Appointment",
                    FilterKey = LeadHistoryFilterKey.None,
                    NewValue = newValue,
                    OldValue = null,
                    UpdatedBy = appointment.LastModifiedByUser,
                    UpdatedOn = appointment.CreatedOn,
                    AuditActionType = EnumHelper.GetEnumDescription(AuditActionType.Updated)
                };
                leadHistoryDtos.Add(leadHistoryDto);
            }
            return leadHistoryDtos;
        }
        private static List<LeadHistoryDto> AttachCommunicationDetails(List<LeadCommunicationDto>? leadCommunicationDetails)
        {
            List<LeadHistoryDto> leadHistoryDtos = new();
            leadCommunicationDetails = leadCommunicationDetails?.Where(i => i.ContactType != ContactType.Call).ToList() ?? new();
            foreach (var communicationDetail in leadCommunicationDetails)
            {
                var newValue = JsonConvert.SerializeObject(communicationDetail.Adapt<ViewLeadCommunicationDto>());
                var leadHistoryDto = new LeadHistoryDto()
                {
                    FieldName = "Lead Comunication",
                    FilterKey = LeadHistoryFilterKey.None,
                    NewValue = newValue,
                    OldValue = null,
                    UpdatedBy = communicationDetail.LastModifiedByUser,
                    UpdatedOn = communicationDetail.CreatedOn,
                    AuditActionType = EnumHelper.GetEnumDescription(AuditActionType.Updated)
                };
                leadHistoryDtos.Add(leadHistoryDto);
            }
            return leadHistoryDtos;
        }
        private static List<LeadHistoryDto> RemoveDuplicateItems(this List<LeadHistoryDto> items)
        {
            items.RemoveAll(i => (((i.FieldName?.Equals("CarpetArea") ?? false) || (i.FieldName?.Equals("Carpet Area") ?? false)) && (!i.NewValue?.Any(char.IsDigit) ?? false)));
            items.RemoveAll(i => (i.FieldName?.Equals("Carpet Area Unit") ?? false));
            items.OrderByDescending(i => i.UpdatedOn.Date).ToList();
            items = items.DistinctBy(i => new { i.FieldName, i.OldValue, i.NewValue, i.UpdatedOn, i.AuditActionType, i.UpdatedBy }).ToList();
            return items;
        }
        private static List<LeadHistoryDto> RemoveDuplicateItemsV1(this List<LeadHistoryDto> items)
        {
            items.RemoveAll(i => (((i.FieldName?.Equals("BuiltUpArea") ?? false) || (i.FieldName?.Equals("Built Up Area") ?? false)) && (!i.NewValue?.Any(char.IsDigit) ?? false)));
            items.RemoveAll(i => (i.FieldName?.Equals("Built Up Area Unit") ?? false));
            items.OrderByDescending(i => i.UpdatedOn.Date).ToList();
            items = items.DistinctBy(i => new { i.FieldName, i.OldValue, i.NewValue, i.UpdatedOn, i.AuditActionType, i.UpdatedBy }).ToList();
            return items;
        }
        private static List<LeadHistoryDto> RemoveDuplicateItemsV2(this List<LeadHistoryDto> items)
        {
            items.RemoveAll(i => (((i.FieldName?.Equals("SaleableArea") ?? false) || (i.FieldName?.Equals("Saleable Area") ?? false)) && (!i.NewValue?.Any(char.IsDigit) ?? false)));
            items.RemoveAll(i => (i.FieldName?.Equals("Saleable Area Unit") ?? false));
            items.OrderByDescending(i => i.UpdatedOn.Date).ToList();
            items = items.DistinctBy(i => new { i.FieldName, i.OldValue, i.NewValue, i.UpdatedOn, i.AuditActionType, i.UpdatedBy }).ToList();
            return items;
        }
        private static List<LeadHistoryDto> RemoveDuplicateItemsV3(this List<LeadHistoryDto> items)
        {
            items.RemoveAll(i => (((i.FieldName?.Equals("NetArea") ?? false) || (i.FieldName?.Equals("Net Area") ?? false)) && (!i.NewValue?.Any(char.IsDigit) ?? false)));
            items.RemoveAll(i => (i.FieldName?.Equals("Net Area Unit") ?? false));
            items.OrderByDescending(i => i.UpdatedOn.Date).ToList();
            items = items.DistinctBy(i => new { i.FieldName, i.OldValue, i.NewValue, i.UpdatedOn, i.AuditActionType, i.UpdatedBy }).ToList();
            return items;
        }
        private static List<LeadHistoryDto> RemoveDuplicateItemsV4(this List<LeadHistoryDto> items)
        {
            items.RemoveAll(i => (((i.FieldName?.Equals("PropertyArea") ?? false) || (i.FieldName?.Equals("Property Area") ?? false)) && (!i.NewValue?.Any(char.IsDigit) ?? false)));
            items.RemoveAll(i => (i.FieldName?.Equals("Property Area Unit") ?? false));
            items.OrderByDescending(i => i.UpdatedOn.Date).ToList();
            items = items.DistinctBy(i => new { i.FieldName, i.OldValue, i.NewValue, i.UpdatedOn, i.AuditActionType, i.UpdatedBy }).ToList();
            return items;
        }
        //public static Dictionary<DateTime, Dictionary<DateTime, List<LeadHistoryDto>>> FormLeadHistoryViewModelMobile(LeadHistory leadHistory, LeadHistoryFilterKey filterKey)
        //{
        //    if (leadHistory == null) return null;
        //    List<LeadHistoryDto> historyItems = GetListOfUpdatedItems(leadHistory);
        //    if (filterKey != LeadHistoryFilterKey.None)
        //    {
        //        historyItems = historyItems.Where(i => i.FilterKey == filterKey).ToList();
        //    }
        //    Dictionary<DateTime, Dictionary<DateTime, List<LeadHistoryDto>>> historyVM = new Dictionary<DateTime, Dictionary<DateTime, List<LeadHistoryDto>>>();
        //    var groupedHistoryItems = historyItems.GroupBy(i => i.UpdatedOn.ToLocalTime().Date).ToList();
        //    foreach (var item in groupedHistoryItems)
        //    {
        //        Dictionary<DateTime, List<LeadHistoryDto>> innerDictionary = new Dictionary<DateTime, List<LeadHistoryDto>>();
        //        var groupedByTime = item.ToList().GroupBy(i => i.UpdatedOn);
        //        foreach (var innerItem in groupedByTime)
        //        {
        //            innerDictionary.Add(innerItem.Key, innerItem.ToList());
        //        }
        //        historyVM.Add(item.Key, innerDictionary);
        //    }
        //    return historyVM;
        //}

        public static Dictionary<DateTime, Dictionary<DateTime, List<LeadHistoryDto>>>  FormLeadHistoryViewModelMobile(List<LeadHistory> leadHistories, List<LeadHistoryFilterKey>? filterKeys, List<Lrb.Application.LeadCallLog.Mobile.LeadCallLogDto> leadCallLogs, List<UserDetailsDto> users, List<AppointmentDataDto>? leadAppointmentDtos = null, List<LeadCommunicationDto>? communicationDtos = null, List<WhatsAppCommunicationDto>? whatsAppCommunications = null, IReadRepository<Address> addressRepo = null, (List<BookedDetailsDto>? bookedDetailsInfo, List<List<DocumentsDto>>? documentsInfo, List<LeadBrokerageInfoDto>? leadBrokerageInfo)? bookedDetails = null)
        {
            if (!leadHistories?.Any() ?? true) return null;
            List<LeadHistoryDto> historyItems = new();
            for (int i = 0; i < leadHistories.Count; i++)
            {
                var appointments = leadAppointmentDtos?.Where(j => j.UserId == leadHistories[i].UserId).ToList();
                var communications = communicationDtos?.Where(j => j.UserId == leadHistories[i].UserId).ToList();
                var waCommunications = whatsAppCommunications?.Where(j => j.UserId == leadHistories[i].UserId).ToList();
                if ((appointments?.Any() ?? false) || (communications?.Any() ?? false) || (waCommunications?.Any() ?? false))
                {
                    historyItems.InsertRange(0, GetListOfUpdatedItems(leadHistories[i], i, addressRepo, appointments, communications, waCommunications));
                }
                else
                {
                    historyItems.InsertRange(0, GetListOfUpdatedItems(leadHistories[i], i));
                }
            }
            if (communicationDtos?.Any() ?? false)
            {
                var communications = AttachCommunicationDetails(communicationDtos);
                historyItems.AddRange(communications);
            }
            if (leadCallLogs?.Any() ?? false)
            {
                foreach (var callLog in leadCallLogs)
                {
                    var user = users.FirstOrDefault(i => i.Id == callLog.UserId);
                    LeadHistoryDto historyDto = new()
                    {
                        FieldName = "LeadCallLog",
                        NewValue = $"{(string.IsNullOrWhiteSpace(callLog.UpdatedCallDirection) ? callLog.CallDirection : callLog.UpdatedCallDirection)} Call -> {(string.IsNullOrWhiteSpace(callLog.UpdatedCallStatus) ? callLog.CallStatus : callLog.UpdatedCallStatus)} -> {FormatDuration(callLog.CallDuration.ToString())}. , CallRecordingUrl -> {(string.IsNullOrWhiteSpace(callLog.CallRecordingUrl) ? string.Empty : callLog.CallRecordingUrl)}",
                        AuditActionType = AuditActionType.Updated.ToString(),
                        UpdatedOn = (DateTime)(callLog.LastModifiedOn ?? DateTime.MinValue),
                        FilterKey = LeadHistoryFilterKey.None,
                        UpdatedBy = user?.FirstName + " " + user?.LastName,
                    };
                    historyItems.Add(historyDto);
                }
            }
            if (bookedDetails?.bookedDetailsInfo?.Any() ?? false)
            {
                var historyItemsWithBookedDetails = AddBookedDetails(bookedDetails?.bookedDetailsInfo);
                historyItems.AddRange(historyItemsWithBookedDetails);
            }
            if (bookedDetails?.documentsInfo?.Any() ?? false)
            {
                var historyItemsWithDocumentDetails = AddDocumentDetails(bookedDetails?.documentsInfo, users);
                historyItems.AddRange(historyItemsWithDocumentDetails);
            }
            if (bookedDetails?.leadBrokerageInfo?.Any() ?? false)
            {
                var historyItemsWithDocumentDetails = AddLeadBrokerageDetails(bookedDetails?.leadBrokerageInfo, users);
                historyItems.AddRange(historyItemsWithDocumentDetails);
            }
            //if (bookedDetails?.unitTypeInfo.Any() ?? false)
            //{
            //    var historyItemsWithDocumentDetails = AddUnitTypeDetails(bookedDetails?.unitTypeInfo, users);
            //    historyItems.AddRange(historyItemsWithDocumentDetails);
            //}
            historyItems = historyItems.OrderByDescending(i => i.UpdatedOn).ToList();
            if (filterKeys != null && filterKeys.Count > 0)
            {
                if (!(filterKeys.Count == 1 && filterKeys.FirstOrDefault() == LeadHistoryFilterKey.None))
                {
                    historyItems = historyItems.Where(i => filterKeys.Contains(i.FilterKey)).ToList();
                }
            }
            if (leadAppointmentDtos?.Any() ?? false)
            {
                var appointments = AddAppointmentDetails(leadAppointmentDtos);
                historyItems.AddRange(appointments);
                historyItems = historyItems.OrderByDescending(i => i.UpdatedOn).ToList();
            }
            Dictionary<DateTime, Dictionary<DateTime, List<LeadHistoryDto>>> historyVM = new Dictionary<DateTime, Dictionary<DateTime, List<LeadHistoryDto>>>();
            var groupedHistoryItems = historyItems.GroupBy(i => i.UpdatedOn).ToList();
            foreach (var item in groupedHistoryItems)
            {
                Dictionary<DateTime, List<LeadHistoryDto>> innerDictionary = new Dictionary<DateTime, List<LeadHistoryDto>>();
                var groupedByTime = item.ToList().GroupBy(i => i.UpdatedOn);
                foreach (var innerItem in groupedByTime)
                {
                    innerDictionary.Add(innerItem.Key, innerItem.ToList());
                }
                historyVM.Add(item.Key, innerDictionary);
            }
            return historyVM;
        }
        public static List<LeadHistoryDto> FormLeadHistoryViewModelBasedOnMobile(List<LeadHistory> leadHistories, List<LeadHistoryFilterKey>? filterKeys, List<Lrb.Application.LeadCallLog.Mobile.LeadCallLogDto> leadCallLogs, List<UserDetailsDto> users, List<AppointmentDataDto>? leadAppointmentDtos = null, List<LeadCommunicationDto>? communicationDtos = null, List<WhatsAppCommunicationDto>? whatsAppCommunications = null, IReadRepository<Address> addressRepo = null, (List<BookedDetailsDto>? bookedDetailsInfo, List<List<DocumentsDto>>? documentsInfo, List<LeadBrokerageInfoDto>? leadBrokerageInfo)? bookedDetails = null)
        {
            if (!leadHistories?.Any() ?? true) return null;
            List<LeadHistoryDto> historyItems = new();
            for (int i = 0; i < leadHistories.Count; i++)
            {
                //var appointments = leadAppointmentDtos?.Where(j => j.UserId == leadHistories[i].UserId).ToList();
                //var communications = communicationDtos?.Where(j => j.UserId == leadHistories[i].UserId).ToList();
                //var waCommunications = whatsAppCommunications?.Where(j => j.UserId == leadHistories[i].UserId).ToList();
                //if ((appointments?.Any() ?? false) || (communications?.Any() ?? false) || (waCommunications?.Any() ?? false))
                //{

                //    historyItems.InsertRange(0, GetListOfUpdatedItems(leadHistories[i], i, addressRepo, appointments, communications));
                //}
                //else
                //{
                    historyItems.InsertRange(0, GetListOfUpdatedItems(leadHistories[i], i, addressRepo));
                //}
            }
            if (leadAppointmentDtos?.Any() ?? false)
            {
                var appointments = AddAppointmentDetails(leadAppointmentDtos);
                historyItems.AddRange(appointments);
            }
            if (communicationDtos?.Any() ?? false)
            {
                var communications = AttachCommunicationDetails(communicationDtos);
                historyItems.AddRange(communications);
            }
            if (leadCallLogs?.Any() ?? false)
            {
                foreach (var callLog in leadCallLogs)
                {
                    var user = users.FirstOrDefault(i => i.Id == callLog.UserId);
                    LeadHistoryDto historyDto = new()
                    {
                        FieldName = "LeadCallLog",
                        NewValue = $"{(string.IsNullOrWhiteSpace(callLog.UpdatedCallDirection) ? callLog.CallDirection : callLog.UpdatedCallDirection)} Call -> {(string.IsNullOrWhiteSpace(callLog.UpdatedCallStatus) ? callLog.CallStatus : callLog.UpdatedCallStatus)} -> {FormatDuration(callLog.CallDuration.ToString())}. , CallRecordingUrl -> {(string.IsNullOrWhiteSpace(callLog.CallRecordingUrl) ? string.Empty : callLog.CallRecordingUrl)}",
                        AuditActionType = AuditActionType.Updated.ToString(),
                        UpdatedOn = (DateTime)(callLog.LastModifiedOn ?? DateTime.MinValue),
                        FilterKey = LeadHistoryFilterKey.None,
                        UpdatedBy = user?.FirstName + " " + user?.LastName,
                    };
                    historyItems.Add(historyDto);
                }
            }
            if (whatsAppCommunications?.Any() ?? false)
            {
                var historyItemsWithCallLogs = AddWhatsAppCommunications(whatsAppCommunications);
                historyItems.AddRange(historyItemsWithCallLogs);
                //historyItems = historyItems.OrderByDescending(i => i.UpdatedOn).ToList();
            }
            if (bookedDetails?.bookedDetailsInfo?.Any() ?? false)
            {
                var historyItemsWithBookedDetails = AddBookedDetails(bookedDetails?.bookedDetailsInfo);
                historyItems.AddRange(historyItemsWithBookedDetails);
            }
            if (bookedDetails?.documentsInfo?.Any() ?? false)
            {
                var historyItemsWithDocumentDetails = AddDocumentDetails(bookedDetails?.documentsInfo, users);
                historyItems.AddRange(historyItemsWithDocumentDetails);
            }
            if (bookedDetails?.leadBrokerageInfo?.Any() ?? false)
            {
                var historyItemsWithDocumentDetails = AddLeadBrokerageDetails(bookedDetails?.leadBrokerageInfo, users);
                historyItems.AddRange(historyItemsWithDocumentDetails);
            }
            historyItems = historyItems.OrderByDescending(i => i.UpdatedOn).ToList();
            if (filterKeys != null && filterKeys.Count > 0)
            {
                if (!(filterKeys.Count == 1 && filterKeys.FirstOrDefault() == LeadHistoryFilterKey.None))
                {
                    historyItems = historyItems.Where(i => filterKeys.Contains(i.FilterKey)).ToList();
                }
            }
            return historyItems;
        }
        private static List<LeadHistoryDto> AddBookedDetails(List<BookedDetailsDto> bookedDetails)
        {
            List<LeadHistoryDto> leadHistoryDtos = new();
            foreach (var bookData in bookedDetails)
            {
                var newValue = JsonConvert.SerializeObject(bookData);
                var leadHistoryDto = new LeadHistoryDto()
                {
                    FieldName = "Booked Details Information",
                    FilterKey = LeadHistoryFilterKey.None,
                    NewValue = newValue,
                    OldValue = null,
                    UpdatedBy = bookData.LastModifiedByUser,
                    UpdatedOn = bookData.LastModifiedOn,
                    AuditActionType = EnumHelper.GetEnumDescription(AuditActionType.Updated)
                };
                leadHistoryDtos.Add(leadHistoryDto);
            }
            return leadHistoryDtos;
        }
        private static List<LeadHistoryDto> AddDocumentDetails(List<List<DocumentsDto>> documentDetails, List<UserDetailsDto>? users)
        {
            List<LeadHistoryDto> leadHistoryDtos = new List<LeadHistoryDto>();
            var flattenedDocuments = documentDetails.SelectMany(documents => documents);

            foreach (var documentInfo in flattenedDocuments)
            {
                var userName = $"{users?.FirstOrDefault(u => u.Id == documentInfo.LastModifiedBy)?.FirstName ?? string.Empty}  {users?.FirstOrDefault(u => u.Id == documentInfo.LastModifiedBy)?.LastName ?? string.Empty}";
                var newValue = JsonConvert.SerializeObject(documentInfo);
                var leadHistoryDto = new LeadHistoryDto()
                {
                    FieldName = "Documents Details Information",
                    FilterKey = LeadHistoryFilterKey.None,
                    NewValue = newValue,
                    OldValue = null,
                    UpdatedBy = userName,
                    UpdatedOn = documentInfo.LastModifiedOn,
                    AuditActionType = EnumHelper.GetEnumDescription(AuditActionType.Updated)
                };
                leadHistoryDtos.Add(leadHistoryDto);
            }
            return leadHistoryDtos;
        }
        private static List<LeadHistoryDto> AddLeadBrokerageDetails(List<LeadBrokerageInfoDto> brokerageDetails, List<UserDetailsDto>? users)
        {
            List<LeadHistoryDto> leadHistoryDtos = new();
            foreach (var brokerageDetail in brokerageDetails)
            {
                var userName = $"{users?.FirstOrDefault(u => u.Id == brokerageDetail.LastModifiedBy)?.FirstName ?? string.Empty}  {users?.FirstOrDefault(u => u.Id == brokerageDetail.LastModifiedBy)?.LastName ?? string.Empty}";
                var newValue = JsonConvert.SerializeObject(brokerageDetail);
                var leadHistoryDto = new LeadHistoryDto()
                {
                    FieldName = "Lead Brokerage Details Information",
                    FilterKey = LeadHistoryFilterKey.None,
                    NewValue = newValue,
                    OldValue = null,
                    UpdatedBy = userName,
                    UpdatedOn = brokerageDetail.LastModifiedOn,
                    AuditActionType = EnumHelper.GetEnumDescription(AuditActionType.Updated)
                };

                leadHistoryDtos.Add(leadHistoryDto);
            }
            return leadHistoryDtos;
        }
        //private static List<LeadHistoryDto> AddUnitTypeDetails(List<UnitTypeDto> unitTypeDetails, List<UserDetailsDto>? users)
        //{
        //    List<LeadHistoryDto> leadHistoryDtos = new();
        //    foreach (var unitTypeDetail in unitTypeDetails)
        //    {
        //        var userName = users?.FirstOrDefault(u => u.Id == unitTypeDetail.LastModifiedBy)?.FirstName ?? string.Empty + "" + users?.FirstOrDefault(u => u.Id == unitTypeDetail.LastModifiedBy)?.LastName ?? string.Empty;
        //        var newValue = JsonConvert.SerializeObject(unitTypeDetail);
        //        var leadHistoryDto = new LeadHistoryDto()
        //        {
        //            FieldName = "Basic Unit Details Information",
        //            FilterKey = LeadHistoryFilterKey.None,
        //            NewValue = newValue,
        //            OldValue = null,
        //            UpdatedBy = userName,
        //            AuditActionType = EnumHelper.GetEnumDescription(AuditActionType.Updated)
        //        };
        //        leadHistoryDtos.Add(leadHistoryDto);
        //    }
        //    return leadHistoryDtos;
        //}
        public static Dictionary<DateTime, List<LeadHistoryDto>> FormLeadHistoryViewModelOfNotes(LeadHistory leadHistory)
        {
            if (leadHistory == null) return null;
            Dictionary<DateTime, List<LeadHistoryDto>> historyVM = new Dictionary<DateTime, List<LeadHistoryDto>>();
            List<LeadHistoryDto> historyItems = new List<LeadHistoryDto>();
            for (int version = leadHistory.CurrentVersion; version >= 1; version--)
            {
                if (leadHistory.Notes?.ContainsKey(version) ?? false)
                {
                    LeadHistoryDto leadHistoryItem = new()
                    {
                        FieldName = SplitCamelCase(nameof(leadHistory.Notes)),
                        FilterKey = LeadHistoryFilterKey.Notes,
                        NewValue = GetAuditCurrentValue(version, leadHistory.Notes),
                        OldValue = GetAuditPreviousValue(version, leadHistory.Notes),
                        UpdatedBy = GetAuditUpdateByValue(version, leadHistory),
                        UpdatedOn = GetAuditUpdatedOnValue(version, leadHistory),
                        AuditActionType = version == 1 ? EnumHelper.GetEnumDescription(AuditActionType.Created) : EnumHelper.GetEnumDescription(AuditActionType.Updated)
                    };
                    if (!string.IsNullOrEmpty(leadHistoryItem.NewValue) && leadHistoryItem.OldValue != leadHistoryItem.NewValue)
                    {
                        historyItems.Add(leadHistoryItem);
                    }
                }
                if (leadHistory.ConfidentialNotes?.ContainsKey(version) ?? false)
                {
                    LeadHistoryDto leadHistoryItem = new()
                    {
                        FieldName = SplitCamelCase(nameof(leadHistory.ConfidentialNotes)),
                        FilterKey = LeadHistoryFilterKey.Notes,
                        NewValue = GetAuditCurrentValue(version, leadHistory.ConfidentialNotes),
                        OldValue = GetAuditPreviousValue(version, leadHistory.ConfidentialNotes),
                        UpdatedBy = GetAuditUpdateByValue(version, leadHistory),
                        UpdatedOn = GetAuditUpdatedOnValue(version, leadHistory),
                        AuditActionType = version == 1 ? EnumHelper.GetEnumDescription(AuditActionType.Created) : EnumHelper.GetEnumDescription(AuditActionType.Updated)
                    };
                    if (!string.IsNullOrEmpty(leadHistoryItem.NewValue) && leadHistoryItem.OldValue != leadHistoryItem.NewValue)
                    {
                        historyItems.Add(leadHistoryItem);
                    }
                }
            }
            var groupedItems = historyItems.GroupBy(i => i.UpdatedOn.Date);
            foreach (var item in groupedItems)
            {
                historyVM.Add(item.Key, item.ToList());
            }
            return historyVM;
        }
        public static Dictionary<DateTime, List<LeadHistoryDto>> FormLeadHistoryViewModelOfNotesNew(List<LeadHistory> leadHistories)
        {
            if (leadHistories == null) return null;
            Dictionary<DateTime, List<LeadHistoryDto>> historyVM = new Dictionary<DateTime, List<LeadHistoryDto>>();
            List<LeadHistoryDto> historyItems = new List<LeadHistoryDto>();
            foreach (var leadHistory in leadHistories)
            {
                var items = GetHistoryItemsOfNotes(leadHistory);
                if (items.Any())
                {
                    historyItems.AddRange(items);
                }
            }

            var groupedItems = historyItems.GroupBy(i => i.UpdatedOn.Date);
            foreach (var item in groupedItems)
            {
                historyVM.Add(item.Key, item.ToList());
            }
            historyVM = historyVM.OrderByDescending(i => i.Key).ToDictionary(i => i.Key, j => j.Value);
            return historyVM;
        }
        private static List<LeadHistoryDto> GetHistoryItemsOfNotes(LeadHistory leadHistory)
        {
            List<LeadHistoryDto> historyItems = new();
            for (int version = leadHistory.CurrentVersion; version >= 1; version--)
            {
                if (leadHistory.Notes?.ContainsKey(version) ?? false)
                {
                    LeadHistoryDto leadHistoryItem = new()
                    {
                        FieldName = SplitCamelCase(nameof(leadHistory.Notes)),
                        FilterKey = LeadHistoryFilterKey.Notes,
                        NewValue = GetAuditCurrentValue(version, leadHistory.Notes),
                        OldValue = GetAuditPreviousValue(version, leadHistory.Notes),
                        UpdatedBy = GetAuditUpdateByValue(version, leadHistory),
                        UpdatedOn = GetAuditUpdatedOnValue(version, leadHistory),
                        AuditActionType = version == 1 ? EnumHelper.GetEnumDescription(AuditActionType.Created) : EnumHelper.GetEnumDescription(AuditActionType.Updated)
                    };
                    historyItems.Add(leadHistoryItem);
                }
                if (leadHistory.ConfidentialNotes?.ContainsKey(version) ?? false)
                {
                    LeadHistoryDto leadHistoryItem = new()
                    {
                        FieldName = SplitCamelCase(nameof(leadHistory.ConfidentialNotes)),
                        FilterKey = LeadHistoryFilterKey.Notes,
                        NewValue = GetAuditCurrentValue(version, leadHistory.ConfidentialNotes),
                        OldValue = GetAuditPreviousValue(version, leadHistory.ConfidentialNotes),
                        UpdatedBy = GetAuditUpdateByValue(version, leadHistory),
                        UpdatedOn = GetAuditUpdatedOnValue(version, leadHistory),
                        AuditActionType = version == 1 ? EnumHelper.GetEnumDescription(AuditActionType.Created) : EnumHelper.GetEnumDescription(AuditActionType.Updated)
                    };
                    if (!string.IsNullOrEmpty(leadHistoryItem.NewValue) && leadHistoryItem.OldValue != leadHistoryItem.NewValue)
                    {
                        historyItems.Add(leadHistoryItem);
                    }
                }
            }
            historyItems = historyItems.OrderByDescending(i => i.UpdatedOn).ToList();
            return historyItems;
        }

        private static bool IsVersionExists(int version, Type type, PropertyInfo property, LeadHistory leadHistory)
        {
            if ((type.FullName == typeof(IDictionary<int, EnquiryType>).FullName))
                return ((Dictionary<int, EnquiryType>)property.GetValue(leadHistory)).ContainsKey(version);
            else if ((type.FullName == typeof(IDictionary<int, LeadSource>).FullName))
                return ((Dictionary<int, LeadSource>)property.GetValue(leadHistory)).ContainsKey(version);
            else if ((type.FullName == typeof(IDictionary<int, DateTime>).FullName))
                return ((Dictionary<int, DateTime>)property.GetValue(leadHistory)).ContainsKey(version);
            else if ((type.FullName == typeof(IDictionary<int, DateTime?>).FullName))
                return ((Dictionary<int, DateTime?>)property.GetValue(leadHistory)).ContainsKey(version);
            else if ((type.FullName == typeof(IDictionary<int, double>).FullName))
                return ((Dictionary<int, double>)property.GetValue(leadHistory)).ContainsKey(version);
            else if ((type.FullName == typeof(IDictionary<int, int>).FullName))
                return ((Dictionary<int, int>)property.GetValue(leadHistory)).ContainsKey(version);
            else if ((type.FullName == typeof(IDictionary<int, long>).FullName))
                return ((Dictionary<int, long>)property.GetValue(leadHistory)).ContainsKey(version);
            else if ((type.FullName == typeof(IDictionary<int, Guid>).FullName))
                return ((Dictionary<int, Guid>)property.GetValue(leadHistory)).ContainsKey(version);
            else if ((type.FullName == typeof(IDictionary<int, bool>).FullName))
                return ((Dictionary<int, bool>)property.GetValue(leadHistory)).ContainsKey(version);
            else if ((type.FullName == typeof(IDictionary<int, string>).FullName))
                return ((Dictionary<int, string>)property.GetValue(leadHistory)).ContainsKey(version);
            else if ((type.FullName == typeof(IDictionary<int, ContactType>).FullName))
                return ((Dictionary<int, ContactType>)property.GetValue(leadHistory)).ContainsKey(version);
            else if ((type.FullName == typeof(IDictionary<int, SaleType>).FullName))
                return ((Dictionary<int, SaleType>)property.GetValue(leadHistory)).ContainsKey(version);
            else if ((type.FullName == typeof(IDictionary<int, BHKType>).FullName))
                return ((Dictionary<int, BHKType>)property.GetValue(leadHistory)).ContainsKey(version);
            else if ((type.FullName == typeof(IDictionary<int, Profession>).FullName))
                return (((Dictionary<int, Profession>)property.GetValue(leadHistory)).ContainsKey(version));
            else if ((type.FullName == typeof(IDictionary<int, BulkType>).FullName))
                return (((Dictionary<int, BulkType>)property.GetValue(leadHistory)).ContainsKey(version));
            else if ((type.FullName == typeof(IDictionary<int, OfferType>).FullName))
                return (((Dictionary<int, OfferType>)property.GetValue(leadHistory)).ContainsKey(version));
            else if ((type.FullName == typeof(IDictionary<int, FurnishStatus>).FullName))
                return (((Dictionary<int, FurnishStatus>)property.GetValue(leadHistory)).ContainsKey(version));
            else if ((type.FullName == typeof(IDictionary<int, Purpose>).FullName))
                return (((Dictionary<int, Purpose>)property.GetValue(leadHistory)).ContainsKey(version));
            else if ((type.FullName == typeof(IDictionary<int, Gender>).FullName))
                return (((Dictionary<int, Gender>)property.GetValue(leadHistory)).ContainsKey(version));
            else if ((type.FullName == typeof(IDictionary<int, MaritalStatusType>).FullName))
                return (((Dictionary<int, MaritalStatusType>)property.GetValue(leadHistory)).ContainsKey(version));

            else if ((type.FullName == typeof(IDictionary<int, PossesionType>).FullName))
                return (((Dictionary<int, PossesionType>)property.GetValue(leadHistory)).ContainsKey(version));
            else if ((type.FullName == typeof(IDictionary<int, LeadAssignmentType>).FullName))
                return (((Dictionary<int, LeadAssignmentType>)property.GetValue(leadHistory)).ContainsKey(version));
            else
                return false;
        }
        private static T GetAuditCurrentValue<T>(int version, IDictionary<int, T> value)
        {
            return value.Where(i => i.Key == version).FirstOrDefault().Value;
        }
        private static string GetCurrentValue<T>(int version, IDictionary<int, T> value)
        {

            var result = value.Where(i => i.Key == version).FirstOrDefault().Value;

            if (result != null)
            {
                DateTime dateTime;

                if (DateTime.TryParseExact
                    (result.ToString(), formats, CultureInfo.InvariantCulture, DateTimeStyles.None, out dateTime))
                {
                    return dateTime.ToString("yyyy-MM-ddTHH:mm:ss.fffffffZ", CultureInfo.InvariantCulture);
                }
                // return (DateTime)result.ToString("yyyy-MM-ddTHH:mm:ss.fffffffZ", CultureInfo.InvariantCulture);
            }
            return string.Empty;

        }
        private static T GetAuditPreviousValue<T>(int version, IDictionary<int, T> value)
        {
            var value1 = value.OrderBy(i => i.Key).TakeWhile(i => i.Key != version).LastOrDefault();
            return value1.Value;
        }
        private static string GetPreviousValue<T>(int version, IDictionary<int, T> value)
        {

            var result = value.OrderBy(i => i.Key).TakeWhile(i => i.Key != version).LastOrDefault();
            if (result.Value != null)
            {
                DateTime dateTime;

                if (DateTime.TryParseExact
                    (result.Value.ToString(), formats, CultureInfo.InvariantCulture, DateTimeStyles.None, out dateTime))
                {
                    return dateTime.ToString("yyyy-MM-ddTHH:mm:ss.fffffffZ", CultureInfo.InvariantCulture);
                }
            }
            return string.Empty;

        }
        private static string GetAuditUpdateByValue(int version, LeadHistory leadHistory)
        {
            return leadHistory.LastModifiedBy.Where(i => i.Key == version).FirstOrDefault().Value == null
                                ? leadHistory.LastModifiedBy.Where(i => i.Key == 1).FirstOrDefault().Value
                                : leadHistory.LastModifiedBy.Where(i => i.Key == version).FirstOrDefault().Value;
        }
        private static DateTime GetAuditUpdatedOnValue(int version, LeadHistory leadHistory)
        {
            return leadHistory.ModifiedDate.Where(i => i.Key == version).FirstOrDefault().Value;
        }
        private static LeadHistoryDto BuildAuditModelForOfferType(int version, Dictionary<int, OfferType> value, PropertyInfo property, LeadHistory leadHistory)
        {
            return new LeadHistoryDto()
            {
                FieldName = SplitCamelCase(property.Name),
                FilterKey = LeadHistoryFilterKey.None,
                NewValue = EnumHelper.GetEnumDescription(GetAuditCurrentValue(version, value)),
                OldValue = EnumHelper.GetEnumDescription(GetAuditPreviousValue(version, value)),
                UpdatedBy = GetAuditUpdateByValue(version, leadHistory),
                UpdatedOn = GetAuditUpdatedOnValue(version, leadHistory),
                AuditActionType = version == 1 ? EnumHelper.GetEnumDescription(AuditActionType.Created) : EnumHelper.GetEnumDescription(AuditActionType.Updated)
            };
        }
        private static LeadHistoryDto BuildAuditModelForPurpose(int version, Dictionary<int, Purpose> value, PropertyInfo property, LeadHistory leadHistory)
        {
            return new LeadHistoryDto()
            {
                FieldName = SplitCamelCase(property.Name),
                FilterKey = LeadHistoryFilterKey.None,
                NewValue = EnumHelper.GetEnumDescription(GetAuditCurrentValue(version, value)),
                OldValue = EnumHelper.GetEnumDescription(GetAuditPreviousValue(version, value)),
                UpdatedBy = GetAuditUpdateByValue(version, leadHistory),
                UpdatedOn = GetAuditUpdatedOnValue(version, leadHistory),
                AuditActionType = version == 1 ? EnumHelper.GetEnumDescription(AuditActionType.Created) : EnumHelper.GetEnumDescription(AuditActionType.Updated)
            };
        }
        private static LeadHistoryDto BuildAuditModelForPosseionType(int version, Dictionary<int, PossesionType> value, PropertyInfo property, LeadHistory leadHistory)
        {
            return new LeadHistoryDto()
            {
                FieldName = SplitCamelCase(property.Name),
                FilterKey = LeadHistoryFilterKey.None,
                NewValue = EnumHelper.GetEnumDescription(GetAuditCurrentValue(version, value)),
                OldValue = EnumHelper.GetEnumDescription(GetAuditPreviousValue(version, value)),
                UpdatedBy = GetAuditUpdateByValue(version, leadHistory),
                UpdatedOn = GetAuditUpdatedOnValue(version, leadHistory),
                AuditActionType = version == 1 ? EnumHelper.GetEnumDescription(AuditActionType.Created) : EnumHelper.GetEnumDescription(AuditActionType.Updated)
            };
        }
        private static LeadHistoryDto BuildAuditModelForLeadSource(int version, Dictionary<int, LeadSource> value, PropertyInfo property, LeadHistory leadHistory)
        {
            return new LeadHistoryDto()
            {
                FieldName = SplitCamelCase(property.Name),
                FilterKey = LeadHistoryFilterKey.None,
                NewValue = EnumHelper.GetEnumDescription(GetAuditCurrentValue(version, value)),
                OldValue = EnumHelper.GetEnumDescription(GetAuditPreviousValue(version, value)),
                UpdatedBy = GetAuditUpdateByValue(version, leadHistory),
                UpdatedOn = GetAuditUpdatedOnValue(version, leadHistory),
                AuditActionType = version == 1 ? EnumHelper.GetEnumDescription(AuditActionType.Created) : EnumHelper.GetEnumDescription(AuditActionType.Updated)
            };
        }
        private static LeadHistoryDto BuildAuditModelForGender(int version, Dictionary<int, Gender> value, PropertyInfo property, LeadHistory leadHistory)
        {
            return new LeadHistoryDto()
            {
                FieldName = SplitCamelCase(property.Name),
                FilterKey = LeadHistoryFilterKey.None,
                NewValue = EnumHelper.GetEnumDescription(GetAuditCurrentValue(version, value)),
                OldValue = EnumHelper.GetEnumDescription(GetAuditPreviousValue(version, value)),
                UpdatedBy = GetAuditUpdateByValue(version, leadHistory),
                UpdatedOn = GetAuditUpdatedOnValue(version, leadHistory),
                AuditActionType = version == 1 ? EnumHelper.GetEnumDescription(AuditActionType.Created) : EnumHelper.GetEnumDescription(AuditActionType.Updated)
            };
        }
        private static LeadHistoryDto BuildAuditModelForMaritalStatus(int version, Dictionary<int, MaritalStatusType> value, PropertyInfo property, LeadHistory leadHistory)
        {
            return new LeadHistoryDto()
            {
                FieldName = SplitCamelCase(property.Name),
                FilterKey = LeadHistoryFilterKey.None,
                NewValue = EnumHelper.GetEnumDescription(GetAuditCurrentValue(version, value)),
                OldValue = EnumHelper.GetEnumDescription(GetAuditPreviousValue(version, value)),
                UpdatedBy = GetAuditUpdateByValue(version, leadHistory),
                UpdatedOn = GetAuditUpdatedOnValue(version, leadHistory),
                AuditActionType = version == 1 ? EnumHelper.GetEnumDescription(AuditActionType.Created) : EnumHelper.GetEnumDescription(AuditActionType.Updated)
            };
        }
        private static LeadHistoryDto BuildAuditModelForContactType(int version, Dictionary<int, ContactType> value, PropertyInfo property, LeadHistory leadHistory)
        {
            return new LeadHistoryDto()
            {
                FieldName = SplitCamelCase(property.Name),
                FilterKey = LeadHistoryFilterKey.None,
                NewValue = EnumHelper.GetEnumDescription(GetAuditCurrentValue(version, value)),
                OldValue = EnumHelper.GetEnumDescription(GetAuditPreviousValue(version, value)),
                UpdatedBy = GetAuditUpdateByValue(version, leadHistory),
                UpdatedOn = GetAuditUpdatedOnValue(version, leadHistory),
                AuditActionType = version == 1 ? EnumHelper.GetEnumDescription(AuditActionType.Created) : EnumHelper.GetEnumDescription(AuditActionType.Updated)
            };
        }
        private static LeadHistoryDto BuildAuditModelForEnquiryType(int version, Dictionary<int, EnquiryType> value, PropertyInfo property, LeadHistory leadHistory)
        {
            return new LeadHistoryDto()
            {
                FieldName = SplitCamelCase(property.Name),
                FilterKey = LeadHistoryFilterKey.None,
                NewValue = EnumHelper.GetEnumDescription(GetAuditCurrentValue(version, value)),
                OldValue = EnumHelper.GetEnumDescription(GetAuditPreviousValue(version, value)),
                UpdatedBy = GetAuditUpdateByValue(version, leadHistory),
                UpdatedOn = GetAuditUpdatedOnValue(version, leadHistory),
                AuditActionType = version == 1 ? EnumHelper.GetEnumDescription(AuditActionType.Created) : EnumHelper.GetEnumDescription(AuditActionType.Updated)
            };
        }
        private static LeadHistoryDto BuildAuditModelForEnum<TEnum>(int version, Dictionary<int, TEnum> value, PropertyInfo property, LeadHistory leadHistory)
       where TEnum : Enum
        {
            if (EnumHelper.GetEnumDescription(GetAuditCurrentValue(version, value)) != "None")
            {
                return new LeadHistoryDto()
                {
                    FieldName = SplitCamelCase(property.Name),
                    FilterKey = LeadHistoryFilterKey.None,
                    NewValue = EnumHelper.GetEnumDescription(GetAuditCurrentValue(version, value)),
                    OldValue = EnumHelper.GetEnumDescription(GetAuditPreviousValue(version, value)),
                    UpdatedBy = GetAuditUpdateByValue(version, leadHistory),
                    UpdatedOn = GetAuditUpdatedOnValue(version, leadHistory),
                    AuditActionType = version == 1
                        ? EnumHelper.GetEnumDescription(AuditActionType.Created)
                        : EnumHelper.GetEnumDescription(AuditActionType.Updated)
                };
            }
            else return null;
        }
        private static LeadHistoryDto BuildAuditModelForSaleType(int version, Dictionary<int, SaleType> value, PropertyInfo property, LeadHistory leadHistory)
        {
            return new LeadHistoryDto()
            {
                FieldName = SplitCamelCase(property.Name),
                FilterKey = LeadHistoryFilterKey.None,
                NewValue = EnumHelper.GetEnumDescription(GetAuditCurrentValue(version, value)),
                OldValue = EnumHelper.GetEnumDescription(GetAuditPreviousValue(version, value)),
                UpdatedBy = GetAuditUpdateByValue(version, leadHistory),
                UpdatedOn = GetAuditUpdatedOnValue(version, leadHistory),
                AuditActionType = version == 1 ? EnumHelper.GetEnumDescription(AuditActionType.Created) : EnumHelper.GetEnumDescription(AuditActionType.Updated)
            };
        }
        private static LeadHistoryDto BuildAuditModelForBHKType(int version, Dictionary<int, BHKType> value, PropertyInfo property, LeadHistory leadHistory)
        {
            return new LeadHistoryDto()
            {
                FieldName = SplitCamelCase(property.Name),
                FilterKey = LeadHistoryFilterKey.None,
                NewValue = EnumHelper.GetEnumDescription(GetAuditCurrentValue(version, value)),
                OldValue = EnumHelper.GetEnumDescription(GetAuditPreviousValue(version, value)),
                UpdatedBy = GetAuditUpdateByValue(version, leadHistory),
                UpdatedOn = GetAuditUpdatedOnValue(version, leadHistory),
                AuditActionType = version == 1 ? EnumHelper.GetEnumDescription(AuditActionType.Created) : EnumHelper.GetEnumDescription(AuditActionType.Updated)
            };
        }
        private static LeadHistoryDto BuildAuditModelForProfession(int version, Dictionary<int, Profession> value, PropertyInfo property, LeadHistory leadHistory)
        {
            return new LeadHistoryDto()
            {
                FieldName = SplitCamelCase(property.Name),
                FilterKey = LeadHistoryFilterKey.None,
                NewValue = EnumHelper.GetEnumDescription(GetAuditCurrentValue(version, value)),
                OldValue = EnumHelper.GetEnumDescription(GetAuditPreviousValue(version, value)),
                UpdatedBy = GetAuditUpdateByValue(version, leadHistory),
                UpdatedOn = GetAuditUpdatedOnValue(version, leadHistory),
                AuditActionType = version == 1 ? EnumHelper.GetEnumDescription(AuditActionType.Created) : EnumHelper.GetEnumDescription(AuditActionType.Updated)
            };
        }
        private static LeadHistoryDto BuildAuditModelForBulkType(int version, Dictionary<int, BulkType> value, PropertyInfo property, LeadHistory leadHistory)
        {
            return new LeadHistoryDto()
            {
                FieldName = SplitCamelCase(property.Name),
                FilterKey = LeadHistoryFilterKey.None,
                NewValue = EnumHelper.GetEnumDescription(GetAuditCurrentValue(version, value)),
                OldValue = EnumHelper.GetEnumDescription(GetAuditPreviousValue(version, value)),
                UpdatedBy = GetAuditUpdateByValue(version, leadHistory),
                UpdatedOn = GetAuditUpdatedOnValue(version, leadHistory),
                AuditActionType = version == 1 ? EnumHelper.GetEnumDescription(AuditActionType.Created) : EnumHelper.GetEnumDescription(AuditActionType.Updated)
            };
        }
        private static LeadHistoryDto BuildAuditModel<T>(int version, Dictionary<int, T> value, PropertyInfo property, LeadHistory leadHistory, IReadRepository<Address>? addressRepo = null)
        {
            LeadHistoryDto leadHistoryDto = new();
            if (property.Name == "MeetingLocation" || property.Name == "SiteLocation")
            {
                leadHistoryDto = new LeadHistoryDto()
                {
                    FieldName = SplitCamelCase(property.Name),
                    FilterKey = LeadHistoryFilterKey.None,
                    NewValue = GetAuditCurrentValue(version, value)?.ToString() ?? string.Empty,
                    OldValue = (typeof(T) == typeof(Enum)) ?
                    EnumHelper.GetEnumDescription((Enum)(object)GetAuditPreviousValue(version, value)) :
                    GetAuditPreviousValue(version, value)?.ToString() ?? string.Empty,

                    UpdatedBy = GetAuditUpdateByValue(version, leadHistory),
                    UpdatedOn = GetAuditUpdatedOnValue(version, leadHistory),
                    AuditActionType = version == 1 ? EnumHelper.GetEnumDescription(AuditActionType.Created) : EnumHelper.GetEnumDescription(AuditActionType.Updated)
                };
                if (!string.IsNullOrEmpty(leadHistoryDto.NewValue) && addressRepo != null)
                {
                    try
                    {
                        Address? address = addressRepo.GetByIdAsync(Guid.Parse(leadHistoryDto.NewValue)).Result;
                        leadHistoryDto.NewValue = JsonConvert.SerializeObject(address);
                    }
                    catch (Exception ex) { }
                }
            }
            else
            {
                var targetType = Nullable.GetUnderlyingType(typeof(T)) ?? typeof(T);
                leadHistoryDto = new LeadHistoryDto()
                {
                    FieldName = (property.Name == "BaseLeadStatus") ?
                "Lead Status" :
                (property.Name == "SubLeadStatus") ?
                "Reason" :
                (property.Name == "CarpetAreaUnit") ?
                "Carpet Area" : (property.Name == "BuiltUpAreaUnit") ?
                "Built Up Area" :
                 (property.Name == "NetAreaUnit") ?"Net Area" :
                 (property.Name == "PropertyAreaUnit") ? "Property Area" :
                (property.Name == "SaleableAreaUnit") ?
                "Saleable Area" : (property.Name == "ChannelPartnerExecutiveName") ?
                "ExecutiveName" : (property.Name == "ChannelPartnerContactNo") ?
                "ExecutiveContactNo" :
                //(property.Name == "UpperBudget") ?
                //"Budget" :
                SplitCamelCase(property.Name),
                    FilterKey = property.Name == "BaseLeadStatus" ? LeadHistoryFilterKey.Status :
                property.Name == "Notes" ? LeadHistoryFilterKey.Notes :
                LeadHistoryFilterKey.None,

                    NewValue = (typeof(T) == typeof(Enum)) ?
                   EnumHelper.GetEnumDescription((Enum)(object)GetAuditCurrentValue(version, value)) :
                   targetType == typeof(DateTime) ? GetCurrentValue(version, value) :
                   GetAuditCurrentValue(version, value)?.ToString() ?? string.Empty,

                    OldValue = (typeof(T) == typeof(Enum)) ?
                    EnumHelper.GetEnumDescription((Enum)(object)GetAuditPreviousValue(version, value)) :
                    targetType == typeof(DateTime) ? GetPreviousValue(version, value) :
                    GetAuditPreviousValue(version, value)?.ToString() ?? string.Empty,

                    UpdatedBy = GetAuditUpdateByValue(version, leadHistory),
                    UpdatedOn = GetAuditUpdatedOnValue(version, leadHistory),
                    AuditActionType = version == 1 ? EnumHelper.GetEnumDescription(AuditActionType.Created) : EnumHelper.GetEnumDescription(AuditActionType.Updated)
                };
                if (property.Name == "CarpetAreaUnit" || property.Name == "CarpetArea")
                {
                    leadHistoryDto = GetUpdatedCarpetArea(leadHistoryDto, property, leadHistory, version);
                }
                if (property.Name == "SaleableAreaUnit" || property.Name == "SaleableArea")
                {
                    leadHistoryDto = GetUpdatedSaleableArea(leadHistoryDto, property, leadHistory, version);
                }
                if (property.Name == "BuiltUpAreaUnit" || property.Name == "BuiltUpArea")
                {
                    leadHistoryDto = GetUpdatedBuiltUpArea(leadHistoryDto, property, leadHistory, version);
                }
                if (property.Name == "NetAreaUnit" || property.Name == "NetAreaArea")
                {
                    leadHistoryDto = GetUpdatedNetArea(leadHistoryDto, property, leadHistory, version);
                }
                if (property.Name == "PropertyAreaUnit" || property.Name == "PropertyArea")
                {
                    leadHistoryDto = GetUpdatedPropertyArea(leadHistoryDto, property, leadHistory, version);
                }
            }
            return leadHistoryDto;
        }
        private static LeadHistoryDto GetUpdatedNetArea(LeadHistoryDto leadHistoryDto, PropertyInfo property, LeadHistory leadHistory, int version)
        {
            if (property.Name == "NetAreaUnit")
            {
                if (leadHistory.NetArea?.Where(i => i.Key == version).Any() ?? false)
                {
                    leadHistoryDto.NewValue = leadHistory.NetArea?.Where(i => i.Key == version).FirstOrDefault().Value + " " + leadHistoryDto.NewValue;
                    leadHistoryDto.OldValue = leadHistory.NetArea?.OrderBy(i => i.Key).LastOrDefault(i => i.Key < version).Value + " " + leadHistoryDto.OldValue;
                }
                else
                {
                    leadHistoryDto.NewValue = leadHistory.NetArea?.OrderBy(i => i.Key).LastOrDefault(i => i.Key < version).Value + " " + leadHistoryDto.NewValue;
                    leadHistoryDto.OldValue = leadHistory.NetArea?.OrderBy(i => i.Key).LastOrDefault(i => i.Key < version).Value + " " + leadHistoryDto.OldValue;
                }
            }
            if (property.Name == "NetArea")
            {
                if (leadHistory.NetAreaUnit?.Where(i => i.Key == version).Any() ?? false)
                {
                    leadHistoryDto.NewValue += " " + leadHistory.NetAreaUnit?.Where(i => i.Key == version).FirstOrDefault().Value;
                    leadHistoryDto.OldValue += " " + leadHistory.NetAreaUnit?.OrderBy(i => i.Key).LastOrDefault(i => i.Key < version).Value;
                }
                else
                {
                    leadHistoryDto.NewValue += " " + leadHistory.NetAreaUnit?.OrderBy(i => i.Key).LastOrDefault(i => i.Key < version).Value;
                    leadHistoryDto.OldValue += " " + leadHistory.NetAreaUnit?.OrderBy(i => i.Key).LastOrDefault(i => i.Key < version).Value;
                }
            }
            return leadHistoryDto;
        }
        private static LeadHistoryDto GetUpdatedPropertyArea(LeadHistoryDto leadHistoryDto, PropertyInfo property, LeadHistory leadHistory, int version)
        {
            if (property.Name == "PropertyAreaUnit")
            {
                if (leadHistory.PropertyArea?.Where(i => i.Key == version).Any() ?? false)
                {
                    leadHistoryDto.NewValue = leadHistory.PropertyArea?.Where(i => i.Key == version).FirstOrDefault().Value + " " + leadHistoryDto.NewValue;
                    leadHistoryDto.OldValue = leadHistory.PropertyArea?.OrderBy(i => i.Key).LastOrDefault(i => i.Key < version).Value + " " + leadHistoryDto.OldValue;
                }
                else
                {
                    leadHistoryDto.NewValue = leadHistory.PropertyArea?.OrderBy(i => i.Key).LastOrDefault(i => i.Key < version).Value + " " + leadHistoryDto.NewValue;
                    leadHistoryDto.OldValue = leadHistory.PropertyArea?.OrderBy(i => i.Key).LastOrDefault(i => i.Key < version).Value + " " + leadHistoryDto.OldValue;
                }
            }
            if (property.Name == "PropertyArea")
            {
                if (leadHistory.PropertyAreaUnit?.Where(i => i.Key == version).Any() ?? false)
                {
                    leadHistoryDto.NewValue += " " + leadHistory.PropertyAreaUnit?.Where(i => i.Key == version).FirstOrDefault().Value;
                    leadHistoryDto.OldValue += " " + leadHistory.PropertyAreaUnit?.OrderBy(i => i.Key).LastOrDefault(i => i.Key < version).Value;
                }
                else
                {
                    leadHistoryDto.NewValue += " " + leadHistory.PropertyAreaUnit?.OrderBy(i => i.Key).LastOrDefault(i => i.Key < version).Value;
                    leadHistoryDto.OldValue += " " + leadHistory.PropertyAreaUnit?.OrderBy(i => i.Key).LastOrDefault(i => i.Key < version).Value;
                }
            }
            return leadHistoryDto;
        }
        private static LeadHistoryDto GetUpdatedCarpetArea(LeadHistoryDto leadHistoryDto, PropertyInfo property, LeadHistory leadHistory, int version)
        {
            if (property.Name == "CarpetAreaUnit")
            {
                if (leadHistory.CarpetArea?.Where(i => i.Key == version).Any() ?? false)
                {
                    leadHistoryDto.NewValue = leadHistory.CarpetArea?.Where(i => i.Key == version).FirstOrDefault().Value + " " + leadHistoryDto.NewValue;
                    leadHistoryDto.OldValue = leadHistory.CarpetArea?.OrderBy(i => i.Key).LastOrDefault(i => i.Key < version).Value + " " + leadHistoryDto.OldValue;
                }
                else
                {
                    leadHistoryDto.NewValue = leadHistory.CarpetArea?.OrderBy(i => i.Key).LastOrDefault(i => i.Key < version).Value + " " + leadHistoryDto.NewValue;
                    leadHistoryDto.OldValue = leadHistory.CarpetArea?.OrderBy(i => i.Key).LastOrDefault(i => i.Key < version).Value + " " + leadHistoryDto.OldValue;
                }
            }
            if (property.Name == "CarpetArea")
            {
                if (leadHistory.CarpetAreaUnit?.Where(i => i.Key == version).Any() ?? false)
                {
                    leadHistoryDto.NewValue += " " + leadHistory.CarpetAreaUnit?.Where(i => i.Key == version).FirstOrDefault().Value;
                    leadHistoryDto.OldValue += " " + leadHistory.CarpetAreaUnit?.OrderBy(i => i.Key).LastOrDefault(i => i.Key < version).Value;
                }
                else
                {
                    leadHistoryDto.NewValue += " " + leadHistory.CarpetAreaUnit?.OrderBy(i => i.Key).LastOrDefault(i => i.Key < version).Value;
                    leadHistoryDto.OldValue += " " + leadHistory.CarpetAreaUnit?.OrderBy(i => i.Key).LastOrDefault(i => i.Key < version).Value;
                }
            }
            return leadHistoryDto;
        }
        private static LeadHistoryDto GetUpdatedBuiltUpArea(LeadHistoryDto leadHistoryDto, PropertyInfo property, LeadHistory leadHistory, int version)
        {
            if (property.Name == "BuiltUpAreaUnit")
            {
                if (leadHistory.BuiltUpArea?.Where(i => i.Key == version).Any() ?? false)
                {
                    leadHistoryDto.NewValue = leadHistory.BuiltUpArea?.Where(i => i.Key == version).FirstOrDefault().Value + " " + leadHistoryDto.NewValue;
                    leadHistoryDto.OldValue = leadHistory.BuiltUpArea?.OrderBy(i => i.Key).LastOrDefault(i => i.Key < version).Value + " " + leadHistoryDto.OldValue;
                }
                else
                {
                    leadHistoryDto.NewValue = leadHistory.BuiltUpArea?.OrderBy(i => i.Key).LastOrDefault(i => i.Key < version).Value + " " + leadHistoryDto.NewValue;
                    leadHistoryDto.OldValue = leadHistory.BuiltUpArea?.OrderBy(i => i.Key).LastOrDefault(i => i.Key < version).Value + " " + leadHistoryDto.OldValue;
                }
            }
            if (property.Name == "BuiltUpArea")
            {
                if (leadHistory.BuiltUpAreaUnit?.Where(i => i.Key == version).Any() ?? false)
                {
                    leadHistoryDto.NewValue += " " + leadHistory.BuiltUpAreaUnit?.Where(i => i.Key == version).FirstOrDefault().Value;
                    leadHistoryDto.OldValue += " " + leadHistory.BuiltUpAreaUnit?.OrderBy(i => i.Key).LastOrDefault(i => i.Key < version).Value;
                }
                else
                {
                    leadHistoryDto.NewValue += " " + leadHistory.BuiltUpAreaUnit?.OrderBy(i => i.Key).LastOrDefault(i => i.Key < version).Value;
                    leadHistoryDto.OldValue += " " + leadHistory.BuiltUpAreaUnit?.OrderBy(i => i.Key).LastOrDefault(i => i.Key < version).Value;
                }
            }
            return leadHistoryDto;
        }
        private static LeadHistoryDto GetUpdatedSaleableArea(LeadHistoryDto leadHistoryDto, PropertyInfo property, LeadHistory leadHistory, int version)
        {
            if (property.Name == "SaleableArea")
            {
                if (leadHistory.SaleableAreaUnit?.Where(i => i.Key == version).Any() ?? false)
                {
                    leadHistoryDto.NewValue = leadHistory.SaleableAreaUnit?.Where(i => i.Key == version).FirstOrDefault().Value + " " + leadHistoryDto.NewValue;
                    leadHistoryDto.OldValue = leadHistory.SaleableAreaUnit?.OrderBy(i => i.Key).LastOrDefault(i => i.Key < version).Value + " " + leadHistoryDto.OldValue;
                }
                else
                {
                    leadHistoryDto.NewValue = leadHistory.SaleableAreaUnit?.OrderBy(i => i.Key).LastOrDefault(i => i.Key < version).Value + " " + leadHistoryDto.NewValue;
                    leadHistoryDto.OldValue = leadHistory.SaleableAreaUnit?.OrderBy(i => i.Key).LastOrDefault(i => i.Key < version).Value + " " + leadHistoryDto.OldValue;
                }
            }
            if (property.Name == "SaleableAreaUnit")
            {
                if (leadHistory.SaleableArea?.Where(i => i.Key == version).Any() ?? false)
                {
                    leadHistoryDto.NewValue += " " + leadHistory.SaleableArea?.Where(i => i.Key == version).FirstOrDefault().Value;
                    leadHistoryDto.OldValue += " " + leadHistory.SaleableArea?.OrderBy(i => i.Key).LastOrDefault(i => i.Key < version).Value;
                }
                else
                {
                    leadHistoryDto.NewValue += " " + leadHistory.SaleableArea?.OrderBy(i => i.Key).LastOrDefault(i => i.Key < version).Value;
                    leadHistoryDto.OldValue += " " + leadHistory.SaleableArea?.OrderBy(i => i.Key).LastOrDefault(i => i.Key < version).Value;
                }
            }
            return leadHistoryDto;
        }
        //private static string GetCurrentBasePropertyType(int version, LeadHistory leadHistory)
        //    => GetAuditCurrentValue<BasePropertyType>(version, leadHistory.PropertyType)?.DisplayName ?? string.Empty;
        //private static string GetPreviousBasePropertyType(int version, LeadHistory leadHistory)
        //    => GetAuditPreviousValue<BasePropertyType>(version, leadHistory.PropertyType)?.DisplayName ?? string.Empty;
        //private static string GetCurrentLeadStatus(int version, LeadHistory leadHistory)
        //    => GetAuditCurrentValue<LeadStatusType>(version, leadHistory.LeadStatus)?.DisplayName ?? string.Empty;

        //private static string GetPreviousLeadStatus(int version, LeadHistory leadHistory)
        //    => GetAuditPreviousValue<LeadStatusType>(version, leadHistory.LeadStatus)?.DisplayName ?? string.Empty;

        private static LeadHistoryDto BuildAuditModelForV1<T>(int version, Dictionary<int, T> value, PropertyInfo property, LeadHistory leadHistory, IReadRepository<Address>? addressRepo = null)
        {
            LeadHistoryDto leadHistoryDto = new();
            var targetType = Nullable.GetUnderlyingType(typeof(T)) ?? typeof(T);
            leadHistoryDto = new LeadHistoryDto()
            {
                FieldName = (property.Name == "BaseLeadStatus") ?
              "Lead Status" :
              (property.Name == "SubLeadStatus") ?
              "Reason" : (property.Name == "ChannelPartnerExecutiveName") ?
              "ExecutiveName" : (property.Name == "ChannelPartnerContactNo") ?
              "ExecutiveContactNo" :
              //(property.Name == "UpperBudget") ?
              //"Budget" :
              SplitCamelCase(property.Name),
                FilterKey = property.Name == "BaseLeadStatus" ? LeadHistoryFilterKey.Status :
              property.Name == "Notes" ? LeadHistoryFilterKey.Notes :
              LeadHistoryFilterKey.None,

                NewValue = (typeof(T) == typeof(Enum)) ?
                EnumHelper.GetEnumDescription((Enum)(object)GetAuditCurrentValue(version, value)) :
                targetType == typeof(DateTime) ? GetCurrentValue(version, value) :
                GetAuditCurrentValue(version, value)?.ToString() ?? string.Empty,

                OldValue = null,

                UpdatedBy = GetAuditUpdateByValue(version, leadHistory),
                UpdatedOn = GetAuditUpdatedOnValue(version, leadHistory),
                AuditActionType = version == 1 ? EnumHelper.GetEnumDescription(AuditActionType.Created) : EnumHelper.GetEnumDescription(AuditActionType.Updated)
            };
            if (property.Name == "CarpetArea")
            {
                leadHistoryDto.NewValue += " " + leadHistory.CarpetAreaUnit?.Where(i => i.Key == version).FirstOrDefault().Value;
                leadHistoryDto.OldValue += " " + leadHistory.CarpetAreaUnit?.OrderBy(i => i.Key).TakeWhile(i => i.Key != version).LastOrDefault().Value;
            }
            if (property.Name == "SaleableArea")
            {
                leadHistoryDto.NewValue += " " + leadHistory.SaleableAreaUnit?.Where(i => i.Key == version).FirstOrDefault().Value;
                leadHistoryDto.OldValue += " " + leadHistory.SaleableAreaUnit?.OrderBy(i => i.Key).TakeWhile(i => i.Key != version).LastOrDefault().Value;
            }
            if (property.Name == "BuiltUpArea")
            {
                leadHistoryDto.NewValue += " " + leadHistory.BuiltUpAreaUnit?.Where(i => i.Key == version).FirstOrDefault().Value;
                leadHistoryDto.OldValue += " " + leadHistory.BuiltUpAreaUnit?.OrderBy(i => i.Key).TakeWhile(i => i.Key != version).LastOrDefault().Value;
            }
            if (property.Name == "PropertyArea")
            {
                leadHistoryDto.NewValue += " " + leadHistory.PropertyAreaUnit?.Where(i => i.Key == version).FirstOrDefault().Value;
                leadHistoryDto.OldValue += " " + leadHistory.PropertyAreaUnit?.OrderBy(i => i.Key).TakeWhile(i => i.Key != version).LastOrDefault().Value;
            }
            if (property.Name == "NetArea")
            {
                leadHistoryDto.NewValue += " " + leadHistory.NetAreaUnit?.Where(i => i.Key == version).FirstOrDefault().Value;
                leadHistoryDto.OldValue += " " + leadHistory.NetAreaUnit?.OrderBy(i => i.Key).TakeWhile(i => i.Key != version).LastOrDefault().Value;
            }
            return leadHistoryDto;
            //var createdVm = new LeadHistoryDto()
            //{
            //    FieldName = "Lead got added",
            //    FilterKey = LeadHistoryFilterKey.None,
            //    NewValue = null,
            //    OldValue = null,
            //    UpdatedBy = GetAuditUpdateByValue(1, leadHistory),
            //    UpdatedOn = GetAuditUpdatedOnValue(1, leadHistory),
            //    AuditActionType = EnumHelper.GetEnumDescription(AuditActionType.Created)
            //};
            //var noteVm = new LeadHistoryDto()
            //{
            //    FieldName = SplitCamelCase(nameof(leadHistory.Notes)),
            //    FilterKey = LeadHistoryFilterKey.Notes,
            //    NewValue = leadHistory.Notes.FirstOrDefault(i => i.Key == 1).Value ?? string.Empty,
            //    OldValue = null,
            //    UpdatedBy = GetAuditUpdateByValue(1, leadHistory),
            //    UpdatedOn = GetAuditUpdatedOnValue(1, leadHistory),
            //    AuditActionType = EnumHelper.GetEnumDescription(AuditActionType.Created)
            //};

            //var assignToVm = new LeadHistoryDto()
            //{
            //    FieldName = SplitCamelCase(nameof(leadHistory.AssignedToUser)),
            //    FilterKey = LeadHistoryFilterKey.None,
            //    NewValue = leadHistory.AssignedToUser?.FirstOrDefault(i => i.Key == 1).Value?.ToString() ?? string.Empty,
            //    OldValue = null,
            //    UpdatedBy = GetAuditUpdateByValue(1, leadHistory),
            //    UpdatedOn = GetAuditUpdatedOnValue(1, leadHistory),
            //    AuditActionType = EnumHelper.GetEnumDescription(AuditActionType.Created)
            //};
            //List<LeadHistoryDto> Vmlist = new List<LeadHistoryDto>();
            //Vmlist.Add(createdVm);
            //if (!string.IsNullOrEmpty(leadHistory.Notes.FirstOrDefault(i => i.Key == 1).Value))
            //    Vmlist.Add(noteVm);
            //if (leadHistory.AssignedToUser?.FirstOrDefault(i => i.Key == 1).Value != null)
            //    Vmlist.Add(assignToVm);
            //return Vmlist;
        }
        public static string SplitCamelCase(string input)
        {
            return Regex.Replace(input, "(?<=[a-z])([A-Z])", " $1", RegexOptions.Compiled).Trim();
        }
        public static LeadHistory LeadHistoryMapper(ViewLeadDto? leadDto, AppointmentType? meetingOrSiteVisit = null, bool? shouldUpdateContactRecord = null)
        {
            var enquiryInfo = leadDto?.Enquiry;
            int version = 1;
            LeadHistory leadHistory = new LeadHistory();
            leadHistory.LeadId = leadDto?.Id ?? Guid.Empty;
            leadHistory.CreatedDate = DateTime.UtcNow;
            leadHistory.ModifiedDate = new Dictionary<int, DateTime>() { { version, DateTime.UtcNow } };
            leadHistory.LastModifiedBy = new Dictionary<int, string>() { { version, leadDto?.LastModifiedByUser?.Name ?? string.Empty } };
            leadHistory.Name = new Dictionary<int, string>() { { version, leadDto?.Name ?? string.Empty } };
            leadHistory.ContactNo = new Dictionary<int, string>() { { version, leadDto?.ContactNo ?? string.Empty } };
            leadHistory.AlternateContactNo = new Dictionary<int, string>() { { version, leadDto?.AlternateContactNo ?? string.Empty } };
            leadHistory.Email = new Dictionary<int, string>() { { version, leadDto?.Email ?? string.Empty } };
            leadHistory.ScheduledDate = new Dictionary<int, DateTime?>() { { version, leadDto?.ScheduledDate } };
            leadHistory.RevertDate = new Dictionary<int, DateTime?>() { { version, leadDto?.RevertDate } };
            leadHistory.LeadNumber = new Dictionary<int, string>() { { version, leadDto?.LeadNumber ?? string.Empty } };
            leadHistory.ChosenProject = new Dictionary<int, string>() { { version, leadDto?.ChosenProject ?? string.Empty } };
            leadHistory.ChosenProperty = new Dictionary<int, string>() { { version, leadDto?.ChosenProperty ?? string.Empty } };
            leadHistory.BookedUnderName = new Dictionary<int, string>() { { version, leadDto?.BookedUnderName ?? string.Empty } };
            leadHistory.ShareCount = new Dictionary<int, int>() { { version, leadDto?.ShareCount ?? 0 } };
            leadHistory.SoldPrice = new Dictionary<int, string>() { { version, leadDto?.SoldPrice ?? string.Empty } };
            leadHistory.Rating = new Dictionary<int, string>() { { version, leadDto?.Rating ?? string.Empty } };
            leadHistory.EnquiredFor = new Dictionary<int, EnquiryType>() { { version, enquiryInfo?.EnquiredFor ?? EnquiryType.None } };
            leadHistory.SaleType = new Dictionary<int, SaleType>() { { version, enquiryInfo?.SaleType ?? SaleType.None } };
            //leadHistory.BasePropertyType = new Dictionary<int, string> { { version, enquiryInfo?.PropertyType?.DisplayName ?? string.Empty } };
            //leadHistory.SubPropertyType = new Dictionary<int, string>() { { version, enquiryInfo?.PropertyType?.ChildType?.DisplayName ?? string.Empty } };
            leadHistory.NoOfBHK = new Dictionary<int, string>() { { version, enquiryInfo?.NoOfBHK.ToString() ?? string.Empty } };
            leadHistory.BHKType = new Dictionary<int, string>() { { version, enquiryInfo?.BHKType.ToString() ?? string.Empty } };
            leadHistory.LowerBudget = new Dictionary<int, long>() { { version, enquiryInfo?.LowerBudget ?? 0 } };
            leadHistory.UpperBudget = new Dictionary<int, long>() { { version, enquiryInfo?.UpperBudget ?? 0 } };
            leadHistory.Area = new Dictionary<int, double>() { { version, enquiryInfo?.Area ?? 0 } };
            leadHistory.AreaUnit = new Dictionary<int, string>() { { version, enquiryInfo?.AreaUnit ?? string.Empty } };
            leadHistory.Notes = new Dictionary<int, string>() { { version, leadDto?.Notes ?? string.Empty } };
            leadHistory.ConfidentialNotes = new Dictionary<int, string>() { { version, leadDto?.ConfidentialNotes ?? string.Empty } };
            leadHistory.EnquiredCity = new Dictionary<int, string>() { { version, enquiryInfo?.Address?.City ?? string.Empty } };
            leadHistory.EnquiredLocation = new Dictionary<int, string>() { { version, (enquiryInfo?.Address?.SubLocality ?? "" + " " + enquiryInfo?.Address?.Locality ?? "").Trim() } };
            leadHistory.EnquiredState = new Dictionary<int, string>() { { version, enquiryInfo?.Address?.State ?? string.Empty } };
            leadHistory.LeadSource = new Dictionary<int, LeadSource>() { { version, enquiryInfo?.LeadSource ?? LeadSource.Direct } };
            leadHistory.BaseLeadStatus = new Dictionary<int, string>() { { version, leadDto?.Status?.DisplayName ?? string.Empty } };
            leadHistory.SubLeadStatus = new Dictionary<int, string>() { { version, leadDto?.Status?.ChildType?.DisplayName ?? string.Empty } };
            leadHistory.IsHighlighted = new Dictionary<int, bool>() { { version, leadDto?.LeadTags?.IsHighlighted ?? false } };
            leadHistory.IsEscalated = new Dictionary<int, bool>() { { version, leadDto?.LeadTags?.IsEscalated ?? false } };
            leadHistory.IsHotLead = new Dictionary<int, bool>() { { version, leadDto?.LeadTags?.IsHotLead ?? false } };
            leadHistory.IsIntegrationLead = new Dictionary<int, bool>() { { version, leadDto?.LeadTags?.IsIntegrationLead ?? false } };
            leadHistory.IsAboutToConvert = new Dictionary<int, bool>() { { version, leadDto?.LeadTags?.IsAboutToConvert ?? false } };
            leadHistory.AssignedTo = new Dictionary<int, Guid>() { { version, leadDto?.AssignTo ?? default } };
            leadHistory.AssignedToUser = new Dictionary<int, string>() { { version, leadDto?.AssignedUser?.Name ?? string.Empty } };
            leadHistory.AssignedFromUser = new Dictionary<int, string>() { { version, leadDto?.AssignedFromUser?.Name ?? string.Empty } };
            leadHistory.IsColdLead = new Dictionary<int, bool>() { { version, leadDto?.LeadTags?.IsColdLead ?? false } };
            leadHistory.IsWarmLead = new Dictionary<int, bool>() { { version, leadDto?.LeadTags?.IsWarmLead ?? false } };
            leadHistory.IsArchived = new Dictionary<int, bool>() { { version, leadDto?.IsArchived ?? default } };
            leadHistory.SecondaryUserId = new Dictionary<int, Guid>() { { version, leadDto?.SecondaryUserId ?? default } };
            leadHistory.SecondaryUser = new Dictionary<int, string>() { { version, leadDto?.SecondaryUser?.Name ?? string.Empty } };
            leadHistory.BookedDate = new Dictionary<int, DateTime?>() { { version, leadDto?.BookedDate ?? null } };
            leadHistory.BookedBy = new Dictionary<int, Guid>() { { version, leadDto?.BookedBy ?? Guid.Empty } };
            leadHistory.BookedByUser = new Dictionary<int, string>() { { version, leadDto?.BookedByUser?.Name ?? string.Empty } };
            leadHistory.SecondaryFromUser = new Dictionary<int, string>() { { version, leadDto?.SecondaryFromUser?.Name ?? string.Empty } };
            if (enquiryInfo?.PropertyTypes?.Any() ?? false)
            {
                leadHistory.BasePropertyType = new Dictionary<int, string> { { version, enquiryInfo?.PropertyType?.DisplayName ?? string.Empty } };
                leadHistory.SubPropertyType = new Dictionary<int, string>() { { version, string.Join(", ", enquiryInfo.PropertyTypes.Where(i => i.ChildType != null).Select(i => i.ChildType?.DisplayName).OrderBy(i => i).ToList()) ?? string.Empty } };
            }
            if (leadDto?.Documents != null)
            {
                leadHistory.Documents = new Dictionary<int, string>() { { version, string.Join(", ", leadDto?.Documents?.Select(i => i.DocumentName).ToList()) ?? string.Empty } };
            }
            if ((shouldUpdateContactRecord ?? false) && leadDto?.ContactRecords != null)
            {
                leadHistory.ContactRecords = new Dictionary<int, ContactType>() { { version, leadDto.ContactRecords.FirstOrDefault().Key } };
            }
            leadHistory.LastModifiedByUser = new Dictionary<int, Guid>() { { version, leadDto?.LastModifiedByUser?.Id ?? Guid.Empty } };
            if (meetingOrSiteVisit != null && meetingOrSiteVisit != AppointmentType.None)
            {
                leadHistory.IsMeetingDone = new Dictionary<int, bool>() { { version, leadDto?.IsMeetingDone ?? false } };
                leadHistory.MeetingLocation = new Dictionary<int, Guid>() { { version, leadDto?.MeetingLocation ?? default } };
                leadHistory.IsSiteVisitDone = new Dictionary<int, bool>() { { version, leadDto?.IsSiteVisitDone ?? false } };
                leadHistory.SiteLocation = new Dictionary<int, Guid>() { { version, leadDto?.SiteLocation ?? default } };
            }
            if (leadDto?.Projects?.Any() ?? false)
            {
                leadHistory.Projects = new Dictionary<int, string>() { { version, string.Join(", ", leadDto?.Projects?.Select(i => i.Name)?.OrderBy(i => i).ToList() ?? new()) ?? string.Empty } };
            }
            if (leadDto?.Properties?.Any() ?? false)
            {
                leadHistory.Properties = new Dictionary<int, string>() { { version, string.Join(", ", leadDto?.Properties?.Select(i => i.Title)?.OrderBy(i => i).ToList() ?? new()) ?? string.Empty } };
            }
            if (leadDto?.CustomFlags?.Any() ?? false)
            {
                leadHistory.CustomFlags = new Dictionary<int, string>() { { version, string.Join(", ", leadDto?.CustomFlags?.Select(i => i?.Flag?.Name ?? string.Empty)?.ToList() ?? new List<string>()) ?? string.Empty } };
            }
            if (leadDto?.Enquiry?.BHKs?.Any() ?? false)
            {
                leadHistory.BHKs = new Dictionary<int, string>() { { version, string.Join(", ", leadDto?.Enquiry?.BHKs?.ToList()) ?? string.Empty } };
            }
            if (leadDto?.Enquiry?.Beds?.Any() ?? false)
            {
                leadHistory.Beds = new Dictionary<int, string>() { { version, string.Join(", ", leadDto?.Enquiry?.Beds?.ToList()) ?? string.Empty } };
            }
            if (leadDto?.Enquiry?.Baths?.Any() ?? false)
            {
                leadHistory.Baths = new Dictionary<int, string>() { { version, string.Join(", ", leadDto?.Enquiry?.Baths?.ToList()) ?? string.Empty } };
            }
            if (leadDto?.Enquiry?.Floors?.Any() ?? false)
            {
                leadHistory.Floors = new Dictionary<int, string>() { { version, string.Join(", ", leadDto?.Enquiry?.Floors?.ToList()) ?? string.Empty } };
            }

            if (leadDto?.Enquiry?.BHKTypes?.Any() ?? false)
            {
                leadHistory.BHKTypes = new Dictionary<int, string>() { { version, string.Join(", ", leadDto?.Enquiry?.BHKTypes?.ToList()) ?? string.Empty } };
            }
            if (leadDto?.Enquiry?.EnquiryTypes?.Any() ?? false)
            {
                leadHistory.EnquiryTypes = new Dictionary<int, string>() { { version, string.Join(", ", leadDto?.Enquiry?.EnquiryTypes?.ToList()) ?? string.Empty } };
            }
            if (leadDto?.Enquiry?.Addresses?.Any() ?? false)
            {
                leadHistory.EnquiredStates = new Dictionary<int, string>() { { version, string.Join(", ", leadDto?.Enquiry?.Addresses?.Where(i => !string.IsNullOrWhiteSpace(i.State))?.Select(i => i.State)?.OrderBy(i => i).ToList()) ?? string.Empty } };
                leadHistory.EnquiredCities = new Dictionary<int, string>() { { version, string.Join(", ", leadDto?.Enquiry?.Addresses?.Where(i => !string.IsNullOrWhiteSpace(i.City))?.Select(i => i.City)?.OrderBy(i => i).ToList()) ?? string.Empty } };
                leadHistory.EnquiredCommunity = new Dictionary<int, string>() { { version, string.Join(", ", leadDto?.Enquiry?.Addresses?.Where(i => !string.IsNullOrWhiteSpace(i.Community))?.Select(i => i.Community)?.OrderBy(i => i).ToList()) ?? string.Empty } };
                leadHistory.EnquiredSubCommunity = new Dictionary<int, string>() { { version, string.Join(", ", leadDto?.Enquiry?.Addresses?.Where(i => !string.IsNullOrWhiteSpace(i.SubCommunity))?.Select(i => i.SubCommunity)?.OrderBy(i => i).ToList()) ?? string.Empty } };
                leadHistory.EnquiredTowerName = new Dictionary<int, string>() { { version, string.Join(", ", leadDto?.Enquiry?.Addresses?.Where(i => !string.IsNullOrWhiteSpace(i.TowerName))?.Select(i => i.TowerName)?.OrderBy(i => i).ToList()) ?? string.Empty } };
                leadHistory.EnquiredLocations = new Dictionary<int, string>() { { version, string.Join(", ", leadDto?.Enquiry?.Addresses?.Where(i => !string.IsNullOrWhiteSpace(i.SubLocality) || !string.IsNullOrWhiteSpace(i.Locality))?.OrderBy(i => i.SubLocality).Select(i => i.SubLocality ?? "" + " " + i.Locality ?? "")?.OrderBy(i => i).ToList()) ?? string.Empty } };
                leadHistory.EnquiredCountry = new Dictionary<int, string>() { { version, string.Join(", ", leadDto?.Enquiry?.Addresses?.Where(i => !string.IsNullOrWhiteSpace(i.Country))?.Select(i => i.Country)?.OrderBy(i => i).ToList()) ?? string.Empty } };

            }
            leadHistory.SubSource = new Dictionary<int, string>() { { version, enquiryInfo?.SubSource ?? string.Empty } };
            leadHistory.CarpetArea = new Dictionary<int, double>() { { version, enquiryInfo?.CarpetArea ?? default } };
            leadHistory.ConversionFactor = new Dictionary<int, float>() { { version, enquiryInfo?.ConversionFactor ?? default } };
            leadHistory.CarpetAreaUnit = new Dictionary<int, string>() { { version, enquiryInfo?.CarpetAreaUnit ?? string.Empty } };
            leadHistory.PossessionDate = new Dictionary<int, DateTime?>() { { version, enquiryInfo?.PossessionDate } };
            leadHistory.ReferralContactNo = new Dictionary<int, string>() { { version, leadDto?.ReferralContactNo ?? string.Empty } };
            leadHistory.ReferralName = new Dictionary<int, string>() { { version, leadDto?.ReferralName ?? string.Empty } };
            leadHistory.AgencyName = new Dictionary<int, string>() { { version, leadDto?.AgencyName ?? string.Empty } };
            leadHistory.CompanyName = new Dictionary<int, string>() { { version, leadDto?.CompanyName ?? string.Empty } };
            leadHistory.PickedDate = new Dictionary<int, DateTime?>() { { version, leadDto?.PickedDate ?? null } };
            leadHistory.IsPicked = new Dictionary<int, bool>() { { version, leadDto?.IsPicked ?? false } };
            leadHistory.DuplicateLeadVersion = leadDto?.DuplicateLeadVersion ?? default;
            leadHistory.SourcingManagerUser = new Dictionary<int, string>() { { version, leadDto?.SourcingManagerUser?.Name ?? string.Empty } };
            leadHistory.ClosingManagerUser = new Dictionary<int, string>() { { version, leadDto?.ClosingManagerUser?.Name ?? string.Empty } };
            leadHistory.Profession = new Dictionary<int, Profession>() { { version, leadDto?.Profession ?? Profession.None } };
            leadHistory.CustomerCity = new Dictionary<int, string>() { { version, leadDto?.Address?.City ?? string.Empty } };
            leadHistory.CustomerState = new Dictionary<int, string>() { { version, leadDto?.Address?.State ?? string.Empty } };
            leadHistory.CustomerLocation = new Dictionary<int, string>() { { version, leadDto?.Address?.SubLocality ?? string.Empty } };
            leadHistory.ChannelPartnerName = new Dictionary<int, string>() { { version, leadDto?.ChannelPartnerName ?? string.Empty } };
            leadHistory.ChannelPartnerExecutiveName = new Dictionary<int, string>() { { version, leadDto?.ChannelPartnerExecutiveName ?? string.Empty } };
            leadHistory.ChannelPartnerContactNo = new Dictionary<int, string>() { { version, leadDto?.ChannelPartnerContactNo ?? string.Empty } };
            leadHistory.ReferralEmail = new Dictionary<int, string>() { { version, leadDto?.ReferralEmail ?? string.Empty } };
            leadHistory.CustomerCommunity = new Dictionary<int, string>() { { version, leadDto?.Address?.Community ?? string.Empty } };
            leadHistory.CustomerSubCommunity = new Dictionary<int, string>() { { version, leadDto?.Address?.SubCommunity ?? string.Empty } };
            leadHistory.CustomerTowerName = new Dictionary<int, string>() { { version, leadDto?.Address?.TowerName ?? string.Empty } };
            leadHistory.CustomerCountry = new Dictionary<int, string>() { { version, leadDto?.Address?.Country ?? string.Empty } };

            
            if (leadDto?.ChannelPartners?.Any() ?? false)
            {
                leadHistory.ChannelPartners = new Dictionary<int, string>() { { version, string.Join(", ", (leadDto?.ChannelPartners?.Select(i => i.FirmName)?.OrderBy(i => i)?.ToList()) ?? new()) ?? string.Empty } };
            }
            if (leadDto?.Agencies?.Any() ?? false)
            {
                leadHistory.Agencies = new Dictionary<int, string>() { { version, string.Join(", ", leadDto?.Agencies?.Select(i => i.Name)?.OrderBy(i => i).ToList() ?? new()) ?? string.Empty } };
            }
            if (leadDto?.Campaigns?.Any() ?? false)
            {
                leadHistory.Campaigns = new Dictionary<int, string>() { { version, string.Join(", ", leadDto?.Campaigns?.Select(i => i.Name)?.OrderBy(i => i).ToList() ?? new()) ?? string.Empty } };
            }
            leadHistory.Designation = new Dictionary<int, string>() { { version, leadDto?.Designation ?? string.Empty } };
            leadHistory.BulkCategory = new Dictionary<int, BulkType>() { { version, leadDto?.BulkCategory ?? BulkType.None } };
            if (enquiryInfo?.OfferType != null)
            {
                leadHistory.OfferType = new Dictionary<int, OfferType>() { { version, enquiryInfo?.OfferType ?? OfferType.None } };
            }
            if (enquiryInfo?.Furnished != null)
            {
                leadHistory.Furnished = new Dictionary<int, FurnishStatus>() { { version, enquiryInfo?.Furnished ?? FurnishStatus.Unknown } };
            }
            leadHistory.BuiltUpArea = new Dictionary<int, double>() { { version, enquiryInfo?.BuiltUpArea ?? default } };
            leadHistory.BuiltUpAreaConversionFactor = new Dictionary<int, float>() { { version, enquiryInfo?.BuiltUpAreaConversionFactor ?? default } };
            leadHistory.BuiltUpAreaUnit = new Dictionary<int, string>() { { version, enquiryInfo?.BuiltUpAreaUnit ?? string.Empty } };
            leadHistory.SaleableArea = new Dictionary<int, double>() { { version, enquiryInfo?.SaleableArea ?? default } };
            leadHistory.SaleableAreaConversionFactor = new Dictionary<int, float>() { { version, enquiryInfo?.SaleableAreaConversionFactor ?? default } };
            leadHistory.SaleableAreaUnit = new Dictionary<int, string>() { { version, enquiryInfo?.SaleableAreaUnit ?? string.Empty } };
            leadHistory.CurrentVersion = version;
            leadHistory.DuplicateLeadVersion = leadDto?.DuplicateLeadVersion ?? default;
            leadHistory.Currency = new Dictionary<int, string>() { { version, enquiryInfo?.Currency ?? string.Empty } };
            leadHistory.PropertyArea = new Dictionary<int, double>() { { version, enquiryInfo?.PropertyArea ?? default } };
            leadHistory.PropertyAreaConversionFactor = new Dictionary<int, float>() { { version, enquiryInfo?.PropertyAreaConversionFactor ?? default } };
            leadHistory.PropertyAreaUnit = new Dictionary<int, string>() { { version, enquiryInfo?.PropertyAreaUnit ?? string.Empty } };
            leadHistory.NetArea = new Dictionary<int, double>() { { version, enquiryInfo?.NetArea ?? default } };
            leadHistory.NetAreaConversionFactor = new Dictionary<int, float>() { { version, enquiryInfo?.NetAreaConversionFactor ?? default } };
            leadHistory.NetAreaUnit = new Dictionary<int, string>() { { version, enquiryInfo?.NetAreaUnit ?? string.Empty } };
            leadHistory.UnitName = new Dictionary<int, string>() { { version, enquiryInfo?.UnitName ?? string.Empty } };
            leadHistory.ClusterName = new Dictionary<int, string>() { { version, enquiryInfo?.ClusterName ?? string.Empty } };
            leadHistory.Nationality = new Dictionary<int, string>() { { version, leadDto?.Nationality ?? string.Empty } };
            leadHistory.CurrentVersion = version;
            leadHistory.UserId = leadDto?.AssignTo ?? Guid.Empty;
            leadHistory.Purpose = new Dictionary<int, Purpose>() { { version, enquiryInfo?.Purpose ?? Purpose.None } };
            leadHistory.PossesionType = new Dictionary<int, PossesionType>() { { version, leadDto?.PossesionType ?? PossesionType.None } };
            leadHistory.LandLine = new Dictionary<int, string>() { { version, leadDto?.LandLine ?? string.Empty } };
            leadHistory.AppointmentDoneOn = new Dictionary<int, DateTime?>() { { version, leadDto?.AppointmentDoneOn } };

            leadHistory.Gender = new Dictionary<int, Gender>() { { version, leadDto?.Gender ?? Gender.NotMentioned } };
            leadHistory.MaritalStatus = new Dictionary<int, MaritalStatusType>() { { version, leadDto?.MaritalStatus ?? MaritalStatusType.NotMentioned } };
            leadHistory.DateOfBirth = new Dictionary<int, DateTime?>() { { version, leadDto?.DateOfBirth ?? null } };

            return leadHistory;
        }
        public static LeadHistory GetUpdatedLeadHistory(LeadHistory existingLeadHistory, LeadHistory newLeadHistory, AppointmentType? meetingOrSiteVisit = null, bool shouldUpdateNotes = false)
        {
            int version = existingLeadHistory.CurrentVersion + 1;
            try
            {
                if (existingLeadHistory?.BulkCategory?.LastOrDefault().Value != newLeadHistory?.BulkCategory?.FirstOrDefault().Value || ((newLeadHistory?.BulkCategory?.FirstOrDefault().Value == BulkType.BulkAssignment) && (newLeadHistory?.AssignedTo?.FirstOrDefault().Value != existingLeadHistory?.AssignedTo?.LastOrDefault().Value) && (existingLeadHistory?.BulkCategory?.LastOrDefault().Value != newLeadHistory?.BulkCategory?.FirstOrDefault().Value)))
                {
                    if (existingLeadHistory.BulkCategory != null)
                    {
                        existingLeadHistory.BulkCategory.Add(version, newLeadHistory.BulkCategory.FirstOrDefault().Value);
                    }
                    else
                    {
                        existingLeadHistory.BulkCategory = new Dictionary<int, BulkType>() { { version, newLeadHistory?.BulkCategory?.FirstOrDefault().Value ?? BulkType.None } };
                    }
                }
                if (!(string.IsNullOrEmpty(existingLeadHistory.Name.LastOrDefault().Value) && string.IsNullOrEmpty(newLeadHistory.Name.FirstOrDefault().Value)) && existingLeadHistory.Name.LastOrDefault().Value != newLeadHistory.Name.FirstOrDefault().Value)
                {
                    if (existingLeadHistory.Name != null)
                    {
                        existingLeadHistory.Name.Add(version, newLeadHistory.Name.FirstOrDefault().Value);
                    }
                    else
                    {
                        existingLeadHistory.Name = new Dictionary<int, string>() { { version, newLeadHistory.Name.FirstOrDefault().Value } };
                    }
                }
                if (!(string.IsNullOrEmpty(existingLeadHistory.ContactNo.LastOrDefault().Value) && string.IsNullOrEmpty(newLeadHistory.ContactNo.FirstOrDefault().Value)) && existingLeadHistory.ContactNo.LastOrDefault().Value != newLeadHistory.ContactNo.FirstOrDefault().Value)
                {
                    if (existingLeadHistory.ContactNo != null)
                    {
                        existingLeadHistory.ContactNo.Add(version, newLeadHistory.ContactNo.FirstOrDefault().Value);
                    }
                    else
                    {
                        existingLeadHistory.ContactNo = new Dictionary<int, string>() { { version, newLeadHistory.ContactNo.FirstOrDefault().Value } };
                    }
                }
                if (!(string.IsNullOrEmpty(existingLeadHistory.AlternateContactNo?.LastOrDefault().Value) && string.IsNullOrEmpty(newLeadHistory.AlternateContactNo?.FirstOrDefault().Value)) && existingLeadHistory.AlternateContactNo?.LastOrDefault().Value != newLeadHistory.AlternateContactNo?.FirstOrDefault().Value)
                {
                    if (existingLeadHistory.AlternateContactNo != null)
                    {
                        existingLeadHistory.AlternateContactNo.Add(version, newLeadHistory.AlternateContactNo?.FirstOrDefault().Value ?? string.Empty);
                    }
                    else
                    {
                        existingLeadHistory.AlternateContactNo = new Dictionary<int, string>() { { version, newLeadHistory?.AlternateContactNo?.FirstOrDefault().Value ?? string.Empty } };
                    }
                }
                //if (existingLeadHistory.LastModifiedBy?.LastOrDefault().Value != newLeadHistory.LastModifiedBy?.FirstOrDefault().Value)
                //{
                //    existingLeadHistory.LastModifiedBy.Add(version, newLeadHistory.LastModifiedBy.FirstOrDefault().Value);
                //}
                existingLeadHistory.LastModifiedBy.Add(version, newLeadHistory.LastModifiedBy.FirstOrDefault().Value);

                if (existingLeadHistory.ModifiedDate?.LastOrDefault().Value != newLeadHistory.ModifiedDate?.FirstOrDefault().Value)
                {
                    if (existingLeadHistory.ModifiedDate != null)
                    {
                        existingLeadHistory.ModifiedDate.Add(version, newLeadHistory.ModifiedDate.FirstOrDefault().Value);
                    }
                    else
                    {
                        existingLeadHistory.ModifiedDate = new Dictionary<int, DateTime>() { { version, newLeadHistory.ModifiedDate.FirstOrDefault().Value } };
                    };
                }

                if (!(string.IsNullOrEmpty(existingLeadHistory.Email.LastOrDefault().Value) && string.IsNullOrEmpty(newLeadHistory.Email.FirstOrDefault().Value)) && existingLeadHistory.Email.LastOrDefault().Value != newLeadHistory.Email.FirstOrDefault().Value)
                {
                    if (existingLeadHistory.Email != null)
                    {
                        existingLeadHistory.Email.Add(version, newLeadHistory.Email.FirstOrDefault().Value);
                    }
                    else
                    {
                        existingLeadHistory.Email = new Dictionary<int, string>() { { version, newLeadHistory.Email.FirstOrDefault().Value } };
                    }
                }
                if (existingLeadHistory.ScheduledDate?.LastOrDefault().Value != newLeadHistory.ScheduledDate?.FirstOrDefault().Value)
                {
                    if (existingLeadHistory.ScheduledDate != null)
                    {
                        existingLeadHistory.ScheduledDate.Add(version, newLeadHistory.ScheduledDate?.FirstOrDefault().Value);
                    }
                    else
                    {
                        existingLeadHistory.ScheduledDate = new Dictionary<int, DateTime?>() { { version, newLeadHistory.ScheduledDate?.FirstOrDefault().Value } };
                    }
                }
                if (existingLeadHistory.RevertDate?.LastOrDefault().Value != newLeadHistory.RevertDate?.FirstOrDefault().Value)
                {
                    if (existingLeadHistory.RevertDate != null)
                    {
                        existingLeadHistory.RevertDate.Add(version, newLeadHistory.RevertDate?.FirstOrDefault().Value);
                    }
                    else
                    {
                        existingLeadHistory.RevertDate = new Dictionary<int, DateTime?>() { { version, newLeadHistory.RevertDate?.FirstOrDefault().Value } };
                    }
                }
                if (!(string.IsNullOrEmpty(existingLeadHistory.LeadNumber.LastOrDefault().Value) && string.IsNullOrEmpty(newLeadHistory.LeadNumber.FirstOrDefault().Value)) && existingLeadHistory.LeadNumber.LastOrDefault().Value != newLeadHistory.LeadNumber.FirstOrDefault().Value)
                {
                    if (existingLeadHistory.LeadNumber != null)
                    {
                        existingLeadHistory.LeadNumber.Add(version, newLeadHistory.LeadNumber.FirstOrDefault().Value);
                    }
                    else
                    {
                        existingLeadHistory.LeadNumber = new Dictionary<int, string>() { { version, newLeadHistory.LeadNumber.FirstOrDefault().Value } };
                    }
                }
                if (!(string.IsNullOrEmpty(existingLeadHistory.ChosenProject.LastOrDefault().Value) && string.IsNullOrEmpty(newLeadHistory.ChosenProject.FirstOrDefault().Value)) && existingLeadHistory.ChosenProject.LastOrDefault().Value != newLeadHistory.ChosenProject.FirstOrDefault().Value)
                {
                    if (existingLeadHistory.ChosenProject != null)
                    {
                        existingLeadHistory.ChosenProject.Add(version, newLeadHistory.ChosenProject.FirstOrDefault().Value);
                    }
                    else
                    {
                        existingLeadHistory.ChosenProject = new Dictionary<int, string>() { { version, newLeadHistory.ChosenProject.FirstOrDefault().Value } };
                    }
                }
                if (!(string.IsNullOrEmpty(existingLeadHistory.ChosenProperty.LastOrDefault().Value) && string.IsNullOrEmpty(newLeadHistory.ChosenProperty.FirstOrDefault().Value)) && existingLeadHistory.ChosenProperty.LastOrDefault().Value != newLeadHistory.ChosenProperty.FirstOrDefault().Value)
                {
                    if (existingLeadHistory.ChosenProperty != null)
                    {
                        existingLeadHistory.ChosenProperty.Add(version, newLeadHistory.ChosenProperty.FirstOrDefault().Value);
                    }
                    else
                    {
                        existingLeadHistory.ChosenProperty = new Dictionary<int, string>() { { version, newLeadHistory.ChosenProperty.FirstOrDefault().Value } };
                    }
                }

                if (!(string.IsNullOrEmpty(existingLeadHistory?.BookedUnderName?.LastOrDefault().Value) && string.IsNullOrEmpty(newLeadHistory?.BookedUnderName?.FirstOrDefault().Value)) && existingLeadHistory?.BookedUnderName?.LastOrDefault().Value != newLeadHistory?.BookedUnderName?.FirstOrDefault().Value)
                {
                    if (existingLeadHistory.BookedUnderName != null)
                    {
                        existingLeadHistory.BookedUnderName.Add(version, newLeadHistory.BookedUnderName.FirstOrDefault().Value);
                    }
                    else
                    {
                        existingLeadHistory.BookedUnderName = new Dictionary<int, string>() { { version, newLeadHistory.BookedUnderName.FirstOrDefault().Value } };
                    }
                }
                if (existingLeadHistory.ShareCount.LastOrDefault().Value != newLeadHistory.ShareCount.FirstOrDefault().Value)
                {
                    if (existingLeadHistory.ShareCount != null)
                    {
                        existingLeadHistory.ShareCount.Add(version, newLeadHistory.ShareCount.FirstOrDefault().Value);
                    }
                    else
                    {
                        existingLeadHistory.ShareCount = new Dictionary<int, int>() { { version, newLeadHistory?.ShareCount?.FirstOrDefault().Value ?? default } };
                    }
                }
                if (!(string.IsNullOrEmpty(existingLeadHistory.Rating.LastOrDefault().Value) && string.IsNullOrEmpty(newLeadHistory.Rating.FirstOrDefault().Value)) && existingLeadHistory.Rating.LastOrDefault().Value != newLeadHistory.Rating.FirstOrDefault().Value)
                {
                    if (existingLeadHistory.Rating != null)
                    {
                        existingLeadHistory.Rating.Add(version, newLeadHistory.Rating.FirstOrDefault().Value);
                    }
                    else
                    {
                        existingLeadHistory.Rating = new Dictionary<int, string>() { { version, newLeadHistory.Rating.FirstOrDefault().Value } };
                    }
                }
                if (existingLeadHistory.EnquiredFor.LastOrDefault().Value != newLeadHistory.EnquiredFor.FirstOrDefault().Value)
                {
                    if (existingLeadHistory.EnquiredFor != null)
                    {
                        existingLeadHistory.EnquiredFor.Add(version, newLeadHistory.EnquiredFor.FirstOrDefault().Value);
                    }
                    else
                    {
                        existingLeadHistory.EnquiredFor = new Dictionary<int, EnquiryType>() { { version, newLeadHistory?.EnquiredFor.FirstOrDefault().Value ?? EnquiryType.None } };
                    }
                }
                if (existingLeadHistory.SaleType.LastOrDefault().Value != newLeadHistory.SaleType.FirstOrDefault().Value)
                {
                    if (existingLeadHistory.SaleType != null)
                    {
                        existingLeadHistory.SaleType.Add(version, newLeadHistory.SaleType.FirstOrDefault().Value);
                    }
                    else
                    {
                        existingLeadHistory.SaleType = new Dictionary<int, SaleType>() { { version, newLeadHistory?.SaleType.FirstOrDefault().Value ?? SaleType.None } };
                    }
                }
                if (!(string.IsNullOrEmpty(existingLeadHistory?.BasePropertyType?.LastOrDefault().Value) && string.IsNullOrEmpty(newLeadHistory?.BasePropertyType?.FirstOrDefault().Value)) && existingLeadHistory?.BasePropertyType?.LastOrDefault().Value != newLeadHistory?.BasePropertyType?.FirstOrDefault().Value)
                {
                    if (existingLeadHistory?.BasePropertyType != null)
                    {
                        existingLeadHistory?.BasePropertyType.Add(version, newLeadHistory?.BasePropertyType?.FirstOrDefault().Value);
                    }
                    else
                    {
                        existingLeadHistory.BasePropertyType = new Dictionary<int, string>() { { version, newLeadHistory?.BasePropertyType?.FirstOrDefault().Value } };
                    }
                }
                if (!(string.IsNullOrEmpty(existingLeadHistory?.SubPropertyType?.LastOrDefault().Value) && string.IsNullOrEmpty(newLeadHistory?.SubPropertyType?.FirstOrDefault().Value)) && existingLeadHistory?.SubPropertyType?.LastOrDefault().Value != newLeadHistory?.SubPropertyType?.FirstOrDefault().Value)
                {
                    if (existingLeadHistory?.SubPropertyType != null)
                    {
                        existingLeadHistory?.SubPropertyType.Add(version, newLeadHistory?.SubPropertyType?.FirstOrDefault().Value);
                    }
                    else
                    {
                        existingLeadHistory.SubPropertyType = new Dictionary<int, string>() { { version, newLeadHistory?.SubPropertyType?.FirstOrDefault().Value } };
                    }
                }
                if (!(string.IsNullOrEmpty(existingLeadHistory.BHKType?.LastOrDefault().Value) && string.IsNullOrEmpty(newLeadHistory.BHKType?.FirstOrDefault().Value)) && existingLeadHistory.BHKType?.LastOrDefault().Value != newLeadHistory?.BHKType.FirstOrDefault().Value)
                {
                    if (existingLeadHistory.BHKType != null)
                    {
                        existingLeadHistory.BHKType.Add(version, newLeadHistory.BHKType.FirstOrDefault().Value);
                    }
                    else
                    {
                        existingLeadHistory.BHKType = new Dictionary<int, string> { { version, newLeadHistory.BHKType.FirstOrDefault().Value } };
                    }
                }
                if (!(string.IsNullOrEmpty(existingLeadHistory.NoOfBHK?.LastOrDefault().Value) && string.IsNullOrEmpty(newLeadHistory.NoOfBHK?.FirstOrDefault().Value)) && existingLeadHistory.NoOfBHK?.LastOrDefault().Value != newLeadHistory.NoOfBHK?.FirstOrDefault().Value)
                {
                    if (existingLeadHistory.NoOfBHK != null)
                    {
                        existingLeadHistory.NoOfBHK.Add(version, newLeadHistory.NoOfBHK.FirstOrDefault().Value);
                    }
                    else
                    {
                        existingLeadHistory.NoOfBHK = new Dictionary<int, string> { { version, newLeadHistory.NoOfBHK.FirstOrDefault().Value } };
                    }
                }
                if (!(string.IsNullOrEmpty(existingLeadHistory?.BHKs?.LastOrDefault().Value) && string.IsNullOrEmpty(newLeadHistory?.BHKs?.FirstOrDefault().Value)) && existingLeadHistory?.BHKs?.LastOrDefault().Value != newLeadHistory?.BHKs?.FirstOrDefault().Value)
                {
                    if (existingLeadHistory?.BHKs != null)
                    {
                        existingLeadHistory.BHKs.Add(version, newLeadHistory?.BHKs?.FirstOrDefault().Value);
                    }
                    else
                    {
                        existingLeadHistory.BHKs = new Dictionary<int, string>() { { version, newLeadHistory?.BHKs?.FirstOrDefault().Value } };
                    }
                }
                if (!(string.IsNullOrEmpty(existingLeadHistory?.Beds?.LastOrDefault().Value) && string.IsNullOrEmpty(newLeadHistory?.Beds?.FirstOrDefault().Value)) && existingLeadHistory?.Beds?.LastOrDefault().Value != newLeadHistory?.Beds?.FirstOrDefault().Value)
                {
                    if (existingLeadHistory?.Beds != null)
                    {
                        existingLeadHistory.Beds.Add(version, newLeadHistory?.Beds?.FirstOrDefault().Value);
                    }
                    else
                    {
                        existingLeadHistory.Beds = new Dictionary<int, string>() { { version, newLeadHistory?.Beds?.FirstOrDefault().Value } };
                    }
                }
                if (!(string.IsNullOrEmpty(existingLeadHistory?.Baths?.LastOrDefault().Value) && string.IsNullOrEmpty(newLeadHistory?.Baths?.FirstOrDefault().Value)) && existingLeadHistory?.Baths?.LastOrDefault().Value != newLeadHistory?.Baths?.FirstOrDefault().Value)
                {
                    if (existingLeadHistory?.Baths != null)
                    {
                        existingLeadHistory.Baths.Add(version, newLeadHistory?.Baths?.FirstOrDefault().Value);
                    }
                    else
                    {
                        existingLeadHistory.Baths = new Dictionary<int, string>() { { version, newLeadHistory?.Baths?.FirstOrDefault().Value } };
                    }
                }
                if (!(string.IsNullOrEmpty(existingLeadHistory?.Floors?.LastOrDefault().Value) && string.IsNullOrEmpty(newLeadHistory?.Floors?.FirstOrDefault().Value)) && existingLeadHistory?.Floors?.LastOrDefault().Value != newLeadHistory?.Floors?.FirstOrDefault().Value)
                {
                    if (existingLeadHistory?.Floors != null)
                    {
                        existingLeadHistory.Floors.Add(version, newLeadHistory?.Floors?.FirstOrDefault().Value);
                    }
                    else
                    {
                        existingLeadHistory.Floors = new Dictionary<int, string>() { { version, newLeadHistory?.Floors?.FirstOrDefault().Value } };
                    }
                }


                if (!(string.IsNullOrEmpty(existingLeadHistory?.EnquiredCommunity?.LastOrDefault().Value) && string.IsNullOrEmpty(newLeadHistory?.EnquiredCommunity?.FirstOrDefault().Value)) && existingLeadHistory?.EnquiredCommunity?.LastOrDefault().Value != newLeadHistory?.EnquiredCommunity?.FirstOrDefault().Value)
                {
                    if (existingLeadHistory.EnquiredCommunity != null)
                    {
                        existingLeadHistory.EnquiredCommunity.Add(version, newLeadHistory?.EnquiredCommunity?.FirstOrDefault().Value);
                    }
                    else
                    {
                        existingLeadHistory.EnquiredCommunity = new Dictionary<int, string>() { { version, newLeadHistory?.EnquiredCommunity?.FirstOrDefault().Value } };
                    }
                }
                if (!(string.IsNullOrEmpty(existingLeadHistory?.EnquiredSubCommunity?.LastOrDefault().Value) && string.IsNullOrEmpty(newLeadHistory?.EnquiredSubCommunity?.FirstOrDefault().Value)) && existingLeadHistory?.EnquiredSubCommunity?.LastOrDefault().Value != newLeadHistory?.EnquiredSubCommunity?.FirstOrDefault().Value)
                {
                    if (existingLeadHistory.EnquiredSubCommunity != null)
                    {
                        existingLeadHistory.EnquiredSubCommunity.Add(version, newLeadHistory?.EnquiredSubCommunity?.FirstOrDefault().Value);
                    }
                    else
                    {
                        existingLeadHistory.EnquiredSubCommunity = new Dictionary<int, string>() { { version, newLeadHistory?.EnquiredSubCommunity?.FirstOrDefault().Value } };
                    }
                }

                if (!(string.IsNullOrEmpty(existingLeadHistory?.EnquiredCountry?.LastOrDefault().Value) && string.IsNullOrEmpty(newLeadHistory?.EnquiredCountry?.FirstOrDefault().Value)) && existingLeadHistory?.EnquiredCountry?.LastOrDefault().Value != newLeadHistory?.EnquiredCountry?.FirstOrDefault().Value)
                {
                    if (existingLeadHistory.EnquiredCountry != null)
                    {
                        existingLeadHistory?.EnquiredCountry.Add(version, newLeadHistory?.EnquiredCountry?.FirstOrDefault().Value);
                    }
                    else
                    {
                        existingLeadHistory.EnquiredCountry = new Dictionary<int, string> { { version, newLeadHistory?.EnquiredCountry?.FirstOrDefault().Value } };
                    }
                }
                if (!(string.IsNullOrEmpty(existingLeadHistory?.CustomerCountry?.LastOrDefault().Value) && string.IsNullOrEmpty(newLeadHistory?.CustomerCountry?.FirstOrDefault().Value)) && existingLeadHistory?.CustomerCountry?.LastOrDefault().Value != newLeadHistory?.CustomerCountry?.FirstOrDefault().Value)
                {
                    if (existingLeadHistory.CustomerCountry != null)
                    {
                        existingLeadHistory.CustomerCountry.Add(version, newLeadHistory?.CustomerCountry?.FirstOrDefault().Value);
                    }
                    else
                    {
                        existingLeadHistory.CustomerCountry = new Dictionary<int, string> { { version, newLeadHistory?.CustomerCountry?.FirstOrDefault().Value } };
                    }
                }
                if (!(string.IsNullOrEmpty(existingLeadHistory?.EnquiredTowerName?.LastOrDefault().Value) && string.IsNullOrEmpty(newLeadHistory?.EnquiredTowerName?.FirstOrDefault().Value)) && existingLeadHistory?.EnquiredTowerName?.LastOrDefault().Value != newLeadHistory?.EnquiredTowerName?.FirstOrDefault().Value)
                {
                    if (existingLeadHistory.EnquiredTowerName != null)
                    {
                        existingLeadHistory.EnquiredTowerName.Add(version, newLeadHistory?.EnquiredTowerName?.FirstOrDefault().Value);
                    }
                    else
                    {
                        existingLeadHistory.EnquiredTowerName = new Dictionary<int, string>() { { version, newLeadHistory?.EnquiredTowerName?.FirstOrDefault().Value } };
                    }
                }


                if (!(string.IsNullOrEmpty(existingLeadHistory?.CustomerCommunity?.LastOrDefault().Value) && string.IsNullOrEmpty(newLeadHistory?.CustomerCommunity?.FirstOrDefault().Value)) && existingLeadHistory?.CustomerCommunity?.LastOrDefault().Value != newLeadHistory?.CustomerCommunity?.FirstOrDefault().Value)
                {
                    if (existingLeadHistory.CustomerCommunity != null)
                    {
                        existingLeadHistory.CustomerCommunity.Add(version, newLeadHistory?.CustomerCommunity?.FirstOrDefault().Value);
                    }
                    else
                    {
                        existingLeadHistory.CustomerCommunity = new Dictionary<int, string>() { { version, newLeadHistory?.CustomerCommunity?.FirstOrDefault().Value } };
                    }
                }
                if (!(string.IsNullOrEmpty(existingLeadHistory?.CustomerSubCommunity?.LastOrDefault().Value) && string.IsNullOrEmpty(newLeadHistory?.CustomerSubCommunity?.FirstOrDefault().Value)) && existingLeadHistory?.CustomerSubCommunity?.LastOrDefault().Value != newLeadHistory?.CustomerSubCommunity?.FirstOrDefault().Value)
                {
                    if (existingLeadHistory.CustomerSubCommunity != null)
                    {
                        existingLeadHistory.CustomerSubCommunity.Add(version, newLeadHistory?.CustomerSubCommunity?.FirstOrDefault().Value);
                    }
                    else
                    {
                        existingLeadHistory.CustomerSubCommunity = new Dictionary<int, string>() { { version, newLeadHistory?.CustomerSubCommunity?.FirstOrDefault().Value } };
                    }
                }
                if (!(string.IsNullOrEmpty(existingLeadHistory?.CustomerTowerName?.LastOrDefault().Value) && string.IsNullOrEmpty(newLeadHistory?.CustomerTowerName?.FirstOrDefault().Value)) && existingLeadHistory?.CustomerTowerName?.LastOrDefault().Value != newLeadHistory?.CustomerTowerName?.FirstOrDefault().Value)
                {
                    if (existingLeadHistory.CustomerTowerName != null)
                    {
                        existingLeadHistory.CustomerTowerName.Add(version, newLeadHistory.CustomerTowerName.FirstOrDefault().Value);
                    }
                    else
                    {
                        existingLeadHistory.CustomerTowerName = new Dictionary<int, string>() { { version, newLeadHistory?.CustomerTowerName?.FirstOrDefault().Value } };
                    }
                }



                if (existingLeadHistory?.Furnished?.LastOrDefault().Value != newLeadHistory?.Furnished?.FirstOrDefault().Value && (newLeadHistory?.Furnished != null))
                {
                    if (existingLeadHistory?.Furnished != null)
                    {
                        existingLeadHistory.Furnished.Add(version, newLeadHistory?.Furnished?.FirstOrDefault().Value ?? FurnishStatus.Unknown);
                    }
                    else
                    {
                        if (newLeadHistory?.Furnished != null && newLeadHistory?.Furnished?.FirstOrDefault().Value != FurnishStatus.Unknown)
                        {
                            existingLeadHistory.Furnished = new Dictionary<int, FurnishStatus>() { { version, newLeadHistory?.Furnished?.FirstOrDefault().Value ?? FurnishStatus.Unknown } };
                        }
                    }
                }
                if (existingLeadHistory?.OfferType?.LastOrDefault().Value != newLeadHistory?.OfferType?.FirstOrDefault().Value && (newLeadHistory?.OfferType != null))
                {
                    if (existingLeadHistory?.OfferType != null)
                    {
                        existingLeadHistory?.OfferType.Add(version, newLeadHistory?.OfferType?.FirstOrDefault().Value ?? OfferType.None);
                    }
                    else
                    {
                        if (newLeadHistory?.OfferType != null && newLeadHistory?.OfferType?.FirstOrDefault().Value != OfferType.None)
                        {
                            existingLeadHistory.OfferType = new Dictionary<int, OfferType>() { { version, newLeadHistory?.OfferType?.FirstOrDefault().Value ?? OfferType.None } };
                        }
                    }
                }

                if (!(string.IsNullOrEmpty(existingLeadHistory?.BHKTypes?.LastOrDefault().Value) && string.IsNullOrEmpty(newLeadHistory?.BHKTypes?.FirstOrDefault().Value)) && existingLeadHistory?.BHKTypes?.LastOrDefault().Value != newLeadHistory?.BHKTypes?.FirstOrDefault().Value)
                {
                    if (existingLeadHistory?.BHKTypes != null)
                    {
                        existingLeadHistory.BHKTypes.Add(version, newLeadHistory?.BHKTypes?.FirstOrDefault().Value);
                    }
                    else
                    {
                        existingLeadHistory.BHKTypes = new Dictionary<int, string>() { { version, newLeadHistory?.BHKTypes?.FirstOrDefault().Value } };
                    }
                }
                if (!(string.IsNullOrEmpty(existingLeadHistory?.EnquiryTypes?.LastOrDefault().Value) && string.IsNullOrEmpty(newLeadHistory?.EnquiryTypes?.FirstOrDefault().Value)) && existingLeadHistory?.EnquiryTypes?.LastOrDefault().Value != newLeadHistory?.EnquiryTypes?.FirstOrDefault().Value)
                {
                    if (existingLeadHistory?.EnquiryTypes != null)
                    {
                        existingLeadHistory.EnquiryTypes.Add(version, newLeadHistory?.EnquiryTypes?.FirstOrDefault().Value);
                    }
                    else
                    {
                        existingLeadHistory.EnquiryTypes = new Dictionary<int, string>() { { version, newLeadHistory?.EnquiryTypes?.FirstOrDefault().Value } };
                    }
                }
                if (!(string.IsNullOrEmpty(existingLeadHistory?.EnquiredCities?.LastOrDefault().Value) && string.IsNullOrEmpty(newLeadHistory?.EnquiredCities?.FirstOrDefault().Value)) && existingLeadHistory.EnquiredCities?.LastOrDefault().Value != newLeadHistory?.EnquiredCities?.FirstOrDefault().Value)
                {
                    if (existingLeadHistory?.EnquiredCities != null)
                    {
                        existingLeadHistory.EnquiredCities.Add(version, newLeadHistory?.EnquiredCities?.FirstOrDefault().Value);
                    }
                    else
                    {
                        existingLeadHistory.EnquiredCities = new Dictionary<int, string>() { { version, newLeadHistory?.EnquiredCities?.FirstOrDefault().Value } };
                    }
                }
                if (!(string.IsNullOrEmpty(existingLeadHistory?.EnquiredStates?.LastOrDefault().Value) && string.IsNullOrEmpty(newLeadHistory?.EnquiredStates?.FirstOrDefault().Value)) && existingLeadHistory?.EnquiredStates?.LastOrDefault().Value != newLeadHistory?.EnquiredStates?.FirstOrDefault().Value)
                {
                    if (existingLeadHistory?.EnquiredStates != null)
                    {
                        existingLeadHistory.EnquiredStates.Add(version, newLeadHistory?.EnquiredStates?.FirstOrDefault().Value);
                    }
                    else
                    {
                        existingLeadHistory.EnquiredStates = new Dictionary<int, string>() { { version, newLeadHistory?.EnquiredStates?.FirstOrDefault().Value } };
                    }
                }
                if (!(string.IsNullOrEmpty(existingLeadHistory?.EnquiredLocations?.LastOrDefault().Value) && string.IsNullOrEmpty(newLeadHistory?.EnquiredLocations?.FirstOrDefault().Value)) && existingLeadHistory?.EnquiredLocations?.LastOrDefault().Value != newLeadHistory?.EnquiredLocations?.FirstOrDefault().Value)
                {
                    if (existingLeadHistory?.EnquiredLocations != null)
                    {
                        existingLeadHistory.EnquiredLocations.Add(version, newLeadHistory?.EnquiredLocations?.FirstOrDefault().Value);
                    }
                    else
                    {
                        existingLeadHistory.EnquiredLocations = new Dictionary<int, string>() { { version, newLeadHistory?.EnquiredLocations?.FirstOrDefault().Value } };
                    }
                }
                if (existingLeadHistory.LowerBudget.LastOrDefault().Value != newLeadHistory.LowerBudget.FirstOrDefault().Value)
                {
                    if (existingLeadHistory.LowerBudget != null)
                    {
                        existingLeadHistory.LowerBudget.Add(version, newLeadHistory.LowerBudget.FirstOrDefault().Value);
                    }
                    else
                    {
                        existingLeadHistory.LowerBudget = new Dictionary<int, long>() { { version, newLeadHistory?.LowerBudget?.FirstOrDefault().Value ?? default } };
                    }
                }
                if (existingLeadHistory.UpperBudget.LastOrDefault().Value != newLeadHistory.UpperBudget.FirstOrDefault().Value)
                {
                    if (existingLeadHistory.UpperBudget != null)
                    {
                        existingLeadHistory.UpperBudget.Add(version, newLeadHistory.UpperBudget.FirstOrDefault().Value);
                    }
                    else
                    {
                        existingLeadHistory.UpperBudget = new Dictionary<int, long>() { { version, newLeadHistory?.UpperBudget?.FirstOrDefault().Value ?? default } };
                    }
                }
                if (existingLeadHistory.Area.LastOrDefault().Value != newLeadHistory.Area.FirstOrDefault().Value)
                {
                    if (existingLeadHistory.Area != null)
                    {
                        existingLeadHistory.Area.Add(version, newLeadHistory.Area.FirstOrDefault().Value);
                    }
                    else
                    {
                        existingLeadHistory.Area = new Dictionary<int, double>() { { version, newLeadHistory.Area?.FirstOrDefault().Value ?? default } };
                    }
                }
                if (!(string.IsNullOrEmpty(existingLeadHistory.AreaUnit.LastOrDefault().Value) && string.IsNullOrEmpty(newLeadHistory.AreaUnit.FirstOrDefault().Value)) && existingLeadHistory.AreaUnit.LastOrDefault().Value != newLeadHistory.AreaUnit.FirstOrDefault().Value)
                {
                    if (existingLeadHistory.AreaUnit != null)
                    {
                        existingLeadHistory.AreaUnit.Add(version, newLeadHistory.AreaUnit.FirstOrDefault().Value);
                    }
                    else
                    {
                        existingLeadHistory.AreaUnit = new Dictionary<int, string> { { version, newLeadHistory.AreaUnit.FirstOrDefault().Value } };
                    }
                }

                if (!shouldUpdateNotes)
                {
                    if (!(string.IsNullOrEmpty(existingLeadHistory?.Notes?.LastOrDefault().Value) && string.IsNullOrEmpty(newLeadHistory?.Notes?.FirstOrDefault().Value)) && existingLeadHistory?.Notes?.LastOrDefault().Value != newLeadHistory?.Notes?.FirstOrDefault().Value)
                    {
                        if (existingLeadHistory.Notes != null)
                        {
                            existingLeadHistory.Notes.Add(version, newLeadHistory.Notes.FirstOrDefault().Value);
                        }
                        else
                        {
                            existingLeadHistory.Notes = new Dictionary<int, string>() { { version, newLeadHistory?.Notes?.FirstOrDefault().Value } };
                        }
                    }
                }
                else if (!(string.IsNullOrEmpty(existingLeadHistory?.Notes?.LastOrDefault().Value) && string.IsNullOrEmpty(newLeadHistory?.Notes?.FirstOrDefault().Value)))
                {
                    if (existingLeadHistory.Notes != null)
                    {
                        existingLeadHistory.Notes.Add(version, newLeadHistory?.Notes?.FirstOrDefault().Value);
                    }
                    else
                    {
                        existingLeadHistory.Notes = new Dictionary<int, string>() { { version, newLeadHistory?.Notes?.FirstOrDefault().Value } };
                    }
                }
                if (!(string.IsNullOrEmpty(existingLeadHistory?.ConfidentialNotes?.LastOrDefault().Value) && string.IsNullOrEmpty(newLeadHistory?.ConfidentialNotes?.FirstOrDefault().Value)) && existingLeadHistory?.ConfidentialNotes?.LastOrDefault().Value != newLeadHistory?.ConfidentialNotes?.FirstOrDefault().Value)
                {
                    if (existingLeadHistory.ConfidentialNotes != null)
                    {
                        existingLeadHistory.ConfidentialNotes.Add(version, newLeadHistory.ConfidentialNotes.FirstOrDefault().Value);
                    }
                    else
                    {
                        existingLeadHistory.ConfidentialNotes = new Dictionary<int, string> { { version, newLeadHistory.ConfidentialNotes.FirstOrDefault().Value } };
                    }
                }
                if (!(string.IsNullOrEmpty(existingLeadHistory.EnquiredCity.LastOrDefault().Value) && string.IsNullOrEmpty(newLeadHistory.EnquiredCity.FirstOrDefault().Value)) && existingLeadHistory.EnquiredCity.LastOrDefault().Value != newLeadHistory.EnquiredCity.FirstOrDefault().Value)
                {
                    if (existingLeadHistory.EnquiredCity != null)
                    {
                        existingLeadHistory.EnquiredCity.Add(version, newLeadHistory.EnquiredCity.FirstOrDefault().Value);
                    }
                    else
                    {
                        existingLeadHistory.EnquiredCity = new Dictionary<int, string> { { version, newLeadHistory.EnquiredCity.FirstOrDefault().Value } };
                    }
                }
                if (!(string.IsNullOrEmpty(existingLeadHistory.EnquiredLocation.LastOrDefault().Value) && string.IsNullOrEmpty(newLeadHistory.EnquiredLocation.FirstOrDefault().Value)) && existingLeadHistory.EnquiredLocation.LastOrDefault().Value != newLeadHistory.EnquiredLocation.FirstOrDefault().Value)
                {
                    if (existingLeadHistory.EnquiredLocation != null)
                    {
                        existingLeadHistory.EnquiredLocation.Add(version, newLeadHistory.EnquiredLocation.FirstOrDefault().Value);
                    }
                    else
                    {
                        existingLeadHistory.EnquiredLocation = new Dictionary<int, string> { { version, newLeadHistory.EnquiredLocation.FirstOrDefault().Value } };
                    }
                }
                if (!(string.IsNullOrEmpty(existingLeadHistory?.EnquiredState?.LastOrDefault().Value) && string.IsNullOrEmpty(newLeadHistory.EnquiredState.FirstOrDefault().Value)) && existingLeadHistory.EnquiredState.LastOrDefault().Value != newLeadHistory.EnquiredState.FirstOrDefault().Value)
                {
                    if (existingLeadHistory.EnquiredState != null)
                    {
                        existingLeadHistory.EnquiredState.Add(version, newLeadHistory.EnquiredState.FirstOrDefault().Value);
                    }
                    else
                    {
                        existingLeadHistory.EnquiredState = new Dictionary<int, string> { { version, newLeadHistory.EnquiredState.FirstOrDefault().Value } };
                    }
                }
                if (existingLeadHistory.LeadSource?.LastOrDefault().Value != newLeadHistory?.LeadSource?.FirstOrDefault().Value)
                {
                    if (existingLeadHistory.LeadSource != null)
                    {
                        existingLeadHistory.LeadSource.Add(version, newLeadHistory.LeadSource.FirstOrDefault().Value);
                    }
                    else
                    {
                        existingLeadHistory.LeadSource = new Dictionary<int, LeadSource>() { { version, newLeadHistory.LeadSource?.FirstOrDefault().Value ?? LeadSource.Direct } };
                    }
                }
                if ((!(string.IsNullOrEmpty(existingLeadHistory.BaseLeadStatus.LastOrDefault().Value)) && !(string.IsNullOrEmpty(newLeadHistory.BaseLeadStatus.FirstOrDefault().Value))) && existingLeadHistory.BaseLeadStatus.LastOrDefault().Value != newLeadHistory.BaseLeadStatus.FirstOrDefault().Value)
                {
                    if (existingLeadHistory.BaseLeadStatus != null)
                    {
                        existingLeadHistory.BaseLeadStatus.Add(version, newLeadHistory.BaseLeadStatus.FirstOrDefault().Value);
                    }
                    else
                    {
                        existingLeadHistory.BaseLeadStatus = new Dictionary<int, string> { { version, newLeadHistory.BaseLeadStatus.FirstOrDefault().Value } };
                    }
                }
                if (!(string.IsNullOrEmpty(existingLeadHistory?.SubLeadStatus?.LastOrDefault().Value) && string.IsNullOrEmpty(newLeadHistory?.SubLeadStatus?.FirstOrDefault().Value)) && existingLeadHistory?.SubLeadStatus?.LastOrDefault().Value != newLeadHistory?.SubLeadStatus?.FirstOrDefault().Value)
                {
                    existingLeadHistory.SubLeadStatus.Add(version, (newLeadHistory?.BaseLeadStatus?.FirstOrDefault().Value == newLeadHistory?.SubLeadStatus?.FirstOrDefault().Value) ? "" : newLeadHistory?.SubLeadStatus?.FirstOrDefault().Value);
                }
                if (existingLeadHistory.IsHotLead.LastOrDefault().Value != newLeadHistory.IsHotLead.FirstOrDefault().Value)
                {
                    if (existingLeadHistory.IsHotLead != null)
                    {
                        existingLeadHistory.IsHotLead.Add(version, newLeadHistory.IsHotLead.FirstOrDefault().Value);
                    }
                    else
                    {
                        existingLeadHistory.IsHotLead = new Dictionary<int, bool>() { { version, newLeadHistory.IsHotLead.FirstOrDefault().Value } };
                    }
                }
                if (existingLeadHistory?.IsWarmLead?.LastOrDefault().Value != newLeadHistory?.IsWarmLead?.FirstOrDefault().Value)
                {
                    if (existingLeadHistory?.IsWarmLead != null)
                    {
                        existingLeadHistory?.IsWarmLead?.Add(version, newLeadHistory.IsWarmLead.FirstOrDefault().Value);
                    }
                    else
                    {
                        existingLeadHistory.IsWarmLead = new Dictionary<int, bool>() { { version, newLeadHistory.IsWarmLead.FirstOrDefault().Value } };
                    }
                }
                if (existingLeadHistory?.IsColdLead?.LastOrDefault().Value != newLeadHistory?.IsColdLead?.FirstOrDefault().Value)
                {
                    if (existingLeadHistory?.IsColdLead != null)
                    {
                        existingLeadHistory?.IsColdLead?.Add(version, newLeadHistory.IsColdLead.FirstOrDefault().Value);
                    }
                    else
                    {
                        existingLeadHistory.IsColdLead = new Dictionary<int, bool>() { { version, newLeadHistory.IsColdLead.FirstOrDefault().Value } };
                    }
                }
                if (existingLeadHistory.IsEscalated.LastOrDefault().Value != newLeadHistory.IsEscalated.FirstOrDefault().Value)
                {
                    if (existingLeadHistory.IsEscalated != null)
                    {
                        existingLeadHistory.IsEscalated.Add(version, newLeadHistory.IsEscalated.FirstOrDefault().Value);
                    }
                    else
                    {
                        existingLeadHistory.IsEscalated = new Dictionary<int, bool>() { { version, newLeadHistory.IsEscalated.FirstOrDefault().Value } };
                    }
                }
                if (existingLeadHistory.IsAboutToConvert.LastOrDefault().Value != newLeadHistory.IsAboutToConvert.FirstOrDefault().Value)
                {
                    if (existingLeadHistory.IsAboutToConvert != null)
                    {
                        existingLeadHistory.IsAboutToConvert.Add(version, newLeadHistory.IsAboutToConvert.FirstOrDefault().Value);
                    }
                    else
                    {
                        existingLeadHistory.IsAboutToConvert = new Dictionary<int, bool>() { { version, newLeadHistory.IsAboutToConvert.FirstOrDefault().Value } };
                    }
                }
                if (existingLeadHistory.IsHighlighted.LastOrDefault().Value != newLeadHistory.IsHighlighted.FirstOrDefault().Value)
                {
                    if (existingLeadHistory.IsHighlighted != null)
                    {
                        existingLeadHistory.IsHighlighted.Add(version, newLeadHistory.IsHighlighted.FirstOrDefault().Value);
                    }
                    else
                    {
                        existingLeadHistory.IsHighlighted = new Dictionary<int, bool>() { { version, newLeadHistory.IsHighlighted.FirstOrDefault().Value } };
                    }
                }
                if (existingLeadHistory?.IsIntegrationLead?.LastOrDefault().Value != newLeadHistory?.IsIntegrationLead?.FirstOrDefault().Value)
                {
                    if (existingLeadHistory.IsIntegrationLead != null)
                    {
                        existingLeadHistory.IsIntegrationLead.Add(version, newLeadHistory.IsIntegrationLead.FirstOrDefault().Value);
                    }
                    else
                    {
                        existingLeadHistory.IsIntegrationLead = new Dictionary<int, bool>() { { version, newLeadHistory.IsIntegrationLead.FirstOrDefault().Value } };
                    }
                }
                if (existingLeadHistory?.AssignedTo?.LastOrDefault().Value != newLeadHistory?.AssignedTo?.FirstOrDefault().Value)
                {
                    if (existingLeadHistory?.AssignedTo != null)
                    {
                        existingLeadHistory.AssignedTo.Add(version, newLeadHistory.AssignedTo.FirstOrDefault().Value);
                    }
                    else
                    {
                        existingLeadHistory.AssignedTo = new Dictionary<int, Guid>() { { version, newLeadHistory.AssignedTo.FirstOrDefault().Value } };
                    }
                }
                if (!(string.IsNullOrEmpty(existingLeadHistory?.AssignedToUser?.LastOrDefault().Value) && string.IsNullOrEmpty(newLeadHistory?.AssignedToUser?.FirstOrDefault().Value)) && existingLeadHistory?.AssignedToUser?.LastOrDefault().Value != newLeadHistory?.AssignedToUser?.FirstOrDefault().Value)
                {
                    if (existingLeadHistory?.AssignedToUser != null)
                    {
                        existingLeadHistory.AssignedToUser?.Add(version, newLeadHistory.AssignedToUser?.FirstOrDefault().Value);
                    }
                    else
                    {
                        existingLeadHistory.AssignedToUser = new Dictionary<int, string>() { { version, newLeadHistory?.AssignedToUser?.FirstOrDefault().Value } };
                    }
                }
                if (!(string.IsNullOrEmpty(existingLeadHistory?.AssignedFromUser?.LastOrDefault().Value) && string.IsNullOrEmpty(newLeadHistory?.AssignedFromUser?.FirstOrDefault().Value)) && existingLeadHistory?.AssignedFromUser?.LastOrDefault().Value != newLeadHistory?.AssignedFromUser?.FirstOrDefault().Value)
                {
                    if (existingLeadHistory?.AssignedFromUser != null)
                    {
                        existingLeadHistory.AssignedFromUser?.Add(version, newLeadHistory.AssignedFromUser?.FirstOrDefault().Value);
                    }
                    else
                    {
                        existingLeadHistory.AssignedFromUser = new Dictionary<int, string>() { { version, newLeadHistory?.AssignedFromUser?.FirstOrDefault().Value } };
                    }
                }
                if (!(string.IsNullOrEmpty(existingLeadHistory?.Documents?.LastOrDefault().Value) && string.IsNullOrEmpty(newLeadHistory?.Documents?.FirstOrDefault().Value)) && existingLeadHistory?.Documents?.LastOrDefault().Value != newLeadHistory?.Documents?.FirstOrDefault().Value)
                {
                    if (existingLeadHistory?.Documents != null)
                    {
                        existingLeadHistory.Documents?.Add(version, newLeadHistory.Documents?.FirstOrDefault().Value);
                    }
                    else
                    {
                        existingLeadHistory.Documents = new Dictionary<int, string>() { { version, newLeadHistory?.Documents?.FirstOrDefault().Value } };
                    }
                }
                if (newLeadHistory?.ContactRecords?.FirstOrDefault().Value != null)
                {
                    if (existingLeadHistory?.ContactRecords != null)
                    {
                        existingLeadHistory.ContactRecords?.Add(version, newLeadHistory.ContactRecords.FirstOrDefault().Value);
                    }
                    else
                    {
                        existingLeadHistory.ContactRecords = new Dictionary<int, ContactType>() { { version, newLeadHistory.ContactRecords.FirstOrDefault().Value } };
                    }
                }
                if (existingLeadHistory?.LastModifiedByUser == null)
                {
                    existingLeadHistory.LastModifiedByUser = new Dictionary<int, Guid>() { { version, newLeadHistory.LastModifiedByUser.FirstOrDefault().Value } };
                }
                else
                {
                    existingLeadHistory?.LastModifiedByUser?.Add(version, newLeadHistory.LastModifiedByUser.FirstOrDefault().Value);
                }

                if (meetingOrSiteVisit != null)
                {
                    if (meetingOrSiteVisit == AppointmentType.Meeting)
                    {
                        if (existingLeadHistory?.IsMeetingDone != null)
                        {
                            existingLeadHistory?.IsMeetingDone?.Add(version, newLeadHistory.IsMeetingDone.FirstOrDefault().Value);
                        }
                        else
                        {
                            existingLeadHistory.IsMeetingDone = new Dictionary<int, bool>() { { version, newLeadHistory.IsMeetingDone.FirstOrDefault().Value } };
                        }
                        if (existingLeadHistory?.MeetingLocation != null)
                        {
                            existingLeadHistory?.MeetingLocation?.Add(version, newLeadHistory.MeetingLocation.FirstOrDefault().Value);
                        }
                        else
                        {
                            existingLeadHistory.MeetingLocation = new Dictionary<int, Guid>() { { version, newLeadHistory.MeetingLocation.FirstOrDefault().Value } };
                        }
                    }
                    else if (meetingOrSiteVisit == AppointmentType.SiteVisit)
                    {
                        if (existingLeadHistory?.IsSiteVisitDone != null)
                        {
                            existingLeadHistory?.IsSiteVisitDone?.Add(version, newLeadHistory.IsSiteVisitDone.FirstOrDefault().Value);
                        }
                        else
                        {
                            existingLeadHistory.IsSiteVisitDone = new Dictionary<int, bool>() { { version, newLeadHistory.IsSiteVisitDone.FirstOrDefault().Value } };
                        }

                        if (existingLeadHistory?.SiteLocation != null)
                        {
                            existingLeadHistory?.SiteLocation?.Add(version, newLeadHistory.SiteLocation.FirstOrDefault().Value);
                        }
                        else
                        {
                            existingLeadHistory.SiteLocation = new Dictionary<int, Guid>() { { version, newLeadHistory.SiteLocation.FirstOrDefault().Value } };
                        }
                    }
                }
                if (existingLeadHistory?.IsArchived?.LastOrDefault().Value != newLeadHistory?.IsArchived?.FirstOrDefault().Value)
                {
                    if (existingLeadHistory?.IsArchived != null)
                    {
                        existingLeadHistory?.IsArchived?.Add(version, newLeadHistory.IsArchived.FirstOrDefault().Value);
                    }
                    else
                    {
                        existingLeadHistory.IsArchived = new Dictionary<int, bool>() { { version, newLeadHistory.IsArchived.FirstOrDefault().Value } };
                    }

                }
                if (!(string.IsNullOrEmpty(existingLeadHistory?.Projects?.LastOrDefault().Value) && string.IsNullOrEmpty(newLeadHistory?.Projects?.FirstOrDefault().Value)) && existingLeadHistory?.Projects?.LastOrDefault().Value != newLeadHistory?.Projects?.FirstOrDefault().Value)
                {
                    if (existingLeadHistory?.Projects != null)
                    {
                        existingLeadHistory.Projects?.Add(version, newLeadHistory?.Projects?.FirstOrDefault().Value);
                    }
                    else
                    {
                        existingLeadHistory.Projects = new Dictionary<int, string>() { { version, newLeadHistory?.Projects?.FirstOrDefault().Value } };
                    }
                }
                if (!(string.IsNullOrEmpty(existingLeadHistory?.Properties?.LastOrDefault().Value) && string.IsNullOrEmpty(newLeadHistory?.Properties?.FirstOrDefault().Value)) && existingLeadHistory?.Properties?.LastOrDefault().Value != newLeadHistory?.Properties?.FirstOrDefault().Value)
                {
                    if (existingLeadHistory?.Properties != null)
                    {
                        existingLeadHistory.Properties?.Add(version, newLeadHistory?.Properties?.FirstOrDefault().Value);
                    }
                    else
                    {
                        existingLeadHistory.Properties = new Dictionary<int, string>() { { version, newLeadHistory?.Properties?.FirstOrDefault().Value } };
                    }
                }
                if (!(string.IsNullOrEmpty(existingLeadHistory?.SubSource?.LastOrDefault().Value) && string.IsNullOrEmpty(newLeadHistory?.SubSource?.FirstOrDefault().Value)) && existingLeadHistory?.SubSource?.LastOrDefault().Value != newLeadHistory?.SubSource?.FirstOrDefault().Value)
                {
                    if (existingLeadHistory?.SubSource != null)
                    {
                        existingLeadHistory.SubSource.Add(version, newLeadHistory?.SubSource?.FirstOrDefault().Value);
                    }
                    else
                    {
                        existingLeadHistory.SubSource = new Dictionary<int, string>() { { version, newLeadHistory.SubSource?.FirstOrDefault().Value } };
                    }
                }
                if ((!(string.IsNullOrEmpty(existingLeadHistory?.Currency?.LastOrDefault().Value)) && !(string.IsNullOrEmpty(newLeadHistory?.Currency?.FirstOrDefault().Value))) && existingLeadHistory?.Currency.LastOrDefault().Value != newLeadHistory?.Currency.FirstOrDefault().Value)
                {
                    if (existingLeadHistory?.Currency != null)
                    {
                        existingLeadHistory.Currency.Add(version, newLeadHistory?.Currency?.FirstOrDefault().Value);
                    }
                    else
                    {
                        existingLeadHistory.Currency = new Dictionary<int, string>() { { version, newLeadHistory?.Currency?.FirstOrDefault().Value } };
                    }
                }
                if (!(string.IsNullOrEmpty(existingLeadHistory?.ReferralContactNo?.LastOrDefault().Value) && string.IsNullOrEmpty(newLeadHistory?.ReferralContactNo?.FirstOrDefault().Value)) && existingLeadHistory?.ReferralContactNo?.LastOrDefault().Value != newLeadHistory?.ReferralContactNo?.FirstOrDefault().Value)
                {
                    if (existingLeadHistory?.ReferralContactNo != null)
                    {
                        existingLeadHistory?.ReferralContactNo?.Add(version, newLeadHistory?.ReferralContactNo?.FirstOrDefault().Value);
                    }
                    else
                    {
                        existingLeadHistory.ReferralContactNo = new Dictionary<int, string>() { { version, newLeadHistory?.ReferralContactNo?.FirstOrDefault().Value } };
                    }

                }
                if (!(string.IsNullOrEmpty(existingLeadHistory?.ReferralEmail?.LastOrDefault().Value) && string.IsNullOrEmpty(newLeadHistory?.ReferralEmail?.FirstOrDefault().Value)) && existingLeadHistory?.ReferralEmail?.LastOrDefault().Value != newLeadHistory?.ReferralEmail?.FirstOrDefault().Value)
                {
                    if (existingLeadHistory?.ReferralEmail != null)
                    {
                        existingLeadHistory?.ReferralEmail?.Add(version, newLeadHistory?.ReferralEmail?.FirstOrDefault().Value);
                    }
                    else
                    {
                        existingLeadHistory.ReferralEmail = new Dictionary<int, string>() { { version, newLeadHistory?.ReferralEmail?.FirstOrDefault().Value } };
                    }

                }
                if (!(string.IsNullOrEmpty(existingLeadHistory?.ReferralName?.LastOrDefault().Value) && string.IsNullOrEmpty(newLeadHistory?.ReferralName?.FirstOrDefault().Value)) && existingLeadHistory?.ReferralName?.LastOrDefault().Value != newLeadHistory?.ReferralName?.FirstOrDefault().Value)
                {
                    if (existingLeadHistory?.ReferralName != null)
                    {
                        existingLeadHistory?.ReferralName?.Add(version, newLeadHistory?.ReferralName?.FirstOrDefault().Value);
                    }
                    else
                    {
                        existingLeadHistory.ReferralName = new Dictionary<int, string>() { { version, newLeadHistory?.ReferralName?.FirstOrDefault().Value } };
                    }

                }
                if (!(string.IsNullOrEmpty(existingLeadHistory?.UnitName?.LastOrDefault().Value) && string.IsNullOrEmpty(newLeadHistory?.UnitName?.FirstOrDefault().Value)) && existingLeadHistory?.UnitName?.LastOrDefault().Value != newLeadHistory?.UnitName?.FirstOrDefault().Value)
                {
                    if (existingLeadHistory?.UnitName != null)
                    {
                        existingLeadHistory?.UnitName?.Add(version, newLeadHistory?.UnitName?.FirstOrDefault().Value);
                    }
                    else
                    {
                        existingLeadHistory.UnitName = new Dictionary<int, string>() { { version, newLeadHistory?.UnitName?.FirstOrDefault().Value } };
                    }

                }
                if (!(string.IsNullOrEmpty(existingLeadHistory?.ClusterName?.LastOrDefault().Value) && string.IsNullOrEmpty(newLeadHistory?.ClusterName?.FirstOrDefault().Value)) && existingLeadHistory?.ClusterName?.LastOrDefault().Value != newLeadHistory?.ClusterName?.FirstOrDefault().Value)
                {
                    if (existingLeadHistory?.ClusterName != null)
                    {
                        existingLeadHistory?.ClusterName?.Add(version, newLeadHistory?.ClusterName?.FirstOrDefault().Value);
                    }
                    else
                    {
                        existingLeadHistory.ClusterName = new Dictionary<int, string>() { { version, newLeadHistory?.ClusterName?.FirstOrDefault().Value } };
                    }

                }
                if (!(string.IsNullOrEmpty(existingLeadHistory?.Nationality?.LastOrDefault().Value) && string.IsNullOrEmpty(newLeadHistory?.Nationality?.FirstOrDefault().Value)) && existingLeadHistory?.Nationality?.LastOrDefault().Value != newLeadHistory?.Nationality?.FirstOrDefault().Value)
                {
                    if (existingLeadHistory?.Nationality != null)
                    {
                        existingLeadHistory?.Nationality?.Add(version, newLeadHistory?.Nationality?.FirstOrDefault().Value);
                    }
                    else
                    {
                        existingLeadHistory.Nationality = new Dictionary<int, string>() { { version, newLeadHistory?.Nationality?.FirstOrDefault().Value } };
                    }

                }

                if (!(string.IsNullOrEmpty(existingLeadHistory?.CompanyName?.LastOrDefault().Value) && string.IsNullOrEmpty(newLeadHistory?.CompanyName?.FirstOrDefault().Value)) && existingLeadHistory?.CompanyName?.LastOrDefault().Value != newLeadHistory?.CompanyName?.FirstOrDefault().Value)
                {
                    if (existingLeadHistory?.CompanyName != null)
                    {
                        existingLeadHistory?.CompanyName?.Add(version, newLeadHistory?.CompanyName?.FirstOrDefault().Value);
                    }
                    else
                    {
                        existingLeadHistory.CompanyName = new Dictionary<int, string>() { { version, newLeadHistory?.CompanyName?.FirstOrDefault().Value } };
                    }
                }
                if (!(string.IsNullOrEmpty(existingLeadHistory?.AgencyName?.LastOrDefault().Value) && string.IsNullOrEmpty(newLeadHistory?.AgencyName?.FirstOrDefault().Value)) && existingLeadHistory?.AgencyName?.LastOrDefault().Value != newLeadHistory?.AgencyName?.FirstOrDefault().Value)
                {
                    if (existingLeadHistory?.AgencyName != null)
                    {
                        existingLeadHistory?.AgencyName?.Add(version, newLeadHistory?.AgencyName?.FirstOrDefault().Value);
                    }
                    else
                    {
                        existingLeadHistory.AgencyName = new Dictionary<int, string>() { { version, newLeadHistory?.AgencyName?.FirstOrDefault().Value } };
                    }

                }
                if (existingLeadHistory?.CarpetArea?.LastOrDefault().Value != newLeadHistory?.CarpetArea?.FirstOrDefault().Value)
                {
                    if (existingLeadHistory?.CarpetArea != null)
                    {
                        existingLeadHistory?.CarpetArea?.Add(version, newLeadHistory.CarpetArea.FirstOrDefault().Value);
                    }
                    else
                    {
                        existingLeadHistory.CarpetArea = new Dictionary<int, double>() { { version, newLeadHistory.CarpetArea.FirstOrDefault().Value } };
                    }
                }
                if (existingLeadHistory?.BuiltUpArea?.LastOrDefault().Value != newLeadHistory?.BuiltUpArea?.FirstOrDefault().Value)
                {
                    if (existingLeadHistory?.BuiltUpArea != null)
                    {
                        existingLeadHistory?.BuiltUpArea?.Add(version, newLeadHistory.BuiltUpArea.FirstOrDefault().Value);
                    }
                    else
                    {
                        existingLeadHistory.BuiltUpArea = new Dictionary<int, double>() { { version, newLeadHistory.BuiltUpArea.FirstOrDefault().Value } };
                    }
                }
                if (existingLeadHistory?.SaleableArea?.LastOrDefault().Value != newLeadHistory?.SaleableArea?.FirstOrDefault().Value)
                {
                    if (existingLeadHistory?.SaleableArea != null)
                    {
                        existingLeadHistory?.SaleableArea?.Add(version, newLeadHistory.SaleableArea.FirstOrDefault().Value);
                    }
                    else
                    {
                        existingLeadHistory.SaleableArea = new Dictionary<int, double>() { { version, newLeadHistory.SaleableArea.FirstOrDefault().Value } };
                    }
                }

                if (existingLeadHistory?.NetArea?.LastOrDefault().Value != newLeadHistory?.NetArea?.FirstOrDefault().Value)
                {
                    if (existingLeadHistory?.NetArea != null)
                    {
                        existingLeadHistory?.NetArea?.Add(version, newLeadHistory.NetArea.FirstOrDefault().Value);
                    }
                    else
                    {
                        existingLeadHistory.NetArea = new Dictionary<int, double>() { { version, newLeadHistory.NetArea.FirstOrDefault().Value } };
                    }
                }

                if (existingLeadHistory?.PropertyArea?.LastOrDefault().Value != newLeadHistory?.PropertyArea?.FirstOrDefault().Value)
                {
                    if (existingLeadHistory?.PropertyArea != null)
                    {
                        existingLeadHistory?.PropertyArea?.Add(version, newLeadHistory.PropertyArea.FirstOrDefault().Value);
                    }
                    else
                    {
                        existingLeadHistory.PropertyArea = new Dictionary<int, double>() { { version, newLeadHistory.PropertyArea.FirstOrDefault().Value } };
                    }
                }
                if ((existingLeadHistory?.PossesionType?.LastOrDefault().Value != newLeadHistory?.PossesionType?.FirstOrDefault().Value) && (newLeadHistory?.PossesionType != null))
                {
                    if (existingLeadHistory?.PossesionType != null)
                    {
                        existingLeadHistory?.PossesionType.Add(version, newLeadHistory?.PossesionType?.FirstOrDefault().Value ?? PossesionType.None);
                    }
                    else
                    {
                        if (newLeadHistory?.PossesionType != null && newLeadHistory?.PossesionType?.FirstOrDefault().Value != PossesionType.None)
                        {
                            existingLeadHistory.PossesionType = new Dictionary<int, PossesionType>() { { version, newLeadHistory?.PossesionType?.FirstOrDefault().Value ?? PossesionType.None } };
                        }
                    }
                }
                if (existingLeadHistory?.PickedDate?.LastOrDefault().Value != newLeadHistory?.PickedDate?.FirstOrDefault().Value)
                {
                    DateTime? existingData = null;
                    DateTime? newDate = null;
                    if (existingLeadHistory?.PickedDate?.LastOrDefault().Value != null)
                    {
                        existingData = DateTime.ParseExact(existingLeadHistory?.PickedDate?.LastOrDefault().Value?.ToString("yyyy-MM-dd HH:mm:ss") ?? string.Empty, "yyyy-MM-dd HH:mm:ss", null);
                    }
                    if (newLeadHistory?.PickedDate?.FirstOrDefault().Value != null)
                    {
                        newDate = DateTime.ParseExact(newLeadHistory?.PickedDate?.FirstOrDefault().Value?.ToString("yyyy-MM-dd HH:mm:ss") ?? string.Empty, "yyyy-MM-dd HH:mm:ss", null);
                    }
                    if (existingData != newDate)
                    {
                        if (existingLeadHistory?.PickedDate != null)
                        {
                            existingLeadHistory?.PickedDate?.Add(version, newLeadHistory?.PickedDate?.FirstOrDefault().Value);
                        }
                        else if (existingLeadHistory != null)
                        {
                            existingLeadHistory.PickedDate = new Dictionary<int, DateTime?>() { { version, newLeadHistory?.PickedDate?.FirstOrDefault().Value } };
                        }
                    }
                }
                if (existingLeadHistory?.IsPicked?.LastOrDefault().Value != newLeadHistory?.IsPicked?.FirstOrDefault().Value)
                {
                    if (existingLeadHistory?.IsPicked != null)
                    {
                        existingLeadHistory?.IsPicked?.Add(version, newLeadHistory?.IsPicked?.FirstOrDefault().Value ?? false);
                    }
                    else if (existingLeadHistory != null)
                    {
                        existingLeadHistory.IsPicked = new Dictionary<int, bool>() { { version, newLeadHistory?.IsPicked?.FirstOrDefault().Value ?? false } };
                    }
                }
                if (!(string.IsNullOrEmpty(existingLeadHistory?.CarpetAreaUnit?.LastOrDefault().Value) && string.IsNullOrEmpty(newLeadHistory?.CarpetAreaUnit?.FirstOrDefault().Value)) && existingLeadHistory?.CarpetAreaUnit?.LastOrDefault().Value != newLeadHistory?.CarpetAreaUnit?.FirstOrDefault().Value)
                {
                    if (existingLeadHistory?.CarpetAreaUnit != null)
                    {
                        existingLeadHistory?.CarpetAreaUnit?.Add(version, newLeadHistory?.CarpetAreaUnit?.FirstOrDefault().Value);
                    }
                    else
                    {
                        existingLeadHistory.CarpetAreaUnit = new Dictionary<int, string?>() { { version, newLeadHistory?.CarpetAreaUnit?.FirstOrDefault().Value } };
                    }
                }
                if (existingLeadHistory?.ConversionFactor?.LastOrDefault().Value != newLeadHistory?.ConversionFactor?.FirstOrDefault().Value)
                {
                    if (existingLeadHistory?.ConversionFactor != null)
                    {
                        existingLeadHistory?.ConversionFactor?.Add(version, newLeadHistory.ConversionFactor.FirstOrDefault().Value);
                    }
                    else
                    {
                        existingLeadHistory.ConversionFactor = new Dictionary<int, float>() { { version, newLeadHistory.ConversionFactor.FirstOrDefault().Value } };
                    }
                }
                if (!(string.IsNullOrEmpty(existingLeadHistory?.BuiltUpAreaUnit?.LastOrDefault().Value) && string.IsNullOrEmpty(newLeadHistory?.BuiltUpAreaUnit?.FirstOrDefault().Value)) && existingLeadHistory?.BuiltUpAreaUnit?.LastOrDefault().Value != newLeadHistory?.BuiltUpAreaUnit?.FirstOrDefault().Value)
                {
                    if (existingLeadHistory?.BuiltUpAreaUnit != null)
                    {
                        existingLeadHistory?.BuiltUpAreaUnit?.Add(version, newLeadHistory?.BuiltUpAreaUnit?.FirstOrDefault().Value);
                    }
                    else
                    {
                        existingLeadHistory.BuiltUpAreaUnit = new Dictionary<int, string?>() { { version, newLeadHistory?.BuiltUpAreaUnit?.FirstOrDefault().Value } };
                    }
                }
                if (existingLeadHistory?.BuiltUpAreaConversionFactor?.LastOrDefault().Value != newLeadHistory?.BuiltUpAreaConversionFactor?.FirstOrDefault().Value)
                {
                    if (existingLeadHistory?.BuiltUpAreaConversionFactor != null)
                    {
                        existingLeadHistory?.BuiltUpAreaConversionFactor?.Add(version, newLeadHistory.BuiltUpAreaConversionFactor.FirstOrDefault().Value);
                    }
                    else
                    {
                        existingLeadHistory.BuiltUpAreaConversionFactor = new Dictionary<int, float>() { { version, newLeadHistory.BuiltUpAreaConversionFactor.FirstOrDefault().Value } };
                    }
                }
                if (!(string.IsNullOrEmpty(existingLeadHistory?.SaleableAreaUnit?.LastOrDefault().Value) && string.IsNullOrEmpty(newLeadHistory?.SaleableAreaUnit?.FirstOrDefault().Value)) && existingLeadHistory?.SaleableAreaUnit?.LastOrDefault().Value != newLeadHistory?.SaleableAreaUnit?.FirstOrDefault().Value)
                {
                    if (existingLeadHistory?.SaleableAreaUnit != null)
                    {
                        existingLeadHistory?.SaleableAreaUnit?.Add(version, newLeadHistory?.SaleableAreaUnit?.FirstOrDefault().Value);
                    }
                    else
                    {
                        existingLeadHistory.SaleableAreaUnit = new Dictionary<int, string?>() { { version, newLeadHistory?.SaleableAreaUnit?.FirstOrDefault().Value } };
                    }
                }
                if (existingLeadHistory?.SaleableAreaConversionFactor?.LastOrDefault().Value != newLeadHistory?.SaleableAreaConversionFactor?.FirstOrDefault().Value)
                {
                    if (existingLeadHistory?.SaleableAreaConversionFactor != null)
                    {
                        existingLeadHistory?.SaleableAreaConversionFactor?.Add(version, newLeadHistory.SaleableAreaConversionFactor.FirstOrDefault().Value);
                    }
                    else
                    {
                        existingLeadHistory.SaleableAreaConversionFactor = new Dictionary<int, float>() { { version, newLeadHistory.SaleableAreaConversionFactor.FirstOrDefault().Value } };
                    }
                }



                if (!(string.IsNullOrEmpty(existingLeadHistory?.NetAreaUnit?.LastOrDefault().Value) && string.IsNullOrEmpty(newLeadHistory?.NetAreaUnit?.FirstOrDefault().Value)) && existingLeadHistory?.NetAreaUnit?.LastOrDefault().Value != newLeadHistory?.NetAreaUnit?.FirstOrDefault().Value)
                {
                    if (existingLeadHistory?.NetAreaUnit != null)
                    {
                        existingLeadHistory?.NetAreaUnit?.Add(version, newLeadHistory?.NetAreaUnit?.FirstOrDefault().Value);
                    }
                    else
                    {
                        existingLeadHistory.NetAreaUnit = new Dictionary<int, string?>() { { version, newLeadHistory?.NetAreaUnit?.FirstOrDefault().Value } };
                    }
                }
                if (existingLeadHistory?.NetAreaConversionFactor?.LastOrDefault().Value != newLeadHistory?.NetAreaConversionFactor?.FirstOrDefault().Value)
                {
                    if (existingLeadHistory?.NetAreaConversionFactor != null)
                    {
                        existingLeadHistory?.NetAreaConversionFactor?.Add(version, newLeadHistory.NetAreaConversionFactor.FirstOrDefault().Value);
                    }
                    else
                    {
                        existingLeadHistory.NetAreaConversionFactor = new Dictionary<int, float>() { { version, newLeadHistory.NetAreaConversionFactor.FirstOrDefault().Value } };
                    }
                }

                if (!(string.IsNullOrEmpty(existingLeadHistory?.PropertyAreaUnit?.LastOrDefault().Value) && string.IsNullOrEmpty(newLeadHistory?.PropertyAreaUnit?.FirstOrDefault().Value)) && existingLeadHistory?.PropertyAreaUnit?.LastOrDefault().Value != newLeadHistory?.PropertyAreaUnit?.FirstOrDefault().Value)
                {
                    if (existingLeadHistory?.PropertyAreaUnit != null)
                    {
                        existingLeadHistory?.PropertyAreaUnit?.Add(version, newLeadHistory?.PropertyAreaUnit?.FirstOrDefault().Value);
                    }
                    else
                    {
                        existingLeadHistory.PropertyAreaUnit = new Dictionary<int, string?>() { { version, newLeadHistory?.PropertyAreaUnit?.FirstOrDefault().Value } };
                    }
                }
                if (existingLeadHistory?.PropertyAreaConversionFactor?.LastOrDefault().Value != newLeadHistory?.PropertyAreaConversionFactor?.FirstOrDefault().Value)
                {
                    if (existingLeadHistory?.PropertyAreaConversionFactor != null)
                    {
                        existingLeadHistory?.PropertyAreaConversionFactor?.Add(version, newLeadHistory.PropertyAreaConversionFactor.FirstOrDefault().Value);
                    }
                    else
                    {
                        existingLeadHistory.PropertyAreaConversionFactor = new Dictionary<int, float>() { { version, newLeadHistory.PropertyAreaConversionFactor.FirstOrDefault().Value } };
                    }
                }

                if (!(string.IsNullOrEmpty(existingLeadHistory?.SoldPrice?.LastOrDefault().Value) && string.IsNullOrEmpty(newLeadHistory?.SoldPrice?.FirstOrDefault().Value)) && existingLeadHistory?.SoldPrice?.LastOrDefault().Value != newLeadHistory?.SoldPrice?.FirstOrDefault().Value)
                {
                    if (existingLeadHistory?.SoldPrice != null)
                    {
                        existingLeadHistory?.SoldPrice?.Add(version, newLeadHistory.SoldPrice.FirstOrDefault().Value);
                    }
                    else
                    {
                        existingLeadHistory.SoldPrice = new Dictionary<int, string>() { { version, newLeadHistory.SoldPrice.FirstOrDefault().Value } };
                    }
                }
                if ((newLeadHistory?.ChildLeadsCount?.FirstOrDefault().Value != default) && (existingLeadHistory?.ChildLeadsCount?.LastOrDefault().Value != newLeadHistory?.ChildLeadsCount?.FirstOrDefault().Value))
                {
                    existingLeadHistory?.ChildLeadsCount?.Add(version, newLeadHistory.ChildLeadsCount.FirstOrDefault().Value);
                }
                else if (existingLeadHistory != null && existingLeadHistory.ChildLeadsCount == null && newLeadHistory?.ChildLeadsCount != null)
                {
                    existingLeadHistory.ChildLeadsCount = new Dictionary<int, int>
                {
                    { version, newLeadHistory.ChildLeadsCount.FirstOrDefault().Value }
                };
                }
                if (!(string.IsNullOrEmpty(existingLeadHistory?.SourcingManagerUser?.LastOrDefault().Value) && string.IsNullOrEmpty(newLeadHistory?.SourcingManagerUser?.FirstOrDefault().Value)) && existingLeadHistory?.SourcingManagerUser?.LastOrDefault().Value != newLeadHistory?.SourcingManagerUser?.FirstOrDefault().Value)
                {
                    if (existingLeadHistory?.SourcingManagerUser != null)
                    {
                        existingLeadHistory?.SourcingManagerUser.Add(version, newLeadHistory.SourcingManagerUser.FirstOrDefault().Value);
                    }
                    else
                    {
                        existingLeadHistory.SourcingManagerUser = new Dictionary<int, string>() { { version, newLeadHistory.SourcingManagerUser.FirstOrDefault().Value } };
                    }
                }
                if (!(string.IsNullOrEmpty(existingLeadHistory?.ClosingManagerUser?.LastOrDefault().Value) && string.IsNullOrEmpty(newLeadHistory?.ClosingManagerUser?.FirstOrDefault().Value)) && existingLeadHistory?.ClosingManagerUser?.LastOrDefault().Value != newLeadHistory?.ClosingManagerUser?.FirstOrDefault().Value)
                {
                    if (existingLeadHistory?.ClosingManagerUser != null)
                    {
                        existingLeadHistory?.ClosingManagerUser.Add(version, newLeadHistory.ClosingManagerUser.FirstOrDefault().Value);
                    }
                    else
                    {
                        existingLeadHistory.ClosingManagerUser = new Dictionary<int, string>() { { version, newLeadHistory.ClosingManagerUser.FirstOrDefault().Value } };
                    }
                }
                if (existingLeadHistory?.Profession != null && newLeadHistory?.Profession != null && existingLeadHistory?.Profession?.LastOrDefault().Value != newLeadHistory?.Profession?.FirstOrDefault().Value)
                {
                    if (existingLeadHistory.Profession != null)
                    {
                        existingLeadHistory.Profession.Add(version, newLeadHistory.Profession.FirstOrDefault().Value);
                    }
                    else
                    {
                        existingLeadHistory.Profession = new Dictionary<int, Profession>() { { version, newLeadHistory.Profession?.FirstOrDefault().Value ?? Profession.None } };
                    }
                }
                if (!(string.IsNullOrEmpty(existingLeadHistory?.CustomerCity?.LastOrDefault().Value) && string.IsNullOrEmpty(newLeadHistory?.CustomerCity?.FirstOrDefault().Value)) && existingLeadHistory?.CustomerCity?.LastOrDefault().Value != newLeadHistory?.CustomerCity?.FirstOrDefault().Value)
                {
                    if (existingLeadHistory?.CustomerCity != null)
                    {
                        existingLeadHistory?.CustomerCity.Add(version, newLeadHistory?.CustomerCity?.FirstOrDefault().Value);
                    }
                    else
                    {
                        existingLeadHistory.CustomerCity = new Dictionary<int, string>() { { version, newLeadHistory?.CustomerCity?.FirstOrDefault().Value } };
                    }
                }
                if (!(string.IsNullOrEmpty(existingLeadHistory?.CustomerState?.LastOrDefault().Value) && string.IsNullOrEmpty(newLeadHistory?.CustomerState?.FirstOrDefault().Value)) && existingLeadHistory?.CustomerState?.LastOrDefault().Value != newLeadHistory?.CustomerState?.FirstOrDefault().Value)
                {
                    if (existingLeadHistory?.CustomerState != null)
                    {
                        existingLeadHistory?.CustomerState.Add(version, newLeadHistory?.CustomerState?.FirstOrDefault().Value);
                    }
                    else
                    {
                        existingLeadHistory.CustomerState = new Dictionary<int, string>() { { version, newLeadHistory.CustomerState.FirstOrDefault().Value } };
                    }
                }
                if (!(string.IsNullOrEmpty(existingLeadHistory?.CustomerLocation?.LastOrDefault().Value) && string.IsNullOrEmpty(newLeadHistory?.CustomerLocation?.FirstOrDefault().Value)) && existingLeadHistory?.CustomerLocation?.LastOrDefault().Value != newLeadHistory?.CustomerLocation?.FirstOrDefault().Value)
                {
                    if (existingLeadHistory?.CustomerLocation != null)
                    {
                        existingLeadHistory?.CustomerLocation.Add(version, newLeadHistory.CustomerLocation.FirstOrDefault().Value);
                    }
                    else
                    {
                        existingLeadHistory.CustomerLocation = new Dictionary<int, string>() { { version, newLeadHistory.CustomerLocation.FirstOrDefault().Value } };
                    }
                }
                if (!(string.IsNullOrEmpty(existingLeadHistory?.ChannelPartnerName?.LastOrDefault().Value) && string.IsNullOrEmpty(newLeadHistory?.ChannelPartnerName?.FirstOrDefault().Value)) && existingLeadHistory?.ChannelPartnerName?.LastOrDefault().Value != newLeadHistory?.ChannelPartnerName?.FirstOrDefault().Value)
                {
                    if (existingLeadHistory?.ChannelPartnerName != null)
                    {
                        existingLeadHistory?.ChannelPartnerName.Add(version, newLeadHistory.ChannelPartnerName.FirstOrDefault().Value);
                    }
                    else
                    {
                        existingLeadHistory.ChannelPartnerName = new Dictionary<int, string>() { { version, newLeadHistory.ChannelPartnerName.FirstOrDefault().Value } };
                    }
                }
                if (!(string.IsNullOrEmpty(existingLeadHistory?.ChannelPartnerExecutiveName?.LastOrDefault().Value) && string.IsNullOrEmpty(newLeadHistory?.ChannelPartnerExecutiveName?.FirstOrDefault().Value)) && existingLeadHistory?.ChannelPartnerExecutiveName?.LastOrDefault().Value != newLeadHistory?.ChannelPartnerExecutiveName?.FirstOrDefault().Value)
                {
                    if (existingLeadHistory?.ChannelPartnerExecutiveName != null)
                    {
                        existingLeadHistory?.ChannelPartnerExecutiveName.Add(version, newLeadHistory.ChannelPartnerExecutiveName.FirstOrDefault().Value);
                    }
                    else
                    {
                        existingLeadHistory.ChannelPartnerExecutiveName = new Dictionary<int, string>() { { version, newLeadHistory.ChannelPartnerExecutiveName.FirstOrDefault().Value } };
                    }
                }
                if (!(string.IsNullOrEmpty(existingLeadHistory?.ChannelPartnerContactNo?.LastOrDefault().Value) && string.IsNullOrEmpty(newLeadHistory?.ChannelPartnerContactNo?.FirstOrDefault().Value)) && existingLeadHistory?.ChannelPartnerContactNo?.LastOrDefault().Value != newLeadHistory?.ChannelPartnerContactNo?.FirstOrDefault().Value)
                {
                    if (existingLeadHistory?.ChannelPartnerContactNo != null)
                    {
                        existingLeadHistory?.ChannelPartnerContactNo.Add(version, newLeadHistory.ChannelPartnerContactNo.FirstOrDefault().Value);
                    }
                    else
                    {
                        existingLeadHistory.ChannelPartnerContactNo = new Dictionary<int, string>() { { version, newLeadHistory.ChannelPartnerContactNo.FirstOrDefault().Value } };
                    }
                }
                if (!(string.IsNullOrEmpty(existingLeadHistory?.ChannelPartners?.LastOrDefault().Value) && string.IsNullOrEmpty(newLeadHistory?.ChannelPartners?.FirstOrDefault().Value)) && existingLeadHistory?.ChannelPartners?.LastOrDefault().Value != newLeadHistory?.ChannelPartners?.FirstOrDefault().Value)
                {
                    if (existingLeadHistory?.ChannelPartners != null)
                    {
                        existingLeadHistory.ChannelPartners?.Add(version, newLeadHistory?.ChannelPartners?.FirstOrDefault().Value);
                    }
                    else
                    {
                        existingLeadHistory.ChannelPartners = new Dictionary<int, string>() { { version, newLeadHistory?.ChannelPartners?.FirstOrDefault().Value } };
                    }
                }
                if (!(string.IsNullOrEmpty(existingLeadHistory?.SecondaryUser?.LastOrDefault().Value) && string.IsNullOrEmpty(newLeadHistory?.SecondaryUser?.FirstOrDefault().Value)) && existingLeadHistory?.SecondaryUser?.LastOrDefault().Value != newLeadHistory?.SecondaryUser?.FirstOrDefault().Value)
                {
                    if (existingLeadHistory?.SecondaryUser != null)
                    {
                        existingLeadHistory.SecondaryUser?.Add(version, newLeadHistory?.SecondaryUser?.FirstOrDefault().Value);
                    }
                    else
                    {
                        existingLeadHistory.SecondaryUser = new Dictionary<int, string>() { { version, newLeadHistory?.SecondaryUser?.FirstOrDefault().Value } };
                    }
                }
                if (existingLeadHistory?.SecondaryUserId?.LastOrDefault().Value != newLeadHistory?.SecondaryUserId?.FirstOrDefault().Value)
                {
                    if (existingLeadHistory?.SecondaryUserId != null)
                    {
                        existingLeadHistory.SecondaryUserId.Add(version, newLeadHistory.SecondaryUserId.FirstOrDefault().Value);
                    }
                    else
                    {
                        existingLeadHistory.SecondaryUserId = new Dictionary<int, Guid>() { { version, newLeadHistory.SecondaryUserId.FirstOrDefault().Value } };
                    }
                }
                if (existingLeadHistory != null && newLeadHistory != null)
                {
                    existingLeadHistory.UserId = newLeadHistory.UserId;
                }

                if (existingLeadHistory?.BookedDate?.LastOrDefault().Value != newLeadHistory?.BookedDate?.FirstOrDefault().Value)
                {
                    if (existingLeadHistory?.BookedDate != null)
                    {
                        existingLeadHistory?.BookedDate?.Add(version, newLeadHistory?.BookedDate?.FirstOrDefault().Value);
                    }
                    else
                    {
                        existingLeadHistory.BookedDate = new Dictionary<int, DateTime?>() { { version, newLeadHistory?.BookedDate?.FirstOrDefault().Value } };
                    }
                }
                if (!(string.IsNullOrEmpty(existingLeadHistory?.BookedByUser?.LastOrDefault().Value) && string.IsNullOrEmpty(newLeadHistory?.BookedByUser?.FirstOrDefault().Value)) && existingLeadHistory?.BookedByUser?.LastOrDefault().Value != newLeadHistory?.BookedByUser?.FirstOrDefault().Value)
                {
                    if (existingLeadHistory?.BookedByUser != null)
                    {
                        existingLeadHistory?.BookedByUser.Add(version, newLeadHistory.BookedByUser.FirstOrDefault().Value);
                    }
                    else
                    {
                        existingLeadHistory.BookedByUser = new Dictionary<int, string>() { { version, newLeadHistory.BookedByUser.FirstOrDefault().Value } };
                    }
                }
                if (existingLeadHistory?.BookedBy?.LastOrDefault().Value != newLeadHistory?.BookedBy?.FirstOrDefault().Value)
                {
                    if (existingLeadHistory?.BookedBy != null)
                    {
                        existingLeadHistory?.BookedBy.Add(version, newLeadHistory.BookedBy.FirstOrDefault().Value);
                    }
                    else
                    {
                        existingLeadHistory.BookedBy = new Dictionary<int, Guid>() { { version, newLeadHistory.BookedBy.FirstOrDefault().Value } };
                    }
                }
                if (!(string.IsNullOrEmpty(existingLeadHistory?.CustomFlags?.LastOrDefault().Value) && string.IsNullOrEmpty(newLeadHistory?.CustomFlags?.FirstOrDefault().Value)) && existingLeadHistory?.CustomFlags?.LastOrDefault().Value != newLeadHistory?.CustomFlags?.FirstOrDefault().Value)
                {
                    if (existingLeadHistory?.CustomFlags != null)
                    {
                        existingLeadHistory.CustomFlags?.Add(version, newLeadHistory?.CustomFlags?.FirstOrDefault().Value);
                    }
                    else
                    {
                        existingLeadHistory.CustomFlags = new Dictionary<int, string>() { { version, newLeadHistory?.CustomFlags?.FirstOrDefault().Value } };
                    }
                }
                if (!(string.IsNullOrEmpty(existingLeadHistory?.Agencies?.LastOrDefault().Value) && string.IsNullOrEmpty(newLeadHistory?.Agencies?.FirstOrDefault().Value)) && existingLeadHistory?.Agencies?.LastOrDefault().Value != newLeadHistory?.Agencies?.FirstOrDefault().Value)
                {
                    if (existingLeadHistory?.Agencies != null)
                    {
                        existingLeadHistory.Agencies?.Add(version, newLeadHistory?.Agencies?.FirstOrDefault().Value);
                    }
                    else
                    {
                        existingLeadHistory.Agencies = new Dictionary<int, string>() { { version, newLeadHistory?.Agencies?.FirstOrDefault().Value } };
                    }
                }
                if (!(string.IsNullOrEmpty(existingLeadHistory?.Designation?.LastOrDefault().Value) && string.IsNullOrEmpty(newLeadHistory?.Designation?.FirstOrDefault().Value)) && existingLeadHistory?.Designation?.LastOrDefault().Value != newLeadHistory?.Designation?.FirstOrDefault().Value)
                {
                    if (existingLeadHistory?.Designation != null)
                    {
                        existingLeadHistory?.Designation.Add(version, newLeadHistory?.Designation.FirstOrDefault().Value);
                    }
                    else
                    {
                        existingLeadHistory.Designation = new Dictionary<int, string>() { { version, newLeadHistory.Designation.FirstOrDefault().Value } };
                    }
                }

                if (!(string.IsNullOrEmpty(existingLeadHistory?.Campaigns?.LastOrDefault().Value) && string.IsNullOrEmpty(newLeadHistory?.Campaigns?.FirstOrDefault().Value)) && existingLeadHistory?.Campaigns?.LastOrDefault().Value != newLeadHistory?.Campaigns?.FirstOrDefault().Value)
                {
                    if (existingLeadHistory?.Campaigns != null)
                    {
                        existingLeadHistory.Campaigns?.Add(version, newLeadHistory?.Campaigns?.FirstOrDefault().Value);
                    }
                    else
                    {
                        existingLeadHistory.Campaigns = new Dictionary<int, string>() { { version, newLeadHistory?.Campaigns?.FirstOrDefault().Value } };
                    }
                }
                if (!(string.IsNullOrEmpty(existingLeadHistory?.ChannelPartners?.LastOrDefault().Value) && string.IsNullOrEmpty(newLeadHistory?.ChannelPartners?.FirstOrDefault().Value)) && existingLeadHistory?.ChannelPartners?.LastOrDefault().Value != newLeadHistory?.ChannelPartners?.FirstOrDefault().Value)
                {
                    if (existingLeadHistory?.ChannelPartners != null)
                    {
                        existingLeadHistory.ChannelPartners?.Add(version, newLeadHistory?.ChannelPartners?.FirstOrDefault().Value);
                    }
                    else
                    {
                        existingLeadHistory.ChannelPartners = new Dictionary<int, string>() { { version, newLeadHistory?.ChannelPartners?.FirstOrDefault().Value } };
                    }
                }
                if (existingLeadHistory?.Purpose?.LastOrDefault().Value != newLeadHistory?.Purpose?.FirstOrDefault().Value && (newLeadHistory?.Purpose != null))
                {
                    if (existingLeadHistory?.Purpose != null)
                    {
                        existingLeadHistory?.Purpose.Add(version, newLeadHistory?.Purpose?.FirstOrDefault().Value ?? Purpose.None);
                    }
                    else
                    {
                        if (newLeadHistory?.Purpose != null && newLeadHistory?.Purpose?.FirstOrDefault().Value != Purpose.None)
                        {
                            existingLeadHistory.Purpose = new Dictionary<int, Purpose>() { { version, newLeadHistory?.Purpose?.FirstOrDefault().Value ?? Purpose.None } };
                        }
                    }
                }
                if (existingLeadHistory?.PossessionDate?.LastOrDefault().Value != newLeadHistory?.PossessionDate?.FirstOrDefault().Value)
                {
                    if (existingLeadHistory?.PossessionDate != null)
                    {
                        existingLeadHistory?.PossessionDate?.Add(version, newLeadHistory?.PossessionDate?.FirstOrDefault().Value);
                    }
                    else
                    {
                        existingLeadHistory.PossessionDate = new Dictionary<int, DateTime?>() { { version, newLeadHistory?.PossessionDate?.FirstOrDefault().Value } };
                    }
                }
                if (!(string.IsNullOrEmpty(existingLeadHistory.LandLine?.LastOrDefault().Value) && string.IsNullOrEmpty(newLeadHistory.LandLine?.FirstOrDefault().Value)) && existingLeadHistory.LandLine?.LastOrDefault().Value != newLeadHistory.LandLine?.FirstOrDefault().Value)
                {
                    if (existingLeadHistory.LandLine != null)
                    {
                        existingLeadHistory.LandLine.Add(version, newLeadHistory.LandLine?.FirstOrDefault().Value ?? string.Empty);
                    }
                    else
                    {
                        existingLeadHistory.LandLine = new Dictionary<int, string>() { { version, newLeadHistory?.LandLine?.FirstOrDefault().Value ?? string.Empty } };
                    }
                }
                if (existingLeadHistory?.Gender?.LastOrDefault().Value != newLeadHistory?.Gender?.FirstOrDefault().Value && (newLeadHistory?.Gender != null))
                {
                    if (existingLeadHistory?.Gender != null)
                    {
                        existingLeadHistory?.Gender.Add(version, newLeadHistory?.Gender?.FirstOrDefault().Value ?? Gender.NotMentioned);
                    }
                    else
                    {
                        if (newLeadHistory?.Gender != null && newLeadHistory?.Gender?.FirstOrDefault().Value != Gender.NotMentioned)
                        {
                            existingLeadHistory.Gender = new Dictionary<int, Gender>() { { version, newLeadHistory?.Gender?.FirstOrDefault().Value ?? Gender.NotMentioned } };
                        }
                    }
                }
                if (existingLeadHistory?.MaritalStatus?.LastOrDefault().Value != newLeadHistory?.MaritalStatus?.FirstOrDefault().Value && (newLeadHistory?.MaritalStatus != null))
                {
                    if (existingLeadHistory?.MaritalStatus != null)
                    {
                        existingLeadHistory?.MaritalStatus.Add(version, newLeadHistory?.MaritalStatus?.FirstOrDefault().Value ?? MaritalStatusType.NotMentioned);
                    }
                    else
                    {
                        if (newLeadHistory?.MaritalStatus != null && newLeadHistory?.MaritalStatus?.FirstOrDefault().Value != MaritalStatusType.NotMentioned)
                        {
                            existingLeadHistory.MaritalStatus = new Dictionary<int, MaritalStatusType>() { { version, newLeadHistory?.MaritalStatus?.FirstOrDefault().Value ?? MaritalStatusType.NotMentioned } };
                        }
                    }
                }
                if (existingLeadHistory?.DateOfBirth?.LastOrDefault().Value != newLeadHistory?.DateOfBirth?.FirstOrDefault().Value)
                {
                    if (existingLeadHistory?.DateOfBirth != null)
                    {
                        existingLeadHistory?.DateOfBirth?.Add(version, newLeadHistory?.DateOfBirth?.FirstOrDefault().Value);
                    }
                    else
                    {
                        existingLeadHistory.DateOfBirth = new Dictionary<int, DateTime?>() { { version, newLeadHistory?.DateOfBirth?.FirstOrDefault().Value } };
                    }
                }
                if (existingLeadHistory.AppointmentDoneOn?.LastOrDefault().Value != newLeadHistory.AppointmentDoneOn?.FirstOrDefault().Value)
                {
                    if (existingLeadHistory.AppointmentDoneOn != null)
                    {
                        existingLeadHistory.AppointmentDoneOn.Add(version, newLeadHistory.AppointmentDoneOn?.FirstOrDefault().Value);
                    }
                    else
                    {
                        existingLeadHistory.AppointmentDoneOn = new Dictionary<int, DateTime?>() { { version, newLeadHistory.AppointmentDoneOn?.FirstOrDefault().Value } };
                    }
                }
                existingLeadHistory.CurrentVersion = version;
                return existingLeadHistory;
            }
            catch
            {
                existingLeadHistory.CurrentVersion = version;
                return existingLeadHistory;
            }
        }
        public static LeadHistory GetUpdatedMeetingOrVisitLeadHistory(LeadHistory existingLeadHistory, LeadHistory newLeadHistory, AppointmentType meetingOrSiteVisit)
        {
            int version = existingLeadHistory.CurrentVersion + 1;

            existingLeadHistory.LastModifiedBy.Add(version, newLeadHistory.LastModifiedBy.FirstOrDefault().Value);
            if (existingLeadHistory.ModifiedDate?.LastOrDefault().Value != newLeadHistory.ModifiedDate?.FirstOrDefault().Value)
            {
                if (existingLeadHistory.ModifiedDate != null) { existingLeadHistory.ModifiedDate.Add(version, newLeadHistory.ModifiedDate.FirstOrDefault().Value); } else { existingLeadHistory.ModifiedDate = new Dictionary<int, DateTime>() { { version, newLeadHistory.ModifiedDate.FirstOrDefault().Value } }; };
            }

            if (existingLeadHistory?.LastModifiedByUser == null)
            {
                existingLeadHistory.LastModifiedByUser = new Dictionary<int, Guid>() { { version, newLeadHistory.LastModifiedByUser.FirstOrDefault().Value } };
            }
            else
            {
                existingLeadHistory?.LastModifiedByUser?.Add(version, newLeadHistory.LastModifiedByUser.FirstOrDefault().Value);
            }
            if (meetingOrSiteVisit == AppointmentType.Meeting)
            {
                if (existingLeadHistory?.IsMeetingDone != null)
                {
                    existingLeadHistory?.IsMeetingDone?.Add(version, newLeadHistory.IsMeetingDone.FirstOrDefault().Value);
                }
                else
                {
                    existingLeadHistory.IsMeetingDone = new Dictionary<int, bool>() { { version, newLeadHistory.IsMeetingDone.FirstOrDefault().Value } };
                }
                if (existingLeadHistory?.MeetingLocation != null)
                {
                    existingLeadHistory?.MeetingLocation?.Add(version, newLeadHistory.MeetingLocation.FirstOrDefault().Value);
                }
                else
                {
                    existingLeadHistory.MeetingLocation = new Dictionary<int, Guid>() { { version, newLeadHistory.MeetingLocation.FirstOrDefault().Value } };
                }
            }
            else if (meetingOrSiteVisit == AppointmentType.SiteVisit)
            {
                if (existingLeadHistory?.IsSiteVisitDone != null)
                {
                    existingLeadHistory?.IsSiteVisitDone?.Add(version, newLeadHistory.IsSiteVisitDone.FirstOrDefault().Value);
                }
                else
                {
                    existingLeadHistory.IsSiteVisitDone = new Dictionary<int, bool>() { { version, newLeadHistory.IsSiteVisitDone.FirstOrDefault().Value } };
                }

                if (existingLeadHistory?.SiteLocation != null)
                {
                    existingLeadHistory?.SiteLocation?.Add(version, newLeadHistory.SiteLocation.FirstOrDefault().Value);
                }
                else
                {
                    existingLeadHistory.SiteLocation = new Dictionary<int, Guid>() { { version, newLeadHistory.SiteLocation.FirstOrDefault().Value } };
                }
            }
            if (!(string.IsNullOrEmpty(existingLeadHistory?.Documents?.LastOrDefault().Value) && string.IsNullOrEmpty(newLeadHistory?.Documents?.FirstOrDefault().Value)) && existingLeadHistory?.Documents?.LastOrDefault().Value != newLeadHistory?.Documents?.FirstOrDefault().Value)
            {
                if (existingLeadHistory?.Documents != null)
                {
                    existingLeadHistory.Documents?.Add(version, newLeadHistory.Documents?.FirstOrDefault().Value);
                }
                else
                {
                    existingLeadHistory.Documents = new Dictionary<int, string>() { { version, newLeadHistory?.Documents?.FirstOrDefault().Value } };
                }
            }
            if (!(string.IsNullOrEmpty(existingLeadHistory?.SecondaryFromUser?.LastOrDefault().Value) && string.IsNullOrEmpty(newLeadHistory?.SecondaryFromUser?.FirstOrDefault().Value)) && existingLeadHistory?.SecondaryFromUser?.LastOrDefault().Value != newLeadHistory?.SecondaryFromUser?.FirstOrDefault().Value)
            {
                if (existingLeadHistory?.SecondaryFromUser != null)
                {
                    existingLeadHistory.SecondaryFromUser?.Add(version, newLeadHistory.SecondaryFromUser?.FirstOrDefault().Value);
                }
                else
                {
                    existingLeadHistory.SecondaryFromUser = new Dictionary<int, string>() { { version, newLeadHistory?.SecondaryFromUser?.FirstOrDefault().Value } };
                }
            }
            if (existingLeadHistory?.PossesionType?.LastOrDefault().Value != newLeadHistory?.PossesionType?.FirstOrDefault().Value)
            {
                if (existingLeadHistory?.PossesionType != null)
                {
                    existingLeadHistory.PossesionType.Add(version, newLeadHistory.PossesionType.FirstOrDefault().Value);
                }
                else
                {
                    existingLeadHistory.PossesionType = new Dictionary<int, PossesionType>() { { version, newLeadHistory?.PossesionType.FirstOrDefault().Value ?? PossesionType.None } };
                }
            }
            if (!(string.IsNullOrEmpty(existingLeadHistory.LandLine?.LastOrDefault().Value) && string.IsNullOrEmpty(newLeadHistory.LandLine?.FirstOrDefault().Value)) && existingLeadHistory.LandLine?.LastOrDefault().Value != newLeadHistory.LandLine?.FirstOrDefault().Value)
            {
                if (existingLeadHistory.LandLine != null)
                {
                    existingLeadHistory.LandLine.Add(version, newLeadHistory.LandLine?.FirstOrDefault().Value ?? string.Empty);
                }
                else
                {
                    existingLeadHistory.LandLine = new Dictionary<int, string>() { { version, newLeadHistory?.LandLine?.FirstOrDefault().Value ?? string.Empty } };
                }
            }
            existingLeadHistory.CurrentVersion = version;
            return existingLeadHistory;
        }

        public static LeadHistory MapV1LeadHistory(this LeadHistory? existingLeadHistory, UserDetailsDto? currentUser, UserDetailsDto? assignedFromUser, UserDetailsDto? assignToUser, bool withNewStatus)
        {
            int version = 1;
            LeadHistory leadHistory = new();
            leadHistory.LeadId = existingLeadHistory.LeadId;
            leadHistory.CreatedDate = DateTime.UtcNow;
            leadHistory.ModifiedDate = new Dictionary<int, DateTime>() { { version, DateTime.UtcNow } };
            leadHistory.LastModifiedBy = new Dictionary<int, string>() { { version, $"{currentUser?.FirstName}{currentUser?.LastName}" ?? string.Empty } };
            leadHistory.Name = new Dictionary<int, string>() { { version, existingLeadHistory?.Name?.FirstOrDefault(i => i.Key == version).Value ?? string.Empty } };
            leadHistory.ContactNo = new Dictionary<int, string>() { { version, existingLeadHistory?.ContactNo?.FirstOrDefault(i => i.Key == version).Value ?? string.Empty } };
            leadHistory.AlternateContactNo = new Dictionary<int, string>() { { version, existingLeadHistory?.AlternateContactNo?.FirstOrDefault(i => i.Key == version).Value ?? string.Empty } };
            leadHistory.Email = new Dictionary<int, string>() { { version, existingLeadHistory?.Email?.FirstOrDefault(i => i.Key == version).Value ?? string.Empty } };
            leadHistory.ScheduledDate = new Dictionary<int, DateTime?>() { { version, existingLeadHistory?.ScheduledDate?.FirstOrDefault(i => i.Key == version).Value ?? null } };
            leadHistory.RevertDate = new Dictionary<int, DateTime?>() { { version, existingLeadHistory?.RevertDate?.FirstOrDefault(i => i.Key == version).Value ?? null } };
            leadHistory.LeadNumber = new Dictionary<int, string>() { { version, existingLeadHistory?.LeadNumber?.FirstOrDefault(i => i.Key == version).Value ?? string.Empty } };
            leadHistory.ChosenProject = new Dictionary<int, string>() { { version, existingLeadHistory?.ChosenProject?.FirstOrDefault(i => i.Key == version).Value ?? string.Empty } };
            leadHistory.ChosenProperty = new Dictionary<int, string>() { { version, existingLeadHistory?.ChosenProperty?.FirstOrDefault(i => i.Key == version).Value ?? string.Empty } };
            leadHistory.BookedUnderName = new Dictionary<int, string>() { { version, existingLeadHistory?.BookedUnderName?.FirstOrDefault(i => i.Key == version).Value ?? string.Empty } };
            leadHistory.ShareCount = new Dictionary<int, int>() { { version, existingLeadHistory?.ShareCount?.FirstOrDefault(i => i.Key == version).Value ?? 0 } };
            leadHistory.SoldPrice = new Dictionary<int, string>() { { version, existingLeadHistory?.SoldPrice?.FirstOrDefault(i => i.Key == version).Value ?? string.Empty } };
            leadHistory.Rating = new Dictionary<int, string>() { { version, existingLeadHistory?.Rating?.FirstOrDefault(i => i.Key == version).Value ?? string.Empty } };
            leadHistory.EnquiredFor = new Dictionary<int, EnquiryType>() { { version, existingLeadHistory?.EnquiredFor?.FirstOrDefault(i => i.Key == version).Value ?? EnquiryType.None } };
            if (existingLeadHistory?.SaleType?.Any() ?? false)
            {
                leadHistory.SaleType = new Dictionary<int, SaleType>() { { version, existingLeadHistory?.SaleType?.FirstOrDefault(i => i.Key == version).Value ?? SaleType.None } };
            }
            leadHistory.BasePropertyType = new Dictionary<int, string> { { version, existingLeadHistory?.BasePropertyType?.FirstOrDefault(i => i.Key == version).Value ?? string.Empty } };
            leadHistory.SubPropertyType = new Dictionary<int, string>() { { version, existingLeadHistory?.SubPropertyType?.FirstOrDefault(i => i.Key == version).Value ?? string.Empty } };
            leadHistory.NoOfBHK = new Dictionary<int, string>() { { version, existingLeadHistory?.NoOfBHK?.FirstOrDefault(i => i.Key == version).Value ?? string.Empty } };
            leadHistory.BHKType = new Dictionary<int, string>() { { version, existingLeadHistory?.BHKType?.FirstOrDefault(i => i.Key == version).Value ?? string.Empty } };
            leadHistory.BHKs = new Dictionary<int, string>() { { version, existingLeadHistory?.BHKs?.FirstOrDefault(i => i.Key == version).Value ?? string.Empty } };
            leadHistory.BHKTypes = new Dictionary<int, string>() { { version, existingLeadHistory?.BHKTypes?.FirstOrDefault(i => i.Key == version).Value ?? string.Empty } };
            leadHistory.EnquiryTypes = new Dictionary<int, string>() { { version, existingLeadHistory?.EnquiryTypes?.FirstOrDefault(i => i.Key == version).Value ?? string.Empty } };
            leadHistory.LowerBudget = new Dictionary<int, long>() { { version, existingLeadHistory?.LowerBudget?.FirstOrDefault(i => i.Key == version).Value ?? 0 } };
            leadHistory.UpperBudget = new Dictionary<int, long>() { { version, existingLeadHistory?.UpperBudget?.FirstOrDefault(i => i.Key == version).Value ?? 0 } };
            leadHistory.Area = new Dictionary<int, double>() { { version, existingLeadHistory?.Area?.FirstOrDefault(i => i.Key == version).Value ?? 0 } };
            leadHistory.AreaUnit = new Dictionary<int, string>() { { version, existingLeadHistory?.AreaUnit?.FirstOrDefault(i => i.Key == version).Value ?? string.Empty } };
            leadHistory.Notes = new Dictionary<int, string>() { { version, existingLeadHistory?.Notes?.FirstOrDefault(i => i.Key == version).Value ?? string.Empty } };
            leadHistory.ConfidentialNotes = new Dictionary<int, string>() { { version, existingLeadHistory?.ConfidentialNotes?.FirstOrDefault(i => i.Key == version).Value ?? string.Empty } };
            leadHistory.EnquiredCity = new Dictionary<int, string>() { { version, existingLeadHistory?.EnquiredCity?.FirstOrDefault(i => i.Key == version).Value ?? string.Empty } };
            leadHistory.EnquiredLocation = new Dictionary<int, string>() { { version, existingLeadHistory?.EnquiredLocation?.FirstOrDefault(i => i.Key == version).Value ?? string.Empty } };
            leadHistory.EnquiredState = new Dictionary<int, string>() { { version, existingLeadHistory?.EnquiredState?.FirstOrDefault(i => i.Key == version).Value ?? string.Empty } };
            leadHistory.EnquiredCities = new Dictionary<int, string>() { { version, existingLeadHistory?.EnquiredCities?.FirstOrDefault(i => i.Key == version).Value ?? string.Empty } };
            leadHistory.EnquiredStates = new Dictionary<int, string>() { { version, existingLeadHistory?.EnquiredStates?.FirstOrDefault(i => i.Key == version).Value ?? string.Empty } };
            leadHistory.EnquiredLocations = new Dictionary<int, string>() { { version, existingLeadHistory?.EnquiredLocations?.FirstOrDefault(i => i.Key == version).Value ?? string.Empty } };
            leadHistory.EnquiredTowerName = new Dictionary<int, string>() { { version, existingLeadHistory?.EnquiredTowerName?.FirstOrDefault(i => i.Key == version).Value ?? string.Empty } };
            leadHistory.EnquiredCommunity = new Dictionary<int, string>() { { version, existingLeadHistory?.EnquiredCommunity?.FirstOrDefault(i => i.Key == version).Value ?? string.Empty } };
            leadHistory.EnquiredSubCommunity = new Dictionary<int, string>() { { version, existingLeadHistory?.EnquiredSubCommunity?.FirstOrDefault(i => i.Key == version).Value ?? string.Empty } };
            leadHistory.LeadSource = new Dictionary<int, LeadSource>() { { version, existingLeadHistory?.LeadSource?.FirstOrDefault(i => i.Key == version).Value ?? LeadSource.Direct } };
            leadHistory.EnquiredCountry = new Dictionary<int, string>() { { version, existingLeadHistory?.EnquiredCountry?.FirstOrDefault(i => i.Key == version).Value ?? string.Empty } };
            leadHistory.CustomerCountry = new Dictionary<int, string>() { { version, existingLeadHistory?.CustomerCountry?.FirstOrDefault(i => i.Key == version).Value ?? string.Empty } };
            leadHistory.CustomerTowerName = new Dictionary<int, string>() { { version, existingLeadHistory?.CustomerTowerName?.FirstOrDefault(i => i.Key == version).Value ?? string.Empty } };
            leadHistory.CustomerSubCommunity = new Dictionary<int, string>() { { version, existingLeadHistory?.CustomerSubCommunity?.FirstOrDefault(i => i.Key == version).Value ?? string.Empty } };
            leadHistory.CustomerCommunity = new Dictionary<int, string>() { { version, existingLeadHistory?.CustomerCommunity?.FirstOrDefault(i => i.Key == version).Value ?? string.Empty } };
            leadHistory.CustomerCity = new Dictionary<int, string>() { { version, existingLeadHistory?.CustomerCity?.FirstOrDefault(i => i.Key == version).Value ?? string.Empty } };
            leadHistory.CustomerState = new Dictionary<int, string>() { { version, existingLeadHistory?.CustomerState?.FirstOrDefault(i => i.Key == version).Value ?? string.Empty } };
            leadHistory.CustomerLocation = new Dictionary<int, string>() { { version, existingLeadHistory?.CustomerLocation?.FirstOrDefault(i => i.Key == version).Value ?? string.Empty } };


            if (withNewStatus)
            {
                leadHistory.BaseLeadStatus = new Dictionary<int, string>() { { version, existingLeadHistory?.BaseLeadStatus?.FirstOrDefault(i => i.Key == version).Value ?? string.Empty } };
                leadHistory.SubLeadStatus = new Dictionary<int, string>() { { version, existingLeadHistory?.SubLeadStatus?.FirstOrDefault(i => i.Key == version).Value ?? string.Empty } };
            }
            else
            {
                leadHistory.BaseLeadStatus = new Dictionary<int, string>() { { version, existingLeadHistory?.BaseLeadStatus?.LastOrDefault().Value ?? string.Empty } };
                leadHistory.SubLeadStatus = new Dictionary<int, string>() { { version, existingLeadHistory?.SubLeadStatus?.LastOrDefault().Value ?? string.Empty } };
            }
            leadHistory.IsHighlighted = new Dictionary<int, bool>() { { version, existingLeadHistory?.IsHighlighted?.FirstOrDefault(i => i.Key == version).Value ?? false } };
            leadHistory.IsEscalated = new Dictionary<int, bool>() { { version, existingLeadHistory?.IsEscalated?.FirstOrDefault(i => i.Key == version).Value ?? false } };
            leadHistory.IsHotLead = new Dictionary<int, bool>() { { version, existingLeadHistory?.IsHotLead?.FirstOrDefault(i => i.Key == version).Value ?? false } };
            leadHistory.IsIntegrationLead = new Dictionary<int, bool>() { { version, existingLeadHistory?.IsIntegrationLead?.FirstOrDefault(i => i.Key == version).Value ?? false } };
            leadHistory.IsAboutToConvert = new Dictionary<int, bool>() { { version, existingLeadHistory?.IsAboutToConvert?.FirstOrDefault(i => i.Key == version).Value ?? false } };
            leadHistory.AssignedTo = new Dictionary<int, Guid>() { { version, assignToUser?.Id ?? default } };
            leadHistory.AssignedToUser = new Dictionary<int, string>() { { version, $"{assignToUser?.FirstName} {assignToUser?.LastName}" ?? string.Empty } };
            // leadHistory.AssignedFromUser = new Dictionary<int, string>() { { version, $"{assignedFromUser?.FirstName}{assignedFromUser?.LastName}" ?? string.Empty } };
            leadHistory.IsColdLead = new Dictionary<int, bool>() { { version, existingLeadHistory?.IsColdLead?.FirstOrDefault(i => i.Key == version).Value ?? false } };
            leadHistory.IsWarmLead = new Dictionary<int, bool>() { { version, existingLeadHistory?.IsWarmLead?.FirstOrDefault(i => i.Key == version).Value ?? false } };
            leadHistory.Documents = new Dictionary<int, string>() { { version, existingLeadHistory?.Documents?.FirstOrDefault(i => i.Key == version).Value ?? string.Empty } };
            //if(existingLeadHistory?.ContactRecords?.Any() ?? false)
            //{
            //    leadHistory.ContactRecords = new Dictionary<int, ContactType>() { { version, existingLeadHistory?.ContactRecords?.FirstOrDefault(i => i.Key == version).Value ?? default } };
            //}
            if (leadHistory?.Beds?.Any() ?? false)
            {
                leadHistory.Beds = new Dictionary<int, string>() { { version, existingLeadHistory?.Beds?.LastOrDefault().Value ?? string.Empty } };
            }
            if (leadHistory?.Baths?.Any() ?? false)
            {
                leadHistory.Baths = new Dictionary<int, string>() { { version, existingLeadHistory?.Baths?.LastOrDefault().Value ?? string.Empty } };
            }
            if (leadHistory?.Floors?.Any() ?? false)
            {
                leadHistory.Floors = new Dictionary<int, string>() { { version, existingLeadHistory?.Floors?.LastOrDefault().Value ?? string.Empty } };
            }
            if (leadHistory?.OfferType != null)
            {
                leadHistory.OfferType = new Dictionary<int, OfferType>() { { version, existingLeadHistory?.OfferType?.FirstOrDefault(i => i.Key == version).Value ?? OfferType.None } };
            }
            if (leadHistory?.Furnished != null)
            {
                leadHistory.Furnished = new Dictionary<int, FurnishStatus>() { { version, existingLeadHistory?.Furnished?.FirstOrDefault(i => i.Key == version).Value ?? FurnishStatus.Unknown } };
            }
            leadHistory.LastModifiedByUser = new Dictionary<int, Guid>() { { version, currentUser?.Id ?? Guid.Empty } };
            leadHistory.Projects = new Dictionary<int, string>() { { version, existingLeadHistory?.Projects?.FirstOrDefault(i => i.Key == version).Value ?? string.Empty } };
            leadHistory.Properties = new Dictionary<int, string>() { { version, existingLeadHistory?.Properties?.FirstOrDefault(i => i.Key == version).Value ?? string.Empty } };
            leadHistory.CustomFlags = new Dictionary<int, string>() { { version, existingLeadHistory?.CustomFlags?.FirstOrDefault(i => i.Key == version).Value ?? string.Empty } };
            leadHistory.SubSource = new Dictionary<int, string>() { { version, existingLeadHistory?.SubSource?.FirstOrDefault(i => i.Key == version).Value ?? string.Empty } };
            leadHistory.Currency = new Dictionary<int, string>() { { version, existingLeadHistory?.Currency?.FirstOrDefault(i => i.Key == version).Value ?? string.Empty } };
            leadHistory.CarpetArea = new Dictionary<int, double>() { { version, existingLeadHistory?.CarpetArea?.FirstOrDefault(i => i.Key == version).Value ?? default } };
            leadHistory.ConversionFactor = new Dictionary<int, float>() { { version, existingLeadHistory?.ConversionFactor?.FirstOrDefault(i => i.Key == version).Value ?? default } };
            leadHistory.CarpetAreaUnit = new Dictionary<int, string>() { { version, existingLeadHistory?.CarpetAreaUnit?.FirstOrDefault(i => i.Key == version).Value ?? string.Empty } };
            leadHistory.PossessionDate = new Dictionary<int, DateTime?>() { { version, existingLeadHistory?.PossessionDate?.FirstOrDefault(i => i.Key == version).Value } };
            leadHistory.ReferralContactNo = new Dictionary<int, string>() { { version, existingLeadHistory?.ReferralContactNo?.FirstOrDefault(i => i.Key == version).Value ?? string.Empty } };
            leadHistory.ReferralEmail = new Dictionary<int, string>() { { version, existingLeadHistory?.ReferralEmail?.FirstOrDefault(i => i.Key == version).Value ?? string.Empty } };
            leadHistory.ReferralName = new Dictionary<int, string>() { { version, existingLeadHistory?.ReferralName?.FirstOrDefault(i => i.Key == version).Value ?? string.Empty } };
            leadHistory.AgencyName = new Dictionary<int, string>() { { version, existingLeadHistory?.AgencyName?.FirstOrDefault(i => i.Key == version).Value ?? string.Empty } };
            leadHistory.CompanyName = new Dictionary<int, string>() { { version, existingLeadHistory?.CompanyName?.FirstOrDefault(i => i.Key == version).Value ?? string.Empty } };
            leadHistory.ChildLeadsCount = new Dictionary<int, int>() { { version, existingLeadHistory?.ChildLeadsCount?.FirstOrDefault(i => i.Key == version).Value ?? default } };
            leadHistory.Designation = new Dictionary<int, string>() { { version, existingLeadHistory?.Designation?.FirstOrDefault(i => i.Key == version).Value ?? string.Empty } };
            leadHistory.BulkCategory = new Dictionary<int, BulkType>() { { version, existingLeadHistory?.BulkCategory?.FirstOrDefault(i => i.Key == version).Value ?? BulkType.None } };
            leadHistory.DuplicateLeadVersion = existingLeadHistory?.DuplicateLeadVersion;
            leadHistory.BuiltUpArea = new Dictionary<int, double>() { { version, existingLeadHistory?.BuiltUpArea?.FirstOrDefault(i => i.Key == version).Value ?? default } };
            leadHistory.BuiltUpAreaConversionFactor = new Dictionary<int, float>() { { version, existingLeadHistory?.BuiltUpAreaConversionFactor?.FirstOrDefault(i => i.Key == version).Value ?? default } };
            leadHistory.BuiltUpAreaUnit = new Dictionary<int, string>() { { version, existingLeadHistory?.BuiltUpAreaUnit?.FirstOrDefault(i => i.Key == version).Value ?? string.Empty } };
            leadHistory.SaleableArea = new Dictionary<int, double>() { { version, existingLeadHistory?.SaleableArea?.FirstOrDefault(i => i.Key == version).Value ?? default } };
            leadHistory.SaleableAreaConversionFactor = new Dictionary<int, float>() { { version, existingLeadHistory?.SaleableAreaConversionFactor?.FirstOrDefault(i => i.Key == version).Value ?? default } };
            leadHistory.SaleableAreaUnit = new Dictionary<int, string>() { { version, existingLeadHistory?.SaleableAreaUnit?.FirstOrDefault(i => i.Key == version).Value ?? string.Empty } };
            leadHistory.NetArea = new Dictionary<int, double>() { { version, existingLeadHistory?.NetArea?.FirstOrDefault(i => i.Key == version).Value ?? default } };
            leadHistory.NetAreaConversionFactor = new Dictionary<int, float>() { { version, existingLeadHistory?.NetAreaConversionFactor?.FirstOrDefault(i => i.Key == version).Value ?? default } };
            leadHistory.NetAreaUnit = new Dictionary<int, string>() { { version, existingLeadHistory?.NetAreaUnit?.FirstOrDefault(i => i.Key == version).Value ?? string.Empty } };
            leadHistory.PropertyArea = new Dictionary<int, double>() { { version, existingLeadHistory?.PropertyArea?.FirstOrDefault(i => i.Key == version).Value ?? default } };
            leadHistory.PropertyAreaConversionFactor = new Dictionary<int, float>() { { version, existingLeadHistory?.PropertyAreaConversionFactor?.FirstOrDefault(i => i.Key == version).Value ?? default } };
            leadHistory.PropertyAreaUnit = new Dictionary<int, string>() { { version, existingLeadHistory?.PropertyAreaUnit?.FirstOrDefault(i => i.Key == version).Value ?? string.Empty } };
            leadHistory.UnitName = new Dictionary<int, string>() { { version, existingLeadHistory?.UnitName?.FirstOrDefault(i => i.Key == version).Value ?? string.Empty } };
            leadHistory.ClusterName = new Dictionary<int, string>() { { version, existingLeadHistory?.ClusterName?.FirstOrDefault(i => i.Key == version).Value ?? string.Empty } };
            leadHistory.Nationality = new Dictionary<int, string>() { { version, existingLeadHistory?.Nationality?.FirstOrDefault(i => i.Key == version).Value ?? string.Empty } };
            leadHistory.UserId = assignToUser?.Id ?? Guid.Empty;
            leadHistory.PossesionType = new Dictionary<int, PossesionType>() { { version, existingLeadHistory?.PossesionType?.FirstOrDefault(i => i.Key == version).Value ?? PossesionType.None } };
            leadHistory.CurrentVersion = version;

            leadHistory.DateOfBirth = new Dictionary<int, DateTime?>() { { version, existingLeadHistory?.DateOfBirth?.FirstOrDefault(i => i.Key == version).Value } };
            if (leadHistory?.Purpose != null)
            {
                leadHistory.Purpose = new Dictionary<int, Purpose>() { { version, existingLeadHistory?.Purpose?.FirstOrDefault(i => i.Key == version).Value ?? Purpose.None } };
            }
            leadHistory.LandLine = new Dictionary<int, string>() { { version, existingLeadHistory?.LandLine?.FirstOrDefault(i => i.Key == version).Value ?? string.Empty } };
            if (leadHistory?.Gender != null)
            {
                leadHistory.Gender = new Dictionary<int, Gender>() { { version, existingLeadHistory?.Gender?.FirstOrDefault(i => i.Key == version).Value ?? Gender.NotMentioned } };
            }
            if (leadHistory?.MaritalStatus != null)
            {
                leadHistory.MaritalStatus = new Dictionary<int, MaritalStatusType>() { { version, existingLeadHistory?.MaritalStatus?.FirstOrDefault(i => i.Key == version).Value ?? MaritalStatusType.NotMentioned } };
            }
            leadHistory.AppointmentDoneOn = new Dictionary<int, DateTime?>() { { version, existingLeadHistory?.AppointmentDoneOn?.FirstOrDefault().Value ?? null } };
            return leadHistory;
        }
        public static LeadHistory GetUpdatedLeadHistory(LeadHistory existingLeadHistory, LeadHistory newLeadHistory, LeadHistory leadHistoryToUpdate)
        {
            int version = leadHistoryToUpdate.CurrentVersion + 1;
            leadHistoryToUpdate?.LastModifiedBy?.Add(version, newLeadHistory?.LastModifiedBy?.FirstOrDefault().Value);

            if (existingLeadHistory.ModifiedDate?.LastOrDefault().Value != newLeadHistory.ModifiedDate?.FirstOrDefault().Value)
            {
                if (leadHistoryToUpdate?.ModifiedDate != null) { leadHistoryToUpdate.ModifiedDate.Add(version, newLeadHistory.ModifiedDate.FirstOrDefault().Value); } else { leadHistoryToUpdate.ModifiedDate = new Dictionary<int, DateTime>() { { version, newLeadHistory.ModifiedDate.FirstOrDefault().Value } }; };
            }


            if (leadHistoryToUpdate?.LastModifiedByUser == null)
            {
                leadHistoryToUpdate.LastModifiedByUser = new Dictionary<int, Guid>() { { version, newLeadHistory.LastModifiedByUser.FirstOrDefault().Value } };
            }
            else
            {
                leadHistoryToUpdate?.LastModifiedByUser?.Add(version, newLeadHistory.LastModifiedByUser.FirstOrDefault().Value);
            }
            if (!(string.IsNullOrEmpty(existingLeadHistory?.Projects?.LastOrDefault().Value) && string.IsNullOrEmpty(newLeadHistory?.Projects?.FirstOrDefault().Value)) && existingLeadHistory?.Projects?.LastOrDefault().Value != newLeadHistory?.Projects?.FirstOrDefault().Value)
            {
                if (leadHistoryToUpdate?.Projects != null)
                {
                    leadHistoryToUpdate.Projects?.Add(version, newLeadHistory?.Projects?.FirstOrDefault().Value);
                }
                else
                {
                    leadHistoryToUpdate.Projects = new Dictionary<int, string>() { { version, newLeadHistory?.Projects?.FirstOrDefault().Value } };
                }
            }
            if (!(string.IsNullOrEmpty(existingLeadHistory?.SubSource?.LastOrDefault().Value) && string.IsNullOrEmpty(newLeadHistory?.SubSource?.FirstOrDefault().Value)) && existingLeadHistory?.SubSource?.LastOrDefault().Value != newLeadHistory?.SubSource?.FirstOrDefault().Value)
            {
                if (leadHistoryToUpdate?.SubSource != null)
                {
                    leadHistoryToUpdate.SubSource.Add(version, newLeadHistory?.SubSource?.FirstOrDefault().Value);
                }
                else
                {
                    leadHistoryToUpdate.SubSource = new Dictionary<int, string>() { { version, newLeadHistory.SubSource?.FirstOrDefault().Value } };
                }
            }
            if ((!(string.IsNullOrEmpty(existingLeadHistory?.Currency?.LastOrDefault().Value)) && !(string.IsNullOrEmpty(newLeadHistory?.Currency?.FirstOrDefault().Value))) && existingLeadHistory?.Currency.LastOrDefault().Value != newLeadHistory?.Currency.FirstOrDefault().Value)
            {
                if (leadHistoryToUpdate?.Currency != null)
                {
                    leadHistoryToUpdate.Currency.Add(version, newLeadHistory?.Currency?.FirstOrDefault().Value);
                }
                else
                {
                    leadHistoryToUpdate.Currency = new Dictionary<int, string>() { { version, newLeadHistory?.Currency?.FirstOrDefault().Value } };
                }
            }
            if (existingLeadHistory.LeadSource?.LastOrDefault().Value != newLeadHistory?.LeadSource?.FirstOrDefault().Value)
            {
                leadHistoryToUpdate.LeadSource.Add(version, newLeadHistory.LeadSource.FirstOrDefault().Value);
            }
            //if (!(string.IsNullOrEmpty(existingLeadHistory?.AssignedToUser?.LastOrDefault().Value) && string.IsNullOrEmpty(newLeadHistory?.AssignedToUser?.FirstOrDefault().Value)) && existingLeadHistory?.AssignedToUser?.LastOrDefault().Value != newLeadHistory?.AssignedToUser?.FirstOrDefault().Value)
            //{
            //    if (leadHistoryToUpdate?.AssignedToUser != null)
            //    {
            //        leadHistoryToUpdate.AssignedToUser?.Add(version, newLeadHistory.AssignedToUser?.FirstOrDefault().Value);
            //    }
            //    else
            //    {
            //        leadHistoryToUpdate.AssignedToUser = new Dictionary<int, string>() { { version, newLeadHistory?.AssignedToUser?.FirstOrDefault().Value } };
            //    }
            //}
            //if (!(string.IsNullOrEmpty(existingLeadHistory?.AssignedFromUser?.LastOrDefault().Value) && string.IsNullOrEmpty(newLeadHistory?.AssignedFromUser?.FirstOrDefault().Value)) && existingLeadHistory?.AssignedFromUser?.LastOrDefault().Value != newLeadHistory?.AssignedFromUser?.FirstOrDefault().Value)
            //{
            //    if (leadHistoryToUpdate?.AssignedFromUser != null)
            //    {
            //        leadHistoryToUpdate.AssignedFromUser?.Add(version, newLeadHistory?.AssignedFromUser?.FirstOrDefault().Value);
            //    }
            //    else
            //    {
            //        leadHistoryToUpdate.AssignedFromUser = new Dictionary<int, string>() { { version, newLeadHistory?.AssignedFromUser?.FirstOrDefault().Value } };
            //    }
            //}
            //if (existingLeadHistory?.AssignedTo?.LastOrDefault().Value != newLeadHistory?.AssignedTo?.FirstOrDefault().Value)
            //{
            //    leadHistoryToUpdate?.AssignedTo?.Add(version, newLeadHistory.AssignedTo.FirstOrDefault().Value);
            //}
            if (!(string.IsNullOrEmpty(existingLeadHistory.BaseLeadStatus.LastOrDefault().Value) && string.IsNullOrEmpty(newLeadHistory.BaseLeadStatus.FirstOrDefault().Value)) && existingLeadHistory.BaseLeadStatus.LastOrDefault().Value != newLeadHistory.BaseLeadStatus.FirstOrDefault().Value)
            {
                leadHistoryToUpdate.BaseLeadStatus.Add(version, newLeadHistory.BaseLeadStatus.FirstOrDefault().Value);
            }
            if (!(string.IsNullOrEmpty(existingLeadHistory.SubLeadStatus.LastOrDefault().Value) && string.IsNullOrEmpty(newLeadHistory.SubLeadStatus.FirstOrDefault().Value)) && existingLeadHistory.SubLeadStatus.LastOrDefault().Value != newLeadHistory.SubLeadStatus.FirstOrDefault().Value)
            {
                leadHistoryToUpdate.SubLeadStatus.Add(version, (newLeadHistory.BaseLeadStatus.FirstOrDefault().Value == newLeadHistory.SubLeadStatus.FirstOrDefault().Value) ? "" : newLeadHistory.SubLeadStatus.FirstOrDefault().Value);
            }
            leadHistoryToUpdate.CurrentVersion = version;
            return leadHistoryToUpdate;
        }
        private static string FormatDuration(string durationStr)
        {
            if (double.TryParse(durationStr, out double duration))
            {
                int durationInSeconds = (int)Math.Floor(duration); // or Math.Round(duration)
                int minutes = durationInSeconds / 60;
                int seconds = durationInSeconds % 60;

                if (minutes > 0 && seconds > 0)
                    return $"{minutes} min {seconds} sec";
                else if (minutes > 0)
                    return $"{minutes} min";
                else
                    return $"{seconds} sec";
            }

            return durationStr; // fallback if parsing fails
        }
        private static LeadHistoryDto BuildAuditModelForLeadAssignmentTypeType(int version, Dictionary<int, LeadAssignmentType> value, PropertyInfo property, LeadHistory leadHistory)
        {
            return new LeadHistoryDto()
            {
                FieldName = SplitCamelCase(property.Name),
                FilterKey = LeadHistoryFilterKey.None,
                NewValue = EnumHelper.GetEnumDescription(GetAuditCurrentValue(version, value)),
                OldValue = EnumHelper.GetEnumDescription(GetAuditPreviousValue(version, value)),
                UpdatedBy = GetAuditUpdateByValue(version, leadHistory),
                UpdatedOn = GetAuditUpdatedOnValue(version, leadHistory),
                AuditActionType = version == 1 ? EnumHelper.GetEnumDescription(AuditActionType.Created) : EnumHelper.GetEnumDescription(AuditActionType.Updated)
            };
        }
    }
}
