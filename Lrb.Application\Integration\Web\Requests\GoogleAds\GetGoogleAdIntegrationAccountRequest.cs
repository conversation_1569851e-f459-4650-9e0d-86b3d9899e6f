﻿
using Lrb.Application.Agency.Web;
using Lrb.Application.Common.GoogleAds;
using Lrb.Domain.Entities.Integration;

namespace Lrb.Application.Integration.Web.Requests
{
    public class GetGoogleAdIntegrationAccountRequest : IRequest<PagedResponse<GoogleAdIntegrationDto, string>>
    {
        public string? SearchByName { get; set; }
    }
    public class GetGoogleAdIntegrationAccountRequestHandler : IRequestHandler<GetGoogleAdIntegrationAccountRequest, PagedResponse<GoogleAdIntegrationDto, string>>
    {
        private readonly IReadRepository<GoogleAdLeadFormIntegrationInfo> _integrationRepo;
        private readonly IReadRepository<IntegrationAccountInfo> _integrationAccInfo;
        private readonly IRepositoryWithEvents<GoogleAdLeadFormData> _googleAdLeadFormRepo;
        private readonly IGoogleAdsService _googleAdsMetricsFetcher;
        public GetGoogleAdIntegrationAccountRequestHandler(IReadRepository<GoogleAdLeadFormIntegrationInfo> integrationRepo,
            IReadRepository<IntegrationAccountInfo> integrationAccInfo,
            IRepositoryWithEvents<GoogleAdLeadFormData> googleAdLeadFormRepo,
            IGoogleAdsService googleAdsMetricsFetcher)
        {
            _integrationRepo = integrationRepo;
            _integrationAccInfo = integrationAccInfo;
            _googleAdLeadFormRepo = googleAdLeadFormRepo;
            _googleAdsMetricsFetcher = googleAdsMetricsFetcher;
        }
        public async Task<PagedResponse<GoogleAdIntegrationDto, string>> Handle(GetGoogleAdIntegrationAccountRequest request, CancellationToken cancellationToken)
        {
            var googleAdAccounts = await _integrationRepo.ListAsync(new GoogleAdIntegrationSpec(request), cancellationToken);
            var googleAdLeadFormData = await _googleAdLeadFormRepo.ListAsync(new GoogleAdLeadFormDataSpec(googleAdAccounts.Select(i => i.ApiKey).ToList()));
            var count = await _integrationRepo.CountAsync(new GoogleAdIntegrationCountSpec(request), cancellationToken);
            var GoogleAdAccDtosdtos = googleAdAccounts.Adapt<List<GoogleAdIntegrationDto>>();

            foreach (var googleAdAcc in GoogleAdAccDtosdtos)
            {
                var integrationAccInfo = (await _integrationAccInfo.ListAsync(new IntegrationAccInfoByGoogleAdIdOrId(googleAdAcc.Id), cancellationToken)).FirstOrDefault();
                //var forms = googleAdLeadFormData
                //.Where(i => i.Googlekey == integrationAccInfo?.ApiKey)
                //.ToList();

                googleAdAcc.AccountId = integrationAccInfo?.Id ?? Guid.Empty;
                googleAdAcc.LeadCount = integrationAccInfo?.LeadCount ?? 0;
                googleAdAcc.AgencyName = integrationAccInfo?.AgencyName;
                googleAdAcc.Agency = integrationAccInfo?.Agency?.Adapt<AgencyDto>();
                googleAdAcc.LeadSource = LeadSource.GoogleAds;
                googleAdAcc.Status = googleAdAcc.LeadCount > 0 ? "Completed" : "InComplete";
                var campaignMetrics = new List<CampaignMetrics>();
                //var metrics = await _googleAdsMetricsFetcher.GetMetricsAsync(7656276774L, null, null);
                //foreach (var group in forms
                //    .Where(f => f.CampaignId.HasValue && f.AdgroupId.HasValue)
                //    .GroupBy(f => new { f.CampaignId, f.AdgroupId }))
                //{
                //    var campaignId = group.Key.CampaignId.Value;
                //    var adGroupId = group.Key.AdgroupId.Value;

                //    // Replace this with actual customerId retrieval if available
                //    var customerId = 7656276774L;

                //    try
                //    {
                //        var metrics = await _googleAdsMetricsFetcher.GetMetricsAsync(customerId, campaignId, adGroupId);
                //        if (metrics != null)
                //            campaignMetrics.Add(metrics);
                //    }
                //    catch (Exception ex)
                //    {
                //        // Optional: log error here with ex.Message or similar
                //    }
                //}

                // Optionally assign aggregated metrics back to googleAdAcc
                // Example: sum cost, leads, ROI, etc. if you want a summary per account
                if (campaignMetrics.Any())
                {
                    googleAdAcc.CplInr = campaignMetrics.Sum(m => m.CplInr);
                    googleAdAcc.RoiPercent = campaignMetrics.Sum(m => m.RoiPercent ?? 0);
                    googleAdAcc.CampaignBudget = campaignMetrics.Sum(m => m.CampaignBudget ?? 0);
                    googleAdAcc.AdBudget = campaignMetrics.Sum(m => m.CampaignBudget ?? 0);
                    // etc.
                }
            }

            return new PagedResponse<GoogleAdIntegrationDto, string>(GoogleAdAccDtosdtos, count);
        }

    }
    public class CampaignMetrics
    {
        public long? CampaignId { get; set; }
        public long? AdId { get; set; }
        public decimal CostInr { get; set; }
        public long? LeadCount { get; set; }
        public decimal? ConversionValueInr { get; set; }
        public decimal? CplInr { get; set; }
        public decimal? RoiPercent { get; set; }
        public decimal? CampaignBudget { get; set; }
        public decimal? AdBudget { get; set; }
    }
}
