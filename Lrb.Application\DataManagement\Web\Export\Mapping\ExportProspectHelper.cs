﻿using DocumentFormat.OpenXml.Spreadsheet;
using Lrb.Application.DailyStatusUpdates.Dtos;
using Lrb.Application.DataManagement.Web.Export.Dtos;
using Lrb.Application.DataManagement.Web.Request;
using Lrb.Application.Identity.Users;
using Lrb.Domain.Entities;
using Lrb.Domain.Entities.MasterData;
using OfficeOpenXml.Style;
using System.Globalization;
using System;
using Lrb.Application.Lead.Utils;
using DocumentFormat.OpenXml.Bibliography;

namespace Lrb.Application.DataManagement.Web.Export.Mapping
{
    public class ExportProspectHelper
    {
        public static List<ExportProspectDto> ConvertToExportProspectDto(
            List<Prospect> prospects,
            List<UserDetailsDto> users,
            List<CustomProspectStatus> statuses = null,
            List<MasterProspectSource> sources = null,
            List<MasterPropertyType> masterPropertyTypes = null,
            List<NotesDetails>? notes = null, bool? isWithNotes = null, string ? timeZoneId=null, TimeSpan baseUTcOffset = default)
        {
            List<ExportProspectDto> exportProspects = new List<ExportProspectDto>();
            if (prospects.Any())
            {
                foreach (var prospect in prospects)
                {
                    var prospectDto = prospect.Adapt<ExportProspectDto>();
                    var enquiry = prospect?.Enquiries?.FirstOrDefault(i => i.IsPrimary);
                    if (enquiry != null)
                    {
                        enquiry.Adapt(prospectDto);
                        prospectDto.PropertyType = masterPropertyTypes?.FirstOrDefault(i => i.Id == enquiry?.PropertyTypes?.Select(i => i.BaseId).FirstOrDefault())?.DisplayName ?? string.Empty;
                        var propertySubType = enquiry.PropertyTypes?.Where(i => i.Level == 1).Select(i => i.DisplayName).ToList();
                        if (enquiry.Address != null)
                        {
                            enquiry.Address.Adapt(prospectDto);
                        }
                        prospectDto.EnquiredTypes = string.Join(",", enquiry?.EnquiryTypes?.ToList() ?? new()) ?? string.Empty;
                        prospectDto.BHKTypes = string.Join(",", enquiry?.BHKTypes?.ToList() ?? new()) ?? string.Empty;
                        prospectDto.BHKs = string.Join(",", enquiry?.BHKs?.ToList() ?? new()) ?? string.Empty;
                        prospectDto.City = string.Join(",", enquiry?.Addresses?.Where(i=>!string.IsNullOrEmpty(i.City)).Select(i => i.City)?.Distinct().ToList() ?? new());
                        prospectDto.State = string.Join(",", enquiry?.Addresses?.Where(i=>!string.IsNullOrEmpty(i.State)).Select(i => i.State)?.Distinct().ToList() ?? new());
                        prospectDto.Country = string.Join(",", enquiry?.Addresses?.Where(i=>!string.IsNullOrEmpty(i.Country)).Select(i => i.Country)?.Distinct().ToList() ?? new());
                        prospectDto.Locality = string.Join(",", enquiry?.Addresses?.Where(i=>!string.IsNullOrEmpty(i.Locality)).Select(i => i.Locality)?.Distinct().ToList() ?? new());
                        prospectDto.SubLocality = string.Join(",", enquiry?.Addresses?.Where(i=>!string.IsNullOrEmpty(i.SubLocality)).Select(i => i.SubLocality)?.Distinct().ToList() ?? new());
                        prospectDto.PropertySubType = propertySubType != null && propertySubType.Any() ? string.Join(", ", propertySubType) : string.Empty;
                        prospectDto.Community = string.Join(",", enquiry?.Addresses?.Where(i => !string.IsNullOrWhiteSpace(i?.Community))?.Select(i => i.Community)?.Distinct().ToList() ?? new List<string?>());
                        prospectDto.SubCommunity = string.Join(",", enquiry?.Addresses?.Where(i => !string.IsNullOrWhiteSpace(i?.SubCommunity))?.Select(i => i.SubCommunity)?.Distinct().ToList() ?? new List<string?>());
                        prospectDto.TowerName = string.Join(",", enquiry?.Addresses?.Where(i => !string.IsNullOrWhiteSpace(i?.TowerName))?.Select(i => i.TowerName)?.Distinct().ToList() ?? new List<string?>());
                        prospectDto.Beds = string.Join(",", enquiry?.Beds?.ToList() ?? new()) ?? string.Empty;

                        if (enquiry?.Source != null)
                        {
                            prospectDto.DataSource = enquiry.Source.DisplayName ?? string.Empty;
                        }

                    }
                    var user = users.FirstOrDefault(i => i.Id == prospectDto.AssignTo);
                    if (user != null)
                    {
                        prospectDto.AssignedUserName = user.FirstName + " " + user.LastName;
                        prospectDto.AssignedUserEmail = user.Email;
                        prospectDto.AssignedUserPhoneNumber = user.PhoneNumber;
                    }
                    if(prospect?.Status?.BaseId != null && prospect?.Status?.BaseId != Guid.Empty)
                    {
                        prospectDto.Status = statuses?.FirstOrDefault(i => i.Id == prospect?.Status?.BaseId)?.DisplayName ?? string.Empty;
                    }
                    else
                    {
                        prospectDto.Status = prospect?.Status?.Level == 0 ? prospect?.Status?.DisplayName : string.Empty;
                    }
                    prospectDto.Reason = prospect?.Status?.Level == 1 ? prospect?.Status?.DisplayName : string.Empty;
                    prospectDto.ReceivedOn = prospect.CreatedOn;
                    prospectDto.Projects = string.Join(",", prospect?.Projects?.Select(i => i.Name)?.ToList() ?? new List<string?>());
                    prospectDto.Properties = string.Join(",", prospect?.Properties?.Select(i => i.Title)?.ToList() ?? new List<string?>());
                    prospectDto.PossessionDate = prospect?.PossesionDate;
                    var customeraddress = prospect?.Address;
                        prospectDto.CustomerTowerName = customeraddress?.TowerName ?? string.Empty;
                        prospectDto.CustomerSubCommunity = customeraddress?.SubCommunity ?? string.Empty;
                        prospectDto.CustomerCommunity = customeraddress?.Community ?? string.Empty;
                        prospectDto.CustomerPostalCode = customeraddress?.PostalCode ?? string.Empty;
                        prospectDto.CustomerCountry = customeraddress?.Country ?? string.Empty;
                        prospectDto.CustomerCity = customeraddress?.City ?? string.Empty;
                        prospectDto.CustomerLocality = customeraddress?.SubLocality ?? string.Empty;
                        prospectDto.CustomerState = customeraddress?.State ?? string.Empty;
                        prospectDto.CustomerSubCommunity = customeraddress?.SubCommunity ?? string.Empty;

                    if (isWithNotes == true)
                    {
                        var notesForProspect = notes
                            .Where(notes => notes.ProspectId == prospect.Id)
                            .ToList();

                        if (notesForProspect?.Any() ?? false)
                        {
                            prospectDto.NotesDetails = new List<NotesDetails>(notesForProspect);
                        }
                    }
                    prospectDto.AgencyName = string.Join(",", prospect?.Agencies?.Select(i => i.Name)?.ToList() ?? new List<string?>());
                    prospectDto.ChannelPartnerEmail = string.Join(",", prospect?.ChannelPartners?.Select(i => i.Email)?.ToList() ?? new List<string?>());
                    prospectDto.ChannelPartnerMobile = string.Join(",", prospect?.ChannelPartners?.Select(i => i.ContactNo)?.ToList() ?? new List<string?>());
                    prospectDto.ChannelPartnerName = string.Join(",", prospect?.ChannelPartners?.Select(i => i.FirmName)?.ToList() ?? new List<string?>());
                    prospectDto.CampaignNames = string.Join(",", prospect?.Campaigns?.Select(i => i.Name)?.ToList() ?? new List<string?>());

                    exportProspects.Add(prospectDto);
                }
            }
            return exportProspects;
        }

        public static string ReplaceVariables(string text, Dictionary<string, string> variableSet)
        {
            if (variableSet == null || !variableSet.Any() || string.IsNullOrWhiteSpace(text))
            {
                return text;
            }
            foreach (var variable in variableSet)
            {
                text = text.Replace(variable.Key, String.Format(variable.Value).Replace(" ", "%20") ?? string.Empty);
            }
            return text;
        }

        public static string ConvertToString(ProspectVisiblity filter)
        {
            switch (filter)
            {
                case ProspectVisiblity.SelfWithReportee:
                    return "AllData";
                case ProspectVisiblity.Self:
                    return "MyData";
                case ProspectVisiblity.Reportee:
                    return "TeamData";
                default:
                    return string.Empty;
            }
        }
        public static List<NotesDetails> GetAllDatawithNotes(List<NotesDetails> noteDetailsData, int notes)
        {
            return noteDetailsData
                    .Where(n => !string.IsNullOrEmpty(n.Notes))
                    .GroupBy(n => n.ProspectId)
                    .SelectMany(group => group
                        .OrderByDescending(n => n.ModifiedOn)
                        .Take(notes))
                    .ToList();
        }


    }
}
