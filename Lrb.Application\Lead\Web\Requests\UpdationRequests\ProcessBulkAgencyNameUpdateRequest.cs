﻿using Lrb.Application.Identity.Users;
using Lrb.Shared.Extensions;
using Microsoft.Extensions.DependencyInjection;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Lrb.Application.Lead.Web.Requests.UpdationRequests
{
    public class ProcessBulkAgencyNameUpdateRequest : IRequest<PagedResponse<DuplicateLeadAssigmentResponseDto, CountDto>>
    {
        public List<Guid>? Ids { get; set; }
        public string? TenantId { get; set; }
        public Guid? CurrentUserId { get; set; }
        public Guid? TrackerId { get; set; }
        public BulkType? BulkCategory { get; set; }
        public List<string>? AgencyNames { get; set; }
        public bool? ShouldRemoveExistingAgency { get; set; }
    }
    public class ProcessBulkAgencyNameUpdateRequestHandler : IRequestHandler<ProcessBulkAgencyNameUpdateRequest, PagedResponse<DuplicateLeadAssigmentResponseDto, CountDto>>
    {
        protected readonly IRepositoryWithEvents<BulkCommonTracker> _bulkCommonTracker;
        protected readonly ILeadRepositoryAsync _leadRepositoryAsync;
        protected readonly IUserService _userService;
        protected readonly IServiceProvider _serviceProvider;
        protected readonly ICurrentUser _currentUser;

        public ProcessBulkAgencyNameUpdateRequestHandler(IServiceProvider serviceProvider, IRepositoryWithEvents<BulkCommonTracker> bulkCommonTracker,
            ILeadRepositoryAsync leadRepositoryAsync,
            ICurrentUser currentUser,
            IUserService userService)
        {


            _bulkCommonTracker = bulkCommonTracker;
            _leadRepositoryAsync = leadRepositoryAsync;
            _serviceProvider = serviceProvider;
            _currentUser = currentUser;
            _userService = userService;
        }
        public async Task<PagedResponse<DuplicateLeadAssigmentResponseDto, CountDto>> Handle(ProcessBulkAgencyNameUpdateRequest request, CancellationToken cancellationToken)
        {
            BulkCommonTracker? tracker = null;
            try
            {
                int totalCount = 0;
                int count = 0;
                if (request.Ids?.Any() ?? false)
                {
                    int leadsPerchunk = request.Ids.Count >= 50 ? 25 : request.Ids.Count;
                    var currentUser = request.CurrentUserId ?? _currentUser.GetUserId();
                    string tenantId = request.TenantId ?? _currentUser.GetTenant();
                    var commonTracker = new BulkCommonTracker();
                    if (request.Ids.Any() && request.Ids.Count >= 50)
                    {
                        commonTracker = new BulkCommonTracker()
                        {
                            TotalCount = request.Ids.Count(),
                            Status = UploadStatus.InProgress,
                            RawJson = request.Serialize(),
                            ClassType = $"BulkAgencyUpdate",
                            Module = "lead",
                            CreatedBy = currentUser,
                            LastModifiedBy = currentUser,
                        };
                        tracker = await _bulkCommonTracker.AddAsync(commonTracker);
                        request.TrackerId = tracker?.Id ?? default;
                    }

                    request.CurrentUserId = request.CurrentUserId ?? currentUser;
                    request.TenantId = request.TenantId ?? _currentUser.GetTenant();
                    var chunks = request.Ids.Chunk(leadsPerchunk).Select(i => new ConcurrentBag<Guid>(i));
                    List<Task> tasks = new List<Task>();
                    var totalLeadCount = request.Ids.Count;
                    try
                    {
                        foreach (var chunk in chunks.ToList())
                        {
                            var newRequest = request.Adapt<UpdateBulkAgencyListRequest>();
                            newRequest.Ids = chunk.ToList();

                            var task = Task.Run(async () =>
                            {

                                using (var scope = _serviceProvider.CreateScope())
                                {
                                    var mediator = scope.ServiceProvider.GetService<IMediator>();

                                    var count = await mediator.Send(newRequest);
                                    Interlocked.Add(ref totalCount, count.Data);

                                }

                            });
                            tasks.Add(task);
                        }
                    }
                    catch (Exception ex) { }
                    int maxDegreeOfParallelism = 10;
                    using var semaphore = new SemaphoreSlim(maxDegreeOfParallelism);

                    var exceptions = new ConcurrentBag<Exception>();

                    var taskList = tasks.Select(async task =>
                    {
                        await semaphore.WaitAsync();
                        try
                        {
                            await task;
                        }
                        catch (Exception ex)
                        {
                            exceptions.Add(ex);
                        }
                        finally
                        {
                            semaphore.Release();
                        }
                    }).ToList();

                    await Task.WhenAll(taskList);

                    #region Update Common Bulk Upload Tracker
                    if (request.TrackerId != null && request.TrackerId != Guid.Empty)
                    {
                        tracker = await _bulkCommonTracker.GetByIdAsync(request.TrackerId);
                        tracker.TotalUploadedCount = tracker.TotalUploadedCount + totalCount;
                        tracker.Status = tracker.TotalUploadedCount < tracker.TotalCount ? UploadStatus.InProgress : UploadStatus.Completed;
                        tracker.DistinctCount = tracker.DistinctCount + totalCount;
                        tracker.UpdatedCount = tracker.UpdatedCount + totalCount;
                        await _bulkCommonTracker.UpdateAsync(tracker);
                    }
                    #endregion
                }
            }
            catch (Exception ex)
            {
                if (tracker != null)
                {
                    tracker.Message = ex.ToString();
                    await _bulkCommonTracker.UpdateAsync(tracker);
                }
                throw new NotFoundException("No leads found by the provided lead Ids.");

            }

            return new();
        }

    }
}