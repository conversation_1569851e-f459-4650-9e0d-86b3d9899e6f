﻿using Lrb.Application.Integration.Web.Specs;
using Lrb.Domain.Entities.Integration;

namespace Lrb.Application.Integration.Web.Requests.GoogleAds
{
    public class DeleteGoogleAdsAccountRequest : IRequest<Response<bool>>
    {
        public Guid Id { get; set; }
        public DeleteGoogleAdsAccountRequest(Guid id)
        {
            Id = id;
        }
    }
    public class DeleteGoogleAdsAccountRequestHandler : IRequestHandler<DeleteGoogleAdsAccountRequest, Response<bool>>
    {
        private readonly IRepositoryWithEvents<GoogleAdsAuthResponse> _googleAdsAuthResponseRepo;
        private readonly IRepositoryWithEvents<IntegrationAccountInfo> _integrationAccInfoRepo;
        private readonly IRepositoryWithEvents<GoogleAdsInfo> _googleAdsRepo;
        private readonly IRepositoryWithEvents<GoogleCampaign> _googleAdsCampaignRepo;
        public DeleteGoogleAdsAccountRequestHandler(IRepositoryWithEvents<GoogleAdsAuthResponse> googleAdsAuthResponseRepo,
            IRepositoryWithEvents<IntegrationAccountInfo> integrationAccInfoRepo,
            IRepositoryWithEvents<GoogleAdsInfo> googleAdsRepo,
            IRepositoryWithEvents<GoogleCampaign> googleAdsCampaignRepo)
        {
            _googleAdsAuthResponseRepo = googleAdsAuthResponseRepo;
            _integrationAccInfoRepo = integrationAccInfoRepo;
            _googleAdsRepo = googleAdsRepo;
            _googleAdsCampaignRepo = googleAdsCampaignRepo;
        }
        public async Task<Response<bool>> Handle(DeleteGoogleAdsAccountRequest request, CancellationToken cancellationToken)
        {
            var googleAuthResponse = await _googleAdsAuthResponseRepo.GetByIdAsync(request.Id);
            if (googleAuthResponse == null) { throw new Exception("No googleAd account found by this Id."); }
            var integrationAccounts = await _integrationAccInfoRepo.ListAsync(new GoogleAdsAccountsSpec(googleAuthResponse.Id));
            if (integrationAccounts?.Any() ?? false)
            {
                await _integrationAccInfoRepo.DeleteRangeAsync(integrationAccounts);
            }
            var ads = await _googleAdsRepo.ListAsync(new GoogleAdAdsByAccountIdSpec(googleAuthResponse.Id), cancellationToken);
            var campaigns = await _googleAdsCampaignRepo.ListAsync(new GoogleAdCampaignsByAccountIdSpec(googleAuthResponse.Id), cancellationToken);
            if (ads?.Any() ?? false)
            {
                await _googleAdsRepo.DeleteRangeAsync(ads);
            }
            if (campaigns?.Any() ?? false)
            {
                await _googleAdsCampaignRepo.DeleteRangeAsync(campaigns);
            }
            await _googleAdsAuthResponseRepo.SoftDeleteAsync(googleAuthResponse, cancellationToken);
            return new(true, "Integrated account deleted successfully.");
        }
    }
}
