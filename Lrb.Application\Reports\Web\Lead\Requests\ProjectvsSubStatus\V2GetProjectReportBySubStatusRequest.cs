﻿using Lrb.Application.Reports.Web.Dtos.ProjectvsSubStatus;
using Lrb.Application.Utils;
using Lrb.Domain.Entities.MasterData;
using Lrb.Shared.Extensions;
using Newtonsoft.Json;

namespace Lrb.Application.Reports.Web
{
    public class V2GetProjectReportBySubStatusRequest : IRequest<PagedResponse<Dictionary<string, object>, string>>
    {
        public DateTime? FromDate { get; set; }
        public DateTime? ToDate { get; set; }
        public DateType? DateType { get; set; }
        public bool IsWithTeam { get; set; }
        public List<Guid>? UserIds { get; set; }
        public string? SearchText { get; set; }
        public List<LeadSource>? Sources { get; set; }
        public List<string>? AgencyNames { get; set; }
        public List<string>? Projects { get; set; }
        public List<string>? SubSources { get; set; }
        public List<string>? Localites { get; set; }
        public List<string>? States { get; set; }
        public List<string>? Cities { get; set; }
        public ReportPermission? ReportPermission { get; set; }
        public int PageNumber { get; set; } = 1;
        public int PageSize { get; set; } = int.MaxValue;
        public List<string>? Countries { get; set; }

    }
    public class V2GetProjectReportBySubStatusRequestHandler : IRequestHandler<V2GetProjectReportBySubStatusRequest, PagedResponse<Dictionary<string, object>, string>>
    {
        private readonly IDapperRepository _dapperRepository;
        private readonly ICurrentUser _currentUser;
        private readonly IRepositoryWithEvents<CustomMasterLeadStatus> _customMastereadStatus;
        public V2GetProjectReportBySubStatusRequestHandler(
            IDapperRepository dapperRepository,
            ICurrentUser currentUser,
             IRepositoryWithEvents<CustomMasterLeadStatus> customMastereadStatus)
        {
            _dapperRepository = dapperRepository;
            _currentUser = currentUser;
            _customMastereadStatus = customMastereadStatus;

        }
        public async Task<PagedResponse<Dictionary<string, object>, string>> Handle(V2GetProjectReportBySubStatusRequest request, CancellationToken cancellationToken)
        {
            var tenantId = _currentUser.GetTenant();
            var userId = _currentUser.GetUserId();
            List<Guid> teamUserIds = new();
            List<Guid> permittedUserIds = new();
            var isAdmin = await _dapperRepository.IsAdminAsync(userId, tenantId ?? string.Empty);
            if (isAdmin)
            {
                permittedUserIds = (await _dapperRepository.GetAllUserIdsAsync(tenantId ?? string.Empty)).ToList();
            }
            else if (request.ReportPermission != null)
            {
                switch (request.ReportPermission)
                {
                    case ReportPermission.All:
                        permittedUserIds = (await _dapperRepository.GetAllUserIdsAsync(tenantId ?? string.Empty)).ToList();
                        break;
                    case ReportPermission.Reportees:
                        permittedUserIds = (await _dapperRepository.GetSubordinateIdsWithOnlyReporteesAsync(new List<Guid>() { userId }, tenantId ?? string.Empty)).ToList();
                        break;
                }
            }
            if (request?.UserIds?.Any() ?? false)
            {
                if (request?.IsWithTeam ?? false)
                {
                    teamUserIds = (await _dapperRepository.GetSubordinateIdsWithOnlyReporteesAsync(request.UserIds, tenantId ?? string.Empty)).ToList();
                }
                else
                {
                    teamUserIds = request?.UserIds ?? new List<Guid>();
                }
            }
            else
            {
                if (!isAdmin)
                {
                    teamUserIds = permittedUserIds;
                }
            }
            if (teamUserIds.Any())
            {
                teamUserIds = teamUserIds.Where(userId => permittedUserIds.Contains(userId)).ToList();
            }
            else
            {
                teamUserIds = permittedUserIds;
            }
            request.FromDate = request.FromDate.HasValue ? request.FromDate.Value.ConvertFromDateToUtc() : null;
            request.ToDate = request.ToDate.HasValue ? request.ToDate.Value.ConvertToDateToUtc() : null;
            var res = (await _dapperRepository.QueryStoredProcedureFromReadReplicaAsync<LeadProjectBySubStatusDto>("LeadratBlack", "GetLeadProjectReportBySubStatus", new
            {
                fromdate = request.FromDate,
                todate = request.ToDate,
                datetype = request.DateType,
                tenantid = tenantId,
                userids = teamUserIds,
                searchtext = string.IsNullOrEmpty(request.SearchText) ? null : request.SearchText.Replace(" ", "").ToLower(),
                sources = request?.Sources?.ConvertAll(i => (int)i),
                projects = request?.Projects?.ConvertAll<string>(i => i.Replace(" ", "").ToLower()),
                agencynames = request?.AgencyNames?.ConvertAll<string>(i => i.Replace(" ", "").ToLower()),
                subsources = request?.SubSources?.ConvertAll<string>(i => i.Replace(" ", "").ToLower()),
                pagesize = request.PageSize,
                pagenumber = request.PageNumber,
                localites = request?.Localites?.ConvertAll<string>(i => i.LrbNormalize()),
                cities = request?.Cities?.ConvertAll<string>(i => i.LrbNormalize()),
                states = request?.States?.ConvertAll<string>(i => i.LrbNormalize()),
                countries = request?.Countries?.ConvertAll<string>(i => i.Replace(" ", "").ToLower())
            })).ToList();
            res.ForEach(i => i.StatusDtos = JsonConvert.DeserializeObject<List<StatusDto>>(i?.Status ?? string.Empty));
            var groupedResult = res.GroupBy(i => i?.ProjectTitle ?? string.Empty).ToDictionary(i => i.Key, j => j.SelectMany(i => i?.StatusDtos ?? new()).ToList());
            List<Dictionary<string, object>> subStatusByUserDtos = new();
            var customStatuses = await _customMastereadStatus.ListAsync();
            foreach (var group in groupedResult)
            {
                Dictionary<string, object> subStatusByUserDto = new();
                subStatusByUserDto.Add("projectTitle", group.Key);
                subStatusByUserDto.Add("All", group.Value?.Sum(i => i.Count) ?? 0);
                subStatusByUserDto.Add("Active", group.Value?.Where(i => i.BaseStatus != "dropped" && i.BaseStatus != "not_interested" && i.BaseStatus != "booked" && i.BaseStatus != "booking_cancel" && i.BaseStatus != "inovoiced").Sum(i => i.Count) ?? 0);
                subStatusByUserDto.Add("overdue", group.Value?.Sum(i => i.OverdueCount) ?? 0);
                subStatusByUserDto.Add("siteVisitDoneCount", group.Value?.Sum(i => i.SiteVisitDoneCount) ?? 0);
                subStatusByUserDto.Add("siteVisitDoneUniqueCount", group.Value?.Sum(i => i.SiteVisitDoneUniqueCount) ?? 0);
                subStatusByUserDto.Add("siteVisitNotDoneCount", group.Value?.Sum(i => i.SiteVisitNotDoneCount) ?? 0);
                subStatusByUserDto.Add("siteVisitNotDoneUniqueCount", group.Value?.Sum(i => i.SiteVisitNotDoneUniqueCount) ?? 0);
                subStatusByUserDto.Add("meetingDoneCount", group.Value?.Sum(i => i.MeetingDoneCount) ?? 0);
                subStatusByUserDto.Add("meetingDoneUniqueCount", group.Value?.Sum(i => i.MeetingDoneUniqueCount) ?? 0);
                subStatusByUserDto.Add("meetingNotDoneCount", group.Value?.Sum(i => i.MeetingNotDoneCount) ?? 0);
                subStatusByUserDto.Add("meetingNotDoneUniqueCount", group.Value?.Sum(i => i.MeetingNotDoneUniqueCount) ?? 0);
                group.Value?.ForEach(status =>
                {
                    var custumStatus = customStatuses.FirstOrDefault(i => i.Id == status.Id);
                    if (custumStatus?.DisplayName != null)
                    {
                        string subStatusDisplayName = char.ToLower(custumStatus.DisplayName[0]) + custumStatus?.DisplayName?[1..];
                        subStatusByUserDto.Add(subStatusDisplayName.Replace(" ", ""), status.Count);
                    }
                });
                subStatusByUserDtos.Add(subStatusByUserDto);
            }
            return new(subStatusByUserDtos, 0);
        }
    }
}
