﻿using Lrb.Application.Utils;
using Lrb.Shared.Extensions;

namespace Lrb.Application.Reports.Web
{
    public class ProjectReportByStatusRequest : IRequest<PagedResponse<ViewProjectReportDto, string>>
    {
        public DateTime? FromDate { get; set; }
        public DateTime? ToDate { get; set; }
        public DateType? DateType { get; set; }
        public List<string>? Projects { get; set; }
        public bool IsWithTeam { get; set; }
        public List<Guid>? UserIds { get; set; }
        public string? SearchText { get; set; }
        public List<LeadSource>? Sources { get; set; }
        public List<string>? AgencyNames { get; set; }
        public List<string>? SubSources { get; set; }
        public List<string>? Localites { get; set; }
        public List<string>? States { get; set; }
        public List<string>? Cities { get; set; }
        public ReportPermission? ReportPermission { get; set; }
        public int PageNumber { get; set; }
        public int PageSize { get; set; } = int.MaxValue;
        public DateTime? FromDateForProject { get; set; }
        public DateTime? ToDateForProject { get; set; }
        public List<string>? Countries { get; set; }

    }
    public class ProjectReportByStatusRequestHandler : IRequestHandler<ProjectReportByStatusRequest, PagedResponse<ViewProjectReportDto, string>>
    {
        private readonly IDapperRepository _dapperRepository;
        private readonly ICurrentUser _currentUser;
        public ProjectReportByStatusRequestHandler(IDapperRepository dapperRepository, ICurrentUser currentUser)
        {
            _dapperRepository = dapperRepository;
            _currentUser = currentUser;
        }
        public async Task<PagedResponse<ViewProjectReportDto, string>> Handle(ProjectReportByStatusRequest request, CancellationToken cancellationToken)
        {
            var tenantId = _currentUser.GetTenant();
            var userId = _currentUser.GetUserId();
            List<Guid> teamUserIds = new();
            List<Guid> permittedUserIds = new();
            var isAdmin = await _dapperRepository.IsAdminAsync(userId, tenantId ?? string.Empty);
            if (isAdmin)
            {
                permittedUserIds = (await _dapperRepository.GetAllUserIdsAsync(tenantId ?? string.Empty)).ToList();
            }
            else if (request.ReportPermission != null)
            {
                switch (request.ReportPermission)
                {
                    case ReportPermission.All:
                        permittedUserIds = (await _dapperRepository.GetAllUserIdsAsync(tenantId ?? string.Empty)).ToList();
                        break;
                    case ReportPermission.Reportees:
                        permittedUserIds = (await _dapperRepository.GetSubordinateIdsWithOnlyReporteesAsync(new List<Guid>() { userId }, tenantId ?? string.Empty)).ToList();
                        break;
                }
            }
            if (request?.UserIds?.Any() ?? false)
            {
                if (request?.IsWithTeam ?? false)
                {
                    teamUserIds = (await _dapperRepository.GetSubordinateIdsWithOnlyReporteesAsync(request.UserIds, tenantId ?? string.Empty)).ToList();
                }
                else
                {
                    teamUserIds = request?.UserIds ?? new List<Guid>();
                }
            }
            else
            {
                if (!isAdmin)
                {
                    teamUserIds = permittedUserIds;
                }
            }
            if (teamUserIds.Any())
            {
                teamUserIds = teamUserIds.Where(userId => permittedUserIds.Contains(userId)).ToList();
            }
            else
            {
                teamUserIds = permittedUserIds;
            }
            List<ViewProjectReportDto> newReportByProjects = new List<ViewProjectReportDto>();
            request.FromDate = request.FromDate.HasValue ? request.FromDate.Value.ConvertFromDateToUtc() : null;
            request.ToDate = request.ToDate.HasValue ? request.ToDate.Value.ConvertToDateToUtc() : null;
            request.FromDateForProject = request.FromDateForProject.HasValue ? request.FromDateForProject.Value.ConvertFromDateToUtc() : null;
            request.ToDateForProject = request.ToDateForProject.HasValue ? request.ToDateForProject.Value.ConvertFromDateToUtc() : null;
            var res = (await _dapperRepository.QueryStoredProcedureFromReadReplicaAsync<ProjectReportByStatusDto>("LeadratBlack", "Lead_ProjectReportByStatus", new
            {
                fromdate = request.FromDate,
                todate = request.ToDate,
                datetype = request.DateType,
                tenantid = tenantId,
                userids = teamUserIds,
                sources = request?.Sources?.ConvertAll(i => (int)i),
                projects = request?.Projects?.ConvertAll<string>(i => i.Replace(" ", "").ToLower()),
                searchtext = string.IsNullOrEmpty(request.SearchText) ? null : request.SearchText.Replace(" ", "").ToLower(),
                subsources = request?.SubSources?.ConvertAll<string>(i => i.Replace(" ", "").ToLower()),
                agencynames = request?.AgencyNames?.ConvertAll<string>(i => i.Replace(" ", "").ToLower()),
                pagesize = request.PageSize,
                pagenumber = request.PageNumber,
                localites = request?.Localites?.ConvertAll<string>(i => i.LrbNormalize()),
                cities = request?.Cities?.ConvertAll<string>(i => i.LrbNormalize()),
                states = request?.States?.ConvertAll<string>(i => i.LrbNormalize()),
                fromdateformeetingorvisit = request?.FromDateForProject,
                todateformeetingorvisit = request?.ToDateForProject,
                countries = request?.Countries?.ConvertAll<string>(i => i.Replace(" ", "").ToLower())
            })).ToList();
            var data = res.GroupBy(i => new Tuple<Guid, string>(i.Id, i?.Name ?? string.Empty)).ToDictionary(i => i.Key, j => j.ToList());
            foreach (var item in data)
            {
                var value = new ViewProjectReportDto();
                value.Name = item.Key.Item2;
                value.Id = item.Key.Item1;
                value.Status = new List<ViewStatusDto>()
                {
                    new ViewStatusDto()
                    {
                        StatusDisplayName = "AllCount",
                        Count = item.Value?.Select(j => j.AllCount)?.Sum(i => i) ?? 0
                    },
                    new ViewStatusDto()
                    {
                        StatusDisplayName = "ActiveCount",
                        Count = item.Value?.Select(j => j.ActiveCount)?.Sum(i => i) ?? 0
                    },
                    new ViewStatusDto()
                    {
                        StatusDisplayName = "MeetingDoneCount",
                        Count = item.Value?.Select(j => j.MeetingDoneCount)?.Sum(i => i) ?? 0
                    },
                    new ViewStatusDto()
                    {
                        StatusDisplayName = "MeetingDoneUniqueCount",
                        Count = item.Value?.Select(j => j.MeetingDoneUniqueCount)?.Sum(i => i) ?? 0
                    },
                    new ViewStatusDto()
                    {
                        StatusDisplayName = "MeetingNotDoneCount",
                        Count = item.Value?.Select(j => j.MeetingNotDoneCount)?.Sum(i => i) ?? 0
                    },
                    new ViewStatusDto()
                    {
                        StatusDisplayName = "MeetingNotDoneUniqueCount",
                        Count = item.Value?.Select(j => j.MeetingNotDoneUniqueCount)?.Sum(i => i) ?? 0
                    },
                    new ViewStatusDto()
                    {
                        StatusDisplayName = "SiteVisitDoneCount",
                        Count = item.Value?.Select(j => j.SiteVisitDoneCount)?.Sum(i => i) ?? 0
                    },
                    new ViewStatusDto()
                    {
                        StatusDisplayName = "SiteVisitDoneUniqueCount",
                        Count = item.Value?.Select(j => j.SiteVisitDoneUniqueCount)?.Sum(i => i) ?? 0
                    },
                    new ViewStatusDto()
                    {
                        StatusDisplayName = "SiteVisitNotDoneCount",
                        Count = item.Value?.Select(j => j.SiteVisitNotDoneCount)?.Sum(i => i) ?? 0
                    },
                    new ViewStatusDto()
                    {
                        StatusDisplayName = "SiteVisitNotDoneUniqueCount",
                        Count = item.Value?.Select(j => j.SiteVisitNotDoneUniqueCount)?.Sum(i => i) ?? 0
                    },
                    new ViewStatusDto()
                    {
                        StatusDisplayName = "OverdueCount",
                        Count = item.Value?.Select(j => j.OverdueCount)?.Sum(i => i) ?? 0
                    }
                };
                var allItems = item.Value?.Where(i => (i.StatusId != null))?.ToList();
                if (allItems?.Any() ?? false)
                {
                    value.Status.AddRange(ReportHelper.MapCustomStatusAndChildStatus(allItems.Adapt<List<ViewStatusDto>>()));
                }
                newReportByProjects.Add(value);
            }
            return new(newReportByProjects, 0);
        }
    }
}
