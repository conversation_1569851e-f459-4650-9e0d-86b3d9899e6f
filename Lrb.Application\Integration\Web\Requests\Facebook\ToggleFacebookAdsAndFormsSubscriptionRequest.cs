﻿using Lrb.Application.Common.Facebook;
using Lrb.Application.Common.GoogleAds;
using Lrb.Application.Integration.Web.Dtos;
using Lrb.Application.Integration.Web.Specs;
using Lrb.Domain.Entities.Integration;

namespace Lrb.Application.Integration.Web.Requests.Facebook
{
    public class ToggleFacebookAdsAndFormsSubscriptionRequest : IRequest<Response<bool>>
    {
        public List<Guid>? AdIds { get; set; }
        public List<Guid>? FormIds { get; set; }
    }
    public class ToggleFacebookAdsAndFormsSubscriptionRequestHandler : F<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, IRequestHandler<ToggleFacebookAdsAndFormsSubscriptionRequest, Response<bool>>
    {
        public ToggleFacebookAdsAndFormsSubscriptionRequestHandler(
           IRepositoryWithEvents<FacebookAuthResponse> facebookAuthResponseRepo,
           IRepositoryWithEvents<FacebookConnectedPageAccount> facebookConnectedPageAccountRepo,
           IRepositoryWithEvents<FacebookLeadGenForm> facebookLeadGenFormRepo,
           IFacebookService facebookService,
           IJobService hangfireService,
           ITenantIndependentRepository repository,
           ICurrentUser currentUser,
           IRepositoryWithEvents<IntegrationAccountInfo> integrationAccInfoRepo,
           IRepositoryWithEvents<FacebookAdsInfo> fbAdsRepo, ILeadRepositoryAsync leadRepositoryAsync, IGoogleAdsService googleAdsService, IRepositoryWithEvents<GoogleAdsAuthResponse> googleAdsAuthResponseRepo,
           IRepositoryWithEvents<GoogleAdsInfo> googleAdsRepo, IRepositoryWithEvents<GoogleCampaign> googleCampaignsRepo)
           : base(facebookAuthResponseRepo,
                 facebookConnectedPageAccountRepo,
                 facebookLeadGenFormRepo,
                 facebookService,
                 hangfireService,
                 repository,
                 currentUser,
                 integrationAccInfoRepo,
                 fbAdsRepo, leadRepositoryAsync,
                 googleAdsService,
                 googleAdsAuthResponseRepo,
                 googleAdsRepo, googleCampaignsRepo)
        { }
        public async Task<Response<bool>> Handle(ToggleFacebookAdsAndFormsSubscriptionRequest request, CancellationToken cancellationToken)
        {


            if(request?.FormIds?.Any() ?? false)
            {
                var forms = await _facebookLeadGenFormRepo.ListAsync(new FacebookLeadGenFormByIdSpec(request.FormIds)); 
                if(forms.Any())
                {
                    forms.ForEach(x => x.IsSubscribed = !x.IsSubscribed);
                    await _facebookLeadGenFormRepo.UpdateRangeAsync(forms);
                }
            }
            if (request?.AdIds?.Any() ?? false)
            {
                var ads = await _fbAdsRepo.ListAsync(new FacebookAdsByIdsSpec(request.AdIds));
                if (ads?.Count > 0)
                {
                    ads.ForEach(x => x.IsSubscribed = !x.IsSubscribed);
                    await _fbAdsRepo.UpdateRangeAsync(ads);
                }
            }
            return new(true);
        }
    }
}


