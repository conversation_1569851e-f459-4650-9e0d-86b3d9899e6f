﻿using Lrb.Application.Utils;
using Lrb.Shared.Extensions;

namespace Lrb.Application.Reports.Web
{
    public class GetAgencyReportByStatusCountRequest : IRequest<int>
    {
        public string? SearchText { get; set; }
        public List<LeadSource>? Sources { get; set; } = new();
        public DateTime? FromDate { get; set; }
        public DateTime? ToDate { get; set; }
        public DateType? DateType { get; set; }
        public bool IsWithTeam { get; set; }
        public List<string>? Projects { get; set; }
        public List<Guid>? UserIds { get; set; }
        public List<string>? AgencyNames { get; set; }
        public List<string>? SubSources { get; set; }
        public List<string>? Localites { get; set; }
        public List<string>? States { get; set; }
        public List<string>? Cities { get; set; }
        public ReportPermission? ReportPermission { get; set; }
        public int PageNumber { get; set; }
        public int PageSize { get; set; } = int.MaxValue;
        public DateTime? FromDateForAgency { get; set; }
        public DateTime? ToDateForAgency { get; set; }
        public List<string>? Countries { get; set; }
    }
    public class GetAgencyReportByStatusCountRequestHandler : IRequestHandler<GetAgencyReportByStatusCountRequest, int>
    {
        private readonly IDapperRepository _dapperRepository;
        private readonly ICurrentUser _currentUser;

        public GetAgencyReportByStatusCountRequestHandler(IDapperRepository dapperRepository, ICurrentUser currentUser)
        {
            _dapperRepository = dapperRepository;
            _currentUser = currentUser;
        }

        public async Task<int> Handle(GetAgencyReportByStatusCountRequest request, CancellationToken cancellationToken)
        {
            var tenantId = _currentUser.GetTenant();
            var userId = _currentUser.GetUserId();
            List<Guid> teamUserIds = new();
            List<Guid> permittedUserIds = new();
            request.FromDate = request.FromDate.HasValue ? request.FromDate.Value.ConvertFromDateToUtc() : null;
            request.ToDate = request.ToDate.HasValue ? request.ToDate.Value.ConvertToDateToUtc() : null;
            request.FromDateForAgency = request.FromDateForAgency.HasValue ? request.FromDateForAgency.Value.ConvertFromDateToUtc() : null;
            request.ToDateForAgency = request.ToDateForAgency.HasValue ? request.ToDateForAgency.Value.ConvertFromDateToUtc() : null;
            var totalCount = (await _dapperRepository.QueryStoredProcedureCountFromReadReplicaAsync("LeadratBlack", "Lead_GetAgencyReportByStatusCount", new
            {
                tenantid = tenantId,
                searchtext = string.IsNullOrEmpty(request.SearchText) ? null : request.SearchText.Replace(" ", "").ToLower(),
                agencynames = request?.AgencyNames?.ConvertAll<string>(i => i.Replace(" ", "").ToLower()),
            }));
            return totalCount;
        }
    }
}