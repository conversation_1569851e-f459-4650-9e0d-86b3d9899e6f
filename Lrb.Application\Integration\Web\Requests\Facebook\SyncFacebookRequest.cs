﻿using Lrb.Application.Common.Facebook;
using Lrb.Application.Common.GoogleAds;
using Lrb.Application.Common.Interfaces;
using Lrb.Domain.Entities.Integration;

namespace Lrb.Application.Integration.Web.Requests.Facebook
{
    public class SyncFacebookRequest : IRequest<Response<bool>>
    {
        public Guid AccountId { get; set; }
    }
    public class SyncFacebookRequesthandler : <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, IRequestHandler<SyncFacebookRequest, Response<bool>>
    {
        private readonly IJobService _jobService;

        public SyncFacebookRequesthandler(
           IRepositoryWithEvents<FacebookAuthResponse> facebookAuthResponseRepo,
           IRepositoryWithEvents<FacebookConnectedPageAccount> facebookConnectedPageAccountRepo,
           IRepositoryWithEvents<FacebookLeadGenForm> facebookLeadGenFormRepo,
           IFacebookService facebookService,
           IJobService hangfireService,
           ITenantIndependentRepository repository,
           ICurrentUser currentUser,
           IRepositoryWithEvents<IntegrationAccountInfo> integrationAccInfoRepo,
           IRepositoryWithEvents<FacebookAdsInfo> fbAdsRepo,
           IJobService jobService,ILeadRepositoryAsync leadRepositoryAsync,
           IGoogleAdsService googleAdsService, IRepositoryWithEvents<GoogleAdsAuthResponse> googleAdsAuthResponseRepo,
           IRepositoryWithEvents<GoogleAdsInfo> googleAdsRepo,
           IRepositoryWithEvents<GoogleCampaign> googleCampaignsRepo)
           : base(facebookAuthResponseRepo,
                 facebookConnectedPageAccountRepo,
                 facebookLeadGenFormRepo,
                 facebookService,
                 hangfireService,
                 repository,
                 currentUser,
                 integrationAccInfoRepo,
                 fbAdsRepo, leadRepositoryAsync, googleAdsService, googleAdsAuthResponseRepo, googleAdsRepo, googleCampaignsRepo)
        {
            _jobService = jobService;
        }
        public async Task<Response<bool>> Handle(SyncFacebookRequest request, CancellationToken cancellationToken)
        {
            var fbAuthResponse = await _facebookAuthResponseRepo.GetByIdAsync(request.AccountId);
            if (fbAuthResponse == null) { throw new NotFoundException("No facebook account found by this id."); }
            var tenantId = _currentUser.GetTenant();
            if (!string.IsNullOrEmpty(tenantId))
            {
                var tInfo = await _repository.GetTenantInfoAsync(tenantId);
                var dto = new FacebookIntegrationDto()
                {
                    FecebookAuthResponseId = fbAuthResponse.Id,
                    AccessToken = fbAuthResponse.LongLivedUserAccessToken,
                    FacebookUserId = fbAuthResponse.FacebookUserId,
                    TenantInfoDto = tInfo,
                    FacebookAccountName = fbAuthResponse.FacebookAccountName,
                    CurrentUserId = _currentUser.GetUserId()
                };
                _jobService.Enqueue(() => ExecuteFbSyncJobAsync(dto));
            }
            return new(true, "Sync started..., this may take few seconds! ");
        }


        public async Task ExecuteFbSyncJobAsync(FacebookIntegrationDto dto)
        {
            await UpdateAdsDetailsInDB(dto.AccessToken, dto.FecebookAuthResponseId);
            await UpdateFacebookAuthResponse(dto);
        }

    }
}
