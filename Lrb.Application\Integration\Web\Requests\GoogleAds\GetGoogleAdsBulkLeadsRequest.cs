﻿using Lrb.Application.Common.ServiceBus;
using Lrb.Application.Common.TimeZone;
using Lrb.Application.Lead.Web.Export;
using Lrb.Domain.Entities.ErrorModule;
using Lrb.Domain.Entities.Integration;
using Newtonsoft.Json;

namespace Lrb.Application.Integration.Web.Requests.GoogleAds
{
    public class GetGoogleAdsBulkLeadsRequest : IRequest<Response<bool>>
    {
        public DateTime? FromDate { get; set; }
        public DateTime? ToDate { get; set; }
        public string? TimeZoneId { get; set; }
        public TimeSpan BaseUTcOffset { get; set; }
    }
    public class GetGoogleAdsBulkLeadsRequestHandler : IRequestHandler<GetGoogleAdsBulkLeadsRequest, Response<bool>>
    {
        private readonly ICurrentUser _currentUser;
        private readonly IRepositoryWithEvents<GoogleAdsBulkLeadFetchTracker> _repository;
        public const string TYPE = "googleadsbulkleadfetch";
        private readonly ILeadRepositoryAsync _leadRepositoryAsync;
        private readonly IServiceBus _serviceBus;

        public GetGoogleAdsBulkLeadsRequestHandler(ICurrentUser currentUser, IRepositoryWithEvents<GoogleAdsBulkLeadFetchTracker> repository, ILeadRepositoryAsync leadRepositoryAsync,
            IServiceBus serviceBus)
        {
            _currentUser = currentUser;
            _repository = repository;
            _leadRepositoryAsync = leadRepositoryAsync;
            _serviceBus = serviceBus;
        }

        public async Task<Response<bool>> Handle(GetGoogleAdsBulkLeadsRequest request, CancellationToken cancellationToken)
        {
            try
            {
                var tracker = new GoogleAdsBulkLeadFetchTracker()
                {
                    Status = UploadStatus.Initiated,
                    FromDate = request.FromDate,
                    ToDate = request.ToDate,
                    CreatedBy = _currentUser.GetUserId(),
                    LastModifiedBy = _currentUser.GetUserId()
                };
                var timezone = request.Adapt<CommonTimeZoneDto>();
                //Dictionary<string, string> timezone = new();
                //timezone["TimeZoneId"] = request.TimeZoneId ?? string.Empty;
                //timezone["BaseUTcOffset"] = request.BaseUTcOffset.ToString();
                await _repository.AddAsync(tracker);
                var tenantId = _currentUser.GetTenant();
                if (!string.IsNullOrEmpty(tenantId))
                {
                    InputPayload input = new(tracker.Id, tenantId, _currentUser.GetUserId(), TYPE, JsonConvert.SerializeObject(timezone));
                    var stringArgument = JsonConvert.SerializeObject(input);
                    var cmdArgs = new List<string>() { stringArgument };
                    await _serviceBus.RunExcelUploadJobAsync(cmdArgs);
                    return new(true, "Fetching leads from Facebook started..., this may take few minutes! ");
                }
                else
                {
                    return new(false, "Fetching leads from Facebook failed, tenant is not resolved! ");
                }
            }
            catch (Exception ex)
            {
                var error = new LrbError()
                {
                    ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                    ErrorSource = ex?.Source,
                    StackTrace = ex?.StackTrace,
                    InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                    ErrorModule = "GetFacebookBulkLeadsRequestHandler -> Handle()"
                };
                await _leadRepositoryAsync.AddErrorAsync(error);
                return new(false, $"Fetching leads from Facebook failed, Error Message: {ex?.InnerException?.Message ?? ex?.Message}");
            }

        }
    }
}
