﻿using ISO3166;
using Lrb.Application.ChannelPartner.Web.Dtos;
using Lrb.Application.Common.Interfaces;
using Lrb.Application.Lead.Web;
using Lrb.Application.Reports.Web;
using Lrb.Application.Utils;
using Lrb.Domain.Entities;
using Lrb.Domain.Entities.MasterData;
using Nager.Country;
using Microsoft.AspNetCore.Http;
using PhoneNumbers;
using System.Data;
using System.Globalization;
using System.Text.RegularExpressions;
using Lrb.Application.Common.TimeZone;
using Newtonsoft.Json;
using Lrb.Application.Project.Web.Mappings;
using DocumentFormat.OpenXml.Office2010.Excel;
using Lrb.Application.Identity.Users;
using System.Threading;
using Lrb.Application.Common.BlobStorage;
using Lrb.Domain.Entities.AmenitiesAttributes;
using Lrb.Domain.Enums;
using Lrb.Application.Lead.Web.Requests.Bulk_upload_new_implementation;

namespace Lrb.Application.Property.Web
{
    public static class BulkUploadHelper
    {
        public static List<string> GetUnmappedColumnNames(this DataTable table, Dictionary<PropertyDataColumns, string> mappedColumnsData)
        {
            List<string> columns = new();
            if (mappedColumnsData.ContainsKey(PropertyDataColumns.Notes))
            {
                if (!string.IsNullOrEmpty(mappedColumnsData[PropertyDataColumns.Notes]))
                {
                    columns.Add(mappedColumnsData[PropertyDataColumns.Notes]);
                    mappedColumnsData.Remove(PropertyDataColumns.Notes);
                }

            }
            foreach (DataColumn column in table.Columns)
            {
                if (!mappedColumnsData.ContainsValue(column.ColumnName) && !column.ColumnName.Contains("S. No"))
                {
                    if (!columns.Contains(column.ColumnName))
                    {
                        columns.Add(column.ColumnName);
                    }
                }
            }
            return columns;
        }
        public static List<Domain.Entities.Property> ConvertToProperty(
            this DataTable table,
            Dictionary<PropertyDataColumns, string>? mappedColumnsData,
            List<string> unMappedColumns,
            List<MasterPropertyType> propertyTypes,
            List<MasterAreaUnit> areaUnits,
            List<CustomMasterAttribute> propertyAttributes,
            List<Lrb.Domain.Entities.Project> projects,
            Domain.Entities.GlobalSettings globalSettings, 
            string jsonData = null,
            List<CustomListingSource>? listingSources = null, 
            List<UserView>? users = null,
            IBlobStorageService? _blobStorageService = null,
            List<ListingSourceAddress>? listingSourceAddresses = null
            )
        {
            List<Domain.Entities.Property> properties = new List<Domain.Entities.Property>();
            Guid? defaultUnitId = Guid.TryParse(globalSettings?.DefaultValues?.FirstOrDefault().Value, out Guid parsedGuid) ? parsedGuid : (Guid?)null;

            foreach (DataRow row in table.Rows)
            {
                try
                {
                    #region GetData
                    string? bHKType = !mappedColumnsData.ContainsKey(PropertyDataColumns.BHKType) || string.IsNullOrEmpty(mappedColumnsData[PropertyDataColumns.BHKType]) ? string.Empty : row[mappedColumnsData[PropertyDataColumns.BHKType]].ToString();
                    string? noOfBHK = !mappedColumnsData.ContainsKey(PropertyDataColumns.NoOfBHK) || string.IsNullOrEmpty(mappedColumnsData[PropertyDataColumns.NoOfBHK]) ? string.Empty : row[mappedColumnsData[PropertyDataColumns.NoOfBHK]].ToString();
                    string? basePropertyType = !mappedColumnsData.ContainsKey(PropertyDataColumns.BasePropertyType) || string.IsNullOrEmpty(mappedColumnsData[PropertyDataColumns.BasePropertyType]) ? string.Empty : row[mappedColumnsData[PropertyDataColumns.BasePropertyType]].ToString();
                    string? subPropertyType = !mappedColumnsData.ContainsKey(PropertyDataColumns.SubPropertyType) || string.IsNullOrEmpty(mappedColumnsData[PropertyDataColumns.SubPropertyType]) ? string.Empty : row[mappedColumnsData[PropertyDataColumns.SubPropertyType]].ToString();
                    string? enquiredFor = !mappedColumnsData.ContainsKey(PropertyDataColumns.EnquiredFor) || string.IsNullOrEmpty(mappedColumnsData[PropertyDataColumns.EnquiredFor]) ? string.Empty : row[mappedColumnsData[PropertyDataColumns.EnquiredFor]].ToString();
                    string? totalPrice = !mappedColumnsData.ContainsKey(PropertyDataColumns.TotalPrice) || string.IsNullOrEmpty(mappedColumnsData[PropertyDataColumns.TotalPrice]) ? string.Empty : row[mappedColumnsData[PropertyDataColumns.TotalPrice]].ToString();
                    string? title = !mappedColumnsData.ContainsKey(PropertyDataColumns.Title) || string.IsNullOrEmpty(mappedColumnsData[PropertyDataColumns.Title]) ? string.Empty : row[mappedColumnsData[PropertyDataColumns.Title]].ToString();
                    string? saleType = !mappedColumnsData.ContainsKey(PropertyDataColumns.SaleType) || string.IsNullOrEmpty(mappedColumnsData[PropertyDataColumns.SaleType]) ? string.Empty : row[mappedColumnsData[PropertyDataColumns.SaleType]].ToString();
                    string? furnishStatus = !mappedColumnsData.ContainsKey(PropertyDataColumns.FurnishStatus) || string.IsNullOrEmpty(mappedColumnsData[PropertyDataColumns.FurnishStatus]) ? string.Empty : row[mappedColumnsData[PropertyDataColumns.FurnishStatus]].ToString();
                    string? status = !mappedColumnsData.ContainsKey(PropertyDataColumns.Status) || string.IsNullOrEmpty(mappedColumnsData[PropertyDataColumns.Status]) ? string.Empty : row[mappedColumnsData[PropertyDataColumns.Status]].ToString();
                    string? rating = !mappedColumnsData.ContainsKey(PropertyDataColumns.Rating) || string.IsNullOrEmpty(mappedColumnsData[PropertyDataColumns.Rating]) ? string.Empty : row[mappedColumnsData[PropertyDataColumns.Rating]].ToString();
                    string? brokerageAmount = !mappedColumnsData.ContainsKey(PropertyDataColumns.BrokerageAmount) || string.IsNullOrEmpty(mappedColumnsData[PropertyDataColumns.BrokerageAmount]) ? string.Empty : row[mappedColumnsData[PropertyDataColumns.BrokerageAmount]].ToString();
                    string? facing = !mappedColumnsData.ContainsKey(PropertyDataColumns.Facing) || string.IsNullOrEmpty(mappedColumnsData[PropertyDataColumns.Facing]) ? string.Empty : row[mappedColumnsData[PropertyDataColumns.Facing]].ToString();
                    string? aboutProperty = !mappedColumnsData.ContainsKey(PropertyDataColumns.AboutProperty) || string.IsNullOrEmpty(mappedColumnsData[PropertyDataColumns.AboutProperty]) ? string.Empty : row[mappedColumnsData[PropertyDataColumns.AboutProperty]].ToString();
                    string? city = !mappedColumnsData.ContainsKey(PropertyDataColumns.City) || string.IsNullOrEmpty(mappedColumnsData[PropertyDataColumns.City]) ? string.Empty : row[mappedColumnsData[PropertyDataColumns.City]].ToString();
                    string? state = !mappedColumnsData.ContainsKey(PropertyDataColumns.State) || string.IsNullOrEmpty(mappedColumnsData[PropertyDataColumns.State]) ? string.Empty : row[mappedColumnsData[PropertyDataColumns.State]].ToString();
                    string? location = !mappedColumnsData.ContainsKey(PropertyDataColumns.Location) || string.IsNullOrEmpty(mappedColumnsData[PropertyDataColumns.Location]) ? string.Empty : row[mappedColumnsData[PropertyDataColumns.Location]].ToString();
                    string? ownerName = !mappedColumnsData.ContainsKey(PropertyDataColumns.OwnerName) || string.IsNullOrEmpty(mappedColumnsData[PropertyDataColumns.OwnerName]) ? string.Empty : row[mappedColumnsData[PropertyDataColumns.OwnerName]].ToString();
                    string? ownerPhoneNumber = !mappedColumnsData.ContainsKey(PropertyDataColumns.OwnerPhoneNumber) || string.IsNullOrEmpty(mappedColumnsData[PropertyDataColumns.OwnerPhoneNumber]) ? string.Empty : row[mappedColumnsData[PropertyDataColumns.OwnerPhoneNumber]].ToString();
                    string? ownerEmail = !mappedColumnsData.ContainsKey(PropertyDataColumns.OwnerEmail) || string.IsNullOrEmpty(mappedColumnsData[PropertyDataColumns.OwnerEmail]) ? string.Empty : row[mappedColumnsData[PropertyDataColumns.OwnerEmail]].ToString();
                    string? balconies = !mappedColumnsData.ContainsKey(PropertyDataColumns.Balconies) || string.IsNullOrEmpty(mappedColumnsData[PropertyDataColumns.Balconies]) ? string.Empty : row[mappedColumnsData[PropertyDataColumns.Balconies]].ToString();
                    string? bathrooms = !mappedColumnsData.ContainsKey(PropertyDataColumns.BathRooms) || string.IsNullOrEmpty(mappedColumnsData[PropertyDataColumns.BathRooms]) ? string.Empty : row[mappedColumnsData[PropertyDataColumns.BathRooms]].ToString();
                    string? totalFloors = !mappedColumnsData.ContainsKey(PropertyDataColumns.TotalFloors) || string.IsNullOrEmpty(mappedColumnsData[PropertyDataColumns.TotalFloors]) ? string.Empty : row[mappedColumnsData[PropertyDataColumns.TotalFloors]].ToString();
                    string? bedrooms = !mappedColumnsData.ContainsKey(PropertyDataColumns.BedRooms) || string.IsNullOrEmpty(mappedColumnsData[PropertyDataColumns.BedRooms]) ? string.Empty : row[mappedColumnsData[PropertyDataColumns.BedRooms]].ToString();
                    string? floorNumber = !mappedColumnsData.ContainsKey(PropertyDataColumns.FloorNumber) || string.IsNullOrEmpty(mappedColumnsData[PropertyDataColumns.FloorNumber]) ? string.Empty : row[mappedColumnsData[PropertyDataColumns.FloorNumber]].ToString();
                    string? utilities = !mappedColumnsData.ContainsKey(PropertyDataColumns.Utilities) || string.IsNullOrEmpty(mappedColumnsData[PropertyDataColumns.Utilities]) ? string.Empty : row[mappedColumnsData[PropertyDataColumns.Utilities]].ToString();
                    string? kitchen = !mappedColumnsData.ContainsKey(PropertyDataColumns.Kitchen) || string.IsNullOrEmpty(mappedColumnsData[PropertyDataColumns.Kitchen]) ? string.Empty : row[mappedColumnsData[PropertyDataColumns.Kitchen]].ToString();
                    string? drawingOrLivingRooms = !mappedColumnsData.ContainsKey(PropertyDataColumns.DrawingOrLivingRooms) || string.IsNullOrEmpty(mappedColumnsData[PropertyDataColumns.DrawingOrLivingRooms]) ? string.Empty : row[mappedColumnsData[PropertyDataColumns.DrawingOrLivingRooms]].ToString();
                    string? propertySize = !mappedColumnsData.ContainsKey(PropertyDataColumns.PropertySize) || string.IsNullOrEmpty(mappedColumnsData[PropertyDataColumns.PropertySize]) ? string.Empty : row[mappedColumnsData[PropertyDataColumns.PropertySize]].ToString();
                    string? unit = !mappedColumnsData.ContainsKey(PropertyDataColumns.AreaUnit) || string.IsNullOrEmpty(mappedColumnsData[PropertyDataColumns.AreaUnit]) ? string.Empty : row[mappedColumnsData[PropertyDataColumns.AreaUnit]].ToString();
                    bool? isNegotiable = !mappedColumnsData.ContainsKey(PropertyDataColumns.IsNegotiable) || string.IsNullOrEmpty(mappedColumnsData[PropertyDataColumns.AreaUnit]) ? false : bool.TryParse(row[mappedColumnsData[PropertyDataColumns.AreaUnit]].ToString(), out var result) ? result : false;
                    string currecncy = !mappedColumnsData.ContainsKey(PropertyDataColumns.Currency) || string.IsNullOrEmpty(mappedColumnsData[PropertyDataColumns.Currency]) ? string.Empty : row[mappedColumnsData[PropertyDataColumns.Currency]].ToString();
                    string countrycode = !mappedColumnsData.ContainsKey(PropertyDataColumns.CountryCode) || string.IsNullOrEmpty(mappedColumnsData[PropertyDataColumns.CountryCode]) ? string.Empty : row[mappedColumnsData[PropertyDataColumns.CountryCode]].ToString();
                    string BrokerageCurrency = !mappedColumnsData.ContainsKey(PropertyDataColumns.BrokerageCurrency) || string.IsNullOrEmpty(mappedColumnsData[PropertyDataColumns.BrokerageCurrency]) ? string.Empty : row[mappedColumnsData[PropertyDataColumns.BrokerageCurrency]].ToString();
                    string newProject = !mappedColumnsData.ContainsKey(PropertyDataColumns.Project) || string.IsNullOrEmpty(mappedColumnsData[PropertyDataColumns.Project]) ? string.Empty : row[mappedColumnsData[PropertyDataColumns.Project]].ToString();
                    string? community = !mappedColumnsData.ContainsKey(PropertyDataColumns.Community) || string.IsNullOrEmpty(mappedColumnsData[PropertyDataColumns.Community]) ? string.Empty : row[mappedColumnsData[PropertyDataColumns.Community]].ToString();
                    string? subCommunity = !mappedColumnsData.ContainsKey(PropertyDataColumns.SubCommunity) || string.IsNullOrEmpty(mappedColumnsData[PropertyDataColumns.SubCommunity]) ? string.Empty : row[mappedColumnsData[PropertyDataColumns.SubCommunity]].ToString();
                    string? towerName = !mappedColumnsData.ContainsKey(PropertyDataColumns.TowerName) || string.IsNullOrEmpty(mappedColumnsData[PropertyDataColumns.TowerName]) ? string.Empty : row[mappedColumnsData[PropertyDataColumns.TowerName]].ToString();
                    string? securityDeposit = !mappedColumnsData.ContainsKey(PropertyDataColumns.SecurityDeposit) || string.IsNullOrEmpty(mappedColumnsData[PropertyDataColumns.SecurityDeposit]) ? string.Empty : row[mappedColumnsData[PropertyDataColumns.SecurityDeposit]].ToString();
                    string? lockInPeriod = !mappedColumnsData.ContainsKey(PropertyDataColumns.LockInPeriod) || string.IsNullOrEmpty(mappedColumnsData[PropertyDataColumns.LockInPeriod]) ? string.Empty : row[mappedColumnsData[PropertyDataColumns.LockInPeriod]].ToString();
                    string? noticePeriod = !mappedColumnsData.ContainsKey(PropertyDataColumns.NoticePeriod) || string.IsNullOrEmpty(mappedColumnsData[PropertyDataColumns.NoticePeriod]) ? string.Empty : row[mappedColumnsData[PropertyDataColumns.NoticePeriod]].ToString();
                    string? tenantPOCName = !mappedColumnsData.ContainsKey(PropertyDataColumns.TenantPOCName) || string.IsNullOrEmpty(mappedColumnsData[PropertyDataColumns.TenantPOCName]) ? string.Empty : row[mappedColumnsData[PropertyDataColumns.TenantPOCName]].ToString();
                    string? tenantPOCDesignation = !mappedColumnsData.ContainsKey(PropertyDataColumns.TenantPOCDesignation) || string.IsNullOrEmpty(mappedColumnsData[PropertyDataColumns.TenantPOCDesignation]) ? string.Empty : row[mappedColumnsData[PropertyDataColumns.TenantPOCDesignation]].ToString();
                    string? tenantPOCPhone = !mappedColumnsData.ContainsKey(PropertyDataColumns.TenantPOCNumber) || string.IsNullOrEmpty(mappedColumnsData[PropertyDataColumns.TenantPOCNumber]) ? string.Empty : row[mappedColumnsData[PropertyDataColumns.TenantPOCNumber]].ToString();
                    string? tenantPOCNumberCountryCode = !mappedColumnsData.ContainsKey(PropertyDataColumns.TenantPOCNumberCountryCode) || string.IsNullOrEmpty(mappedColumnsData[PropertyDataColumns.TenantPOCNumberCountryCode]) ? string.Empty : row[mappedColumnsData[PropertyDataColumns.TenantPOCNumberCountryCode]].ToString();
                    string? coWorkingOperator = !mappedColumnsData.ContainsKey(PropertyDataColumns.CoWorkingOperator) || string.IsNullOrEmpty(mappedColumnsData[PropertyDataColumns.CoWorkingOperator]) ? string.Empty : row[mappedColumnsData[PropertyDataColumns.CoWorkingOperator]].ToString();
                    string? coWorkingOperatorPOCName = !mappedColumnsData.ContainsKey(PropertyDataColumns.CoWorkingOperatorPOCName) || string.IsNullOrEmpty(mappedColumnsData[PropertyDataColumns.CoWorkingOperatorPOCName]) ? string.Empty : row[mappedColumnsData[PropertyDataColumns.CoWorkingOperatorPOCName]].ToString();
                    string? coWorkingOperatorPOCPhone = !mappedColumnsData.ContainsKey(PropertyDataColumns.CoWorkingOperatorPOCNumber) || string.IsNullOrEmpty(mappedColumnsData[PropertyDataColumns.CoWorkingOperatorPOCNumber]) ? string.Empty : row[mappedColumnsData[PropertyDataColumns.CoWorkingOperatorPOCNumber]].ToString();
                    string? coWorkingOperatorPOCNumberCountryCode = !mappedColumnsData.ContainsKey(PropertyDataColumns.CoWorkingOperatorPOCNumberCountryCode) || string.IsNullOrEmpty(mappedColumnsData[PropertyDataColumns.CoWorkingOperatorPOCNumberCountryCode]) ? string.Empty : row[mappedColumnsData[PropertyDataColumns.CoWorkingOperatorPOCNumberCountryCode]].ToString();
                    string? monthlyRentAmount = !mappedColumnsData.ContainsKey(PropertyDataColumns.RentOrLeaseAmountPerMonth) || string.IsNullOrEmpty(mappedColumnsData[PropertyDataColumns.RentOrLeaseAmountPerMonth]) ? string.Empty : row[mappedColumnsData[PropertyDataColumns.RentOrLeaseAmountPerMonth]].ToString();
                    string? escalationPercentage = !mappedColumnsData.ContainsKey(PropertyDataColumns.Escalation) || string.IsNullOrEmpty(mappedColumnsData[PropertyDataColumns.Escalation]) ? string.Empty : row[mappedColumnsData[PropertyDataColumns.Escalation]].ToString();
                    string? offering = !mappedColumnsData.ContainsKey(PropertyDataColumns.OfferingType) || string.IsNullOrEmpty(mappedColumnsData[PropertyDataColumns.OfferingType]) ? string.Empty : row[mappedColumnsData[PropertyDataColumns.OfferingType]].ToString();
                    string? completetion = !mappedColumnsData.ContainsKey(PropertyDataColumns.CompletionStatus) || string.IsNullOrEmpty(mappedColumnsData[PropertyDataColumns.CompletionStatus]) ? string.Empty : row[mappedColumnsData[PropertyDataColumns.CompletionStatus]].ToString();
                    string? dldPermit = !mappedColumnsData.ContainsKey(PropertyDataColumns.DLDPermitNumber) || string.IsNullOrEmpty(mappedColumnsData[PropertyDataColumns.DLDPermitNumber]) ? string.Empty : row[mappedColumnsData[PropertyDataColumns.DLDPermitNumber]].ToString();
                    string? dtcmPermit = !mappedColumnsData.ContainsKey(PropertyDataColumns.DTCMPermit) || string.IsNullOrEmpty(mappedColumnsData[PropertyDataColumns.DTCMPermit]) ? string.Empty : row[mappedColumnsData[PropertyDataColumns.DTCMPermit]].ToString();
                    string? paymentFrequency = !mappedColumnsData.ContainsKey(PropertyDataColumns.PaymentFrequency) || string.IsNullOrEmpty(mappedColumnsData[PropertyDataColumns.PaymentFrequency]) ? string.Empty : row[mappedColumnsData[PropertyDataColumns.PaymentFrequency]].ToString();
                    string? netAreaUnit = !mappedColumnsData.ContainsKey(PropertyDataColumns.NetAreaUnit) || string.IsNullOrEmpty(mappedColumnsData[PropertyDataColumns.AreaUnit]) ? string.Empty : row[mappedColumnsData[PropertyDataColumns.NetAreaUnit]].ToString();
                    string? NetAreaSize = !mappedColumnsData.ContainsKey(PropertyDataColumns.NetArea) || string.IsNullOrEmpty(mappedColumnsData[PropertyDataColumns.NetArea]) ? string.Empty : row[mappedColumnsData[PropertyDataColumns.NetArea]].ToString();
                    string? listingPortal = !mappedColumnsData.ContainsKey(PropertyDataColumns.ListingPortal) || string.IsNullOrEmpty(mappedColumnsData[PropertyDataColumns.ListingPortal]) ? string.Empty : row[mappedColumnsData[PropertyDataColumns.ListingPortal]].ToString();
                    string? listedByUser = !mappedColumnsData.ContainsKey(PropertyDataColumns.ListedByUser) || string.IsNullOrEmpty(mappedColumnsData[PropertyDataColumns.ListedByUser]) ? string.Empty : row[mappedColumnsData[PropertyDataColumns.ListedByUser]].ToString();
                    string? imagesUrls = !mappedColumnsData.ContainsKey(PropertyDataColumns.ImageUrls) || string.IsNullOrEmpty(mappedColumnsData[PropertyDataColumns.ImageUrls]) ? string.Empty : row[mappedColumnsData[PropertyDataColumns.ImageUrls]].ToString();
                    string? isListed = !mappedColumnsData.ContainsKey(PropertyDataColumns.IsListed) || string.IsNullOrEmpty(mappedColumnsData[PropertyDataColumns.IsListed]) ? string.Empty : row[mappedColumnsData[PropertyDataColumns.IsListed]].ToString();
                    var netArea1 = UnitHelper.GetUnitDetails(NetAreaSize ?? string.Empty, areaUnits, netAreaUnit ?? string.Empty, defaultUnitId);
                    var area1 = UnitHelper.GetUnitDetails(propertySize ?? string.Empty, areaUnits, unit ?? string.Empty, defaultUnitId);
                    string? securityDepositUnit = !mappedColumnsData.ContainsKey(PropertyDataColumns.SecurityDepositUnit) || string.IsNullOrEmpty(mappedColumnsData[PropertyDataColumns.SecurityDepositUnit]) ? string.Empty : row[mappedColumnsData[PropertyDataColumns.SecurityDepositUnit]].ToString();
                    string? securityDepositAmount = !mappedColumnsData.ContainsKey(PropertyDataColumns.SecurityDepositAmount) || string.IsNullOrEmpty(mappedColumnsData[PropertyDataColumns.SecurityDepositAmount]) ? string.Empty : row[mappedColumnsData[PropertyDataColumns.SecurityDepositAmount]].ToString();
                    string? propertyAge = !mappedColumnsData.ContainsKey(PropertyDataColumns.Age) || string.IsNullOrEmpty(mappedColumnsData[PropertyDataColumns.Age]) ? string.Empty : row[mappedColumnsData[PropertyDataColumns.Age]].ToString();
                    string? finishingType = !mappedColumnsData.ContainsKey(PropertyDataColumns.FinishingType) || string.IsNullOrEmpty(mappedColumnsData[PropertyDataColumns.FinishingType]) ? string.Empty : row[mappedColumnsData[PropertyDataColumns.FinishingType]].ToString();
                    string? uaeEmirate = !mappedColumnsData.ContainsKey(PropertyDataColumns.UaeEmirate) || string.IsNullOrEmpty(mappedColumnsData[PropertyDataColumns.UaeEmirate]) ? string.Empty : row[mappedColumnsData[PropertyDataColumns.UaeEmirate]].ToString();
                    string? complianceType = !mappedColumnsData.ContainsKey(PropertyDataColumns.ComplianceType) || string.IsNullOrEmpty(mappedColumnsData[PropertyDataColumns.ComplianceType]) ? string.Empty : row[mappedColumnsData[PropertyDataColumns.ComplianceType]].ToString();
                    string? downPayment = !mappedColumnsData.ContainsKey(PropertyDataColumns.DownPayment) || string.IsNullOrEmpty(mappedColumnsData[PropertyDataColumns.DownPayment]) ? string.Empty : row[mappedColumnsData[PropertyDataColumns.DownPayment]].ToString();
                    string? parking = !mappedColumnsData.ContainsKey(PropertyDataColumns.Parking) || string.IsNullOrEmpty(mappedColumnsData[PropertyDataColumns.Parking]) ? string.Empty : row[mappedColumnsData[PropertyDataColumns.Parking]].ToString();
                    string? country = !mappedColumnsData.ContainsKey(PropertyDataColumns.Country) || string.IsNullOrEmpty(mappedColumnsData[PropertyDataColumns.Country]) ? string.Empty : row[mappedColumnsData[PropertyDataColumns.Country]].ToString();

                    #endregion

                    var propertyInfo = GetPropertyType(basePropertyType, subPropertyType, propertyTypes, bHKType, noOfBHK);
                    var budgetInfo = ConvertBuget(totalPrice ?? string.Empty);
                    var monthlyRentAmountinfo = ConvertBuget(monthlyRentAmount ?? string.Empty);
                    var escalationPercentageInfo = ConvertBuget(escalationPercentage ?? string.Empty);
                    var brokerageInfo = GetBrokerageAmount(brokerageAmount ?? string.Empty);
                    var isValidEnquiryInfo = Enum.TryParse<EnquiryType>(enquiredFor, true, out var enquiryType);
                    var isValidSaleTypeInfo = Enum.TryParse<SaleType>(saleType, true, out var saleInfo);
                    var isValidFurnishStatusInfo = Enum.TryParse<FurnishStatus>(furnishStatus, true, out var furnishStatusInfo);
                    var isValidPropertyStatusInfo = Enum.TryParse<PropertyStatus>(status, true, out var propertyStatusInfo);
                    var isValidSecurityDeposit = Enum.TryParse<SecurityDeposit>(securityDeposit, true, out var propertySecurityDeposit);
                    var isValidLockInPeriod = Enum.TryParse<LockInPeriod>(lockInPeriod, true, out var propertyLockInPeriod);
                    var isValidNoticePeriod = Enum.TryParse<NoticePeriod>(noticePeriod, true, out var propertyNoticePeriod);
                    var isValidFacingInfo = Enum.TryParse<Facing>(facing, true, out var facingInfo);
                    var currency = mappedColumnsData.GetCurrencySymbol1(row, currecncy, globalSettings?.Countries?.FirstOrDefault().DefaultCurrency);
                    var brokeragecurrency = mappedColumnsData.GetBrokerageCurrencySymbol(row, BrokerageCurrency, globalSettings?.Countries?.FirstOrDefault().DefaultCurrency);
                    var ownerphoneNo = mappedColumnsData.ValidateContactNo(row, ownerPhoneNumber, countrycode, globalSettings?.Countries?.FirstOrDefault().DefaultCallingCode);
                    var tenentPOCPhoneNo = mappedColumnsData.ValidateContactNo(row, tenantPOCPhone, tenantPOCNumberCountryCode, globalSettings?.Countries?.FirstOrDefault().DefaultCallingCode);
                    var coWorkingOperatorPOCPhoneNo = mappedColumnsData.ValidateContactNo(row, coWorkingOperatorPOCPhone, coWorkingOperatorPOCNumberCountryCode, globalSettings?.Countries?.FirstOrDefault().DefaultCallingCode);
                    var project = GetProject(newProject ?? string.Empty, globalSettings, projects);
                    var isValidOfferingType = Enum.TryParse<OfferingType>(offering, true, out var offeringType);
                    var isValidCompletionStatus = Enum.TryParse<CompletionStatus>(completetion, true, out var completionStatus);
                    var isValidPaymentFrequemcy = Enum.TryParse<PaymentFrequency>(paymentFrequency, true, out var frequncy);
                    string? listingOnBehalfUser = !mappedColumnsData.ContainsKey(PropertyDataColumns.ListingOnBehalf) || string.IsNullOrEmpty(mappedColumnsData[PropertyDataColumns.ListingOnBehalf]) ? string.Empty : row[mappedColumnsData[PropertyDataColumns.ListingOnBehalf]].ToString();
                    string? ownerAltContactNo = !mappedColumnsData.ContainsKey(PropertyDataColumns.OwnerAltContactNo) || string.IsNullOrEmpty(mappedColumnsData[PropertyDataColumns.OwnerAltContactNo]) ? string.Empty : row[mappedColumnsData[PropertyDataColumns.OwnerAltContactNo]].ToString();
                    var ownerAltNo = mappedColumnsData.ValidateContactNo(row, ownerAltContactNo, countrycode, globalSettings?.Countries?.FirstOrDefault().DefaultCallingCode);
                    var possesionType = !mappedColumnsData.ContainsKey(PropertyDataColumns.PossesionType) || string.IsNullOrEmpty(mappedColumnsData[PropertyDataColumns.PossesionType]) ? string.Empty : row[mappedColumnsData[PropertyDataColumns.PossesionType]].ToString();
                    var securityDepositAmountInt = GetSecurityDepositAmount(securityDeposit ?? string.Empty);
                    var possessionDate = mappedColumnsData.GetPossessionDate(PropertyDataColumns.PossessionDate, row, jsonData);
                    bool isValidPossessionDate = possessionDate.HasValue;
                    DateTime? dateTime = possessionDate;

                    if (string.Equals(possesionType, "Custom", StringComparison.OrdinalIgnoreCase) || (dateTime.HasValue && (string.IsNullOrWhiteSpace(possesionType) || possesionType == "None")))
                    {
                        possesionType = "CustomDate";
                    }
                    DateTime currentUtcTime = new DateTime(DateTime.UtcNow.Year, DateTime.UtcNow.Month, DateTime.DaysInMonth(DateTime.UtcNow.Year, DateTime.UtcNow.Month));
                    if (!string.IsNullOrWhiteSpace(possesionType) && possesionType != "None" && possesionType != "CustomDate")
                    {
                        possessionDate = currentUtcTime;
                        isValidPossessionDate = true;
                        switch (possesionType)
                        {
                            case "UnderConstruction":
                            case "Immediate":
                                dateTime = null;
                                break;
                            case "SixMonth":
                                dateTime = currentUtcTime.AddMonths(6);
                                break;
                            case "Year":
                                dateTime = currentUtcTime.AddYears(1);
                                break;
                            case "TwoYears":
                                dateTime = currentUtcTime.AddYears(2);
                                break;
                        }
                    }
                    var secDepositUnit = mappedColumnsData.GetBrokerageCurrencySymbol(row, securityDepositUnit, globalSettings?.Countries?.FirstOrDefault().DefaultCurrency);

                    //var propertyDimension = GetPropertyDimension(propertySize ?? string.Empty, areaUnits, unit ?? string.Empty, NetAreaSize ?? string.Empty, netAreaUnit);
                    var newAttributes = GetPropertyAttribute(new Dictionary<PropertyDataColumns, string>()
                    {
                        { PropertyDataColumns.Balconies, balconies ?? string.Empty},
                        { PropertyDataColumns.TotalFloors,totalFloors ?? string.Empty},
                        { PropertyDataColumns.Utilities,utilities ?? string.Empty},
                        { PropertyDataColumns.BedRooms,bedrooms ?? string.Empty},
                        { PropertyDataColumns.FloorNumber, floorNumber ?? string.Empty},
                        { PropertyDataColumns.Kitchen,kitchen ?? string.Empty},
                        { PropertyDataColumns.DrawingOrLivingRooms, drawingOrLivingRooms ?? string.Empty},
                        { PropertyDataColumns.BathRooms, bathrooms ?? string.Empty},
                        { PropertyDataColumns.Parking, parking ?? string.Empty},
                       
                    },
                    propertyAttributes.Where(i => i.FieldType == PropertyAttributeFieldType.Int).ToList());

                    UserView? user = null;
                    if (!string.IsNullOrEmpty(listedByUser))
                    {
                        user = users?.Where(i => (listedByUser != null) && (i.UserName.ToLower().Trim() == listedByUser.ToLower().Trim() || i.UserName.Trim().Replace(" ", "").ToLower() == listedByUser.Trim().Replace(" ", "").ToLower() ||
                                   i.FirstName != null && i.FirstName.ToLower() == listedByUser.ToLower() || i.FirstName != null && i.FirstName.Trim().ToLower().Replace(" ", "") == listedByUser.Trim().Replace(" ", "").ToLower()
                                   || ((i.FirstName != null && i.LastName != null) && (string.Concat(i.FirstName, " ", i.LastName).Replace(" ", "").Trim().ToLower() == listedByUser.ToLower().Trim().Replace(" ", "")
                                   || string.Concat(i.FirstName, " ", i.LastName).Replace(" ", "").Trim().ToLower() == listedByUser.ToLower().Trim().Replace(" ", ""))))).FirstOrDefault();
                    }
                    UserView? listingOnUser = null;
                    if (!string.IsNullOrEmpty(listingOnBehalfUser))
                    {
                        listingOnUser = users?.Where(i => (listingOnBehalfUser != null) && (i.UserName.ToLower().Trim() == listingOnBehalfUser.ToLower().Trim() || i.UserName.Trim().Replace(" ", "").ToLower() == listingOnBehalfUser.Trim().Replace(" ", "").ToLower() ||
                                   i.FirstName != null && i.FirstName.ToLower() == listingOnBehalfUser.ToLower() || i.FirstName != null && i.FirstName.Trim().ToLower().Replace(" ", "") == listingOnBehalfUser.Trim().Replace(" ", "").ToLower()
                                   || ((i.FirstName != null && i.LastName != null) && (string.Concat(i.FirstName, " ", i.LastName).Replace(" ", "").Trim().ToLower() == listingOnBehalfUser.ToLower().Trim().Replace(" ", "")
                                   || string.Concat(i.FirstName, " ", i.LastName).Replace(" ", "").Trim().ToLower() == listingOnBehalfUser.ToLower().Trim().Replace(" ", ""))))).FirstOrDefault();
                    }

                    List<Guid> listingOnBehalfUsers = new List<Guid>();
                    if (listingOnUser != null)
                    {
                        listingOnBehalfUsers.Add(listingOnUser.Id);
                    }

                    CustomListingSource? listingSource = null;
                    if (!string.IsNullOrEmpty(listingPortal))
                    {
                        listingSource = listingSources?.Where(i => i.DisplayName?.ToLower().Trim() == listingPortal.ToLower().Trim()).FirstOrDefault();
                    }

                    List<string> images = new();
                    if (!string.IsNullOrEmpty(imagesUrls))
                    {
                        images = imagesUrls.Split('|').ToList();
                    }

                    ListingSourceAddress? sourcelocation = null;
                    if (listingSourceAddresses?.Any() ?? false)
                    {
                        
                        if (!string.IsNullOrEmpty(towerName))
                        {
                            sourcelocation = listingSourceAddresses?
                                .Where(i => i.TowerName.ToLower().Trim() == towerName.ToLower().Trim() && i.SubCommunity.ToLower().Trim() == subCommunity?.ToLower().Trim() && i.Community.ToLower().Trim() == community?.ToLower().Trim() && 
                                i.City.ToLower().Trim() == city?.ToLower().Trim()).FirstOrDefault();
                        }
                        else
                        {
                            sourcelocation = listingSourceAddresses?
                                .Where(i => i.SubCommunity.ToLower().Trim() == subCommunity?.ToLower().Trim() && i.Community.ToLower().Trim() == community?.ToLower().Trim() && i.City.ToLower().Trim() == city?.ToLower().Trim())
                                .FirstOrDefault();
                        }
                    }
                    if(possesionType == null && possessionDate!=null)
                    {
                        possesionType = PossesionType.CustomDate.ToString();
                    }

                    var iscomplianceType = Enum.TryParse<ComplianceType>(complianceType, true, out var complianceTypeRes);
                    var isValiduaeEmirate = Enum.TryParse<UaeEmirate>(uaeEmirate, true, out var uaeEmirateRes);
                    var isValidfinishingType = Enum.TryParse<FinishingType>(finishingType, true, out var finishingTypeRes);
                    var isValidPropertyAge = double.TryParse(propertyAge, out double age); 
                    var isValidDownPayment = double.TryParse(downPayment, out double downPaymentAmount);
                    var property = new Domain.Entities.Property()
                    {
                        Id = Guid.NewGuid(),
                        Title = title,
                        Rating = rating,
                        AboutProperty = aboutProperty,
                        SaleType = isValidSaleTypeInfo ? saleInfo : default,
                        EnquiredFor = isValidEnquiryInfo ? enquiryType : default,
                        FurnishStatus = isValidFurnishStatusInfo ? furnishStatusInfo : default,
                        Status = isValidPropertyStatusInfo ? propertyStatusInfo : default,
                        Facing = isValidFacingInfo ? facingInfo : default,
                        PossessionDate = isValidPossessionDate ? dateTime?.ToUniversalTime() : null,
                        PropertyType = propertyInfo.IsValidInfo ? propertyInfo.PropertyType : default,
                        BHKType = propertyInfo.IsValidInfo ? propertyInfo.BHKType : default,
                        NoOfBHKs = propertyInfo.IsValidInfo ? propertyInfo.NoOfBHK : default,
                        //Dimension = propertyDimension,
                        Dimension = new()
                        {
                            Area = area1.Item1,
                            AreaUnitId = area1.Item2,
                            NetArea = netArea1.Item1,
                            NetAreaUnitId = netArea1.Item2,
                        },
                        Address = (!string.IsNullOrEmpty(city) || !string.IsNullOrEmpty(state) || !string.IsNullOrEmpty(location) || !string.IsNullOrEmpty(country) || !string.IsNullOrEmpty(community)
                                || !string.IsNullOrEmpty(towerName) || !string.IsNullOrEmpty(subCommunity)) ?
                        new()
                        {
                            City = city,
                            State = state,
                            SubLocality = location,
                            Community = community,
                            SubCommunity = subCommunity,
                            TowerName = towerName,
                            Country=country
                        } : null,
                        //OwnerDetails = new()
                        //{
                        //    Name = ownerName,
                        //    Phone = ownerphoneNo,
                        //    Email = ownerEmail
                        //},

                        PropertyOwnerDetails = (!string.IsNullOrWhiteSpace(ownerphoneNo) ||!string.IsNullOrWhiteSpace(ownerName) ||!string.IsNullOrWhiteSpace(ownerEmail))
                        ? new List<PropertyOwnerDetails>
                        { 
                            new PropertyOwnerDetails
                            {
                                Name = ownerName,
                                Phone = ownerphoneNo,
                                Email = ownerEmail,
                                AlternateContactNo=ownerAltNo
                            }
                        }: null,

                        MonetaryInfo = new()
                        {
                            ExpectedPrice = budgetInfo.IsValidInfo ? budgetInfo.Budget : default,
                            Brokerage = brokerageInfo,
                            Currency = currency,
                            BrokerageCurrency = brokeragecurrency,
                            MonthlyRentAmount = monthlyRentAmountinfo.IsValidInfo ? monthlyRentAmountinfo.Budget : default,
                            EscalationPercentage = escalationPercentageInfo.IsValidInfo ? escalationPercentageInfo.Budget : default,
                            IsNegotiable = isNegotiable ?? false,
                            PaymentFrequency = isValidPaymentFrequemcy ? frequncy : default,
                            Downpayment = isValidDownPayment ? downPaymentAmount : null
                        },
                        TagInfo = new(),
                        Attributes = newAttributes,
                        Notes = unMappedColumns.Any() ? string.Join(", \n", unMappedColumns.Select(column => !string.IsNullOrEmpty(row[column].ToString()) ? column + " - " + row[column] : null).Where(i => i != null)) : string.Empty,
                        Project = project,
                        SecurityDeposit = isValidSecurityDeposit ? propertySecurityDeposit : default,
                        LockInPeriod = isValidLockInPeriod ? propertyLockInPeriod : default,
                        NoticePeriod = isValidNoticePeriod ? propertyNoticePeriod : default,
                        CoWorkingOperator = coWorkingOperator,
                        CoWorkingOperatorName = coWorkingOperatorPOCName,
                        CoWorkingOperatorPhone = coWorkingOperatorPOCPhoneNo,
                        OfferingType = isValidOfferingType ? offeringType : default,
                        CompletionStatus = isValidCompletionStatus ? completionStatus : default,
                        PermitNumber = dldPermit,
                        DTCMPermit = dtcmPermit,
                        ListingStatus = isListed?.ToLower().Trim() == "yes" ? ListingStatus.Approved : ListingStatus.Draft,
                        ListingOnBehalf = listingOnBehalfUsers,
                        SecurityDepositAmount = securityDepositAmountInt,
                        SecurityDepositUnit = secDepositUnit,
                        PossesionType = Enum.TryParse(possesionType, true, out PossesionType parsedType) && (parsedType != PossesionType.CustomDate || isValidPossessionDate) ? parsedType : default,
                        Compliance = new()
                        {
                            Type = iscomplianceType ? complianceTypeRes : ComplianceType.None,
                            ListingAdvertisementNumber = dldPermit
                        },
                        Age = isValidPropertyAge ? age : null,
                        FinishingType = isValidfinishingType ? finishingTypeRes : null,
                        UaeEmirate = isValiduaeEmirate ? uaeEmirateRes : null,
                    };
                    if (!string.IsNullOrWhiteSpace(tenantPOCName))
                    {
                        TenantContactInfo tenantContactInfo = new()
                        {
                            Name = tenantPOCName,
                            Phone = tenentPOCPhoneNo,
                            Designation = tenantPOCDesignation
                        };
                        property.TenantContactInfo = tenantContactInfo;
                    }
                    if (!isValidSaleTypeInfo && saleInfo != SaleType.None)
                    {
                        property.Notes += !string.IsNullOrEmpty(saleInfo.ToString()) ? "SaleType" + " - " + saleInfo.ToString() + ", \n" : string.Empty;
                    }
                    if (!isValidFurnishStatusInfo && furnishStatusInfo != FurnishStatus.Unknown)
                    {
                        property.Notes += !string.IsNullOrEmpty(furnishStatusInfo.ToString()) ? "FurnishStatus" + " - " + furnishStatusInfo.ToString() + ", \n" : string.Empty;
                    }
                    if (!isValidPropertyStatusInfo)
                    {
                        property.Notes += !string.IsNullOrEmpty(propertyStatusInfo.ToString()) ? "PropertyStatus" + " - " + propertyStatusInfo.ToString() + ", \n" : string.Empty;
                    }
                    if (!isValidFacingInfo && facingInfo != Facing.Unknown)
                    {
                        property.Notes += !string.IsNullOrEmpty(facingInfo.ToString()) ? "Facing" + " - " + facingInfo.ToString() + ", \n" : string.Empty;
                    }
                    if (!isValidSecurityDeposit && propertySecurityDeposit != SecurityDeposit.None)
                    {
                        property.Notes += !string.IsNullOrEmpty(propertySecurityDeposit.ToString()) ? "SecurityDeposit" + " - " + propertySecurityDeposit.ToString() + ", \n" : string.Empty;
                    }
                    if (!isValidLockInPeriod && propertyLockInPeriod != LockInPeriod.None)
                    {
                        property.Notes += !string.IsNullOrEmpty(propertyLockInPeriod.ToString()) ? "LockInPeriod" + " - " + propertyLockInPeriod.ToString() + ", \n" : string.Empty;
                    }
                    if (!isValidNoticePeriod && propertyNoticePeriod != NoticePeriod.None)
                    {
                        property.Notes += !string.IsNullOrEmpty(propertyNoticePeriod.ToString()) ? "NoticePeriod" + " - " + propertyNoticePeriod.ToString() + ", \n" : string.Empty;
                    }

                    #region New Additions
                    if (listingSource != null)
                    {
                        property.ListingSources = new List<CustomListingSource>() { listingSource };
                    }
                    if (user != null)
                    {
                        List<PropertyAssignment> propertyAssignments = new();
                        PropertyAssignment propertyAssigned = new();
                        propertyAssigned.AssignedTo = user.Id;
                        propertyAssigned.AssignedUser = user.FirstName + " " + user.LastName;
                        propertyAssigned.IsCurrentlyAssigned = true;
                        propertyAssignments.Add(propertyAssigned);
                        property.PropertyAssignments = propertyAssignments;
                    }
                    if ((images?.Any() ?? false) && _blobStorageService != null)
                    {
                        var keys = UploadImageInS3(images, _blobStorageService);
                        List<PropertyGallery> galleries = new();
                        foreach (var key in keys)
                        {
                            PropertyGallery propertyGallery = new()
                            {
                                ImageKey = "photo",
                                ImageFilePath = key,
                                PropertyId = property.Id,
                                IsCoverImage = key == keys.FirstOrDefault() ? true : false,
                                Name = $"{GenerateRandomFileName()}",
                                GalleryType = PropertyGalleryType.Image,
                            };
                            galleries.Add(propertyGallery);
                        }
                        property.Galleries = galleries;
                    }
                    if (sourcelocation != null)
                    {
                        List<ListingSourceAddress> sourceAddresses = new List<ListingSourceAddress>();
                        sourceAddresses.Add(sourcelocation);
                        property.ListingSourceAddresses = sourceAddresses;
                    }
                    #endregion

                    properties.Add(property);
                }
                catch (Exception ex)
                {
                    var property = new Domain.Entities.Property();
                    property.Notes = ex.Message;
                    properties.Add(property);

                }
            }
            return properties;
        }

        #region Upload Property Image In S3
        public static List<string> UploadImageInS3(List<string> imageUrl, IBlobStorageService _blobStorageService)
        {
            List<string> keys = new List<string>();
            try
            {
                foreach (var url in imageUrl)
                {
                    var client = new HttpClient();
                    var request = new HttpRequestMessage(HttpMethod.Get, url);
                    var response = client.SendAsync(request);
                    response.Result.EnsureSuccessStatusCode();
                    var data =  response.Result.Content.ReadAsByteArrayAsync();

                    using (var stream = new MemoryStream(data.Result))
                    {
                        var key =  _blobStorageService.UploadObjectAsync(_blobStorageService.BucketName ?? "leadrat-black", "Images/PropertyImages", GenerateRandomFileName(), stream);
                        keys.Add(key.Result);
                    }
                }
                return keys;
            }
            catch (Exception ex)
            {
                return keys;
            }
        }

        public static string GenerateRandomFileName()
        {
            string datePart = DateTime.Now.ToString("ddMMyyyy");
            Random random = new Random();
            string randomPart = random.Next(100000000, 999999999).ToString() +
                                random.Next(100000000, 999999999).ToString();

            return datePart + randomPart;
        }
        #endregion


        public static BudgetInfo ConvertBuget(string inputBudget)
        {
            var budget = inputBudget.Replace(" ", "");
            BudgetInfo budgetInfo = new();
            if (long.TryParse(budget, out long result))
            {
                budgetInfo.IsValidInfo = true;
                budgetInfo.Budget = result;
                return budgetInfo;
            }
            if (double.TryParse(budget, out double doubleValue))
            {
                budgetInfo.IsValidInfo = true;
                budgetInfo.Budget = (long)Math.Floor(doubleValue);
                return budgetInfo;
            }
            var lakhRegex = new Regex(@"\d+(?:\.\d+)?\s*(lak|lac|lack|lacks|lakh|l)s?", RegexOptions.IgnoreCase);
            var lakhMatch = lakhRegex.Match(budget);

            if (lakhMatch.Success)
            {
                var lakhString = lakhMatch.Value.ToLower();
                var croreRegex = new Regex(@"\d+(?:\.\d+)?\s*(crore|crores|cr|crs|c)", RegexOptions.IgnoreCase);
                var croreMatch = croreRegex.Match(budget);
                var crore = 0L;

                if (croreMatch.Success)
                {
                    crore = long.Parse(croreMatch.Value.ToLower().Replace("crore", "").Replace("crores", "").Replace("cr", "").Replace("crs", "").Replace("c", "").Trim());
                }
                var thousandRegex = new Regex(@"\d+(?:\.\d+)?\s*(thousand|thousands|tho|thosand|k)s?", RegexOptions.IgnoreCase);
                var thousandMatch = thousandRegex.Match(budget);
                var thousand = 0L;
                if (thousandMatch.Success)
                {
                    thousand = long.Parse(thousandMatch.Value.ToLower().Replace("thousand", "").Replace("thousands", "").Replace("thousands", "").Replace("tho", "").Replace("k", "").Trim());
                }
                var lakh = decimal.Parse(Regex.Replace(lakhString, @"[^\d+.\d+]", "")) * 100000;
                result = (long)Math.Floor(crore * 10000000 + lakh + thousand * 1000);
            }
            else
            {
                var thousandRegex = new Regex(@"\d+(?:\.\d+)?\s*(thousand|thousands|tho|thosand|k)s?", RegexOptions.IgnoreCase);
                var thousandMatch = thousandRegex.Match(budget);

                if (thousandMatch.Success)
                {
                    var thousandString = thousandMatch.Value.ToLower();
                    var croreRegex = new Regex(@"\d+(?:\.\d+)?\s*(crore|crores|cr|crs)", RegexOptions.IgnoreCase);
                    var croreMatch = croreRegex.Match(budget);
                    var crore = 0L;

                    if (croreMatch.Success)
                    {
                        crore = long.Parse(croreMatch.Value.ToLower().Replace("crore", "").Replace("crores", "").Replace("cr", "").Replace("crs", "").Trim());
                    }

                    var thousand = decimal.Parse(Regex.Replace(thousandString, @"[^\d+.\d+]", "")) * 1000;
                    result = (long)Math.Floor(crore * 10000000 + thousand);
                }
                else
                {
                    var croreRegex = new Regex(@"\d+(?:\.\d+)?\s*(crore|crores|cr|crs)", RegexOptions.IgnoreCase);
                    var croreMatch = croreRegex.Match(budget);

                    if (croreMatch.Success)
                    {
                        var croreString = croreMatch.Value.ToLower();
                        var millionRegex = new Regex(@"\d+(?:\.\d+)?\s*(million|millions)", RegexOptions.IgnoreCase);
                        var millionMatch = millionRegex.Match(budget);
                        var million = 0L;
                        if (millionMatch.Success)
                        {
                            million = long.Parse(millionMatch.Value.ToLower().Replace("million", "").Replace("millions", "").Trim());
                        }

                        var crore = decimal.Parse(Regex.Replace(croreString, @"[^\d+.\d+]", "")) * 10000000;
                        result = (long)Math.Floor(crore + million * 1000000);
                    }
                }

            }
            if (result != 0)
            {
                budgetInfo.IsValidInfo = true;
                budgetInfo.Budget = result;
            }
            else
            {
                budgetInfo.Invalidbudget = budget.ToString();
            }
            return budgetInfo;
        }
        public static PropertyTypeInfo GetPropertyType(string basePropertyType, string subPropertyType, List<MasterPropertyType> propertyTypes, string bhkType, string? noOfBHK)
        {
            if (basePropertyType.ToLower().Contains("residential") && !string.IsNullOrWhiteSpace(noOfBHK) && string.IsNullOrWhiteSpace(subPropertyType))
            {
                subPropertyType = "flat";
            }
            if (basePropertyType.ToLower().Contains("commercial") && string.IsNullOrWhiteSpace(subPropertyType))
            {
                subPropertyType = "Plot";
            }
            if (basePropertyType.ToLower().Contains("agricultural") && string.IsNullOrWhiteSpace(subPropertyType))
            {
                subPropertyType = "land";
            }

            MasterPropertyType propertyType = null;
            List<string> proprtyTypes = new() { "flat", "independent house", "villa", "residential", "shop", "row villa", "land", "office space", "hostel guest house", "plot", "showroom", "godown", "chambers", "farm house", "basement", "guest house", "kiosk", "complete building", "studio", "farmhouse land", "hotel space", "agricultural land", "industrial space", "co working office space", "whole building", "duplex", "bulk rent unit", "full floor", "bungalow", "apartment", "business_centre", "townhouse", "farm", "penthouse",
                "bulk sale unit", "warehouse", "staff accommodation", "labor camp", "half floor", "villament", "floor" , "restaurants", "cafes", "commercial shop","towerhouse", "hotel apartment","compound","pent house", "factory","business centre","retail" };
            BHKType bHKType = default;
            if (noOfBHK == "Studio")
            {
                noOfBHK = "0.5";
            }
            double bHK = GetNumber(noOfBHK);
            if (!string.IsNullOrEmpty(bhkType) || !string.IsNullOrEmpty(noOfBHK))
            {
                if ((Enum.TryParse<BHKType>(bhkType, true, out bHKType) || bHK != 0) && !proprtyTypes.Contains(subPropertyType.ToLower()))
                {
                    return new PropertyTypeInfo()
                    {
                        InvalidBHKType = bhkType,
                        InvalidNoOfBHK = noOfBHK,
                        IsValidInfo = false,
                        BasePropertyType = basePropertyType,
                        SubPropertyType = subPropertyType
                    };
                }
            }
            else
            {
                if (string.IsNullOrEmpty(basePropertyType) && string.IsNullOrEmpty(subPropertyType))
                {
                    return new PropertyTypeInfo() { IsValidInfo = false };

                }
            }
           /* string subProperty = string.Empty;
            string baseProperty = string.Empty;
            if (!string.IsNullOrEmpty(subPropertyType))
            {
                subProperty = subPropertyType;
            }
            else
            {
                if (!string.IsNullOrEmpty(basePropertyType))
                {
                    if (basePropertyType.ToLower().Contains("residential"))
                    {
                        subProperty = "flat";
                    }
                    if (basePropertyType.ToLower().Contains("commercial"))
                    {
                        subProperty = "Plot";
                    }
                    if (basePropertyType.ToLower().Contains("agricultural"))
                    {
                        subProperty = "land";
                    }
                }
            }*/

            if (!string.IsNullOrEmpty(basePropertyType) && basePropertyType?.ToLower() == "commercial")
            {
                propertyType = propertyTypes.FirstOrDefault(i => i.BaseId == Guid.Parse("c3a1cd50-0ec4-4541-8291-c898c96cd573") && i.DisplayName.ToLower() == subPropertyType.ToLower() && !i.IsDeleted);
            }
            if (string.IsNullOrEmpty(basePropertyType) || basePropertyType?.ToLower() == "residential")
            {
                propertyType = propertyTypes.FirstOrDefault(i => i.BaseId == Guid.Parse("310c9209-8390-4744-8e75-984ddff24a3c") && i.DisplayName.ToLower() == subPropertyType.ToLower() && !i.IsDeleted);
            }
            else
            {
                propertyType = propertyTypes.FirstOrDefault(i => i.DisplayName.ToLower() == subPropertyType.ToLower());
            }
            return new PropertyTypeInfo()
            {
                PropertyType = propertyType,
                BHKType = bHKType,
                NoOfBHK = bHK,
                IsValidInfo = true
            };
        }
        public static double GetNumber(string number)
        {
            try
            {
                Regex regex = new Regex(@"^\d+(\.\d+)?");
                Match match = regex.Match(number);
                double value = 0;

                if (match.Success)
                {
                    if (double.TryParse(match.Value, out double parsedValue))
                    {
                        value = parsedValue;
                    }
                }
                return value;
            }
            catch (Exception e)
            {
                throw;
            }

        }

        public static double? GetBrokerageAmount(string number)
        {
            try
            {
                Regex regex = new Regex(@"^\d+(\.\d+)?");
                Match match = regex.Match(number);
                double? value = null;

                if (match.Success)
                {
                    value = double.Parse(match.Value);
                }
                return value;
            }
            catch
            {
                throw;
            }
        }
        public static double? GetSecurityDepositAmount(string number)
        {
            try
            {
                Regex regex = new Regex(@"^\d+");
                Match match = regex.Match(number);
                double? integer = null;

                if (match.Success)
                {
                    integer = int.Parse(match.Value);
                }
                return integer;
            }
            catch (Exception e)
            {
                throw;
            }

        }

        public static double GetArea(string number)
        {
            try
            {
                Regex regex = new Regex(@"^\d+.\d+");
                Match match = regex.Match(number);
                double res = 0;

                if (match.Success)
                {
                    res = double.Parse(match.Value);
                }
                return res;
            }
            catch (Exception e)
            {
                throw;
            }

        }

        //public static PropertyDimension GetPropertyDimension(string propertySize, List<MasterAreaUnit> areaUnits, string unit,string netareasize=null,string netareaunit=null,Guid? defaultId=null)
        //{
        //    var properyArea = GetArea(propertySize);
        //    var unitArea = GetArea(unit);
        //    var netArea = GetArea(netareasize);
        //    var netunit = GetArea(netareaunit);
        //    PropertyDimension propertyDimension = new PropertyDimension();
        //    if (areaUnits.Count > 0)
        //    {
        //        foreach (var areaUnit in areaUnits)
        //        {
        //            var masterUnit = areaUnit?.Unit?.Replace(" ", "")?.Replace(".", "")?.ToLower()?.Trim();
        //            var propertyUnit = Regex.Replace(propertySize, "[^a-zA-Z]+", string.Empty).ToLower().Trim();
        //            if (!string.IsNullOrWhiteSpace(propertyUnit) && (masterUnit?.Contains(propertyUnit) ?? false))
        //            {
        //                propertyDimension.Area = properyArea == default ? unitArea == default ? default : unitArea : properyArea;
        //                propertyDimension.AreaUnitId = areaUnit?.Id ?? Guid.Empty;
        //                propertyDimension.AreaInSqMtr = (0.092903) * propertyDimension.Area;

        //            }
        //            else if (!string.IsNullOrWhiteSpace(unit) && (masterUnit?.Contains((Regex.Replace(unit, "[^a-zA-Z]+", string.Empty)).ToLower().Trim()) ?? false))
        //            {
        //                propertyDimension.Area = properyArea == default ? unitArea == default ? default : unitArea : properyArea;
        //                propertyDimension.AreaUnitId = areaUnit?.Id ?? Guid.Empty;
        //                propertyDimension.AreaInSqMtr = (areaUnit?.ConversionFactor ?? default) * propertyDimension.Area;
        //                break;
        //            }
        //            else if (string.IsNullOrWhiteSpace(unit) && (masterUnit?.Contains((Regex.Replace(unit, "[^a-zA-Z]+", string.Empty)).ToLower().Trim()) ?? false))
        //            {
        //                if (areaUnit?.Unit == "Sq. Feet")
        //                {
        //                    propertyDimension.Area = properyArea == default ? unitArea == default ? default : unitArea : properyArea;
        //                    propertyDimension.AreaUnitId = areaUnit?.Id ?? Guid.Empty;
        //                    propertyDimension.AreaInSqMtr = (areaUnit?.ConversionFactor ?? default) * propertyDimension.Area;
        //                    break;
        //                }
        //            }
        //            var NetAreaUnit = Regex.Replace(netareasize, "[^a-zA-Z]+", string.Empty).ToLower().Trim();
        //            if (!string.IsNullOrWhiteSpace(NetAreaUnit) && (masterUnit?.Contains(NetAreaUnit) ?? false))
        //            {
        //                propertyDimension.NetArea = netArea == default ? netunit == default ? default : netunit : netArea;
        //                propertyDimension.NetAreaUnitId = areaUnit?.Id ?? Guid.Empty;
        //                propertyDimension.NetAreaInSqMtr = (0.092903) * propertyDimension.Area;

        //            }
        //            else if (!string.IsNullOrWhiteSpace(unit) && (masterUnit?.Contains((Regex.Replace(unit, "[^a-zA-Z]+", string.Empty)).ToLower().Trim()) ?? false))
        //            {
        //                propertyDimension.NetArea = netArea == default ? netunit == default ? default : netunit : netArea;
        //                propertyDimension.NetAreaUnitId = areaUnit?.Id ?? Guid.Empty;
        //                propertyDimension.NetAreaInSqMtr = (areaUnit?.ConversionFactor ?? default) * propertyDimension.Area;
        //                break;
        //            }
        //            else if (string.IsNullOrWhiteSpace(unit) && (masterUnit?.Contains((Regex.Replace(unit, "[^a-zA-Z]+", string.Empty)).ToLower().Trim()) ?? false))
        //            {
        //                if (areaUnit?.Unit == "Sq. Feet")
        //                {
        //                    propertyDimension.NetArea = netArea == default ? netunit == default ? default : netunit : netArea;
        //                    propertyDimension.NetAreaUnitId = areaUnit?.Id ?? Guid.Empty;
        //                    propertyDimension.NetAreaInSqMtr = (areaUnit?.ConversionFactor ?? default) * propertyDimension.Area;
        //                    break;
        //                }
        //            }
        //        }
        //    }
        //    return propertyDimension;
        //}
        public static void SetProperty(this Domain.Entities.Property property,
             Dictionary<PropertyDataColumns, string>? mappedColumnsData, Guid currentUserId)
        {
            try
            {
                if (property != null)
                {
                    if (!string.IsNullOrWhiteSpace(property?.OwnerDetails?.Phone))
                    {
                        var multiNumbers = property.OwnerDetails.Phone.Contains(',') ? property.OwnerDetails.Phone.Split(',') : property.OwnerDetails.Phone.Contains('\\') ? property.OwnerDetails.Phone.Split('\\') : property.OwnerDetails.Phone.Split('/');
                        if (multiNumbers.Length > 1 && !mappedColumnsData.ContainsKey(PropertyDataColumns.Notes))
                        {
                            property.Notes += !string.IsNullOrEmpty(multiNumbers[1]) ? ", \n" + "AdditionalNumber" + " - " + multiNumbers[1] : string.Empty;
                        }
                        /*property.OwnerDetails.Phone = multiNumbers[0];
                        if (property.OwnerDetails.Phone.ToLower().Contains('e'))
                        {
                            if (double.TryParse(property.OwnerDetails.Phone.Replace("+91", ""), out double cNumber))
                            {
                                property.OwnerDetails.Phone = (cNumber).ToString().Split('.')[0];
                            }
                        }
                        property.OwnerDetails.Phone = Regex.Replace(property.OwnerDetails.Phone, @"[^0-9]+", "");
                        if (property.OwnerDetails.Phone.Length > 10) { property.OwnerDetails.Phone = property.OwnerDetails.Phone[^10..]; }; property.OwnerDetails.Phone = $"+91{property.OwnerDetails.Phone.Trim()}";*/
                    }
                    property.CreatedBy = currentUserId;
                    property.LastModifiedBy = currentUserId;
                }
            }
            catch (Exception ex)
            {
                throw;
            }
        }
        public static List<InvalidData> GetInvalidProperties(List<Domain.Entities.Property> properties, bool shouldEnablePropertyListing)
        {
            List<InvalidData> invalidData = new();
            #region Invalid
            List<Domain.Entities.Property>? invalidProperties = null;

            if (!shouldEnablePropertyListing)
            {
                invalidProperties = properties.Where(i => i.PossessionDate == DateTime.MinValue || i.PossessionDate == null).ToList();

                invalidProperties = properties.Where(i => i.PossesionType == PossesionType.None || i.PossesionType == null).ToList();
                if (invalidProperties.Any())
                {
                    var ivalidPosseionDate = invalidProperties.Adapt<List<InvalidData>>();
                    ivalidPosseionDate.ForEach(i => i.Errors = "Invalid PossessionDate");
                    properties.RemoveAll(i => i.PossesionType == PossesionType.None || i.PossesionType == null);
                    invalidData.AddRange(ivalidPosseionDate);
                    invalidProperties = null;
                }

                invalidProperties = properties.Where(i => i?.Address?.SubLocality == null && i?.Address?.City == null && i?.Address?.State == null).ToList();
                if (invalidProperties.Any())
                {
                    var ivalidPosseionDate = invalidProperties.Adapt<List<InvalidData>>();
                    ivalidPosseionDate.ForEach(i => i.Errors = "Invalid Address");
                    properties.RemoveAll(i => i?.Address?.SubLocality == null && i?.Address?.City == null && i?.Address?.State == null);
                    invalidData.AddRange(ivalidPosseionDate);
                    invalidProperties = null;
                }
            }
            invalidProperties = properties.Where(i => string.IsNullOrEmpty(i.Title)).ToList();
            if (invalidProperties.Any())
            {
                var ivalidPosseionDate = invalidProperties.Adapt<List<InvalidData>>();
                ivalidPosseionDate.ForEach(i => i.Errors = "Invalid Title");
                properties.RemoveAll(i => string.IsNullOrEmpty(i.Title));
                invalidData.AddRange(ivalidPosseionDate);
                invalidProperties = null;
            }
            invalidProperties = properties.Where(i => (i.EnquiredFor == EnquiryType.None)).ToList();
            if (invalidProperties.Any())
            {
                var ivalidPosseionDate = invalidProperties.Adapt<List<InvalidData>>();
                ivalidPosseionDate.ForEach(i => i.Errors = "Invalid EnquiredFor");
                properties.RemoveAll(i => (i.EnquiredFor == EnquiryType.None));
                invalidData.AddRange(ivalidPosseionDate);
                invalidProperties = null;
            }
            invalidProperties = properties.Where(i => i?.MonetaryInfo?.ExpectedPrice == default).ToList();
            if (invalidProperties.Any())
            {
                var ivalidPosseionDate = invalidProperties.Adapt<List<InvalidData>>();
                ivalidPosseionDate.ForEach(i => i.Errors = "Invalid Price");
                properties.RemoveAll(i => i?.MonetaryInfo?.ExpectedPrice == default);
                invalidData.AddRange(ivalidPosseionDate);
                invalidProperties = null;
            }
            invalidProperties = properties.Where(i => i?.Dimension?.Area == default).ToList();
            if (invalidProperties.Any())
            {
                var ivalidPosseionDate = invalidProperties.Adapt<List<InvalidData>>();
                ivalidPosseionDate.ForEach(i => i.Errors = "Invalid Area");
                properties.RemoveAll(i => i?.Dimension?.Area == default);
                invalidData.AddRange(ivalidPosseionDate);
                invalidProperties = null;
            }
            invalidProperties = properties.Where(i => i?.PropertyType == null).ToList();
            if (invalidProperties.Any())
            {
                var ivalidPosseionDate = invalidProperties.Adapt<List<InvalidData>>();
                ivalidPosseionDate.ForEach(i => i.Errors = "Invalid propertyType");
                properties.RemoveAll(i => i?.PropertyType == null);
                invalidData.AddRange(ivalidPosseionDate);
                invalidProperties = null;
            }

            #endregion
            return invalidData;
        }
        public static List<PropertyAttribute> GetPropertyAttribute(Dictionary<PropertyDataColumns, string> attributes, List<CustomMasterAttribute> masterPropertyAttributes)
        {
            List<PropertyAttribute> propertyAttributes = new List<PropertyAttribute>();
            foreach (var attribute in attributes)
            {
                var attributeValue = GetNumber(attribute.Value).ToString();

                if (attribute.Value.ToLower().Trim().Replace(" ", "").Contains("ground") && attribute.Key == PropertyDataColumns.TotalFloors)
                {
                    attributeValue = "Ground Floor";
                }
                else if (attribute.Value.ToLower().Trim().Replace(" ","").Contains("B1".ToLower()) && attribute.Key == PropertyDataColumns.FloorNumber)
                {
                    attributeValue = "B1";
                }
                else if (attribute.Value.ToLower().Trim().Replace(" ", "").Contains("B2".ToLower()) && attribute.Key == PropertyDataColumns.FloorNumber)
                {
                    attributeValue = "B2";
                }
                PropertyAttribute propertyAttribute = new();
                CustomMasterAttribute matchingAttribute = new();
                if (masterPropertyAttributes.Count > 0)
                {
                    var formattedKey = SplitCamelCase(attribute.Key.ToString()).ToLower();
                    if (formattedKey == "parking")
                    {
                        matchingAttribute = masterPropertyAttributes.FirstOrDefault(i => Regex.Replace(i?.AttributeDisplayName?.ToLower() ?? string.Empty, "[^a-zA-Z]+", " ").Equals(formattedKey));
                    }
                    else
                    {
                        matchingAttribute = masterPropertyAttributes.FirstOrDefault(i => Regex.Replace(i?.AttributeDisplayName?.ToLower() ?? string.Empty, "[^a-zA-Z]+", " ").Contains(formattedKey));
                    }
                    if (matchingAttribute != null)
                    {
                        propertyAttribute.MasterPropertyAttributeId = masterPropertyAttributes.FirstOrDefault(i => Regex.Replace(i?.AttributeDisplayName?.ToLower() ?? string.Empty, "[^a-zA-Z]+", " ").Contains(formattedKey))?.Id ?? Guid.Empty;
                        propertyAttribute.Value = attributeValue.ToString();
                        propertyAttributes.Add(propertyAttribute);
                    }
                }
            }
            return propertyAttributes;
        }
        private static string SplitCamelCase(string input)
        {
            return Regex.Replace(input, "(?<=[a-z])([A-Z])", " $1", RegexOptions.Compiled).Trim();
        }
      

            public static string GetCurrencySymbol1(this Dictionary<PropertyDataColumns, string> mappedColumnsData, DataRow row, string? currency, string? defaultcurrency)
            {
                if (string.IsNullOrWhiteSpace(currency))
                {
                    return defaultcurrency;
                }
                currency = currency.Replace(" ", "").ToUpper();
                var currencies = ISO3166.Country.List
                .SelectMany(country => new Nager.Country.CountryProvider().GetCountries()
                    .Where(c => c.CommonName.Equals(country.Name, StringComparison.OrdinalIgnoreCase))
                    .SelectMany(c => c.Currencies)
                    .Select(currency => currency.IsoCode))
                .ToList();
            bool isCurrencyCode = !currencies.Contains(currency);
            if (isCurrencyCode)
                {
                    string symbol;
                    if (TryGetCurrencySymbol(currency, out symbol))
                    {
                        return symbol;
                    }
                    else
                    {
                        return defaultcurrency;
                    }
                }
                if (GetValidCurrencySymbols().Contains(currency))
                {
                    return currency;
                }

                else
                {
                    return defaultcurrency;
                }

            }

            public static bool TryGetCurrencySymbol(string currencySymbol, out string isoCurrencyCode)
            {
                isoCurrencyCode = CultureInfo
            .GetCultures(CultureTypes.AllCultures)
            .Where(c => !c.IsNeutralCulture)
            .Select(culture =>
            {
                try
                {
                    return new RegionInfo(culture.Name);
                }
                catch
                {
                    return null;
                }
            })
            .Where(ri => ri != null && ri.CurrencySymbol.Equals(currencySymbol, StringComparison.OrdinalIgnoreCase))
            .Select(ri => ri.ISOCurrencySymbol)
            .FirstOrDefault();

                return isoCurrencyCode != null;
            }
            public static List<string> GetValidCurrencySymbols()
            {
                var symbols = new List<string>();
                var countries = ISO3166.Country.List;

                var currencyProvider = new Nager.Country.CountryProvider();

                foreach (var country in countries)
                {
                    var matchingCountry = currencyProvider.GetCountries()
                        .FirstOrDefault(c => c.CommonName.Equals(country.Name, StringComparison.OrdinalIgnoreCase));

                    if (matchingCountry != null)
                    {
                        foreach (var currency in matchingCountry.Currencies)
                        {
                            var symbol = currency.IsoCode;
                            symbols.Add(symbol);
                        }
                    }
                }

                return symbols;

            }
        
        

        public static string GetBrokerageCurrencySymbol(this Dictionary<PropertyDataColumns, string> mappedColumnsData, DataRow row, string? currency, string? defaultcurrency)
        {
            if (string.IsNullOrEmpty(currency))
            {
                return defaultcurrency;
            }
            if (currency == "%")
            {
                return currency;
            }
            currency = currency.Replace(" ", "").ToUpper();
            bool isCurrencyCode = currency.Length != 3;

            if (isCurrencyCode)
            {
                string symbol;
                if (TryGetCurrencySymbol(currency, out symbol))
                {
                    return symbol;
                }
                else
                {
                    return defaultcurrency;
                }
            }
            if (GetValidCurrencySymbols().Contains(currency))
            {
                return currency;
            }

            else
            {
                return defaultcurrency;
            }
        }
        public static Lrb.Domain.Entities.Project? GetProject(string newProject, Lrb.Domain.Entities.GlobalSettings globalSettings, List<Lrb.Domain.Entities.Project> projects)
        {
            var project = string.IsNullOrWhiteSpace(newProject) ? null :
                   projects.FirstOrDefault(i => i.Name?.ToLower().Trim() == newProject.ToLower().Trim())
                               ?? new Lrb.Domain.Entities.Project()
                               {
                                   Name = newProject,
                                   MonetaryInfo = new ProjectMonetaryInfo
                                   {
                                       Currency = globalSettings?.Countries?.FirstOrDefault()?.DefaultCurrency ?? "INR"
                                   }
                               };
            return project;
        }
        public static string ValidateContactNo(this Dictionary<PropertyDataColumns, string> mappedColumnsData, DataRow row, string? contactNo, string? countryCode, string? callingCode)
        {
            try
            {
                if (string.IsNullOrEmpty(contactNo))
                {
                    return null;
                }

                if (countryCode == string.Empty)
                {
                    countryCode = callingCode;
                }
                PhoneNumberUtil phoneUtil = PhoneNumberUtil.GetInstance();

                string defaultRegion = null;
                if (!string.IsNullOrEmpty(countryCode))
                {
                    int CountryCode = Convert.ToInt32(countryCode);

                    var countryToRegionMap = CountryCodeToRegionCodeMap.GetCountryCodeToRegionCodeMap();
                    if (countryToRegionMap.TryGetValue(CountryCode, out var regionCodes))
                    {
                        defaultRegion = regionCodes.FirstOrDefault();
                    }
                }

                PhoneNumber phoneNumber = phoneUtil.Parse(contactNo, defaultRegion);

                PhoneNumber numberExample = phoneUtil.GetExampleNumberForType(defaultRegion, PhoneNumberType.MOBILE);

                string formattedNumber = phoneUtil.Format(numberExample, PhoneNumberFormat.E164);

                string contactWithCountryCode = phoneUtil.Format(phoneNumber, PhoneNumberFormat.E164);

                string numericMobileNumber = Regex.Replace(contactWithCountryCode, @"\D", "");

                bool isValid = numericMobileNumber.Length == contactWithCountryCode.Length - 1;

                if (isValid)
                {
                    return contactWithCountryCode;
                }
                else
                {
                    return null;
                }
            }
            catch
            {
                return null;
            }
        }
        public static DateTime? GetPossessionDate(this Dictionary<PropertyDataColumns, string> mappedColumnsData, PropertyDataColumns column, DataRow row, string jsonData = null)
        {
            try
            {
                var dateString = GetStringValue(mappedColumnsData, column, row);
                if (string.IsNullOrEmpty(dateString))
                {
                    return column == PropertyDataColumns.PossessionDate ? (DateTime?)null : DateTime.UtcNow;
                }
                DateTime? date = null;
                CommonTimeZoneDto? commonTimeZoneDto = null;
                if (!string.IsNullOrWhiteSpace(jsonData))
                {
                    commonTimeZoneDto = JsonConvert.DeserializeObject<CommonTimeZoneDto>(jsonData);
                }
                if (double.TryParse(dateString, out var excelDateValue))
                {
                    date = DateTime.FromOADate(excelDateValue);
                }
                else
                {
                    if (!DateTime.TryParseExact(dateString, LeadMigrateHelper.formats, CultureInfo.InvariantCulture, DateTimeStyles.None, out var parsedDate))
                    {
                        date = DateTime.ParseExact(dateString, LeadMigrateHelper.formats, CultureInfo.InvariantCulture);
                    }
                    else
                    {
                        date = parsedDate;
                    }
                }
                if (commonTimeZoneDto != null)
                {
                    date = date?.ConvertDateTime(commonTimeZoneDto.TimeZoneId ?? string.Empty, commonTimeZoneDto.BaseUTcOffset);
                }

                return date;
            }
            catch
            {
                return null;
            }
        }

        private static string? GetStringValue(this Dictionary<PropertyDataColumns, string> mappedColumnsData, PropertyDataColumns column, DataRow row)
        {
            var data = !mappedColumnsData.ContainsKey(column)
                    || string.IsNullOrEmpty(mappedColumnsData[column])
                    ? string.Empty
                    : row[mappedColumnsData[column]]?.ToString();
            return data?.Trim() ?? null;
        }
    }
}