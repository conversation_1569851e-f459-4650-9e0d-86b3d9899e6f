﻿using Lrb.Application.DataManagement.Mobile.Dtos;
using Lrb.Application.Lead.Mobile.Dtos.v1;
using Lrb.Application.Lead.Mobile.Requests.v1;

namespace Lrb.Application.Lead.Mobile
{
    public class GetAllLeadsEnquiriesSpec : EntitiesByPaginationFilterSpec<Domain.Entities.Lead>
    {
        public GetAllLeadsEnquiriesSpec(GetAllLeadAddressRequest filter) : base(filter)
        {
            Query.Where(i => !i.IsDeleted && i.Enquiries != null && i.Enquiries.Any(i => i.Address != null && ((i.Address.SubLocality != null && i.Address.SubLocality != string.Empty)
            || (i.Address.Locality != null && i.Address.Locality != string.Empty) || (i.Address.City != null && i.Address.City != string.Empty) || (i.Address.State != null && i.Address.State != string.Empty))))
                .Where(i => !i.IsArchived)
               .Include(i => i.TagInfo)
               //.Include(i => i.Status)
               .Include(i => i.Enquiries)
               //.ThenInclude(i => i.Address)
               .ThenInclude(i => i.Addresses)
               .Include(i => i.Enquiries)
               .ThenInclude(i => i.PropertyType)
               .Include(i => i.Enquiries)
               .ThenInclude(i => i.PropertyTypes);
        }
    }
    public class GetAllLeadsEnquiriesCountSpec : Specification<Domain.Entities.Lead>
    {
        public GetAllLeadsEnquiriesCountSpec()
        {
            Query.Where(i => !i.IsDeleted && i.Enquiries != null && i.Enquiries.Any(i => i.Address != null && ((i.Address.SubLocality != null && i.Address.SubLocality != string.Empty)
             || (i.Address.Locality != null && i.Address.Locality != string.Empty) || (i.Address.City != null && i.Address.City != string.Empty) || (i.Address.State != null && i.Address.State != string.Empty))))
                .Include(i => i.TagInfo)
               //.Include(i => i.Status)
               .Include(i => i.Enquiries)
               //.ThenInclude(i => i.Address)
               .ThenInclude(i => i.Addresses)
               .Include(i => i.Enquiries)
               .ThenInclude(i => i.PropertyType)
               .Include(i => i.Enquiries)
               .ThenInclude(i => i.PropertyTypes);
        }
    }

    public class GetAllLeadsInfoSpec : EntitiesByPaginationFilterSpec<Domain.Entities.Lead, GetAllOfflineLeadsDto>
    {
        public GetAllLeadsInfoSpec(GetAllLeadsOfflineRequest filter) : base(filter)
        {
            Query.Select(i => new GetAllOfflineLeadsDto
            {
                Id = i.Id,
                Name = i.Name,
                ContactNo = i.ContactNo,
                AlternateContactNo = i.AlternateContactNo,
                AssignTo = i.AssignTo,
                IsDeleted = i.IsDeleted,
                LastModifiedOn = i.LastModifiedOn,
                IsArchived = i.IsArchived
            });
            Query.OrderByDescending(i => i.LastModifiedOn);
            if (filter.SendOnlyAssignedLeads == true)
            {
                Query.Where(i => i.AssignTo == filter.UserId);
            }
        }
    }

    public class GetAllLeadsByLastModifiedRangeSpec : EntitiesByPaginationFilterSpec<Domain.Entities.Lead, GetAllOfflineLeadsDto>
    {
        public GetAllLeadsByLastModifiedRangeSpec(GetAllLeadsByLastModifiedRequest filter) : base(filter)
        {
            if (filter.DateRangeFrom != null && filter.DateRangeTo != null) 
            {
                Query.Where(i => i.LastModifiedOn >= filter.DateRangeFrom && i.LastModifiedOn <= filter.DateRangeTo); 
            }
            if(filter.SendOnlyAssignedLeads == true)
            {
                Query.Where(i => i.AssignTo == filter.UserId || i.SecondaryUserId == filter.UserId);
            }
            if ((filter.DateRangeFrom != null && filter.DateRangeTo != null) || (filter.SendOnlyAssignedLeads == true))
            {
                Query.Select(i => new GetAllOfflineLeadsDto
                {
                    Id = i.Id,
                    Name = i.Name,
                    ContactNo = i.ContactNo,
                    AlternateContactNo = i.AlternateContactNo,
                    AssignTo = i.AssignTo,
                    IsDeleted = i.IsDeleted,
                    LastModifiedOn = i.LastModifiedOn,
                    IsArchived = i.IsArchived
                });
            }
        }
    }
}
