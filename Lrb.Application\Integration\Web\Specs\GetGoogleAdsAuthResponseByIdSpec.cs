﻿using Lrb.Application.Integration.Web.Requests.Facebook;
using Lrb.Application.Integration.Web.Requests.GoogleAds;
using Lrb.Domain.Entities.Integration;

namespace Lrb.Application.Integration.Web.Specs
{
    public class GetGoogleAdsAuthResponseByIdSpec : Specification<GoogleAdsAuthResponse>
    {
        public GetGoogleAdsAuthResponseByIdSpec(string customerId)
        {
            Query.Where(i => i.CustomerId == customerId && !i.IsDeleted);
        }
        public GetGoogleAdsAuthResponseByIdSpec(List<Guid>? Ids)
        {
            Query.Where(i => Ids.Contains(i.Id) && !i.IsDeleted);
        }
        public GetGoogleAdsAuthResponseByIdSpec(Guid? Id)
        {
            Query.Where(i => i.Id == Id && !i.IsDeleted);
        }
    }
    public class GoogleAdAdsByAccountIdSpec : Specification<GoogleAdsInfo>
    {
        public GoogleAdAdsByAccountIdSpec(Guid accId)
        {
            Query.Where(i => !i.IsDeleted && i.GoogleAuthResponseId == accId);
        }
        public GoogleAdAdsByAccountIdSpec(List<Guid>? accId)
        {
            Query.Where(i => !i.IsDeleted && accId.Contains(i.GoogleAuthResponseId));
        }

    }

    public class GoogleAdCampaignsByAccountIdSpec : Specification<GoogleCampaign>
    {
        public GoogleAdCampaignsByAccountIdSpec(Guid accId)
        {
            Query.Where(i => !i.IsDeleted && i.GoogleAuthResponseId == accId);
        }

    }

    public class GetCustomerIdsByCampaignsSpecs : Specification<GoogleAdsInfo>
    {
        public GetCustomerIdsByCampaignsSpecs(List<string>? campaignIds)
        {
            Query.Where(i => !i.IsDeleted && campaignIds.Contains(i.CampaignId));
        }
    }
    public class GetGoogleAdByIdSpec : Specification<GoogleAdsInfo>
    {
        public GetGoogleAdByIdSpec(long? creativeId)
        {
            Query.Where(i => !i.IsDeleted && i.AdId.Equals(creativeId));
        }
        public GetGoogleAdByIdSpec(string? campaignId)
        {
            Query.Where(i => !i.IsDeleted && i.CampaignId.Equals(campaignId));
        }
    }

    public class GetGoogleAdsByGoogleAccountRequestSpec : EntitiesByPaginationFilterSpec<GoogleAdsInfo>
    {
        public GetGoogleAdsByGoogleAccountRequestSpec(GetAllGoogleAdAdsRequest filter) : base(filter)
        {
            Query.Where(i => !i.IsDeleted && i.GoogleAuthResponseId == filter.AccountId)
                          .OrderBy(i => i.Status).ThenBy(i => !i.IsSubscribed).ThenBy(i => i.LastModifiedOn);
        }
    }

}
