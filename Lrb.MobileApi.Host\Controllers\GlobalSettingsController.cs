﻿using Lrb.Application.GlobalSettings.Mobile;
using Lrb.Application.GlobalSettings.Mobile.Dto;
using Lrb.Application.GlobalSettings.Mobile.Requests;
using Lrb.Application.Project.Web.Requests;
using Lrb.Domain.Entities;

namespace Lrb.MobileApi.Host.Controllers
{
    [Authorize]
    public class GlobalSettingsController : VersionedApiController
    {
        [HttpPut("notification")]
        [TenantIdHeader]
    //  [MustHavePermission(LrbAction.Update, LrbResource.GlobalSettings)]
        [OpenApiOperation("Update notification settings.", "")]
        public async Task<Response<bool>> UpdateAsync(UpdateNotificationSettingsRequest request)
        {
            return await Mediator.Send(request);
        }
        [HttpPut("call")]
        [TenantIdHeader]
      //  [MustHavePermission(LrbAction.Update, LrbResource.GlobalSettings)]
        [OpenApiOperation("Update call settings.", "")]
        public async Task<Response<bool>> UpdateAsync(UpdateCallSettingsRequest request)
        {
            return await Mediator.Send(request);
        }
        [HttpPut]
        [TenantIdHeader]
        //[MustHavePermission(LrbAction.Update, LrbResource.GlobalSettings)]
        [OpenApiOperation("Update global settings.", "")]
        public async Task<Response<bool>> UpdateAsync(UpdateGlobalSettingsRequest request)
        {
            return await Mediator.Send(request);
        }
        [HttpGet]
        [TenantIdHeader]
        //[MustHavePermission(LrbAction.View, LrbResource.GlobalSettings)]
        [OpenApiOperation("Get global settings.", "")]
        public async Task<Response<ViewGlobalSettingsDto>> GetAsync()
        {
            return await Mediator.Send(new GetGlobalSettingsRequest());
        }
        [HttpPut("notes")]
        [TenantIdHeader]
        //[MustHavePermission(LrbAction.Update, LrbResource.GlobalSettings)]
        [OpenApiOperation("Update call settings.", "")]
        public async Task<Response<bool>> UpdateAsync(UpdateLeadNotesSettingRequest request)
        {
            return await Mediator.Send(request);
        }
        [HttpGet("dual-ownership-details")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.GlobalSettings)]
        [OpenApiOperation("Get dual ownership details.", "")]
        public async Task<Response<bool>> GetDualOwnershipDetailsAsync()
        {
            return await Mediator.Send(new GetDualOwnershipDetailsRequest());
        }
        [HttpGet("countriesInfo")]
        [TenantIdHeader]
        public async Task<Response<BaseCountryInfoDto>> GetCustomMasterLeadStatuses1()
        {
            return await Mediator.Send(new GetAllCountryCodeInfoRequest());
        }
        [AllowAnonymous]
        [HttpGet("otp/anonymous")]
        [TenantIdHeader]
        [OpenApiOperation("Get OTP settings.", "")]
        public async Task<Response<OTPSettings>> GetOTPSettingsAsync()
        {
            return await Mediator.Send(new GetOTPSettingsRequest());
        }
        [AllowAnonymous]
        [HttpGet("callSettings/anonymous")]
        [TenantIdHeader]
        [OpenApiOperation("Get Call settings.", "")]
        public async Task<Response<CallSettings>> GetCallSettingsAsync()
        {
            var tenantId = this.HttpContext.Request.Headers["tenant"];
            var request = new GetCallSettingsRequest
            {
                TenantId = tenantId
            };
            return await Mediator.Send(request);
        }
    }
}
