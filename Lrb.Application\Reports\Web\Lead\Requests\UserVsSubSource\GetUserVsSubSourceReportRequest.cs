﻿using Lrb.Application.Reports.Web.Lead.Dtos.UserVsSource;
using Lrb.Application.Reports.Web.Lead.Dtos.UserVsSubSource;
using Lrb.Application.Utils;
using Lrb.Shared.Extensions;
using Newtonsoft.Json;

namespace Lrb.Application.Reports.Web.UserVsSource.Requests
{
    public class GetUserVsSubSourceReportRequest : IRequest<PagedResponse<UservsSubSourceReportDto, string>>
    {
        public DateTime? FromDate { get; set; }
        public DateTime? ToDate { get; set; }
        public DateType? DateType { get; set; }
        public bool IsWithTeam { get; set; }
        public List<Guid>? UserIds { get; set; }
        public string? SearchText { get; set; }
        public List<LeadSource>? Sources { get; set; }
        public List<string>? SubSources { get; set; }
        public List<string>? Projects { get; set; }
        public UserStatus? UserStatus { get; set; }
        public ReportPermission? ReportPermission { get; set; }
        public int PageNumber { get; set; }
        public int PageSize { get; set; } = int.MaxValue;
        public List<string>? Localites { get; set; }
        public List<string>? States { get; set; }
        public List<string>? Cities { get; set; }
        public List<string>? Countries { get; set; }

    }

    public class GetSubSourceReportByUserRequestHandler : IRequestHandler<GetUserVsSubSourceReportRequest, PagedResponse<UservsSubSourceReportDto, string>>
    {
        private readonly IDapperRepository _dapperRepository;
        private readonly ICurrentUser _currentUser;
        public GetSubSourceReportByUserRequestHandler(IDapperRepository dapperRepository, ICurrentUser currentUser)
        {
            _dapperRepository = dapperRepository;
            _currentUser = currentUser;
        }

        public async Task<PagedResponse<UservsSubSourceReportDto, string>> Handle(GetUserVsSubSourceReportRequest request, CancellationToken cancellationToken)
        {
            var tenantId = _currentUser.GetTenant();
            var userId = _currentUser.GetUserId();
            var isAdminTask = _dapperRepository.IsAdminAsync(userId, tenantId ?? string.Empty);

            List<Guid> permittedUserIds = new();
            List<Guid> teamUserIds = new();
            var isAdmin = await isAdminTask;
            if (isAdmin)
            {
                permittedUserIds = (await _dapperRepository.GetAllUserIdsAsync(tenantId ?? string.Empty)).ToList();
            }
            else if (request.ReportPermission != null)
            {
                switch (request.ReportPermission)
                {
                    case ReportPermission.All:
                        permittedUserIds = (await _dapperRepository.GetAllUserIdsAsync(tenantId ?? string.Empty)).ToList();
                        break;
                    case ReportPermission.Reportees:
                        permittedUserIds = (await _dapperRepository.GetSubordinateIdsWithOnlyReporteesAsync(new List<Guid>() { userId }, tenantId ?? string.Empty)).ToList();
                        break;
                }
            }

            if (request?.UserIds?.Any() ?? false)
            {
                if (request?.IsWithTeam ?? false)
                {
                    teamUserIds = (await _dapperRepository.GetSubordinateIdsWithOnlyReporteesAsync(request.UserIds, tenantId ?? string.Empty)).ToList();
                }
                else
                {
                    teamUserIds = request?.UserIds ?? new List<Guid>();
                }
            }
            else
            {
                if (!isAdmin)
                {
                    teamUserIds = permittedUserIds;
                }
            }

            if (teamUserIds.Any())
            {
                teamUserIds = teamUserIds.Where(userId => permittedUserIds.Contains(userId)).ToList();
            }
            else
            {
                teamUserIds = permittedUserIds;
            }

            request.FromDate = request.FromDate.HasValue ? request.FromDate.Value.ConvertFromDateToUtc() : null;
            request.ToDate = request.ToDate.HasValue ? request.ToDate.Value.ConvertToDateToUtc() : null;


            var leadsReportTask = _dapperRepository.QueryStoredProcedureFromReadReplicaAsync<LeadsReportDto>("LeadratBlack", "GetSourceByUserReportSubsource", new
            {
                fromdate = request.FromDate,
                todate = request.ToDate,
                datetype = request.DateType,
                tenantid = tenantId,
                userids = teamUserIds,
                searchtext = string.IsNullOrEmpty(request.SearchText) ? null : request.SearchText.Replace(" ", "").ToLower(),
                sources = request?.Sources?.ConvertAll(i => (int)i),
                projects = request?.Projects?.ConvertAll<string>(i => i.Replace(" ", "").ToLower()),
                userstatus = (request?.UserStatus ?? 0),
                subsources = request?.SubSources?.ConvertAll<string>(i => i.Replace(" ", "").ToLower()),
                pagesize = request.PageSize,
                pagenumber = request.PageNumber,
                localites = request?.Localites?.ConvertAll<string>(i => i.LrbNormalize()),
                cities = request?.Cities?.ConvertAll<string>(i => i.LrbNormalize()),
                states = request?.States?.ConvertAll<string>(i => i.LrbNormalize()),
                countries = request?.Countries?.ConvertAll<string>(i => i.Replace(" ", "").ToLower())
            }, 300);

            var userVsSourceReportTask = _dapperRepository.QueryStoredProcedureFromReadReplicaAsync<UserVsSourceReportDto>("LeadratBlack", "UserVsSourceReports", new
            {
                fromdate = request.FromDate,
                todate = request.ToDate,
                datetype = request.DateType,
                tenantid = tenantId,
                userids = teamUserIds,
                searchtext = string.IsNullOrEmpty(request.SearchText) ? null : request.SearchText.Replace(" ", "").ToLower(),
                sources = request?.Sources?.ConvertAll(i => (int)i),
                projects = request?.Projects?.ConvertAll<string>(i => i.Replace(" ", "").ToLower()),
                userstatus = (request?.UserStatus ?? 0),
                subsources = request?.SubSources?.ConvertAll<string>(i => i.Replace(" ", "").ToLower()),
                pagenumber = request.PageNumber,
                pagesize = request.PageSize,
                localites = request?.Localites?.ConvertAll<string>(i => i.LrbNormalize()),
                cities = request?.Cities?.ConvertAll<string>(i => i.LrbNormalize()),
                states = request?.States?.ConvertAll<string>(i => i.LrbNormalize()),
                countries = request?.Countries?.ConvertAll<string>(i => i.Replace(" ", "").ToLower())
            }, 300);

            await Task.WhenAll(leadsReportTask, userVsSourceReportTask);

            var leadsReportResult = leadsReportTask.Result;
            var userVsSourceReportResult = userVsSourceReportTask.Result;

            var userSourceReportDtos = leadsReportResult
                .Select(i => JsonConvert.DeserializeObject<UserSourceReportDto>(i.Report ?? string.Empty) ?? new UserSourceReportDto())
                .ToList();

            var groupedResult = userSourceReportDtos
                .GroupBy(i => i?.User ?? new User())
                .ToDictionary(i => i.Key, j => j.SelectMany(i => i?.Source).ToList());

            var userVsSourceReportDtos = userVsSourceReportResult
                .Select(i =>
                {
                    i.SourcesDtos = JsonConvert.DeserializeObject<List<SourceDtos>>(i?.Source ?? string.Empty);
                    return i;
                })
                .ToList();

            var groupedResultSubSource = userVsSourceReportDtos
                .GroupBy(i => i?.UserId ?? Guid.Empty)
                .ToDictionary(i => i.Key, j => j.SelectMany(i => i?.SourcesDtos ?? new List<SourceDtos>()).ToList());

            List<UservsSubSourceReportDto> modifiedSubSourceReportDtos = new();

            foreach (var group in groupedResult)
            {
                foreach (var groupSubSource in groupedResultSubSource)
                {
                    if (group.Key.UserId == groupSubSource.Key)
                    {

                        if (groupedResultSubSource.TryGetValue(group.Key.UserId, out var subSourceDtos))
                        {
                            UservsSubSourceReportDto reportDto = new()
                            {
                                Source = group.Value,
                                UserName = group.Key.UserName,
                                UserId = group.Key.UserId,
                                SourceWithSubSourceCount = await GetGroupedStatusAsync(new KeyValuePair<Guid, List<SourceDtos>>(group.Key.UserId, subSourceDtos)) ?? new()
                            };
                            modifiedSubSourceReportDtos.Add(reportDto);
                        }
                    }
                }
            }

            return new PagedResponse<UservsSubSourceReportDto, string>(modifiedSubSourceReportDtos, 0);
        }

        private async Task<Dictionary<string, object>?> GetGroupedStatusAsync(KeyValuePair<Guid, List<SourceDtos>> group)
        {
            var groupedValues = group.Value?.GroupBy(i => i.BaseSource)?.ToDictionary(i => i.Key, j => j.ToList());
            Dictionary<string, object> baseSourceWithSubSourceCount = new();
            if (groupedValues == null)
            {
                return null;
            }

            foreach (var source in groupedValues)
            {
                Dictionary<string, int> subSource = new();
                foreach (var subSources in source.Value)
                {
                    string subSourceName = string.IsNullOrEmpty(subSources.SubSource) ? source.Key + "(WithOutSubSourceCount)" : subSources.SubSource;
                    string sanitizedSubStatusDisplayName = subSourceName;

                    if (subSource.ContainsKey(sanitizedSubStatusDisplayName))
                    {
                        subSource[sanitizedSubStatusDisplayName] += subSources.Count;
                    }
                    else
                    {
                        subSource.Add(sanitizedSubStatusDisplayName, subSources.Count);
                    }
                }
                if (source.Key != null)
                {
                    string sanitizedSourceKey = source.Key.Replace("_", "");

                    if (subSource.Count == 1 && subSource.ContainsKey(sanitizedSourceKey))
                    {
                        baseSourceWithSubSourceCount[source.Key] = subSource.FirstOrDefault().Value;
                    }
                    else
                    {
                        baseSourceWithSubSourceCount[source.Key] = subSource;
                    }
                }
            }
            return baseSourceWithSubSourceCount;
        }
    }
}



