﻿using DocumentFormat.OpenXml.Spreadsheet;
using Lrb.Application.Common.Interfaces;
using Lrb.Application.Common.Persistence.New_Implementation;
using Lrb.Application.Dashboard.Mobile;
using Lrb.Application.Identity.Users;
using Lrb.Application.UserDetails.Mobile;
using Lrb.Domain.Entities;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Lrb.Application.Todo.Mobile
{
    public class GetTodobySheduledDateRequest : IRequest<PagedResponse<BaseViewTodoDto, TodoCountDto>>
    {
        public TodoFilterType TodoFilterType { get; set; }
        public int PageNumber { get; set; } = 1;
        public int PageSize { get; set; } = int.MaxValue;
        public DateTime? Fromdate { get; set; }
        public DateTime? Todate { get; set; }
        public string? TimeZoneId { get; set; }
    }

    public class GetTodobySheduledDateRequestHandler : IRequestHandler<GetTodobySheduledDateRequest, PagedResponse<BaseViewTodoDto, TodoCountDto>>
    {
        private readonly IReadRepository<Lrb.Domain.Entities.Todo> _todoRepo;
        private readonly ICurrentUser _currentUser;
        private readonly IUserService _userService;
        private readonly INpgsqlRepository _npgsqlRepository;
        private readonly ITodoRepository _todoRepository;

        public GetTodobySheduledDateRequestHandler(
            IReadRepository<Lrb.Domain.Entities.Todo> todoRepo,
            ICurrentUser currentUser,
            IUserService userService,
            INpgsqlRepository npgsqlRepository,
            ITodoRepository todoRepository)
        {
            _todoRepo = todoRepo;
            _currentUser = currentUser;
            _userService = userService;
            _npgsqlRepository = npgsqlRepository;
            _todoRepository = todoRepository;
        }

        public async Task<PagedResponse<BaseViewTodoDto, TodoCountDto>> Handle(GetTodobySheduledDateRequest request, CancellationToken cancellationToken)
        {
            Guid userId = _currentUser.GetUserId();
            var totalCount = await _todoRepo.CountAsync(new GetTodoBySheduleDateSpec(request, _currentUser, applyPagination: false), cancellationToken);
            var pagedTodos = await _todoRepo.ListAsync(new GetTodoBySheduleDateSpec(request, _currentUser, applyPagination: true), cancellationToken);

            var now = DateTime.UtcNow;
            var todayStart = now.Date;
            var tomorrowStart = todayStart.AddDays(1);

            var countDto = new TodoCountDto
            {
                TodaysTodosCount = pagedTodos.Count(i => i.ScheduledDateTime >= todayStart && i.ScheduledDateTime < tomorrowStart),
                UpcomingTodosCount = pagedTodos.Count(i => i.ScheduledDateTime >= tomorrowStart && !i.IsMarkedDone),
                CompletedTodosCount = pagedTodos.Count(i => i.ScheduledDateTime != null && i.IsMarkedDone),
                OverdueTodosCount = pagedTodos.Count(i => i.ScheduledDateTime < now && !i.IsMarkedDone),
                AllTodosCount = totalCount
            };

            var userIds = pagedTodos.SelectMany(todo =>
                {
                    var ids = todo.AssignedUserIds?.Select(id => id.ToString()) ?? Enumerable.Empty<string>();
                    if (todo.AssignedFrom.HasValue)
                        ids = ids.Append(todo.AssignedFrom.Value.ToString());
                    return ids;
                }).Distinct().ToList();

            var users = await _userService.GetListOfUsersByIdsAsync(userIds, cancellationToken);
            var todoDtos = new List<BaseViewTodoDto>();

            foreach (var todo in pagedTodos)
            {
                var todoDto = todo.Adapt<BaseViewTodoDto>();
                todoDto.AssignedUsers = users
                    .Where(u => todo.AssignedUserIds?.Contains(u.Id) == true)
                    .Adapt<List<ViewUserDto>>();

                if (todo.AssignedFrom.HasValue)
                {
                    var assignedFrom = users.FirstOrDefault(i => i.Id == todo.AssignedFrom);
                    if (assignedFrom != null)
                        todoDto.AssignedFrom = assignedFrom.Adapt<ViewUserDto>();
                }
                todoDtos.Add(todoDto);
            }
            var taskDto = new TasksDto
            {
                Tasks = todoDtos
                    .GroupBy(i => i.CreatedOn.Date)
                    .ToDictionary(g => g.Key, g => g.Select(t => t.Adapt<TodoViewModel>()).ToList())
            };
            countDto.TasksDto = taskDto;
            return new PagedResponse<BaseViewTodoDto, TodoCountDto>(todoDtos, totalCount, countDto);
        }
    }
}