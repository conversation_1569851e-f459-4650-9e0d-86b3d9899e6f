﻿using Lrb.Application.Integration.Web.Dtos;
using Lrb.Application.LeadGenRequests;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.WebUtilities;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Lrb.Application.Integration.Web.Requests.Bayut
{
    public class BayutWAStoriesPushIntegrationRequest : IRequest<Response<bool>>
    {
        public string ApiKey { get; set; }
        public HttpRequest HttpRequest { get; set; }
        public string TenantId { get; set; }

        public BayutWAStoriesPushIntegrationRequest(HttpRequest request, string tenant, string base64)
        {
            HttpRequest = request;
            TenantId = tenant;
            ApiKey = base64;

        }
        public class BayutWAStoriesPushIntegrationRequestHandler : IRequestHandler<BayutWAStoriesPushIntegrationRequest, Response<bool>>
        {
            private readonly IMediator _mediator;
            private readonly Serilog.ILogger _logger;
            public BayutWAStoriesPushIntegrationRequestHandler(IMediator mediator, Serilog.ILogger logger)
            {
                _mediator = mediator;
                _logger = logger;
            }

            public async Task<Response<bool>> Handle(BayutWAStoriesPushIntegrationRequest request, CancellationToken cancellationToken)
            {
                var httpRequest = request.HttpRequest;
                var bodyInString = "";
                BayutNewWhatsappDto? payload = null;
                if (request.HttpRequest.HasFormContentType)
                {
                    var form = await httpRequest.ReadFormAsync();
                    var formData = form.ToDictionary(x => x.Key, x => x.Value.ToString());
                    bodyInString = JsonConvert.SerializeObject(formData);
                    if (string.IsNullOrWhiteSpace(bodyInString))
                    {
                        throw new ArgumentNullException("Payload Cannot be empty");
                    }
                    payload = JsonConvert.DeserializeObject<BayutNewWhatsappDto>(bodyInString);
                }
                else if (httpRequest.QueryString.HasValue)
                {
                    var queryParamsData = httpRequest.Query.ToDictionary(x => x.Key, x => x.Value.ToString());
                    bodyInString = JsonConvert.SerializeObject(queryParamsData);
                    if (string.IsNullOrWhiteSpace(bodyInString))
                    {
                        throw new ArgumentNullException("Payload Cannot be empty");
                    }
                    payload = JsonConvert.DeserializeObject<BayutNewWhatsappDto>(bodyInString);
                }
                else
                {
                    Stream stream = httpRequest.Body;
                    HttpRequestStreamReader reader = new(stream, System.Text.Encoding.UTF8);
                    bodyInString = await reader.ReadToEndAsync();
                    if (string.IsNullOrWhiteSpace(bodyInString))
                    {
                        throw new ArgumentNullException("Payload Cannot be empty");
                    }
                    payload = JsonConvert.DeserializeObject<BayutNewWhatsappDto>(bodyInString);
                }
                if (payload != null)
                {
                    _logger.Information("BayutWAPushIntegrationRequestHandler -> POST(Bayut) -> called, Dto: " + payload);
                    CreateLeadGenRequest leadGenRequest = new(LeadSource.Bayut, payload);

                    await _mediator.Send(leadGenRequest);

                    var lead = new WhatsAppListingSitesIntegrationRequest()
                    {
                        LeadSource = Domain.Enums.LeadSource.Bayut,
                        ApiKey = request.ApiKey,
                        Name = payload?.enquirer?.name ?? "Unknown",
                        Mobile = payload?.enquirer?.phone_number ?? string.Empty,
                        Notes = payload?.messaage ?? string.Empty,
                        Link = payload?.enquirer?.contact_link ?? string.Empty,
                        ReferenceId = payload?.story?.listing_reference ?? string.Empty,
                        PrimaryUser = payload?.agent?.name ?? string.Empty,
                        AdditionalProperties = new Dictionary<string, string>() { { "EnquiredFor", (payload?.story?.purpose ?? string.Empty) } },
                        Location = payload?.story?.loc3_name ?? string.Empty,
                        Property = payload?.story?.listing_title ?? string.Empty,
                        Project = payload?.story?.project_title ?? string.Empty,
                    };
                    await _mediator.Send(lead);
                }
                return new(true);
            }
        }
    }
}
