<Project Sdk="Microsoft.NET.Sdk">

	<PropertyGroup>
		<OutputType>Exe</OutputType>
		<TargetFramework>net6.0</TargetFramework>
		<ImplicitUsings>enable</ImplicitUsings>
		<Nullable>enable</Nullable>
	</PropertyGroup>

	<ItemGroup>
		<PackageReference Include="Dapper" Version="2.0.123" />
		<PackageReference Include="Google.Ads.GoogleAds" Version="23.0.0" />
		<PackageReference Include="ISO3166" Version="1.0.4" />
		<PackageReference Include="Mapster" Version="7.3.0" />
		<PackageReference Include="Nager.Country" Version="4.0.0" />
		<PackageReference Include="Npgsql" Version="6.0.0" />
	</ItemGroup>

	<ItemGroup>
		<ProjectReference Include="..\Lrb.Domain\Lrb.Domain.csproj" />
	</ItemGroup>

</Project>
