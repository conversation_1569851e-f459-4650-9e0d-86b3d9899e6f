﻿using Lrb.Application.CustomStatus.Web;
using Lrb.Application.Integration.Mobile;
using Lrb.Domain.Entities.MasterData;

namespace Lrb.Application.CustomStatus.Mobile.Request
{
    public class GetAllStatusAnonymousRequest : IRequest<Response<List<StatusNamesDto>>>
    {
        public string? ApiKey { get; set; }
    }
    public class GetAllStatusAnonymousRequesthandler : IRequestHandler<GetAllStatusAnonymousRequest, Response<List<StatusNamesDto>>>
    {
        private readonly IReadRepository<CustomMasterLeadStatus> _customRepo;
        private readonly IRepositoryWithEvents<IntegrationAccountInfo> _integrationAccRepo;
        public GetAllStatusAnonymousRequesthandler(IReadRepository<CustomMasterLeadStatus> customRepo, IRepositoryWithEvents<IntegrationAccountInfo> integrationAccRepo)
        {
            _customRepo = customRepo;
            _integrationAccRepo = integrationAccRepo;
        }

        public async Task<Response<List<StatusNamesDto>>> Handle(GetAllStatusAnonymousRequest request, CancellationToken cancellationToken)
        {
            var accountId = AccountIdHelper.GetAccountId(request.ApiKey ?? string.Empty);
            _ = await _integrationAccRepo.GetByIdAsync(accountId) ?? throw new NotFoundException("The API Key may be expired or invalid.");
            List<CustomMasterLeadStatus>? statusDtos = null;
            int count = 0;
            try
            {
                statusDtos = await _customRepo.ListAsync(new GetAllStatusAnonymous(), cancellationToken);
                var result = StatusHelper.GetCustomStatus(statusDtos.Adapt<List<StatusNamesDto>>());
                return new(result);
            }
            catch (Exception ex)
            {
                return new(ex.Message);
            }

        }
    }
}
