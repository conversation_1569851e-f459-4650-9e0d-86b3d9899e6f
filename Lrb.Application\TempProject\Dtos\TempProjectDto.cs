﻿using Lrb.Application.Automation.Dtos;

namespace Lrb.Application.TempProject.Dtos
{
    public class TempProjectDto : IDto
    {
        public Guid Id { get; set; }
        public string? Name { get; set; }
        public Guid AutomationId { get; set; }
        public bool IsAutomated { get; set; }
        public List<Guid>? AssignedUserIds { get; set; }
        public Guid CreatedBy { get; set; }
        public DateTime CreatedOn { get; set; }
        public Guid LastModifiedBy { get; set; }
        public DateTime? LastModifiedOn { get; set; }
        public DateTime? DeletedOn { get; set; }
        public Guid? DeletedBy { get; set; }
        public List<string>? Properties { get; set; }
    }
    public class TempProjectWithUserAssignmentDto : IDto
    {
        public Guid Id { get; set; }
        public string? Name { get; set; }
        public ViewUserAssignmentDto? UserAssignment { get; set; }
        public bool? IsArchived { get; set; }
        public bool? IsDeleted { get; set; }
    }
    public class ProjectLeadCountDto : IDto
    {
        public Guid ProjectId { get; set; }
        public long LeadCount { get; set; }
        public long? ProspectCount { get; set; }
        public long? MeetingDoneCount { get; set; }
        public long? MeetingDoneUniqueCount { get; set; }
        public long? SiteVisitDoneCount { get; set; }
        public long? SiteVisitDoneUniqueCount { get; set; }
    }
    public class PropertyAssignmentDto : IDto
    {
        public Guid Id { get; set; }
        public string? Title { get; set; }
        public bool? IsArchived { get; set; }
        public bool? IsDeleted { get; set; }
    }
    public class ChannelPartnerAssignmentDto : IDto
    {
        public Guid Id { get; set; }
        public string? FirmName { get; set; }
        public bool? IsDeleted { get; set; }
    }
    public class CampaignAssignmentDto : IDto
    {
        public Guid Id { get; set; }
        public string? Name { get; set; }
        public bool? IsDeleted { get; set; }
    }
    public class AgencyAssignmentDto : IDto
    {
        public Guid Id { get; set; }
        public string? Name { get; set; }
        public bool? IsDeleted { get; set; }
    }
}
