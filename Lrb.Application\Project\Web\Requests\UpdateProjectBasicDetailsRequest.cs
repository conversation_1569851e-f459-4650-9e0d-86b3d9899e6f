﻿using Lrb.Application.Project.Web.Requests.CommonHandler;
using Lrb.Application.Project.Web.Specs;
using Lrb.Domain.Entities.MasterData;

namespace Lrb.Application.Project.Web.Requests
{
    public class UpdateProjectBasicDetailsRequest : UpdateProjectDto, IRequest<Response<Guid>>
    {
    }
    public class UpdateProjectBasicDetailsRequestHandler : ProjectCommonRequestHandler, IRequestHandler<UpdateProjectBasicDetailsRequest, Response<Guid>>
    {
        private readonly IRepositoryWithEvents<MasterProjectType> _masterprojectRepo;
        private readonly IRepositoryWithEvents<CustomMasterProjectType> _customProjectTypeRepo;
        private readonly IRepositoryWithEvents<ProjectBuilderDetails> _buidlerDetailsRepo;
        private readonly IRepositoryWithEvents<ProjectMonetaryInfo> _monetaryInfoRepo;
        private readonly IRepositoryWithEvents<AssociatedBank> _associatesBankRepo;
        private readonly IRepositoryWithEvents<MasterAssociatedBank> _masterAssociatesBankRepo;
        public UpdateProjectBasicDetailsRequestHandler(
            IRepositoryWithEvents<MasterProjectType> masterprojectRepo,
            IServiceProvider serviceProvider,
            IRepositoryWithEvents<CustomMasterProjectType> customProjectTypeRepo,
            IRepositoryWithEvents<ProjectBuilderDetails> buidlerDetailsRepo,
            IRepositoryWithEvents<ProjectMonetaryInfo> monetaryInfoRepo,
            IRepositoryWithEvents<AssociatedBank> associatesBankRepo,
            IRepositoryWithEvents<MasterAssociatedBank> masterAssociatesBankRepo

            ) : base(serviceProvider)
        {
            _masterprojectRepo = masterprojectRepo;
            _customProjectTypeRepo = customProjectTypeRepo;
            _buidlerDetailsRepo = buidlerDetailsRepo;
            _monetaryInfoRepo = monetaryInfoRepo;
            _associatesBankRepo = associatesBankRepo;
            _masterAssociatesBankRepo = masterAssociatesBankRepo;
        }
        public async Task<Response<Guid>> Handle(UpdateProjectBasicDetailsRequest request, CancellationToken cancellationToken)
        {
            var project = (await _projectRepo.ListAsync(new GetProjectByIdToUpdateSpecs(request.Id), cancellationToken)).FirstOrDefault();
            if (project == null)
            {
                throw new NotFoundException("Project does not exist!");
            }

            var newAddress = await CreateAddressAsync(request.Address, cancellationToken);
            request.Address = null;
            project = request.Adapt(project);
            if (request.ProjectTypeId != Guid.Empty && request.ProjectTypeId != default)
            {
                var masterPropertyType = await _masterprojectRepo.GetByIdAsync(request.ProjectTypeId, cancellationToken);
                project.ProjectType = masterPropertyType;
            }
            
            project.Address = newAddress;
            project.ReraNumber = request.ReraNumbers;
         
            #region Buider Deatils

            if (request.BuilderDetail != null)
            {
                var existingBuilder = (await _buidlerDetailsRepo.ListAsync(new GetProjectBuilderDetailsByProjectIdSpecs(request.Id), cancellationToken)).FirstOrDefault();
                if (existingBuilder != null)
                {
                    var projectBuilder = request.BuilderDetail.Adapt(existingBuilder);
                    project.BuilderDetail = projectBuilder;
                }
                else
                {
                    ProjectBuilderDetails builderDetails = request.BuilderDetail?.Adapt<ProjectBuilderDetails>() ?? new();
                    builderDetails.ProjectId = project.Id;
                    await _buidlerDetailsRepo.AddAsync(builderDetails);
                }
            }

            #endregion

            #region MonetaryInfo

            if (request.MonetaryInfo != null)
            {
                var existingMonetaryInfo = (await _monetaryInfoRepo.ListAsync(new GetProjectMonetaryInfosByProjectIdSpecs(request.Id), cancellationToken)).FirstOrDefault();
                if (existingMonetaryInfo != null)
                {
                    existingMonetaryInfo.Brokerage = request.MonetaryInfo.Brokerage;
                    existingMonetaryInfo.Brokerage = request.MonetaryInfo.Brokerage;
                    existingMonetaryInfo.BrokerageUnit = request.MonetaryInfo.BrokerageUnit;
                    existingMonetaryInfo.Currency = request.MonetaryInfo.Currency;
                    existingMonetaryInfo.BrokerageCurrency = request.MonetaryInfo.BrokerageCurrency;
                    project.MonetaryInfo = existingMonetaryInfo;
                }
                else
                {
                    ProjectMonetaryInfo projectMonetaryInfo = request.MonetaryInfo?.Adapt<ProjectMonetaryInfo>() ?? new();
                    projectMonetaryInfo.ProjectId = project.Id;
                    projectMonetaryInfo = await _monetaryInfoRepo.AddAsync(projectMonetaryInfo);
                }
            }

            #endregion

            await _projectRepo.UpdateAsync(project, cancellationToken);

            #region Associates Bank
            var bankIds = await _associatesBankRepo.ListAsync(new GetProjectAssociatedBankByProjectIdSpecs(project.Id), cancellationToken);
            await _associatesBankRepo.DeleteRangeAsync(bankIds, cancellationToken);
            if (request?.AssociateBank?.Any() ?? false)
            {
                foreach (var amenityId in request.AssociateBank)
                {
                    var fetchedPropertyAmenity = await _masterAssociatesBankRepo.GetByIdAsync(amenityId);
                    if (fetchedPropertyAmenity == null)
                    {
                        throw new Exception($"{amenityId} Id does not belong to master project Amenity data.");
                    }
                }
                foreach (var amenityId in request.AssociateBank)
                {
                    var projectAmenity = new AssociatedBank();
                    projectAmenity.MasterAssociatedBankId = amenityId;
                    projectAmenity.ProjectId = project.Id;
                    await _associatesBankRepo.AddAsync(projectAmenity);
                }
            }
            #endregion

            return new(project.Id);
        }
    }
}
