﻿using Lrb.Domain.Entities;

namespace Lrb.Application.Integration.Web
{
    public class IntegrationLeadInfoByContactsSpec : Specification<IntegrationLeadInfo>
    {
        public IntegrationLeadInfoByContactsSpec(List<string> contacts)
        {
            Query.Where(i => !i.IsDeleted && contacts.Contains(i.Mobile));
        }
    }
    public class IntegrationAccByGmailIdOrIdSpec : Specification<IntegrationAccountInfo>
    {
        public IntegrationAccByGmailIdOrIdSpec(Guid gmailAccId)
        {
            Query
                .Include(i => i.UserAssignment)
                .Include(i => i.Assignment)
                    .ThenInclude(i => i.Location)
                        .ThenInclude(i => i.UserAssignment)
                .Include(i => i.Assignment)
                    .ThenInclude(i => i.Project)
                        .ThenInclude(i => i.UserAssignment)
                .Include(i => i.Agency)
                .Include(i => i.Assignment)
                        .ThenInclude(i => i.Agency)
                .Include(i => i.Assignment)
                        .ThenInclude(i => i.ChannelPartner)
                .Include(i => i.Assignment)
                        .ThenInclude(i => i.Property)
                .Include(i => i.Assignment)
                        .ThenInclude(i => i.Campaign)
                .Where(i => !i.IsDeleted && (i.GmailAccountId == gmailAccId || i.Id == gmailAccId));
        }
        public IntegrationAccByGmailIdOrIdSpec(List<Guid> gmailAccIds)
        {
            Query
                .Include(i => i.UserAssignment)
                .Include(i => i.Assignment)
                    .ThenInclude(i => i.Location)
                .Include(i => i.Assignment)
                    .ThenInclude(i => i.Project)
                .Where(i => !i.IsDeleted && (gmailAccIds.Contains(i.GmailAccountId) || gmailAccIds.Contains(i.Id)));
        }
    }
}
