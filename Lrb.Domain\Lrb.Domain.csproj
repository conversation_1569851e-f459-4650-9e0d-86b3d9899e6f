﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net6.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="AWSSDK.DynamoDBv2" Version="3.7.101.54" />
    <PackageReference Include="Google.Ads.GoogleAds" Version="23.0.0" />
    <PackageReference Include="Google.Apis.Auth" Version="1.70.0" />
    <PackageReference Include="ISO3166" Version="1.0.4" />
    <PackageReference Include="Nager.Country" Version="4.0.0" />
    <PackageReference Include="NewId" Version="3.0.3" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="TimeZoneConverter" Version="6.1.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Lrb.Shared\Lrb.Shared.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Folder Include="Common\Dtos\" />
  </ItemGroup>

</Project>
