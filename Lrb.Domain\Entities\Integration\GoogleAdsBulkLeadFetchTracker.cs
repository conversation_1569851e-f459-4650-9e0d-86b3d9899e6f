﻿namespace Lrb.Domain.Entities.Integration
{
    public class GoogleAdsBulkLeadFetchTracker : AuditableEntity, IAggregateRoot
    {
        public DateTime? FromDate { get; set; }
        public DateTime? ToDate { get; set; }
        public int ActiveAdsCount { get; set; }
        public int ActiveFormsCount { get; set; }
        public int FetchedLeadsCount { get; set; }
        public int UniqueLeadsCount { get; set; }
        public int StoredLeadsCount { get; set; }
        public UploadStatus Status { get; set; }
        public string? Error { get; set; }
    }
}
