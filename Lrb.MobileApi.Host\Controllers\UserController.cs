﻿using Lrb.Application.Lead.Mobile.Dtos.v1;
using Lrb.Application.UserDetails.Mobile;
using Lrb.Application.UserDetails.Mobile.Request;
using Lrb.Application.UserDetails.Mobile.Dtos;
using Lrb.Domain.Enums;
using static Lrb.Application.UserDetails.Mobile.Request.GetReporteesRequestHandler;
using Microsoft.Extensions.Primitives;

namespace Lrb.MobileApi.Host.Controllers
{
    [Authorize]
    public class UserController : VersionedApiController
    {
        [HttpPost]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.Create, LrbResource.Users)]
        [OpenApiOperation("Create a new User.", "")]
        public Task<Response<Guid>> CreateAsync(CreateUserDetailsRequest request)
        {
            return Mediator.Send(request);
        }
        [HttpPost("bulk")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.Create, LrbResource.Users)]
        [OpenApiOperation("Create a new bulk Users.", "")]
        public Task<Response<bool>> CreateAsync(IFormFile file)
        {
            return Mediator.Send(new CreateBulkUsersRequest(file, GetOriginFromRequest()));
        }

        //[HttpGet("getAllUsers")]
        //[TenantIdHeader]
        //[MustHavePermission(LrbAction.View, LrbResource.Users)]
        //[OpenApiOperation("Get all user details.", "")]
        //public Task<PagedResponse<UserDetailsDto, string>> SearchAsync([FromQuery] GetAllUserDetailsRequest request)
        //{
        //    return Mediator.Send(request);
        //}

        [HttpGet("getAllUsers")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Users)]
        [OpenApiOperation("Get all user details.", "")]
        public Task<PagedResponse<UserDetailsDto, string>> SearchAsync([FromQuery] GetAllUserInfoViewRequest request)
        {
            return Mediator.Send(request);
        }

        [HttpGet]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.ViewForFilter, LrbResource.Users)]
        [OpenApiOperation("Get all users.", "")]
        public Task<PagedResponse<ViewUserDto, string>> SearchAsync([FromQuery] GetAllUsersRequest request)
        {
            return Mediator.Send(request);
        }

        [HttpGet("allUsersToAssign")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.AssignToAny, LrbResource.Users)]
        [OpenApiOperation("Get All users To Assign.", "")]
        public Task<PagedResponse<ReportUserDto, string>> GetAllUsersAsync([FromQuery] GetAllUsersToAssignRequest request)
        {
            return Mediator.Send(request);
        }

        [HttpGet("getUsersByRoleId")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Users)]
        [OpenApiOperation("Get users by role id.", "")]
        public Task<PagedResponse<ViewUserDto, string>> GetUsersByRoleIdAsync([FromQuery] GetUsersByRoleIdRequest request)
        {
            return Mediator.Send(request);
        }


        [HttpPut("{id:guid}")]
        [MustHavePermission(LrbAction.Update, LrbResource.Users)]
        [TenantIdHeader]
        [OpenApiOperation("Update a user.", "")]
        public async Task<ActionResult<Response<Guid>>> UpdateAsync(UpdateUserDetailsRequest request, Guid id)
        {
            return id != request.UserId
                ? BadRequest()
                : Ok(await Mediator.Send(request));
        }
        [HttpDelete("{userId:guid}")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.Delete, LrbResource.Users)]
        [OpenApiOperation("Delete a user.", "")]
        public Task<Response<bool>> DeleteAsync(Guid userId)
        {
            return Mediator.Send(new DeleteUserRequest(userId));
        }
        [HttpPost("department")]
        [TenantIdHeader]
        [OpenApiOperation("Create a new department.", "")]
        public Task<Response<Guid>> CreateAsync(CreateDepartmantRequest request)
        {
            return Mediator.Send(request);
        }
        [HttpGet("getAllDepartments")]
        [TenantIdHeader]
        [OpenApiOperation("Get all department details.", "")]
        public Task<PagedResponse<DepartmantDto, string>> SearchAsync([FromQuery] GetAllDepartmantRequset request)
        {
            return Mediator.Send(request);
        }

        [HttpDelete("department/{id:guid}")]
        [TenantIdHeader]
        [OpenApiOperation("Delete a department.", "")]
        public Task<Response<bool>> DeleteDepartmentAsync(Guid id)
        {
            return Mediator.Send(new DeleteDepartmentRequest(id));
        }

        [HttpPost("designation")]
        [TenantIdHeader]
        [OpenApiOperation("Create a new designation.", "")]
        public Task<Response<Guid>> CreateAsync(CreateDesignationRequest request)
        {
            return Mediator.Send(request);
        }

        [HttpGet("getAllDesignations")]
        [TenantIdHeader]
        [OpenApiOperation("Get all designation details.", "")]
        public Task<PagedResponse<DesignationDto, string>> SearchAsync([FromQuery] GetAllDesignationRequset request)
        {
            return Mediator.Send(request);
        }

        [HttpDelete("designation/{id:guid}")]
        [TenantIdHeader]
        [OpenApiOperation("Delete a designation.", "")]
        public Task<Response<bool>> DeleteDesignationAsync(Guid id)
        {
            return Mediator.Send(new DeleteDesignationRequest(id));
        }

        //[AllowAnonymous]
        //[HttpGet("{id:guid}")]
        //[TenantIdHeader]
        //[MustHavePermission(LrbAction.View, LrbResource.Users)]
        //[OpenApiOperation("Get user details.", "")]
        //public async Task<Response<UserDetailsDto>> GetAsync(Guid id)
        //{
        //    return await Mediator.Send(new GetUserDetailsByIdRequest(id));
        //}

        [AllowAnonymous]
        [HttpGet("{id:guid}")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Users)]
        [OpenApiOperation("Get user details.", "")]
        public async Task<Response<UserDetailsDto>> GetAsync(Guid id)
        {
            _ = this.HttpContext.Request.Headers.TryGetValue("tenant", out StringValues tenantId);
            return await Mediator.Send(new GetFullUserViewByIdRequest(id,tenantId));
        }

        [HttpDelete("document/{id:guid}")]
        [MustHavePermission(LrbAction.Update, LrbResource.Users)]
        [TenantIdHeader]
        [OpenApiOperation("Delete a document.", "")]
        public Task<Response<bool>> DeleteDocumentAsync(Guid id)
        {
            return Mediator.Send(new DeleteDocumentRequest(id));
        }

        [HttpPut("document/{userId:guid}")]
        [MustHavePermission(LrbAction.Update, LrbResource.Users)]
        [TenantIdHeader]
        [OpenApiOperation("Update a document.", "")]
        public async Task<ActionResult<Response<Guid>>> UpdateAsync(UpdateDocumentRequest request, Guid userId)
        {
            return userId != request.UserId
                ? BadRequest()
                : Ok(await Mediator.Send(request));
        }
        [HttpGet("document/{userId:guid}")]
        [TenantIdHeader]
        [OpenApiOperation("Get All a documents.", "")]
        public async Task<ActionResult<PagedResponse<UserDocumentDto, string>>> GetDocumentsAsync(Guid userId)
        {
            return Ok(await Mediator.Send(new GetAllDocumentsByUserIdRequest(userId)));
        }
        [HttpGet("getAllReportees")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.ViewForFilter, LrbResource.Users)]
        [OpenApiOperation("Get All Reportees.", "")]
        public async Task<PagedResponse<ReportUserDto, string>> GetReportees([FromQuery] GetReporteesRequest request)
        {
            return await Mediator.Send(request);
        }
        [HttpPut("image")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.Update, LrbResource.Users)]
        [OpenApiOperation("Update user image.", "")]
        public Task<Response<Guid>> UpdateAsync([FromBody] string? imgUrl)
        {
            return Mediator.Send(new UpdateUserImgRequest(imgUrl));
        }
        [HttpPost("profileCompletion")]
        [TenantIdHeader]
        [OpenApiOperation("Get user profile completion.", "")]
        public async Task<Response<int>> GetAsync(GetProfileCompletionRequest request)
        {
            return await Mediator.Send(request);
        }
        [HttpGet("adminsandreportees")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.ViewForFilter, LrbResource.Users)]
        [OpenApiOperation("Get All admins and reportees.", "")]
        public async Task<PagedResponse<Application.UserDetails.Web.ReportUserDto, string>> GetAdminsAndReporteesAsync()
        {
            return await Mediator.Send(new GetReporteesAndAdminsRequest());
        }

        [HttpGet("admin-mfa")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Users)]
        [OpenApiOperation("Get All admins and mfa enabled users.", "")]
        public async Task<Response<MFAEnabledUserDto>> GetAdminsAndMFAEnabledUsersAsync()
        {
            return await Mediator.Send(new GetAdminsAndMFAEnabledUsersRequest());
        }

        [HttpGet("settings/call-through")]
        [TenantIdHeader]
        [OpenApiOperation("Get call through type by user id.", "")]
        public async Task<Response<CallThrough>> GetAsync([FromQuery] GetCallThroughTypeRequest request)
        {
            return await Mediator.Send(request);
        }
        [HttpGet("without/admin")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Users)]
        [OpenApiOperation("Get all users without admin role .", "")]
        public Task<PagedResponse<ViewUserDto, int>> SearchUsersAsync([FromQuery] Application.UserDetails.Mobile.Request.GetAllUsersWithoutAdminRoleRequest request)
        {
            return Mediator.Send(request);
        }
        [HttpGet("get/all/Users/<USER>")]
        [TenantIdHeader]
        [OpenApiOperation("Get All Users Based on these designations.", "")]
        public async Task<Response<Dictionary<string, List<UserViewDto>>>> GetAllUsers()
        {
            return await Mediator.Send(new GetAllUsersRequestinDashBoard());

        }
        [HttpGet("generalManagers")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.ViewForFilter, LrbResource.Users)]
        [OpenApiOperation("Get All GeneralManagers.", "")]
        public async Task<PagedResponse<GeneralManagerUserDto, string>> GetGeneralManagers()
        {
            return await Mediator.Send(new GetGenaralManagersRequest());
        }
        [HttpPut]
        [TenantIdHeader]
        [OpenApiOperation("Create or  update search results.", "")]
        public Task<Response<bool>> UpdateSearchResultsAsync(UpdateUserSearchResultsRequest request)
        {
            return Mediator.Send(request);
        }
        [HttpGet("searchresults")]
        [TenantIdHeader]
        [OpenApiOperation("Get all User searchresults.", "")]
        public Task<Response<List<PropertyListsDto>>> GetSearchResultsAsync([FromQuery] GetUserSearchResultsRequest request)
        {
            return Mediator.Send(request);
        }
        private string GetOriginFromRequest() => $"{Request.Scheme}://{Request.Host.Value}{Request.PathBase.Value}";

        [HttpGet("geofence-details/{userId:guid}")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.SetGeoFence, LrbResource.Users)]
        [OpenApiOperation("Get user details for clock-in including geo-fencing settings", "")]
        public async Task<Response<UserDetailsClockInDto>> GetUserDetailsForClockIn(Guid userId)
        {
            var request = new GetUserDetailsClockInRequest { UserId = userId };
            return await Mediator.Send(request);
        }
        [HttpPost("geofence-settings")]
        [HttpPut("geofence-settings")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.SetGeoFence, LrbResource.Users)]
        [OpenApiOperation("Configure geo-fencing settings for a user", "")]
        public async Task<Response<bool>> CreateGeofenceSettings([FromBody] GeofenceSettingsRequest request)
        {
            return await Mediator.Send(request);
        }
        [HttpPut("geofence-active-status")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.SetGeoFence, LrbResource.Users)]
        [OpenApiOperation("Update geo-fencing active status for a user", "")]
        public async Task<Response<bool>> UpdateGeofenceActiveStatus([FromBody] UpdateGeofenceActiveStatusRequest request)
        {
            return await Mediator.Send(request);
        }

        [HttpGet("getSubsriptionDetails")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Users)]
        [OpenApiOperation("Get subscription details.", "")]
        public async Task<Response<SubscriptionDetails>> GetSubscriptionDetailsAsync(Guid id)
        {
            _ = this.HttpContext.Request.Headers.TryGetValue("tenant", out StringValues tenantId);
            return await Mediator.Send(new GetSubscriptionDetailsRequest(id, tenantId));
        }
    }
}
