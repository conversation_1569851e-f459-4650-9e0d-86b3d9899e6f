﻿using Lrb.Application.Utils;
using Lrb.Shared.Extensions;

namespace Lrb.Application.Reports.Web
{
    public class V2GetCallLogReportByUserRequest : IRequest<PagedResponse<ViewCallLogReportDto, string>>
    {
        public DateTime? FromDate { get; set; }
        public DateTime? ToDate { get; set; }
        public DateType? DateType { get; set; }
        public bool IsWithTeam { get; set; }
        public List<Guid>? UserIds { get; set; }
        public string? SearchText { get; set; }
        public List<LeadSource>? Sources { get; set; }
        public List<string>? SubSources { get; set; }
        public List<string>? Projects { get; set; }
        public UserStatus? UserStatus { get; set; }
        public List<string>? Localites { get; set; }
        public List<string>? States { get; set; }
        public List<string>? Cities { get; set; }
        public ReportPermission? ReportPermission { get; set; }
        public List<string>? AgencyNames { get; set; }
        public bool? IsWithCallLogsOnly { get; set; }
        public DateTime? CallLogFromDate { get; set; }
        public DateTime? CallLogToDate { get; set; }
        public int PageNumber { get; set; } = 1;
        public int PageSize { get; set; } = int.MaxValue;
        public List<string>? Countries { get; set; }
    }
    public class V2GetCallLogReportByUserRequestHandler : IRequestHandler<V2GetCallLogReportByUserRequest, PagedResponse<ViewCallLogReportDto, string>>
    {
        private readonly IDapperRepository _dapperRepository;
        private readonly ICurrentUser _currentUser;
        public V2GetCallLogReportByUserRequestHandler(IDapperRepository dapperRepository, ICurrentUser currentUser)
        {
            _dapperRepository = dapperRepository;
            _currentUser = currentUser;
        }
        public async Task<PagedResponse<ViewCallLogReportDto, string>> Handle(V2GetCallLogReportByUserRequest request, CancellationToken cancellationToken)
        {
            var tenantId = _currentUser.GetTenant();
            var userId = _currentUser.GetUserId();
            List<Guid> teamUserIds = new();
            List<Guid> permittedUserIds = new();
            var isAdmin = await _dapperRepository.IsAdminAsync(userId, tenantId ?? string.Empty);
            if (isAdmin)
            {
                permittedUserIds = (await _dapperRepository.GetAllUserIdsAsync(tenantId ?? string.Empty)).ToList();
            }
            else if (request.ReportPermission != null)
            {
                switch (request.ReportPermission)
                {
                    case ReportPermission.All:
                        permittedUserIds = (await _dapperRepository.GetAllUserIdsAsync(tenantId ?? string.Empty)).ToList();
                        break;
                    case ReportPermission.Reportees:
                        permittedUserIds = (await _dapperRepository.GetSubordinateIdsWithOnlyReporteesAsync(new List<Guid>() { userId }, tenantId ?? string.Empty)).ToList();
                        break;
                }
            }
            if (request?.UserIds?.Any() ?? false)
            {
                if (request?.IsWithTeam ?? false)
                {
                    teamUserIds = (await _dapperRepository.GetSubordinateIdsWithOnlyReporteesAsync(request.UserIds, tenantId ?? string.Empty)).ToList();
                }
                else
                {
                    teamUserIds = request?.UserIds ?? new List<Guid>();
                }
            }
            else
            {
                if (!isAdmin)
                {
                    teamUserIds = permittedUserIds;
                }

            }
            if (teamUserIds.Any())
            {
                teamUserIds = teamUserIds.Where(userId => permittedUserIds.Contains(userId)).ToList();
            }
            else
            {
                teamUserIds = permittedUserIds;
            }
            request.FromDate = request.FromDate.HasValue ? request.FromDate.Value.ConvertFromDateToUtc() : null;
            request.ToDate = request.ToDate.HasValue ? request.ToDate.Value.ConvertToDateToUtc() : null;
            request.CallLogFromDate = request.CallLogFromDate.HasValue ? request.CallLogFromDate.Value.ConvertFromDateToUtc() : null;
            request.CallLogToDate = request.CallLogToDate.HasValue ? request?.CallLogToDate.Value.ConvertToDateToUtc() : null;
            var callLogReportDtos = (await _dapperRepository.QueryStoredProcedureFromReadReplicaAsync<CallLogReportDto>("LeadratBlack", "TempCallLogReport", new
            {
                fromdate = request?.FromDate,
                todate = request?.ToDate,
                datetype = request?.DateType,
                tenantid = tenantId,
                userids = teamUserIds,
                searchtext = string.IsNullOrWhiteSpace(request?.SearchText) ? null : request.SearchText.Replace(" ", "").ToLower(),
                sources = request?.Sources?.ConvertAll(i => (int)i),
                projects = request?.Projects?.ConvertAll<string>(i => i.Replace(" ", "").ToLower()),
                userstatus = request?.UserStatus ?? 0,
                subsources = request?.SubSources?.ConvertAll<string>(i => i.Replace(" ", "").ToLower()),
                agencies = request?.AgencyNames?.ConvertAll<string>(i => i.Replace(" ", "").ToLower()),
                pagesize = request?.PageSize,
                pagenumber = request?.PageNumber,
                withcalllogs = request?.IsWithCallLogsOnly ?? false,
                calllogfromdate = request?.CallLogFromDate,
                calllogtodate = request?.CallLogToDate
            }, 300)).ToList();
            var viewCallLogReportDtos = callLogReportDtos.Adapt<List<ViewCallLogReportDto>>();
            return new(viewCallLogReportDtos, 0);
        }
    }
}
