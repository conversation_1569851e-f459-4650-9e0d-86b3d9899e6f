﻿using Lrb.Application.Agency.Web;
using Lrb.Application.Lead.Web.Dtos;
using Lrb.Application.LeadCallLog.Web;
using Lrb.Application.Property.Web;
using Lrb.Application.WhatsAppCloudApi.Web;
using Lrb.Domain.Entities.MasterData;
using System.ComponentModel.DataAnnotations.Schema;

namespace Lrb.Application.Lead.Web
{
    public class PickedLeadDto : IDto
    {
        public Guid? Id { get; set; }
        public string? Name { get; set; }
        public string? ContactNo { get; set; }
        public string? AlternateContactNo { get; set; }
        public string? LandLine { get; set; }
        public string? Email { get; set; }
        public string? Notes { get; set; }
        public string? ConfidentialNotes { get; set; }
        public DateTime? ScheduledDate { get; set; }
        public DateTime? RevertDate { get; set; }
        public string? ChosenProject { get; set; }
        public string? ChosenProperty { get; set; }
        public string? BookedUnderName { get; set; }
        public string? LeadNumber { get; set; }
        public Guid? AssignTo { get; set; }
        public int ShareCount { get; set; }
        public string? SoldPrice { get; set; }
        public string? Rating { get; set; }
        public Dictionary<DateTime, string>? CallRecordingUrls { get; set; }
        public List<LeadDocument>? Documents { get; set; }
        //table references
        public LeadTagDto? TagInfo { get; set; }
        public LeadTagDto? LeadTags { get; set; } // From UpdateLeadDto
        public MasterLeadStatus? Status { get; set; }
        public Guid AccountId { get; set; }
        public IList<CreateLeadEnquiryDto> Enquiries { get; set; }
        public IList<ProjectsDtoV2>? Projects { get; set; }
        public IList<ViewPropertyDto>? Properties { get; set; }
        public DateTime? PostponedDate { get; set; }
        public long? UnmatchedBudget { get; set; }
        public string? PurchasedFrom { get; set; }
        public string? PreferredLocation { get; set; }
        public Guid? AssignedFrom { get; set; }
        [Column(TypeName = "jsonb")]
        public Dictionary<ContactType, int>? ContactRecords { get; set; }
        public IList<LeadCommunicationDto>? Communications { get; set; }
        public bool IsArchived { get; set; }
        public Guid? ArchivedBy { get; set; }
        public DateTime? ArchivedOn { get; set; }
        public Guid? RestoredBy { get; set; }
        public DateTime? RestoredOn { get; set; }
        public bool IsMeetingDone { get; set; }
        public Guid? MeetingLocation { get; set; }
        public bool IsSiteVisitDone { get; set; }
        public Guid? SiteLocation { get; set; }
        public IList<LeadCallLogDto>? LeadCallLogs { get; set; }
        public string? ReferralName { get; set; }
        public string? ReferralContactNo { get; set; }
        public string? ReferralEmail { get; set; }
        public string? AgencyName { get; set; }
        public string? CompanyName { get; set; }
        public IList<LeadAppointmentDto>? Appointments { get; set; }
        public Guid? ClosingManager { get; set; }
        public Guid? SourcingManager { get; set; }
        public Profession Profession { get; set; }
        public string? ChannelPartnerName { get; set; }
        public string? ChannelPartnerExecutiveName { get; set; }
        public string? ChannelPartnerContactNo { get; set; }
        public IList<ChannelPartnerDto>? ChannelPartners { get; set; }
        public CustomMasterLeadStatus? CustomLeadStatus { get; set; }
        public List<string>? ChannelPartnerList { get; set; }


        public string? SerialNumber { get; set; }
        public DateTime? PickedDate { get; set; }
        public string? DuplicateLeadVersion { get; set; }
        public int ChildLeadsCount { get; set; }
        public Guid? ParentLeadId { get; set; }
        public bool IsIntegrationLead { get; set; }
        [NotMapped]
        public bool ShouldUpdatePickedDate { get; set; }

        public Guid? RootId { get; set; }

        public Guid? UserId { get; set; }
        public Guid? CreatedBy { get; set; }
        public DateTime CreatedOn { get; set; }
        public Guid? LastModifiedBy { get; set; }
        public DateTime? LastModifiedOn { get; set; }
        public DateTime? DeletedOn { get; set; }
        public Guid? DeletedBy { get; set; }

        //Update Lead Request
        public Guid? LeadStatusId { get; set; }
        public Guid? CustomLeadStatusId { get; set; }
        public CreateLeadEnquiryDto? Enquiry { get; set; }
        public List<string>? ProjectsList { get; set; }
        public List<string>? PropertiesList { get; set; }
        public string? Designation { get; set; }

        //Update Lead Status
        public AddressDto? Address { get; set; }

        public List<string>? ProjectNames { get; set; }
        public ContactType? ContactType { get; set; }
        public string? Message { get; set; }
        public IList<CampaignsDtoV1>? Campaigns { get; set; }
        public IList<AgencyDto>? Agency { get; set; }


    }

    public class TempProjectsDto
    {
        public Guid? Id { get; set; }
        public string? Name { get; set; }
        public Guid AutomationId { get; set; }
        public bool IsAutomated { get; set; }
        public UserAssignment? UserAssignment { get; set; }
    }

    public class ProjectsDtoV2
    {
        public string? Name { get; set; }
        public Guid? AutomationId { get; set; }
        public bool IsAutomated { get; set; }
        public UserAssignment? UserAssignment { get; set; }
    }
   
}
