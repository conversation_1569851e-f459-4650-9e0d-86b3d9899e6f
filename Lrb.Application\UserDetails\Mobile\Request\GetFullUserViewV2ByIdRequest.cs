﻿using Lrb.Application.OrgProfile.Mobile;

namespace Lrb.Application.UserDetails.Mobile.Request
{
    public class GetFullUserViewV2ByIdRequest :IRequest<Response<UserDetailsDto>>
    {
        public Guid UserId { get; set; }
        public GetFullUserViewV2ByIdRequest(Guid userId)
        {
            UserId = userId;
        }
    }
    public class GetFullUserViewV2ByIdRequestHandler : IRequestHandler<GetFullUserViewV2ByIdRequest, Response<UserDetailsDto>>
    {
        private readonly IRepositoryWithEvents<FullUserView> _userViewRepo;
        private readonly IRepositoryWithEvents<Domain.Entities.Profile> _profileRepo;
        private readonly IDapperRepository _dapperRepository;
        public GetFullUserViewV2ByIdRequestHandler(IRepositoryWithEvents<FullUserView> userViewRepo,
            IRepositoryWithEvents<Profile> profileRepo,
            IDapperRepository dapperRepository)
        {
            _userViewRepo = userViewRepo;
            _profileRepo = profileRepo;
            _dapperRepository = dapperRepository;
        }
        public async Task<Response<UserDetailsDto>> Handle(GetFullUserViewV2ByIdRequest request, CancellationToken cancellationToken)
        {
            try
            {
                var userData = await _userViewRepo.GetByIdAsync(request.UserId) ?? throw new NotFoundException("No User found by the provided Id.");
                var profile = await _profileRepo.FirstOrDefaultAsync(new GetOnlyProfileSpec());
                var leadCount = 0;
                var userDto = userData.Adapt<UserDetailsDto>();
                userDto.OrganizationName = profile?.DisplayName ?? "";
                userDto.LeadCount = leadCount;
                return new(userDto);
            }
            catch (Exception ex)
            {
                return new();
            }
        }
    }
}
