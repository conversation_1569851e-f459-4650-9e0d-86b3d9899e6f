﻿using Lrb.Application.Common.Interfaces;
using Lrb.Application.DataManagement.Web;
using Lrb.Application.DataManagement.Web.Dtos;
using Lrb.Application.DataManagement.Web.Dtos.CustomData;
using Lrb.Application.DataManagement.Web.Mapping;
using Lrb.Application.DataManagement.Web.Request.CommonHandler;
using Lrb.Application.DataManagement.Web.Specs;
using Lrb.Application.Identity.Users;
using Lrb.Shared.Utils;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Lrb.Application.Lead.Web.Requests.UpdationRequests
{
    public class UpdateProspectBulkCampaignListRequest : IRequest<Response<int>>
    {
        public List<string>? CampaignNames { get; set; }
        public List<Guid> Ids { get; set; } = default!;
        public bool? ShouldRemoveExistingCampaign { get; set; }
        public Guid? CurrentUserId { get; set; }
        public string? TenantId { get; set; }


    }
    public class UpdateProspectBulkCampaignListRequestHandler : DataCommonRequestHandler, IRequestHandler<UpdateProspectBulkCampaignListRequest, Response<int>>
    {
        protected readonly ICurrentUser _currentUser;
        private readonly IRepositoryWithEvents<CustomProspectStatus> _prospectStatusRepo;
        private readonly IRepositoryWithEvents<MasterProspectSource> _prospectSourceRepo;
        private readonly IUserService _userService;
        private readonly IDapperRepository _dapperRepository;

        public UpdateProspectBulkCampaignListRequestHandler(IServiceProvider serviceProvider,
            ICurrentUser currentUser,
            IRepositoryWithEvents<CustomProspectStatus> prospectStatusRepo,
            IRepositoryWithEvents<MasterProspectSource> prospectSourceRepo,
            IUserService userService,
            IDapperRepository dapperRepository) : base(serviceProvider)
        {
            _currentUser = currentUser;
            _userService = userService;
            _prospectStatusRepo = prospectStatusRepo;
            _prospectSourceRepo = prospectSourceRepo;
            _dapperRepository = dapperRepository;
        }
        public async Task<Response<int>> Handle(UpdateProspectBulkCampaignListRequest request, CancellationToken cancellationToken)
        {
            try
            {
                int totalUpdatedCount = 0;
                var existingProspects = await _prospectRepo.ListAsync(new GetProspectByIdsSpecs(request.Ids), cancellationToken);
                var currentUserId = request.CurrentUserId ?? _currentUser.GetUserId();
                var updatedProspects = new List<Prospect>();
                var prospectHistorys = new List<ProspectHistory>();
                List<Domain.Entities.Campaign> campaigns = null;
                MasterProspectSource? source = null;
                if (request.CampaignNames != null)
                {
                    campaigns = await _campaignRepo.ListAsync(new GetAllCampaignsSpec(request?.CampaignNames?.ConvertAll(i => i.ToLower()) ?? new()), cancellationToken);

                    if (campaigns == null)
                    {
                        throw new NotFoundException("No Campaign found by this Id");
                    }
                }

                if (existingProspects?.Any() ?? false)
                {
                    var statuses = await _prospectStatusRepo.ListAsync();
                    var propertyTypes = await _propertyTypeRepo.ListAsync();
                    var sources = await _prospectSourceRepo.ListAsync();
                    var userIds = new List<string?>();
                    existingProspects.ForEach(i => {
                        userIds.AddRange(new[] { i.AssignedFrom?.ToString(), i.AssignTo.ToString(), i.LastModifiedBy.ToString(), i.SourcingManager?.ToString(), i.ClosingManager?.ToString() });
                    });
                    userIds.Add(currentUserId.ToString());
                    userIds = userIds.Where(i => i != null && i != string.Empty)?.DistinctBy(i => i).ToList();
                    var users = await _userService.GetListOfUsersByIdsAsync(userIds, cancellationToken);
                    foreach (var existingProspect in existingProspects)
                    {
                        var oldProspect = existingProspect.Adapt<ViewProspectDto>();
                        var prospect = request.Adapt(existingProspect);
                        if ((prospect.Campaigns?.Any() ?? false) && request?.ShouldRemoveExistingCampaign == false)
                        {
                            List<Lrb.Domain.Entities.Campaign> newCampaign = new();
                            newCampaign.AddRange(prospect.Campaigns);
                            newCampaign.AddRange(campaigns);
                            newCampaign = newCampaign.DistinctBy(p => p.Id).ToList();
                            prospect.Campaigns = newCampaign;
                        }
                        else
                        {
                            prospect.Campaigns = campaigns;
                        }


                        prospect.LastModifiedBy = currentUserId;
                        prospect.LastModifiedOn = DateTime.UtcNow;
                        totalUpdatedCount++;
                        updatedProspects.Add(prospect);

                        var prospectVM = prospect.Adapt<ViewProspectDto>();
                        prospectVM = await ProspectHistoryHelper.SetUserViewForProspect(prospectVM, _userService, cancellationToken, currentUserId: currentUserId, userDetails: users);
                        oldProspect = await ProspectHistoryHelper.SetUserViewForProspect(oldProspect, _userService, cancellationToken, currentUserId: currentUserId, userDetails: users);
                        var histories = await ProspectHistoryHelper.UpdateProspectHistoryForVM(prospectVM, oldProspect, currentUserId, 1, statuses, propertyTypes, sources, _userService, cancellationToken);
                        prospectHistorys.AddRange(histories);
                    }
                    if (updatedProspects.Any())
                    {
                        var prospectCampaigns = updatedProspects.Where(prospect => prospect.Campaigns != null && prospect.Campaigns.Any()).SelectMany(prospect => prospect.Campaigns.Select(i => new CampaignProspectDto
                        {
                            ProspectsId = prospect.Id,
                            CampaignsId = i.Id
                        })).ToList();
                        if (prospectCampaigns.Any())
                        {
                            if (request?.ShouldRemoveExistingCampaign == true)
                            {
                                var prospectIdsToClear = updatedProspects.Select(l => l.Id).Distinct().ToList();
                                if (prospectIdsToClear.Any())
                                {
                                    var prospectIdList = string.Join(", ", prospectIdsToClear.Select(id => $"'{id}'"));
                                    var deleteQuery = $"DELETE FROM \"{DataBaseDetails.LRBSchema}\".\"CampaignProspect\" WHERE \"ProspectsId\" IN ({prospectIdList});";
                                    await _dapperRepository.ExecuteQueryAsync(deleteQuery);
                                }
                            }
                            List<string> columnNames = new List<string> { "CampaignsId", "ProspectsId" };
                            var campaignInsertQuery = QueryGenerator.GenerateInsertQueryV1(null, DataBaseDetails.LRBSchema, "CampaignProspect", columnNames, prospectCampaigns);
                            await _dapperRepository.ExecuteQueryAsync(campaignInsertQuery);
                        }
                        var addHistories = prospectHistorys.Adapt<List<ProspectHistoryDapperDto>>();
                        var columns = QueryGenerator.GetMappedProperties<ProspectHistoryDapperDto>();
                        var insertQuery = QueryGenerator.GenerateInsertQuery(request?.TenantId, DataBaseDetails.LRBSchema, "ProspectHistories", columns, addHistories);
                        await _dapperRepository.ExecuteQueryAsync(insertQuery);
                    }
                }

                return new Response<int>(totalUpdatedCount);
            }
            catch (Exception ex)
            {
                throw new ApplicationException("Error updating prospect Campaigns", ex);
            }
        }
    }
    
}
