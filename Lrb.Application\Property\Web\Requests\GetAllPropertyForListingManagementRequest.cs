﻿using Lrb.Application.Common.Persistence.New_Implementation;
using Lrb.Application.Property.Web.Dtos;
using Lrb.Application.Property.Web.Requests.CommonHandler;
using Lrb.Application.Property.Web.Specs;
using Lrb.Domain.Entities.AmenitiesAttributes;
using Lrb.Domain.Entities.ErrorModule;
using Newtonsoft.Json;

namespace Lrb.Application.Property.Web.Requests
{
    public class GetAllListingManagementParameter : PaginationFilter
    {
        public PropertyVisiblity PropertyVisiblity { get; set; }
        public FirstLevelFilter FirstLevelFilter { get; set; }
        public SecondLevelFilter? SecondLevelFilter { get; set; }
        public PropertyDimensionDto? PropertySize { get; set; }

        public double? MinPropertySize { get; set; }
        public double? MaxPropertySize { get; set; }
        public Guid? PropertySizeUnit { get; set; }

        public double? MinCarpetArea { get; set; }
        public double? MaxCarpetArea { get; set; }
        public Guid? CarpetAreaUnit { get; set; }

        public double? MinBuiltUpArea { get; set; }
        public double? MaxBuiltUpArea { get; set; }
        public Guid? BuiltUpAreaUnit { get; set; }

        public double? MinSaleableArea { get; set; }
        public double? MaxSaleableArea { get; set; }
        public Guid? SaleableAreaUnit { get; set; }

        public List<string>? Locations { get; set; }
        public List<string>? Cities { get; set; }
        public List<string>? States { get; set; }
        public EnquiryType? EnquiredFor { get; set; }
        public double? NoOfBHK { get; set; }
        public List<double>? BHKs { get; set; }
        public string? Ratings { get; set; }
        public PropertyStatus? PropertyStatus { get; set; }
        public List<Guid>? PropertyTypes { get; set; }
        public List<Guid>? PropertySubTypes { get; set; }
        public Guid? BasePropertyTypeId { get; set; }
        public string? PropertySearch { get; set; }
        public PropertyDateType? DateType { get; set; }
        public DateTime? FromDate { get; set; }
        public DateTime? ToDate { get; set; }
        public long? MinPrice { get; set; }
        public long? MaxPrice { get; set; }
        public List<Guid>? Amenities { get; set; }
        public List<BHKType>? BHKTypes { get; set; }
        public DateTime? FromPossessionDate { get; set; }
        public DateTime? ToPossessionDate { get; set; }
        public List<string>? Projects { get; set; }
        public List<FurnishStatus>? FurnishStatuses { get; set; }
        public List<SaleType>? SaleTypes { get; set; }
        public string? OwnerName { get; set; }
        public string? PropertyTitle { get; set; }
        public Facing? Facing { get; set; }
        public List<int>? NoOfBathrooms { get; set; }
        public List<int>? NoOfLivingrooms { get; set; }
        public List<int>? NoOfBedrooms { get; set; }
        public List<int>? NoOfUtilites { get; set; }
        public List<int>? NoOfKitchens { get; set; }
        public List<int>? NoOfBalconies { get; set; }
        public List<int>? NoOfFloor { get; set; }
        public int? FloorNumber { get; set; }
        public long? MaxBudget { get; set; }
        public long? MinBudget { get; set; }
        public List<string>? OwnerNames { get; set; }
        public bool? IsWithTeam { get; set; }
        public List<Guid>? UserIds { get; set; }
        public ViewAssignmentsPermission? Permission { get; set; }
        public string? Currency { get; set; }
        public List<Guid>? ListingSourceIds { get; set; }
        public List<string>? Communities { get; set; }
        public List<string>? SubCommunities { get; set; }
        public CompletionStatus? CompletionStatus { get; set; }
        public ListingLevel? ListingLevel { get; set; }
        public string? SerialNo { get; set; }
        public double? MinNetArea { get; set; }
        public double? MaxNetArea { get; set; }
        public Guid? NetAreaUnit { get; set; }
        public List<Guid>? ListingOnBehalf { get; set; }
        public bool? IsListingOnBehalf { get; set; }
        public int? MinLeadCount { get; set; }
        public int? MaxLeadCount { get; set; }
        public int? MaxProspectCount { get; set; }
        public int? MinProspectCount { get; set; }
        public long? FromMinPrice { get; set; }
        public long? ToMinPrice { get; set; }
        public long? FromMaxPrice { get; set; }
        public long? ToMaxPrice { get; set; }
        public PossesionType? PossesionType { get; set; }
        public DateTime? FromPossesionDate { get; set; }
        public DateTime? ToPossesionDate { get; set; }
        public UaeEmirate? UaeEmirate { get; set; }
        public FinishingType? FinishingType { get; set; }
        public List<string>? NoOfFloors { get; set; }
        public List<int>? Parking { get; set; }
        public List<string>? Countries { get; set; }

    }

    public class GetAllPropertyForListingManagementRequest : GetAllListingManagementParameter, IRequest<PagedResponse<ViewListingManagementDto, string>>
    {
    }

    public class GetAllPropertyForListingManagementRequestHandler : PropertyCommonRequestHandler,
        IRequestHandler<GetAllPropertyForListingManagementRequest, PagedResponse<ViewListingManagementDto, string>>
    {
        private readonly IPropertyRepository _efPropertyRepository;
        private readonly IReadRepository<CustomMasterAttribute> _masterAttributeRepo;
        public GetAllPropertyForListingManagementRequestHandler(
            IServiceProvider serviceProvider,
            IPropertyRepository propertyRepository,
            IReadRepository<CustomMasterAttribute> masterAttributeRepo) : base(serviceProvider)
        {
            _efPropertyRepository = propertyRepository;
            _masterAttributeRepo = masterAttributeRepo;
        }
        public async Task<PagedResponse<ViewListingManagementDto, string>> Handle(GetAllPropertyForListingManagementRequest request, CancellationToken cancellationToken)
        {
            if (request != null && request.Permission == ViewAssignmentsPermission.None)
            {
                return new(null, 0);
            }

            var tenantId = _currentUser.GetTenant();
            var currentUserId = _currentUser.GetUserId();
            List<Guid>? propertyDimensionIds = new();

            var userWithPermission = await GetUserIdsByPermissionAsync(request, tenantId, currentUserId);

            NumericAttributesDto numericAttributeDto = new();
            List<int> noOfAttributes = Enumerable.Range(1, 5).ToList();

            try
            {
                var tasks = new Task[]
                {
                //Task.Run(async () => propertyDimensionIds = await GetPropertyDimensionIdsAsync(request ?? new())),
                Task.Run(async () => numericAttributeDto = await InitializeNumericAttributes(noOfAttributes, request))
                };
                await Task.WhenAll(tasks);
            }
            catch (Exception ex)
            {
                var error = new LrbError()
                {
                    ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                    ErrorSource = ex?.Source,
                    StackTrace = ex?.StackTrace,
                    InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                    ErrorModule = "GetAllPropertyRequestHandler -> Handle()"
                };
                await _leadRepositoryAsync.AddErrorAsync(error);
            }
            int count = 0;

            PropertyTypeBaseId propertyTypeIds = new();
            propertyTypeIds.ResidentialBaseId = (await _masterPropertyTypeRepo.FirstOrDefaultAsync(new GetMasterPropertyTypeByTypeSpec("residential")))?.Id;
            propertyTypeIds.AgricultureBaseId = (await _masterPropertyTypeRepo.FirstOrDefaultAsync(new GetMasterPropertyTypeByTypeSpec("agricultural")))?.Id;
            propertyTypeIds.CommercialBaseId = (await _masterPropertyTypeRepo.FirstOrDefaultAsync(new GetMasterPropertyTypeByTypeSpec("commercial")))?.Id;
            List<Guid> propertyIds = new();
            if (request.MinLeadCount != null || request.MaxLeadCount != null || request.MinProspectCount != null || request.MaxProspectCount != null)
            {
                var property = (await _dapperRepository.QueryStoredProcedureFromReadReplicaAsync<PropertyIdsDto>("LeadratBlack", "Lead&Prospects_PropertiesAssociatedCountFilter", new
                {
                    tenantid = tenantId,
                    minprospectcount = request.MinProspectCount,
                    maxprospectcount = request.MaxProspectCount,
                    minleadcount = request.MinLeadCount,
                    maxleadcount = request.MaxLeadCount
                })).FirstOrDefault()?.PropertyIds ?? new List<Guid>();
                propertyIds = property.ToList();
            }
            List<CustomPropertyAttributeDto> attributes = new();
            if (request?.NoOfFloors != null || request?.NoOfKitchens != null || request?.NoOfUtilites != null || request?.NoOfBedrooms != null || request?.NoOfLivingrooms != null || request?.NoOfBalconies != null || request?.NoOfBathrooms != null || request?.Parking != null)
            {
                attributes = await _dapperRepository.GetAttributeDetails(tenantId ?? string.Empty);
            }

            var propertiesResult = await _efPropertyRepository.GetAllPropertiesListingAsync(request, propertyDimensionIds, numericAttributeDto, userWithPermission.Item1, propertyTypeIds, userWithPermission.Item2, tenantId, propertyIds, attributes);

            var properties = propertiesResult.Item1?.Adapt<List<ViewListingManagementDto>>() ?? new List<ViewListingManagementDto>();
            count = propertiesResult.Item2;

            List<Guid> allAttributeIds = properties.Where(p => p.Attributes != null).SelectMany(p => p.Attributes.Select(a => a.MasterPropertyAttributeId)).Distinct().ToList();
            var masterAttributes = await _masterAttributeRepo.ListAsync(new CustomMasterAttributesByIdsSpec(allAttributeIds), cancellationToken);
            foreach (var property in properties)
            {
                if (property?.Attributes?.Any() ?? false)
                {
                    var relevantMasterAttributes = masterAttributes.Where(ma => property.Attributes.Select(a => a.MasterPropertyAttributeId).Contains(ma.Id)).ToList();
                    await UpdateMasterAttributes(property, relevantMasterAttributes);
                }
            }
            return new(properties, count);
        }
        private async static Task<ViewListingManagementDto> UpdateMasterAttributes(ViewListingManagementDto propertyDto, List<CustomMasterAttribute> masterAttributes)
        {
            try
            {
                var attributes = propertyDto.Attributes?.ToList();
                attributes?.ForEach(i =>
                {
                    var matchingAttribute = masterAttributes.FirstOrDefault(j => j.Id == i.MasterPropertyAttributeId);
                    i.AttributeDisplayName = masterAttributes.Where(j => j.Id == i.MasterPropertyAttributeId)?.FirstOrDefault()?.AttributeDisplayName;
                    i.AttributeName = masterAttributes.Where(j => j.Id == i.MasterPropertyAttributeId)?.FirstOrDefault()?.AttributeName;
                    i.AttributeType = masterAttributes.Where(j => j.Id == i.MasterPropertyAttributeId)?.FirstOrDefault()?.AttributeType;
                    i.ActiveImageURL = masterAttributes.Where(j => j.Id == i.MasterPropertyAttributeId)?.FirstOrDefault()?.ActiveImageURL;
                    i.IsActive = masterAttributes.Where(j => j.Id == i.MasterPropertyAttributeId)?.FirstOrDefault()?.IsActive;

                });

                propertyDto.Attributes = attributes;
                return propertyDto;
            }
            catch
            {
                return propertyDto;
            }
        }
    }

    public enum PropertyVisiblity
    {
        All,
        Draft,
        Approved,
        Refused,
        Archived,
        Sold,
        Expired
    }
    public enum FirstLevelFilter
    {
        All,
        Ready,
        OffPlan,
        Secondary
    }
    public enum SecondLevelFilter
    {
        All,
        Residential,
        Commercial,
        Agricultural
    }
    public class PropertyIdsDto
    {
        public IList<Guid>? PropertyIds { get; set; }
    }
}
