﻿using Lrb.Application.Email.Web;
using Lrb.Application.Email.Web.Specs;
using Lrb.Application.GlobalSettings.Web.Dto;
using Lrb.Application.Reports.Web;
using Lrb.Application.Reports.Web.Data.Dtos.User;
using Lrb.Application.Reports.Web.Data.Requests.User;
using Lrb.Application.Reports.Web.Dtos.Activity;
using Lrb.Application.Reports.Web.Dtos.ExportTrackerDto;
using Lrb.Application.Reports.Web.Dtos.FiltersName;
using Lrb.Domain.Entities.ErrorModule;
using Lrb.Domain.Enums;
using Lrb.Shared.Extensions;
using Mapster;
using Newtonsoft.Json;
using static Lrb.Application.Data.Utils.DataExcelHelper;
using static Lrb.Application.Utils.DateTimeExtensions;

namespace ExcelUpload
{
    public partial class FunctionEntryPoint
    {
        public async Task ExportDataStatusReportByUserHandler(InputPayload input, ReportsConfigurationDto? reportConfiguration = null)
        {
            if (reportConfiguration?.Id != null)
            {
                var reportConfig = await GetReportConfiguration(reportConfiguration);
                if (reportConfig == null)
                {
                    return;
                }
            }
            CancellationToken cancellationToken = CancellationToken.None;
            var tracker = await _exportReportsTrackerRepo.GetByIdAsync(input.TrackerId, cancellationToken);
            bool isSent = false;
            Guid CreatredById = tracker?.CreatedBy ?? Guid.Empty;
            string trackerIdString = CreatredById.ToString();
            ExportTrackerDto? exportTracker = tracker?.Adapt<ExportTrackerDto>();
            var userDetails = _userService.GetAsync(trackerIdString, cancellationToken);
            string firstName = userDetails?.Result?.FirstName ?? string.Empty;
            string lastName = userDetails?.Result.LastName ?? string.Empty;
            string createdBy = $"{firstName} {lastName}";
            if (exportTracker != null)
            {
                exportTracker.CreatedBy = createdBy;
            }
            var exportTracker1 = new ExportTrackerDto
            {
                CreatedBy = createdBy,

            };
            try
            {
                if (tracker != null)
                {
                    RunAWSBatchForDataStatusReportByUsersRequest? request = JsonConvert.DeserializeObject<RunAWSBatchForDataStatusReportByUsersRequest>(tracker?.Request ?? string.Empty);
                    if (request != null)
                    {
                        FormattedFiltersDto? filtersDto = request.Adapt<FormattedFiltersDto>();
                        FiltersDto formattedFiltersDto = filtersDto.Adapt<FiltersDto>();
                        List<Guid> UsersByIds = request.UserIds ?? new();

                        if (UsersByIds != null && UsersByIds.Count > 0)
                        {
                            List<string> UsersIdString = UsersByIds.Select(guid => guid.ToString()).ToList();

                            var usernameDetails = await _userService.GetListOfUsersByIdsAsync(UsersIdString, cancellationToken);

                            string userstodetails = "";

                            if (usernameDetails != null && usernameDetails.Count > 0)
                            {
                                foreach (var userDetailsDto in usernameDetails)
                                {
                                    string firstName1 = userDetailsDto.FirstName ?? string.Empty;
                                    string lastName1 = userDetailsDto.LastName ?? string.Empty;

                                    string userDetail = $"{firstName1} {lastName1}";

                                    userstodetails += userDetail + ",";
                                }
                                userstodetails = userstodetails.TrimEnd(',');
                            }
                            formattedFiltersDto.UserNames = userstodetails;
                            var request2 = new FiltersDto
                            {
                                UserNames = userstodetails,

                            };
                        }
                        var tenantId = input.TenantId;
                        var userId = input.CurrentUserId;
                        List<Guid> teamUserIds = new();
                        List<Guid> permittedUserIds = new();
                        var isAdmin = await _dapperRepository.IsAdminAsync(userId, tenantId ?? string.Empty);
                        if (isAdmin)
                        {
                            permittedUserIds = (await _dapperRepository.GetAllUserIdsAsync(tenantId ?? string.Empty)).ToList();
                        }
                        else if (request?.ReportPermission != null)
                        {
                            switch (request.ReportPermission)
                            {
                                case ReportPermission.All:
                                    permittedUserIds = (await _dapperRepository.GetAllUserIdsAsync(tenantId ?? string.Empty)).ToList();
                                    break;
                                case ReportPermission.Reportees:
                                    permittedUserIds = (await _dapperRepository.GetSubordinateIdsWithOnlyReporteesAsync(new List<Guid>() { userId }, tenantId ?? string.Empty)).ToList();
                                    break;
                            }
                        }
                        if (request?.UserIds?.Any() ?? false)
                        {
                            if (request?.IsWithTeam ?? false)
                            {
                                teamUserIds = (await _dapperRepository.GetSubordinateIdsWithOnlyReporteesAsync(request.UserIds, tenantId ?? string.Empty)).ToList();
                            }
                            else
                            {
                                teamUserIds = request?.UserIds ?? new List<Guid>();
                            }
                        }
                        else
                        {
                            if (!isAdmin)
                            {
                                teamUserIds = permittedUserIds;
                            }
                        }
                        if (teamUserIds.Any())
                        {
                            teamUserIds = teamUserIds.Where(userId => permittedUserIds.Contains(userId)).ToList();
                        }
                        else
                        {
                            teamUserIds = permittedUserIds;
                        }
                        request.FromDate = request.FromDate.HasValue ? request.FromDate.Value.ConvertFromDateToUtc() : null;
                        request.ToDate = request.ToDate.HasValue ? request.ToDate.Value.ConvertToDateToUtc() : null;
                        List<BaseDataReportByUserDto> result = new List<BaseDataReportByUserDto>();
                        var users = (await _dapperRepository.QueryStoredProcedureFromReadReplicaAsync<DataUserDto>("LeadratBlack", "Data_GetUsersForDataReport", new
                        {
                            tenantid = tenantId,
                            userids = teamUserIds,
                            searchtext = string.IsNullOrEmpty(request.SearchText) ? null : request.SearchText.Replace(" ", "").ToLower(),
                            userstatus = (request?.UserStatus ?? 0),
                            pagesize = request.PageSize,
                            pagenumber = request.PageNumber,
                            localites = request?.Localites?.ConvertAll<string>(i => i.LrbNormalize()),
                            cities = request?.Cities?.ConvertAll<string>(i => i.LrbNormalize()),
                            states = request?.States?.ConvertAll<string>(i => i.LrbNormalize()),
                            countries = request?.Countries?.ConvertAll<string>(i => i.LrbNormalize())

                        }, 300)).ToList();
                        var res = (await _dapperRepository.QueryStoredProcedureFromReadReplicaAsync<DataReportByUserDto>("LeadratBlack", "Data_GetDataReportByUser", new
                        {
                            fromdate = request.FromDate,
                            todate = request.ToDate,
                            datetype = request.DateType,
                            tenantid = tenantId,
                            userids = teamUserIds,
                            projects = request?.Projects?.ConvertAll<string>(i => i.Replace(" ", "").ToLower()),
                            subsources = request?.SubSources?.ConvertAll<string>(i => i.Replace(" ", "").ToLower()),
                            sources = request?.SourceIds,
                            localites = request?.Localites?.ConvertAll<string>(i => i.LrbNormalize()),
                            cities = request?.Cities?.ConvertAll<string>(i => i.LrbNormalize()),
                            states = request?.States?.ConvertAll<string>(i => i.LrbNormalize()),
                            countries = request?.Countries?.ConvertAll<string>(i => i.LrbNormalize())
                        })).ToList();
                        result = users.Adapt<List<BaseDataReportByUserDto>>();
                        result.ForEach(i =>
                        {
                            var data = res?.Where(j => j.UserId == i.UserId)?.ToList() ?? new();
                            if (data?.Any() ?? false)
                            {
                                i.Data = data.Adapt<List<ViewDataReportByUserDto>>(); ;
                                i.ConvertedDataCount = data?.Select(i => i.ConvertedDataCount).Sum(i => i) ?? 0;
                            }
                        });
                        var dataStatus = await _prospectStatusRepo.ListAsync(cancellationToken);
                        var allData = result.Adapt<List<FormattedDataReportByUserDto>>();
                        var headers = ExportDataHelper.GetDataHeaders<FormattedDataReportByUserDto>(new FormattedDataReportByUserDto(), dataStatus);
                        var fileBytes = ExcelGeneration<FormattedDataReportByUserDto>.GenerateExcel(allData, "Data Status Reports", formattedFiltersDto, exportTracker, headers, request.TimeZoneId, request.BaseUTcOffset);
                        string timeZoneName = Lrb.Application.Utils.TimeZoneHelper.GetZoneName(request.TimeZoneId ?? "Asia/Kolkata");
                        var key = await _blobStorageService.UploadObjectAsync(_blobStorageService?.BucketName ?? "leadrat-black", $"Data_Status", $"Export_Data_Status_Reports_" + input.TenantId + request.FileName + "(" + timeZoneName + ")" + ".xlsx", fileBytes);
                        var presignedUrl = _blobStorageService?.AWSS3BucketUrl + key;
                        _logger.Information($"data Activity Report: {presignedUrl}, {exportTracker.CreatedBy}, {input.TenantId}");
                        await ProcessReportsAutomationAsync(presignedUrl, tracker, reportConfiguration, userDetails.Result.Email, "Data Status Report");
                        isSent = true;
                        tracker.Count = allData?.Count() ?? 0;
                        tracker.Request = JsonConvert.SerializeObject(request);
                        tracker.S3BucketKey = presignedUrl;
                        tracker.FileName = $"Export_Data_Status_Reports_" + request.FileName + "(" + timeZoneName + ")" + ".xlsx";
                        tracker.LastModifiedBy = input.CurrentUserId;
                        tracker.CreatedBy = input.CurrentUserId;
                        await _exportReportsTrackerRepo.UpdateAsync(tracker, cancellationToken);
                    }
                }
            }
            catch (Exception ex)
            {
                if (tracker != null)
                {
                    tracker.Message = ex.Message.ToString();
                    tracker.LastModifiedBy = input.CurrentUserId;
                    tracker.CreatedBy = input.CurrentUserId;
                    await _exportReportsTrackerRepo.UpdateAsync(tracker);
                }
                var error = new LrbError()
                {
                    ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                    ErrorSource = ex?.Source,
                    StackTrace = ex?.StackTrace,
                    InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                    ErrorModule = "FunctionEntryPoint -> RunAWSBatchForDataStatusReportByUsersRequest()"
                };
                await _leadRepositoryAsync.AddErrorAsync(error);
            }
        }

        public async Task ExportDataStatusReportBySubSourceHandler(InputPayload input, ReportsConfigurationDto? reportConfiguration = null)
        {
            if (reportConfiguration?.Id != null)
            {
                var reportConfig = await GetReportConfiguration(reportConfiguration);
                if (reportConfig == null)
                {
                    return;
                }
            }
            CancellationToken cancellationToken = CancellationToken.None;
            var tracker = await _exportReportsTrackerRepo.GetByIdAsync(input.TrackerId, cancellationToken);
            bool isSent = false;
            Guid CreatredById = tracker?.CreatedBy ?? Guid.Empty;
            string trackerIdString = CreatredById.ToString();
            ExportTrackerDto? exportTracker = tracker?.Adapt<ExportTrackerDto>();
            var userDetails = _userService.GetAsync(trackerIdString, cancellationToken);
            string firstName = userDetails?.Result?.FirstName ?? string.Empty;
            string lastName = userDetails?.Result.LastName ?? string.Empty;
            string createdBy = $"{firstName} {lastName}";
            if (exportTracker != null)
            {
                exportTracker.CreatedBy = createdBy;
            }
            var exportTracker1 = new ExportTrackerDto
            {
                CreatedBy = createdBy,

            };
            try
            {
                if (tracker != null)
                {
                    RunAWSBatchForDataStatusReportBySubSourceRequest? request = JsonConvert.DeserializeObject<RunAWSBatchForDataStatusReportBySubSourceRequest>(tracker?.Request ?? string.Empty);
                    if (request != null)
                    {
                        FormattedFiltersDto? filtersDto = request.Adapt<FormattedFiltersDto>();
                        FiltersDto formattedFiltersDto = filtersDto.Adapt<FiltersDto>();
                        List<Guid> UsersByIds = request.UserIds ?? new();

                        if (UsersByIds != null && UsersByIds.Count > 0)
                        {
                            List<string> UsersIdString = UsersByIds.Select(guid => guid.ToString()).ToList();

                            var usernameDetails = await _userService.GetListOfUsersByIdsAsync(UsersIdString, cancellationToken);

                            string userstodetails = "";

                            if (usernameDetails != null && usernameDetails.Count > 0)
                            {
                                foreach (var userDetailsDto in usernameDetails)
                                {
                                    string firstName1 = userDetailsDto.FirstName ?? string.Empty;
                                    string lastName1 = userDetailsDto.LastName ?? string.Empty;

                                    string userDetail = $"{firstName1} {lastName1}";

                                    userstodetails += userDetail + ",";
                                }
                                userstodetails = userstodetails.TrimEnd(',');
                            }
                            formattedFiltersDto.UserNames = userstodetails;
                            var request2 = new FiltersDto
                            {
                                UserNames = userstodetails,

                            };
                        }
                        var tenantId = input.TenantId;
                        var userId = input.CurrentUserId;
                        List<Guid> teamUserIds = new();
                        List<Guid> permittedUserIds = new();
                        var isAdmin = await _dapperRepository.IsAdminAsync(userId, tenantId ?? string.Empty);
                        if (isAdmin)
                        {
                            permittedUserIds = (await _dapperRepository.GetAllUserIdsAsync(tenantId ?? string.Empty)).ToList();
                        }
                        else if (request?.ReportPermission != null)
                        {
                            switch (request.ReportPermission)
                            {
                                case ReportPermission.All:
                                    permittedUserIds = (await _dapperRepository.GetAllUserIdsAsync(tenantId ?? string.Empty)).ToList();
                                    break;
                                case ReportPermission.Reportees:
                                    permittedUserIds = (await _dapperRepository.GetSubordinateIdsWithOnlyReporteesAsync(new List<Guid>() { userId }, tenantId ?? string.Empty)).ToList();
                                    break;
                            }
                        }
                        if (request?.UserIds?.Any() ?? false)
                        {
                            if (request?.IsWithTeam ?? false)
                            {
                                teamUserIds = (await _dapperRepository.GetSubordinateIdsWithOnlyReporteesAsync(request.UserIds, tenantId ?? string.Empty)).ToList();
                            }
                            else
                            {
                                teamUserIds = request?.UserIds ?? new List<Guid>();
                            }
                        }
                        else
                        {
                            if (!isAdmin)
                            {
                                teamUserIds = permittedUserIds;
                            }
                        }
                        if (teamUserIds.Any())
                        {
                            teamUserIds = teamUserIds.Where(userId => permittedUserIds.Contains(userId)).ToList();
                        }
                        else
                        {
                            teamUserIds = permittedUserIds;
                        }
                        request.FromDate = request.FromDate.HasValue ? request.FromDate.Value.ConvertFromDateToUtc() : null;
                        request.ToDate = request.ToDate.HasValue ? request.ToDate.Value.ConvertToDateToUtc() : null;
                        List<ViewDataSubSourceDto> result = new List<ViewDataSubSourceDto>();
                        var res = (await _dapperRepository.QueryStoredProcedureFromReadReplicaAsync<DataSubSourceDto>("LeadratBlack", "Data_GetDataReportBySubSource", new
                        {
                            fromdate = request.FromDate,
                            todate = request.ToDate,
                            datetype = request.DateType,
                            tenantid = tenantId,
                            userids = teamUserIds,
                            searchtext = string.IsNullOrEmpty(request.SearchText) ? null : request.SearchText.Replace(" ", "").ToLower(),
                            projects = request?.Projects?.ConvertAll<string>(i => i.Replace(" ", "").ToLower()),
                            subsources = request?.SubSources?.ConvertAll<string>(i => i.Replace(" ", "").ToLower()),
                            sources = request?.SourceIds,
                            pagesize = request?.PageSize,
                            pagenumber = request?.PageNumber,
                            localites = request?.Localites?.ConvertAll<string>(i => i.LrbNormalize()),
                            cities = request?.Cities?.ConvertAll<string>(i => i.LrbNormalize()),
                            states = request?.States?.ConvertAll<string>(i => i.LrbNormalize()),
                            countries = request?.Countries?.ConvertAll<string>(i => i.Replace(" ", "").ToLower())
                        })).ToList();
                        var data = res.Where(i => !string.IsNullOrWhiteSpace(i.Name)).GroupBy(i => i.Name ?? string.Empty).ToDictionary(i => i.Key, j => j.ToList());
                        foreach (var item in data)
                        {
                            var value = new ViewDataSubSourceDto();
                            value.Name = item.Key;
                            value.ConvertedDataCount = item.Value?.Select(j => j.ConvertedDataCount)?.Sum(i => i) ?? 0;
                            var allItems = item.Value?.Where(i => i.StatusId != Guid.Empty)?.ToList();
                            if (allItems?.Any() ?? false)
                            {
                                value.Data = allItems.Adapt<List<ViewDataReportBySubSourceDto>>();
                            }
                            result.Add(value);
                        }
                        var dataStatus = await _prospectStatusRepo.ListAsync(cancellationToken);
                        var allData = result.Adapt<List<FormattedDataSubSourceDto>>();
                        var headers = ExportDataHelper.GetDataHeaders<FormattedDataSubSourceDto>(new FormattedDataSubSourceDto(), dataStatus);
                        var fileBytes = ExcelGeneration<FormattedDataSubSourceDto>.GenerateExcel(allData, "Data Sub Source Status Reports", formattedFiltersDto, exportTracker, headers, request.TimeZoneId, request.BaseUTcOffset);
                        string timeZoneName = Lrb.Application.Utils.TimeZoneHelper.GetZoneName(request.TimeZoneId ?? "Asia/Kolkata");
                        var key = await _blobStorageService.UploadObjectAsync(_blobStorageService?.BucketName ?? "leadrat-black", $"Data_Status", $"Export_Data_SubSource_Status_Reports_" + input.TenantId + request.FileName + "(" + timeZoneName + ")" + ".xlsx", fileBytes);
                        var presignedUrl = _blobStorageService?.AWSS3BucketUrl + key;
                        await ProcessReportsAutomationAsync(presignedUrl, tracker, reportConfiguration, userDetails.Result.Email, "Data SubSource Status Report");
                        isSent = true;
                        tracker.Count = allData?.Count() ?? 0;
                        tracker.Request = JsonConvert.SerializeObject(request);
                        tracker.S3BucketKey = presignedUrl;
                        tracker.FileName = $"Export_Data_SubSource_Status_Reports_" + request.FileName + "(" + timeZoneName + ")" + ".xlsx";
                        tracker.LastModifiedBy = input.CurrentUserId;
                        tracker.CreatedBy = input.CurrentUserId;
                        await _exportReportsTrackerRepo.UpdateAsync(tracker, cancellationToken);
                    }
                }
            }
            catch (Exception ex)
            {
                if (tracker != null)
                {
                    tracker.Message = ex.Message.ToString();
                    tracker.LastModifiedBy = input.CurrentUserId;
                    tracker.CreatedBy = input.CurrentUserId;
                    await _exportReportsTrackerRepo.UpdateAsync(tracker);
                }
                var error = new LrbError()
                {
                    ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                    ErrorSource = ex?.Source,
                    StackTrace = ex?.StackTrace,
                    InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                    ErrorModule = "FunctionEntryPoint -> RunAWSBatchForDataStatusReportBySubSourceRequest()"
                };
                await _leadRepositoryAsync.AddErrorAsync(error);
            }
        }

        public async Task ExportDataStatusReportBySourceHandler(InputPayload input, ReportsConfigurationDto? reportConfiguration = null)
        {
            if (reportConfiguration?.Id != null)
            {
                var reportConfig = await GetReportConfiguration(reportConfiguration);
                if (reportConfig == null)
                {
                    return;
                }
            }
            CancellationToken cancellationToken = CancellationToken.None;
            var tracker = await _exportReportsTrackerRepo.GetByIdAsync(input.TrackerId, cancellationToken);
            bool isSent = false;
            Guid CreatredById = tracker?.CreatedBy ?? Guid.Empty;
            string trackerIdString = CreatredById.ToString();
            ExportTrackerDto? exportTracker = tracker?.Adapt<ExportTrackerDto>();
            var userDetails = _userService.GetAsync(trackerIdString, cancellationToken);
            string firstName = userDetails?.Result?.FirstName ?? string.Empty;
            string lastName = userDetails?.Result.LastName ?? string.Empty;
            string createdBy = $"{firstName} {lastName}";
            if (exportTracker != null)
            {
                exportTracker.CreatedBy = createdBy;
            }
            var exportTracker1 = new ExportTrackerDto
            {
                CreatedBy = createdBy,

            };
            try
            {
                if (tracker != null)
                {
                    RunAWSBatchForDataStatusReportBySourceRequest? request = JsonConvert.DeserializeObject<RunAWSBatchForDataStatusReportBySourceRequest>(tracker?.Request ?? string.Empty);
                    if (request != null)
                    {
                        FormattedFiltersDto? filtersDto = request.Adapt<FormattedFiltersDto>();
                        FiltersDto formattedFiltersDto = filtersDto.Adapt<FiltersDto>();
                        List<Guid> UsersByIds = request.UserIds ?? new();

                        if (UsersByIds != null && UsersByIds.Count > 0)
                        {
                            List<string> UsersIdString = UsersByIds.Select(guid => guid.ToString()).ToList();

                            var usernameDetails = await _userService.GetListOfUsersByIdsAsync(UsersIdString, cancellationToken);

                            string userstodetails = "";

                            if (usernameDetails != null && usernameDetails.Count > 0)
                            {
                                foreach (var userDetailsDto in usernameDetails)
                                {
                                    string firstName1 = userDetailsDto.FirstName ?? string.Empty;
                                    string lastName1 = userDetailsDto.LastName ?? string.Empty;

                                    string userDetail = $"{firstName1} {lastName1}";

                                    userstodetails += userDetail + ",";
                                }
                                userstodetails = userstodetails.TrimEnd(',');
                            }
                            formattedFiltersDto.UserNames = userstodetails;
                            var request2 = new FiltersDto
                            {
                                UserNames = userstodetails,

                            };
                        }
                        var tenantId = input.TenantId;
                        var userId = input.CurrentUserId;
                        List<Guid> teamUserIds = new();
                        List<Guid> permittedUserIds = new();
                        var isAdmin = await _dapperRepository.IsAdminAsync(userId, tenantId ?? string.Empty);
                        if (isAdmin)
                        {
                            permittedUserIds = (await _dapperRepository.GetAllUserIdsAsync(tenantId ?? string.Empty)).ToList();
                        }
                        else if (request?.ReportPermission != null)
                        {
                            switch (request.ReportPermission)
                            {
                                case ReportPermission.All:
                                    permittedUserIds = (await _dapperRepository.GetAllUserIdsAsync(tenantId ?? string.Empty)).ToList();
                                    break;
                                case ReportPermission.Reportees:
                                    permittedUserIds = (await _dapperRepository.GetSubordinateIdsWithOnlyReporteesAsync(new List<Guid>() { userId }, tenantId ?? string.Empty)).ToList();
                                    break;
                            }
                        }
                        if (request?.UserIds?.Any() ?? false)
                        {
                            if (request?.IsWithTeam ?? false)
                            {
                                teamUserIds = (await _dapperRepository.GetSubordinateIdsWithOnlyReporteesAsync(request.UserIds, tenantId ?? string.Empty)).ToList();
                            }
                            else
                            {
                                teamUserIds = request?.UserIds ?? new List<Guid>();
                            }
                        }
                        else
                        {
                            if (!isAdmin)
                            {
                                teamUserIds = permittedUserIds;
                            }
                        }
                        if (teamUserIds.Any())
                        {
                            teamUserIds = teamUserIds.Where(userId => permittedUserIds.Contains(userId)).ToList();
                        }
                        else
                        {
                            teamUserIds = permittedUserIds;
                        }
                        request.FromDate = request.FromDate.HasValue ? request.FromDate.Value.ConvertFromDateToUtc() : null;
                        request.ToDate = request.ToDate.HasValue ? request.ToDate.Value.ConvertToDateToUtc() : null;
                        List<ViewDataSourceDto> result = new List<ViewDataSourceDto>();
                        var res = (await _dapperRepository.QueryStoredProcedureFromReadReplicaAsync<DataSourceDto>("LeadratBlack", "Data_GetDataReportBySource", new
                        {
                            fromdate = request.FromDate,
                            todate = request.ToDate,
                            datetype = request.DateType,
                            tenantid = tenantId,
                            userids = teamUserIds,
                            searchtext = string.IsNullOrEmpty(request.SearchText) ? null : request.SearchText.Replace(" ", "").ToLower(),
                            projects = request?.Projects?.ConvertAll<string>(i => i.Replace(" ", "").ToLower()),
                            subsources = request?.SubSources?.ConvertAll<string>(i => i.Replace(" ", "").ToLower()),
                            sources = request?.SourceIds,
                            pagesize = request?.PageSize,
                            pagenumber = request?.PageNumber,
                            localites = request?.Localites?.ConvertAll<string>(i => i.LrbNormalize()),
                            cities = request?.Cities?.ConvertAll<string>(i => i.LrbNormalize()),
                            states = request?.States?.ConvertAll<string>(i => i.LrbNormalize())
                        })).ToList();
                        var data = res.GroupBy(i => new Tuple<Guid, string>(i.Id, i?.Name ?? string.Empty)).ToDictionary(i => i.Key, j => j.ToList());
                        foreach (var item in data)
                        {
                            var value = new ViewDataSourceDto();
                            value.Id = item.Key.Item1;
                            value.Name = item.Key.Item2;
                            value.ConvertedDataCount = item.Value?.Select(j => j.ConvertedDataCount)?.Sum(i => i) ?? 0;
                            var allItems = item.Value?.Where(i => i.StatusId != Guid.Empty)?.ToList();
                            if (allItems?.Any() ?? false)
                            {
                                value.Data = allItems.Adapt<List<ViewDataReportBySourceDto>>();
                            }
                            result.Add(value);
                        }
                        var dataStatus = await _prospectStatusRepo.ListAsync(cancellationToken);
                        var allData = result.Adapt<List<FormattedDataSourceDto>>();
                        var headers = ExportDataHelper.GetDataHeaders<FormattedDataSourceDto>(new FormattedDataSourceDto(), dataStatus);
                        var fileBytes = ExcelGeneration<FormattedDataSourceDto>.GenerateExcel(allData, "Data Source Status Reports", formattedFiltersDto, exportTracker, headers, request.TimeZoneId, request.BaseUTcOffset);
                        string timeZoneName = Lrb.Application.Utils.TimeZoneHelper.GetZoneName(request.TimeZoneId ?? "Asia/Kolkata");
                        var key = await _blobStorageService.UploadObjectAsync(_blobStorageService?.BucketName ?? "leadrat-black", $"Data_Status", $"Export_Data_Source_Reports_" + input.TenantId + request.FileName + "(" + timeZoneName + ")" + ".xlsx", fileBytes);
                        var presignedUrl = _blobStorageService?.AWSS3BucketUrl + key;
                        await ProcessReportsAutomationAsync(presignedUrl, tracker, reportConfiguration, userDetails.Result.Email, "Data Source Report");
                        isSent = true;
                        tracker.Count = allData?.Count() ?? 0;
                        tracker.Request = JsonConvert.SerializeObject(request);
                        tracker.S3BucketKey = presignedUrl;
                        tracker.FileName = $"Export_Data_Source_Reports_" + request.FileName + "(" + timeZoneName + ")" + ".xlsx";
                        tracker.LastModifiedBy = input.CurrentUserId;
                        tracker.CreatedBy = input.CurrentUserId;
                        await _exportReportsTrackerRepo.UpdateAsync(tracker, cancellationToken);
                    }
                }
            }
            catch (Exception ex)
            {

                if (tracker != null)
                {
                    tracker.Message = ex.Message.ToString();
                    tracker.LastModifiedBy = input.CurrentUserId;
                    tracker.CreatedBy = input.CurrentUserId;
                    await _exportReportsTrackerRepo.UpdateAsync(tracker);
                }
                var error = new LrbError()
                {
                    ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                    ErrorSource = ex?.Source,
                    StackTrace = ex?.StackTrace,
                    InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                    ErrorModule = "FunctionEntryPoint -> RunAWSBatchForDataStatusReportBySourceRequest()"
                };
                await _leadRepositoryAsync.AddErrorAsync(error);
            }
        }

        public async Task ExportDataStatusReportByProjectHandler(InputPayload input, ReportsConfigurationDto? reportConfiguration = null)
        {
            if (reportConfiguration?.Id != null)
            {
                var reportConfig = await GetReportConfiguration(reportConfiguration);
                if (reportConfig == null)
                {
                    return;
                }
            }
            CancellationToken cancellationToken = CancellationToken.None;
            var tracker = await _exportReportsTrackerRepo.GetByIdAsync(input.TrackerId, cancellationToken);
            bool isSent = false;
            Guid CreatredById = tracker?.CreatedBy ?? Guid.Empty;
            string trackerIdString = CreatredById.ToString();
            ExportTrackerDto? exportTracker = tracker?.Adapt<ExportTrackerDto>();
            var userDetails = _userService.GetAsync(trackerIdString, cancellationToken);
            string firstName = userDetails?.Result?.FirstName ?? string.Empty;
            string lastName = userDetails?.Result.LastName ?? string.Empty;
            string createdBy = $"{firstName} {lastName}";
            if (exportTracker != null)
            {
                exportTracker.CreatedBy = createdBy;
            }
            var exportTracker1 = new ExportTrackerDto
            {
                CreatedBy = createdBy,

            };
            try
            {
                if (tracker != null)
                {
                    RunAWSBatchForDataStatusReportByProjectRequest? request = JsonConvert.DeserializeObject<RunAWSBatchForDataStatusReportByProjectRequest>(tracker?.Request ?? string.Empty);
                    if (request != null)
                    {
                        FormattedFiltersDto? filtersDto = request.Adapt<FormattedFiltersDto>();
                        FiltersDto formattedFiltersDto = filtersDto.Adapt<FiltersDto>();
                        List<Guid> UsersByIds = request.UserIds ?? new();

                        if (UsersByIds != null && UsersByIds.Count > 0)
                        {
                            List<string> UsersIdString = UsersByIds.Select(guid => guid.ToString()).ToList();

                            var usernameDetails = await _userService.GetListOfUsersByIdsAsync(UsersIdString, cancellationToken);

                            string userstodetails = "";

                            if (usernameDetails != null && usernameDetails.Count > 0)
                            {
                                foreach (var userDetailsDto in usernameDetails)
                                {
                                    string firstName1 = userDetailsDto.FirstName ?? string.Empty;
                                    string lastName1 = userDetailsDto.LastName ?? string.Empty;

                                    string userDetail = $"{firstName1} {lastName1}";

                                    userstodetails += userDetail + ",";
                                }
                                userstodetails = userstodetails.TrimEnd(',');
                            }
                            formattedFiltersDto.UserNames = userstodetails;
                            var request2 = new FiltersDto
                            {
                                UserNames = userstodetails,

                            };
                        }
                        var tenantId = input.TenantId;
                        var userId = input.CurrentUserId;
                        List<Guid> teamUserIds = new();
                        List<Guid> permittedUserIds = new();
                        var isAdmin = await _dapperRepository.IsAdminAsync(userId, tenantId ?? string.Empty);
                        if (isAdmin)
                        {
                            permittedUserIds = (await _dapperRepository.GetAllUserIdsAsync(tenantId ?? string.Empty)).ToList();
                        }
                        else if (request?.ReportPermission != null)
                        {
                            switch (request.ReportPermission)
                            {
                                case ReportPermission.All:
                                    permittedUserIds = (await _dapperRepository.GetAllUserIdsAsync(tenantId ?? string.Empty)).ToList();
                                    break;
                                case ReportPermission.Reportees:
                                    permittedUserIds = (await _dapperRepository.GetSubordinateIdsWithOnlyReporteesAsync(new List<Guid>() { userId }, tenantId ?? string.Empty)).ToList();
                                    break;
                            }
                        }
                        if (request?.UserIds?.Any() ?? false)
                        {
                            if (request?.IsWithTeam ?? false)
                            {
                                teamUserIds = (await _dapperRepository.GetSubordinateIdsWithOnlyReporteesAsync(request.UserIds, tenantId ?? string.Empty)).ToList();
                            }
                            else
                            {
                                teamUserIds = request?.UserIds ?? new List<Guid>();
                            }
                        }
                        else
                        {
                            if (!isAdmin)
                            {
                                teamUserIds = permittedUserIds;
                            }
                        }
                        if (teamUserIds.Any())
                        {
                            teamUserIds = teamUserIds.Where(userId => permittedUserIds.Contains(userId)).ToList();
                        }
                        else
                        {
                            teamUserIds = permittedUserIds;
                        }
                        request.FromDate = request.FromDate.HasValue ? request.FromDate.Value.ConvertFromDateToUtc() : null;
                        request.ToDate = request.ToDate.HasValue ? request.ToDate.Value.ConvertToDateToUtc() : null;
                        List<ViewDataProjectDto> result = new List<ViewDataProjectDto>();
                        var res = (await _dapperRepository.QueryStoredProcedureFromReadReplicaAsync<DataProjectDto>("LeadratBlack", "Data_GetDataReportByProject", new
                        {
                            fromdate = request.FromDate,
                            todate = request.ToDate,
                            datetype = request.DateType,
                            tenantid = tenantId,
                            userids = teamUserIds,
                            searchtext = string.IsNullOrEmpty(request.SearchText) ? null : request.SearchText.Replace(" ", "").ToLower(),
                            projects = request?.Projects?.ConvertAll<string>(i => i.Replace(" ", "").ToLower()),
                            subsources = request?.SubSources?.ConvertAll<string>(i => i.Replace(" ", "").ToLower()),
                            sources = request?.SourceIds,
                            pagesize = request?.PageSize,
                            pagenumber = request?.PageNumber,
                            localites = request?.Localites?.ConvertAll<string>(i => i.LrbNormalize()),
                            cities = request?.Cities?.ConvertAll<string>(i => i.LrbNormalize()),
                            states = request?.States?.ConvertAll<string>(i => i.LrbNormalize()),
                            countries = request?.Countries?.ConvertAll<string>(i => i.Replace(" ", "").ToLower())
                        })).ToList();
                        var data = res.GroupBy(i => new Tuple<Guid, string>(i.Id, i?.Name ?? string.Empty)).ToDictionary(i => i.Key, j => j.ToList());
                        foreach (var item in data)
                        {
                            var value = new ViewDataProjectDto();
                            value.Id = item.Key.Item1;
                            value.Name = item.Key.Item2;
                            value.ConvertedDataCount = item.Value?.Select(j => j.ConvertedDataCount)?.Sum(i => i) ?? 0;
                            var allItems = item.Value?.Where(i => i.StatusId != Guid.Empty)?.ToList();
                            if (allItems?.Any() ?? false)
                            {
                                value.Data = allItems.Adapt<List<ViewDataReportByProjectDto>>();
                            }
                            result.Add(value);
                        }
                        var dataStatus = await _prospectStatusRepo.ListAsync(cancellationToken);
                        var allData = result.Adapt<List<FormattedDataProjectDto>>();
                        var headers = ExportDataHelper.GetDataHeaders<FormattedDataProjectDto>(new FormattedDataProjectDto(), dataStatus);
                        var fileBytes = ExcelGeneration<FormattedDataProjectDto>.GenerateExcel(allData, "Data Project Status Reports", formattedFiltersDto, exportTracker, headers, request.TimeZoneId, request.BaseUTcOffset);
                        string timeZoneName = Lrb.Application.Utils.TimeZoneHelper.GetZoneName(request.TimeZoneId ?? "Asia/Kolkata");
                        var key = await _blobStorageService.UploadObjectAsync(_blobStorageService?.BucketName ?? "leadrat-black", $"Data_Status", $"Export_Data_Project_Status_Reports_" + input.TenantId + request.FileName + "(" + timeZoneName + ")" + ".xlsx", fileBytes);
                        var presignedUrl = _blobStorageService?.AWSS3BucketUrl + key;
                        await ProcessReportsAutomationAsync(presignedUrl, tracker, reportConfiguration, userDetails.Result.Email, "Data Project Status Report");
                        isSent = true;
                        tracker.Count = allData?.Count() ?? 0;
                        tracker.Request = JsonConvert.SerializeObject(request);
                        tracker.S3BucketKey = presignedUrl;
                        tracker.FileName = $"Export_Data_Project_Status_Reports_" + request.FileName + "(" + timeZoneName + ")" + ".xlsx";
                        tracker.LastModifiedBy = input.CurrentUserId;
                        tracker.CreatedBy = input.CurrentUserId;
                        await _exportReportsTrackerRepo.UpdateAsync(tracker, cancellationToken);
                    }
                }
            }
            catch (Exception ex)
            {
                if (tracker != null)
                {
                    tracker.Message = ex.Message.ToString();
                    tracker.LastModifiedBy = input.CurrentUserId;
                    tracker.CreatedBy = input.CurrentUserId;
                    await _exportReportsTrackerRepo.UpdateAsync(tracker);
                }
                var error = new LrbError()
                {
                    ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                    ErrorSource = ex?.Source,
                    StackTrace = ex?.StackTrace,
                    InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                    ErrorModule = "FunctionEntryPoint -> RunAWSBatchForDataStatusReportByProjectRequest()"
                };
                await _leadRepositoryAsync.AddErrorAsync(error);
            }
        }

        public async Task ExportDataCallLogReportsHandler(InputPayload input, ReportsConfigurationDto? reportConfiguration = null)
        {
            if (reportConfiguration?.Id != null)
            {
                var reportConfig = await GetReportConfiguration(reportConfiguration);
                if (reportConfig == null)
                {
                    return;
                }
            }
            CancellationToken cancellationToken = CancellationToken.None;
            var tracker = await _exportReportsTrackerRepo.GetByIdAsync(input.TrackerId, cancellationToken);
            bool isSent = false;
            Guid CreatredById = tracker.CreatedBy;
            string trackerIdString = CreatredById.ToString();
            ExportTrackerDto? exportTracker = tracker?.Adapt<ExportTrackerDto>();
            var userDetails = _userService.GetAsync(trackerIdString, cancellationToken);
            string firstName = userDetails?.Result?.FirstName ?? string.Empty;
            string lastName = userDetails?.Result.LastName ?? string.Empty;
            string createdBy = $"{firstName} {lastName}";
            exportTracker.CreatedBy = createdBy;

            var exportTracker1 = new ExportTrackerDto
            {
                CreatedBy = createdBy,

            };
            try
            {
                if (tracker != null)
                {
                    RunAWSBatchForDataCallLogReportRequest? request = JsonConvert.DeserializeObject<RunAWSBatchForDataCallLogReportRequest>(tracker?.Request ?? string.Empty);
                    if (request != null)
                    {
                        FormattedFiltersDto? filtersDto = request.Adapt<FormattedFiltersDto>();
                        FiltersDto formattedFiltersDto = filtersDto.Adapt<FiltersDto>();
                        List<Guid> UsersByIds = request.UserIds ?? new();

                        if (UsersByIds != null && UsersByIds.Count > 0)
                        {
                            List<string> UsersIdString = UsersByIds.Select(guid => guid.ToString()).ToList();

                            var usernameDetails = await _userService.GetListOfUsersByIdsAsync(UsersIdString, cancellationToken);

                            string userstodetails = "";

                            if (usernameDetails != null && usernameDetails.Count > 0)
                            {
                                foreach (var userDetailsDto in usernameDetails)
                                {
                                    string firstName1 = userDetailsDto.FirstName ?? string.Empty;
                                    string lastName1 = userDetailsDto.LastName ?? string.Empty;

                                    string userDetail = $"{firstName1} {lastName1}";

                                    userstodetails += userDetail + ",";
                                }
                                userstodetails = userstodetails.TrimEnd(',');
                            }
                            formattedFiltersDto.UserNames = userstodetails;
                            var request2 = new FiltersDto
                            {
                                UserNames = userstodetails,

                            };
                        }
                        var tenantId = input.TenantId;
                        var userId = input.CurrentUserId;
                        List<Guid> teamUserIds = new();
                        List<Guid> permittedUserIds = new();
                        var isAdmin = await _dapperRepository.IsAdminAsync(userId, tenantId ?? string.Empty);
                        if (isAdmin)
                        {
                            permittedUserIds = (await _dapperRepository.GetAllUserIdsAsync(tenantId ?? string.Empty)).ToList();
                        }
                        else if (request?.ExportPermission != null)
                        {
                            switch (request.ExportPermission)
                            {
                                case ReportPermission.All:
                                    permittedUserIds = (await _dapperRepository.GetAllUserIdsAsync(tenantId ?? string.Empty)).ToList();
                                    break;
                                case ReportPermission.Reportees:
                                    permittedUserIds = (await _dapperRepository.GetSubordinateIdsWithOnlyReporteesAsync(new List<Guid>() { userId }, tenantId ?? string.Empty)).ToList();
                                    break;
                            }
                        }
                        if (request?.UserIds?.Any() ?? false)
                        {
                            if (request?.IsWithTeam ?? false)
                            {
                                teamUserIds = (await _dapperRepository.GetSubordinateIdsWithOnlyReporteesAsync(request.UserIds, tenantId ?? string.Empty)).ToList();
                            }
                            else
                            {
                                teamUserIds = request?.UserIds ?? new List<Guid>();
                            }
                        }
                        else
                        {
                            if (!isAdmin)
                            {
                                teamUserIds = permittedUserIds;
                            }
                        }
                        if (teamUserIds.Any())
                        {
                            teamUserIds = teamUserIds.Where(userId => permittedUserIds.Contains(userId)).ToList();
                        }
                        else
                        {
                            teamUserIds = permittedUserIds;
                        }
                        request.FromDate = request.FromDate.HasValue ? request.FromDate.Value.ConvertFromDateToUtc() : null;
                        request.ToDate = request.ToDate.HasValue ? request.ToDate.Value.ConvertToDateToUtc() : null;
                        request.CallLogFromDate = request.CallLogFromDate.HasValue ? request.CallLogFromDate.Value.ConvertFromDateToUtc() : null;
                        request.CallLogToDate = request.CallLogToDate.HasValue ? request?.CallLogToDate.Value.ConvertToDateToUtc() : null;
                        var callLogReportDtos = (await _dapperRepository.QueryStoredProcedureFromReadReplicaAsync<CallLogReportDto>("LeadratBlack", "Data_GetDataCountReportByCallLogs", new
                        {
                            fromdate = request?.FromDate,
                            todate = request?.ToDate,
                            datetype = request?.DateType,
                            tenantid = tenantId,
                            userids = teamUserIds,
                            searchtext = string.IsNullOrWhiteSpace(request?.SearchText) ? null : request.SearchText.Replace(" ", "").ToLower(),
                            sourceids = request?.Sources?.ConvertAll(i => (int)i),
                            projects = request?.Projects?.ConvertAll(i => i.Replace(" ", "").ToLower()),
                            userstatus = request?.UserStatus ?? 0,
                            subsources = request?.SubSources?.ConvertAll(i => i.Replace(" ", "").ToLower()),
                            agencies = request?.AgencyNames?.ConvertAll(i => i.Replace(" ", "").ToLower()),
                            pagesize = request?.PageSize,
                            pagenumber = request?.PageNumber,
                            withcalllogs = request?.IsWithCallLogsOnly ?? false,
                            calllogfromdate = request?.CallLogFromDate,
                            calllogtodate = request?.CallLogToDate,
                        }, 300)).ToList();
                        //callLogReportDtos = await UpdateTalkTimeAsync(callLogReportDtos);
                        //var dataStatus = await _prospectStatusRepo.ListAsync(cancellationToken);

                        var headers = ExportDataHelper.GetDataHeaders<CallLogReportFormattedDto>(new CallLogReportFormattedDto(), new());
                        var formattedDtos = callLogReportDtos?.Adapt<List<CallLogReportFormattedDto>>().ToList();
                        var fileBytes = ExcelGeneration<CallLogReportFormattedDto>.GenerateExcel(formattedDtos ?? new(), "Data Call-Log Report", formattedFiltersDto, exportTracker, request.TimeZoneId, request.BaseUTcOffset);

                        // var fileBytes = ExcelGeneration<CallLogReportFormattedDto>.GenerateExcel(formattedDtos ?? new(), "Data-Call-Log Report", formattedFiltersDto, exportTracker, headers);
                        string timeZoneName = Lrb.Application.Utils.TimeZoneHelper.GetZoneName(request.TimeZoneId ?? "Asia/Kolkata");
                        var key = await _blobStorageService.UploadObjectAsync(_blobStorageService?.BucketName ?? "leadrat-black", $"Data_Call_Log", $"Export_Data_Call-Log_Reports_" + input.TenantId + request.FileName + "(" + timeZoneName + ")" + ".xlsx", fileBytes);
                        var presignedUrl = _blobStorageService?.AWSS3BucketUrl + key;
                        await ProcessReportsAutomationAsync(presignedUrl, tracker, reportConfiguration, userDetails.Result.Email, "Data Call-Log Report");
                        isSent = true;
                        tracker.Count = callLogReportDtos?.Count();
                        tracker.Request = JsonConvert.SerializeObject(request);
                        tracker.S3BucketKey = presignedUrl;
                        tracker.FileName = $"Export_Data_Call-Log_Reports_" + request.FileName + "(" + timeZoneName + ")" + ".xlsx";
                        tracker.LastModifiedBy = input.CurrentUserId;
                        tracker.CreatedBy = input.CurrentUserId;
                        await _exportReportsTrackerRepo.UpdateAsync(tracker, cancellationToken);
                    }
                }
            }

            catch (Exception ex)
            {
                if (tracker != null)
                {
                    tracker.Message = ex.Message.ToString();
                    tracker.LastModifiedBy = input.CurrentUserId;
                    tracker.CreatedBy = input.CurrentUserId;
                    await _exportReportsTrackerRepo.UpdateAsync(tracker);
                }
                var error = new LrbError()
                {
                    ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                    ErrorSource = ex?.Source,
                    StackTrace = ex?.StackTrace,
                    InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                    ErrorModule = "FunctionEntryPoint -> ExportLeadProjectReportBySubStatusHandler()"
                };
                await _leadRepositoryAsync.AddErrorAsync(error);
            }
        }
        public async Task ExportDataUserActivityReportHandler(InputPayload input, ReportsConfigurationDto? reportConfiguration = null)
        {
            if (reportConfiguration?.Id != null)
            {
                var reportConfig = await GetReportConfiguration(reportConfiguration);
                if (reportConfig == null)
                {
                    return;
                }
            }
            CancellationToken cancellationToken = CancellationToken.None;
            var tracker = await _exportReportsTrackerRepo.GetByIdAsync(input.TrackerId, cancellationToken);
            bool isSent = false;
            Guid CreatedById = tracker.CreatedBy;
            string trackerIdString = CreatedById.ToString();
            ExportTrackerDto? exportTracker = tracker?.Adapt<ExportTrackerDto>();

            var userDetails = _userService.GetAsync(trackerIdString, cancellationToken);
            string firstName = userDetails.Result.FirstName;
            string lastName = userDetails.Result.LastName;
            string createdBy = $"{firstName} {lastName}";
            exportTracker.CreatedBy = createdBy;

            var exportTracker1 = new ExportTrackerDto
            {
                CreatedBy = createdBy,

            };
            try
            {
                if (tracker != null)
                {
                    RunAWSBatchForDataActivityReportRequest? request = JsonConvert.DeserializeObject<RunAWSBatchForDataActivityReportRequest>(tracker?.Request ?? string.Empty);
                    if (request != null)
                    {
                        FormattedFiltersDto? filtersDto = request.Adapt<FormattedFiltersDto>();
                        FiltersDto formattedFiltersDto = filtersDto.Adapt<FiltersDto>();
                        List<Guid> UsersByIds = request.UserIds;

                        if (UsersByIds != null && UsersByIds.Count > 0)
                        {
                            List<string> UsersIdString = UsersByIds.Select(guid => guid.ToString()).ToList();

                            var usernameDetails = await _userService.GetListOfUsersByIdsAsync(UsersIdString, cancellationToken);

                            string userstodetails = "";

                            if (usernameDetails != null && usernameDetails.Count > 0)
                            {
                                foreach (var userDetailsDto in usernameDetails)
                                {
                                    string firstName1 = userDetailsDto.FirstName;
                                    string lastName1 = userDetailsDto.LastName;

                                    string userDetail = $"{firstName1} {lastName1}";

                                    userstodetails += userDetail + ",";
                                }
                                userstodetails = userstodetails.TrimEnd(',');
                            }



                            formattedFiltersDto.UserNames = userstodetails;



                            var request2 = new FiltersDto
                            {
                                UserNames = userstodetails,

                            };
                        }
                        var tenantId = input.TenantId;
                        var userId = input.CurrentUserId;
                        List<Guid> teamUserIds = new();
                        List<Guid> permittedUserIds = new();
                        var isAdmin = await _dapperRepository.IsAdminAsync(userId, tenantId ?? string.Empty);
                        if (isAdmin)
                        {
                            permittedUserIds = (await _dapperRepository.GetAllUserIdsAsync(tenantId ?? string.Empty)).ToList();
                        }
                        else if (request.ReportPermission != null)
                        {
                            switch (request.ReportPermission)
                            {
                                case ReportPermission.All:
                                    permittedUserIds = (await _dapperRepository.GetAllUserIdsAsync(tenantId ?? string.Empty)).ToList();
                                    break;
                                case ReportPermission.Reportees:
                                    permittedUserIds = (await _dapperRepository.GetSubordinateIdsWithOnlyReporteesAsync(new List<Guid>() { userId }, tenantId ?? string.Empty)).ToList();
                                    break;
                            }
                        }
                        if (request?.UserIds?.Any() ?? false)
                        {
                            if (request?.IsWithTeam ?? false)
                            {
                                teamUserIds = (await _dapperRepository.GetSubordinateIdsWithOnlyReporteesAsync(request.UserIds, tenantId ?? string.Empty)).ToList();
                            }
                            else
                            {
                                teamUserIds = request?.UserIds ?? new List<Guid>();
                            }
                        }
                        else
                        {
                            if (!isAdmin)
                            {
                                teamUserIds = permittedUserIds;
                            }
                        }
                        if (teamUserIds.Any())
                        {
                            teamUserIds = teamUserIds.Where(userId => permittedUserIds.Contains(userId)).ToList();
                        }
                        else
                        {
                            teamUserIds = permittedUserIds;
                        }
                        List<UserActivityReportDto> userActivityReportDtos = new();
                        request.FromDate = request.FromDate.HasValue ? request.FromDate.Value.ConvertFromDateToUtc() : DateTime.UtcNow.Date.ConvertFromDateToUtc();
                        request.ToDate = request.ToDate.HasValue ? request.ToDate.Value.ConvertToDateToUtc() : DateTime.UtcNow.Date.ConvertToDateToUtc();
                        var dataFromHistory = (await _dapperRepository.QueryStoredProcedureFromReadReplicaAsync<DataActivityReportDto>("LeadratBlack", "GetActivityReportFromDataManagement", new
                        {
                            pagesize = request.PageSize,
                            pagenumber = request.PageNumber,
                            from_date = request.FromDate,
                            to_date = request.ToDate,
                            tenant_id = tenantId,
                            user_ids = teamUserIds,
                            searchtext = string.IsNullOrEmpty(request.SearchText) ? null : request.SearchText.Replace(" ", "").ToLower().Trim(),
                            userstatus = (request?.UserStatus ?? 0),
                        }, 300)).ToList();
                        var userActivityReport = (await _dapperRepository.QueryStoredProcedureFromReadReplicaAsync<UserDataCommunicationReportDto>("LeadratBlack", "GetActivityReportFromProspectCommunication", new
                        {
                            pagesize = request.PageSize,
                            pagenumber = request.PageNumber,
                            from_date = request.FromDate,
                            to_date = request.ToDate,
                            tenant_id = tenantId,
                            user_ids = teamUserIds,
                            searchtext = string.IsNullOrEmpty(request.SearchText) ? null : request.SearchText.Replace(" ", "").ToLower().Trim(),
                            userstatus = request?.UserStatus ?? 0,
                        }, 300)).ToList();

                        dataFromHistory.ForEach(i =>
                        {
                            i.CallsInitiatedCount = userActivityReport.Where(j => j.UserId == i.UserId)?.FirstOrDefault()?.CallsInitiatedCount ?? 0;
                            i.CallsInitiatedDataCount = userActivityReport.Where(j => j.UserId == i.UserId)?.FirstOrDefault()?.CallsInitiatedDataCount ?? 0;
                            i.WhatsAppInitiatedCount = userActivityReport.Where(j => j.UserId == i.UserId)?.FirstOrDefault()?.WhatsAppInitiatedCount ?? 0;
                            i.WhatsAppInitiatedDataCount = userActivityReport.Where(j => j.UserId == i.UserId)?.FirstOrDefault()?.WhatsAppInitiatedDataCount ?? 0;
                        });
                        var dtos = dataFromHistory.Adapt<List<DataActivityFormattedDto>>();

                        var headers = ExportDataHelper.GetDataHeaders<DataActivityFormattedDto>(new DataActivityFormattedDto(), null);

                        var fileBytes = ExcelGeneration<DataActivityFormattedDto>.GenerateExcel(dtos, "Data Activity Reports", formattedFiltersDto, exportTracker, headers, request.TimeZoneId, request.BaseUTcOffset);
                        string timeZoneName = Lrb.Application.Utils.TimeZoneHelper.GetZoneName(request.TimeZoneId ?? "Asia/Kolkata");
                        var key = await _blobStorageService.UploadObjectAsync(_blobStorageService?.BucketName ?? "leadrat-black", $"DataActivity", $"Export_Data_Activity_Reports_" + input.TenantId + request.FileName + "(" + timeZoneName + ")" + ".xlsx", fileBytes);
                        var presignedUrl = _blobStorageService?.AWSS3BucketUrl + key;
                        await ProcessReportsAutomationAsync(presignedUrl, tracker, reportConfiguration, userDetails.Result.Email, "Data Activity Report");
                        isSent = true;
                        tracker.Count = dataFromHistory.Count();
                        tracker.S3BucketKey = presignedUrl;
                        tracker.FileName = $"Export_Data_Activity_Reports_" + request.FileName + "(" + timeZoneName + ")" + ".xlsx";
                        tracker.LastModifiedBy = input.CurrentUserId;
                        tracker.CreatedBy = input.CurrentUserId;
                        await _exportReportsTrackerRepo.UpdateAsync(tracker, cancellationToken);
                    }
                }
            }

            catch (Exception ex)
            {
                if (tracker != null)
                {
                    tracker.Message = ex.Message.ToString();
                    tracker.LastModifiedBy = input.CurrentUserId;
                    tracker.CreatedBy = input.CurrentUserId;
                    await _exportReportsTrackerRepo.UpdateAsync(tracker);
                }
                var error = new LrbError()
                {
                    ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                    ErrorSource = ex?.Source,
                    StackTrace = ex?.StackTrace,
                    InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                    ErrorModule = "FunctionEntryPoint -> ExportDataUserActivityReportHandler()"
                };
                await _leadRepositoryAsync.AddErrorAsync(error);
            }
        }
    }
}