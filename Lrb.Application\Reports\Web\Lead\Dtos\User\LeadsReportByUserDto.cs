﻿namespace Lrb.Application.Reports.Web
{
    public class LeadsReportByUserDto : IDto
    {
        public Guid UserId { get; set; }
        private string? _firstName;
        private string? _lastName;
        private string? _userName;

        public string? FirstName
        {
            get => _firstName;
            set
            {
                _firstName = value;
                UpdateUserName();
            }
        }

        public string? LastName
        {
            get => _lastName;
            set
            {
                _lastName = value;
                UpdateUserName();
            }
        }

        public string? UserName
        {
            get => _userName;
            set => _userName = value;
        }
        private void UpdateUserName()
        {
            _userName = $"{_firstName} {_lastName}".Trim();
        }
        public string? GeneralManager {  get; set; }
        public string? ReportingManager {  get; set; }
        public long AllCount { get; set; }
        public long ActiveCount { get; set; }
        public long NewCount { get; set; }
        public string? NewCountPercentage { get; set; }
        public long PendingCount { get; set; }
        public string? PendingCountPercentage { get; set; }
        public long OverdueCount { get; set; }
        public string? OverdueCountPercentage { get; set; }
        public long CallbackCount { get; set; }
        public string? CallbackCountPercentage { get; set; }
        public long MeetingScheduledCount { get; set; }
        public string? MeetingScheduledCountPercentage { get; set; }
        public long SiteVisitScheduledCount { get; set; }
        public string? SiteVisitScheduledCountPercentage { get; set; }
        public long MeetingDoneCount { get; set; }
        public long MeetingNotDoneCount { get; set; }
        public long SiteVisitDoneCount { get; set; }
        public long SiteVisitNotDoneCount { get; set; }
        public long BookedCount { get; set; }
        public string? BookedCountPercentage { get; set; }
        public long NotInterestedCount { get; set; }
        public string? NotInterestedCountPercentage { get; set; }
        public long DroppedCount { get; set; }
        public string? DroppedCountPercentage { get; set; }
        public long MeetingDoneUniqueCount { get; set; }
        public string? MeetingDoneUniqueCountPercentage { get; set; }
        public long MeetingNotDoneUniqueCount { get; set; }
        public string? MeetingNotDoneUniqueCountPercentage { get; set; }
        public long SiteVisitDoneUniqueCount { get; set; }
        public string? SiteVisitDoneUniqueCountPercentage { get; set; }
        public long SiteVisitNotDoneUniqueCount { get; set; }
        public string? SiteVisitNotDoneUniqueCountPercentage { get; set; }
        public long BookingCancelCount { get; set; }
        public string? BookingCancelCountPercentage { get; set; }
        public long ExpressionOfInterestLeadCount { get; set; }
        public string? ExpressionOfInterestLeadCountPercentage { get; set; }
        public long InvoicedLeadsCount { get; set; }
        public string? InvoicedLeadsCountPercentage { get; set; }

    }
    public class LeadsReportByFormattedUserDto
    {
        public string SlNo { get; set; } = default!;
        public string Name { get; set; }
        public string? GeneralManager {  get; set; }
        public string? ReportingManager { get; set; }
        public long Active { get; set; }
        public long All { get; set; }
        public long New { get; set; }
        public string? NewPercentage { get; set; }
        public long Pending { get; set; }
        public string? PendingPercentage { get; set; }
        public long Overdue { get; set; }
        public string? OverduePercentage { get; set; }
        public long Callback { get; set; }
        public string? CallbackPercentage { get; set; }
        public long MeetingScheduled { get; set; }
        public string? MeetingScheduledPercentage { get; set; }
        public long MeetingDone { get; set; }
        public long MeetingNotDone { get; set; }
        public long MeetingDoneUniqueCount { get; set; }
        public string? MeetingDoneUniqueCountPercentage { get; set; }
        public long MeetingNotDoneUniqueCount { get; set; }
        public string? MeetingNotDoneUniqueCountPercentage { get; set; }
        public long SiteVisitScheduled { get; set; }
        public string? SiteVisitScheduledPercentage { get; set; }
        public long SiteVisitDone { get; set; }
        public long SiteVisitNotDone { get; set; }
        public long SiteVisitDoneUniqueCount { get; set; }
        public string? SiteVisitDoneUniqueCountPercentage { get; set; }
        public long SiteVisitNotDoneUniqueCount { get; set; }
        public string? SiteVisitNotDoneUniqueCountPercentage { get; set; }
        public long Booked { get; set; }
        public string? BookedPercentage { get; set; }
        public long NotInterested { get; set; }
        public string? NotInterestedPercentage { get; set; }
        public long Dropped { get; set; }
        public string? DroppedPercentage { get; set; }
        public long BookingCancel { get; set; }
        public string? BookingCancelPercentage { get; set; }
        public long ExpressionOfInterest { get; set; }
        public string? ExpressionOfInterestPercentage { get; set; }
        public long Invoiced { get; set; }  
        public string? InvoicedPercentage { get; set; }
        //Note :-  DefaultObject is created just for making this class complex type,
        //there is no reference of DefaultObject.
        public object? DefaultObject { get; set; } = null;
    }
    public class LeadsReportByNewFormattedUserDto
    {
        public string SlNo { get; set; } = default!;
        public string Name { get; set; }
        public string? GeneralManager { get; set; }
        public string? ReportingManager { get; set; }
        public long Active { get; set; }
        public long All { get; set; }
        public long New { get; set; }
        public string? NewPercentage { get; set; }
        public long Pending { get; set; }
        public string? PendingPercentage { get; set; }
        public long Overdue { get; set; }
        public string? OverduePercentage { get; set; }
        public long Callback { get; set; }
        public string? CallbackPercentage { get; set; }
        public long MeetingScheduled { get; set; }
        public string? MeetingScheduledPercentage { get; set; }
        public long MeetingDone { get; set; }
        public long MeetingNotDone { get; set; }
        public long MeetingDoneUniqueCount { get; set; }
        public string? MeetingDoneUniqueCountPercentage { get; set; }
        public long MeetingNotDoneUniqueCount { get; set; }
        public string? MeetingNotDoneUniqueCountPercentage { get; set; }
        public long ReferralScheduled { get; set; }
        public string? ReferralScheduledPercentage { get; set; }
        public long ReferralTaken { get; set; }
        public long ReferralNotTaken { get; set; }
        public long ReferralTakenUniqueCount { get; set; }
        public string? ReferralTakenUniqueCountPercentage { get; set; }
        public long ReferralNotTakenUniqueCount { get; set; }
        public string? ReferralNotTakenUniqueCountPercentage { get; set; }
        public long Booked { get; set; }
        public string? BookedPercentage { get; set; }
        public long NotInterested { get; set; }
        public string? NotInterestedPercentage { get; set; }
        public long Dropped { get; set; }
        public string? DroppedPercentage { get; set; }
        public long BookingCancel { get; set; }
        public string? BookingCancelPercentage { get; set; }
        public long ExpressionOfInterest { get; set; }
        public string? ExpressionOfInterestPercentage { get; set; }
        public long Invoiced { get; set; }
        public string? InvoicedPercentage { get; set; }
        //Note :-  DefaultObject is created just for making this class complex type,
        //there is no reference of DefaultObject.
        public object? DefaultObject { get; set; } = null;
    }
    public class LeadsCountByStatusDto : IDto
    {
        public Guid AssignTo { get; set; }
        public string? FirstName { get; set; }
        public string? LastName { get; set; }
        public string? Status { get; set; }
        public int Count { get; set; }
        public long OverdueCount { get; set; }
        public long MeetingDoneCount { get; set; }
        public long MeetingNotDoneCount { get; set; }
        public long SiteVisitDoneCount { get; set; }
        public long SiteVisitNotDoneCount { get; set; }
    }
    public class LeadsReportByUserV2Dto : IDto
    {
        public User? User { get; set; }
        public List<StatusCountDto>? Status { get; set; }
    }
    public class User
    {
        public Guid UserId { get; set; }
        public string UserName { get; set; }
        public string? FirstName { get; set; }
        public string? LastName { get; set; }
        public string? GeneralManager { get; set; }
        public string? ReportingManager { get; set; }   
    }
    public class LeadsReportDto : IDto
    {
        public string? Report { get; set; }
    }
}
