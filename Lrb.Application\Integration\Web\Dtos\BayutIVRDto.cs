﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Lrb.Application.Integration.Web.Dtos
{
    public class BayutIVRDto : IDto
    {
        public string? id { get; set; }
        public AgentIVRClass? agent { get; set; }
        public CallDetailsClass? call_details { get; set; }
        public EnquirerIVRClass? enquirer { get; set; }
        public string? portal { get; set; }

    }
    public class AgentIVRClass : IDto
    {
        public string? name { get; set; }
        public string? phone { get; set; }
        public string? email { get; set; }
    }

    public class CallDetailsClass : IDto
    {
        public string? pickup_timestamp { get; set; }
        public string? call_status { get; set; }
        public string? proxy_number { get; set; }
        public List<string>? receiver_number { get; set; }
        public string? call_duration { get; set; }
        public string? call_total_duration { get; set; }
    }
    public class EnquirerIVRClass : IDto
    {
        public string? phone_number { get; set; }
    }
}

