﻿using LinqKit;
using Lrb.Application.Common.Interfaces;
using Lrb.Application.Common.Persistence;
using Lrb.Application.Common.Persistence.New_Implementation;
using Lrb.Application.Lead.Mobile;
using Lrb.Application.Lead.Mobile.v3;
using Lrb.Application.Utils;
using Lrb.Domain.Entities;
using Lrb.Domain.Entities.MasterData;
using Lrb.Domain.Enums;
using Lrb.Infrastructure.Persistence.Context;
using Lrb.Shared.Extensions;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Newtonsoft.Json;
using Serilog;
using System.Linq.Expressions;

namespace Lrb.Infrastructure.Persistence.Repository.New_Implementation
{
    public partial class LeadRepository : EFRepository<Lead>, ILeadRepository
    {
        private readonly IServiceProvider _provider;
        private readonly ILogger _logger;
        private readonly IDapperRepository _dapperRepository;
        private readonly ICurrentUser _currentUser;

        public LeadRepository(ApplicationDbContext dbContext, IServiceProvider provider, Serilog.ILogger logger, IDapperRepository dapperRepository, ICurrentUser currentUser) : base(dbContext)
        {
            _provider = provider;
            _logger = logger;
            _dapperRepository = dapperRepository;
            _currentUser = currentUser;
        }
        #region v1 methods for mobile
        public async Task<IEnumerable<Lead>> GetAllCategoryLeadsForMobileAsync(GetAllLeadsRequest request, Guid userId, IList<Guid> subIds, LeadFilterTypeMobile category, List<Guid>? leadHistoryIds, List<Guid>? scheduledMeetingLeadHistoryIds, List<Guid>? scheduledVisitLeadHistoryIds, List<CustomMasterLeadStatus> custumStatuses, bool? isAdmin = null)
        {
            var query = BuildQuery(request, userId, subIds, category, leadHistoryIds, scheduledMeetingLeadHistoryIds, scheduledVisitLeadHistoryIds, custumStatuses);

            List<LeadFilterTypeMobile> scheduledFilterTypes = new()
            {
                LeadFilterTypeMobile.ScheduledMeeting, LeadFilterTypeMobile.ScheduleToday, LeadFilterTypeMobile.Overdue, LeadFilterTypeMobile.SiteVisitScheduled, LeadFilterTypeMobile.ScheduledTomorrow, LeadFilterTypeMobile.UpcomingSchedules, LeadFilterTypeMobile.CallBack
            };
            if (scheduledFilterTypes.Contains(category))
            {
                query = query.OrderBy(i => i.ScheduledDate - new DateTime(2000, 01, 01, 00, 00, 00, DateTimeKind.Utc));
                //query = query.OrderBy(i => (i.ScheduledDate ?? DateTime.MinValue)).ThenBy(i => i.Name);
            }
            else
            {
                query = query.OrderByDescending(i => i.LastModifiedOn - new DateTime(2000, 01, 01, 00, 00, 00, DateTimeKind.Utc));
                //query = query.OrderByDescending(i => (i.LastModifiedOn ?? DateTime.MaxValue)).ThenBy(i => i.Name);
            }

            query = query
                .Skip(request.PageSize * (request.PageNumber - 1))
                .Take(request.PageSize)
                .AsQueryable();

            var leads = await query.ToListAsync().ConfigureAwait(false);
            return leads;
        }
        public async Task<int> GetAllCategoryLeadsCountForMobileAsync(GetAllLeadsRequest request, Guid userId, IList<Guid> subIds, LeadFilterTypeMobile category, List<Guid>? leadHistoryIds, List<Guid>? scheduledMeetingLeadHistoryIds, List<Guid>? scheduledVisitLeadHistoryIds, List<CustomMasterLeadStatus> custumStatuses = null, bool? isAdmin = null)
        {
            var query = BuildQuery(request, userId, subIds, category, leadHistoryIds, scheduledMeetingLeadHistoryIds, scheduledVisitLeadHistoryIds, custumStatuses, isAdmin);
            var count = await query.Select(i => i.Id).CountAsync();
            return count;
        }
        public async Task<IEnumerable<Lead>> GetLeadsByCategoryForMobileAsync(GetLeadCategoryRequest request, Guid userId, IList<Guid> subIds, List<Guid>? leadHistoryIds, List<Guid>? scheduledMeetingLeadHistoryIds, List<Guid>? scheduledVisitLeadHistoryIds, List<CustomMasterLeadStatus>? customMasterLeadStatus = null)
        {
            var query = BuildQuery(request, userId, subIds, leadHistoryIds, scheduledMeetingLeadHistoryIds, scheduledVisitLeadHistoryIds, customMasterLeadStatus);
            query = query.OrderByDescending(i => i.LastModifiedOn - new DateTime(2000, 01, 01, 00, 00, 00, DateTimeKind.Utc))
                .Skip(request.PageSize * (request.PageNumber - 1))
                .Take(request.PageSize)
                .AsQueryable();
            return await query.ToListAsync();
        }
        public async Task<int> GetLeadsCountByCategoryForMobileAsync(GetLeadCategoryRequest request, Guid userId, IList<Guid> subIds, List<Guid>? leadHistoryIds, List<Guid>? scheduledMeetingLeadHistoryIds, List<Guid>? scheduledVisitLeadHistoryIds, List<CustomMasterLeadStatus>? customMasterLeadStatus = null)
        {
            var query = BuildQuery(request, userId, subIds, leadHistoryIds, scheduledMeetingLeadHistoryIds, scheduledVisitLeadHistoryIds, customMasterLeadStatus);
            return await query.Select(i => i.Id).CountAsync();
        }
        public async Task<IEnumerable<Lead>> SearchLeadForMobileAsync(SearchLeadRequest request, Guid userId, IList<Guid> subIds, List<CustomMasterLeadStatus>? customMasterLeadStatus = null)
        {
            var query = BuildQuery(request, userId, subIds , customMasterLeadStatus);
            query = query.OrderByDescending(i => i.LastModifiedOn - new DateTime(2000, 01, 01, 00, 00, 00, DateTimeKind.Utc))
                .Skip(request.PageSize * (request.PageNumber - 1))
                .Take(request.PageSize)
                .AsQueryable();
            return await query.ToListAsync();
        }
        public async Task<int> SearchLeadCountForMobileAsync(SearchLeadRequest request, Guid userId, IList<Guid> subIds, List<CustomMasterLeadStatus>? customMasterLeadStatus = null)
        {
            var query = BuildQuery(request, userId, subIds, customMasterLeadStatus);
            return await query.Select(i => i.Id).CountAsync();
        }
        private IQueryable<Lead> BuildQuery(GetAllLeadsRequest request, Guid userId, IList<Guid> subIds, LeadFilterTypeMobile category, List<Guid>? leadHistoryIds, List<Guid>? scheduledMeetingLeadHistoryIds, List<Guid>? scheduledVisitLeadHistoryIds = null, List<CustomMasterLeadStatus> customStatuses = null, bool? isAdmin = null)
        {
            var scope = _provider.CreateScope();
            var context = scope.ServiceProvider.GetRequiredService<ApplicationDbContext>();
            var reporteeIds = subIds.Where(i => i != userId && i != Guid.Empty);
            var selfWithReporteeIds = subIds.Where(i => i != Guid.Empty);
            var query = context.Leads.Where(i => !i.IsDeleted)
                 .Include(i => i.CustomLeadStatus)
                 .Include(i => i.Enquiries)
                     .ThenInclude(i => i.PropertyType)
                 .Include(i => i.Appointments)
                 .Include(i => i.Enquiries)
                .ThenInclude(i => i.PropertyTypes)
                 .AsQueryable();

            if (request.CustomFlags?.Any() ?? false)
            {
                query = query.Where(i => i.CustomFlags != null && i.CustomFlags.Any(j => j.Flag != null && request.CustomFlags.Contains(j.Flag.Name ?? string.Empty)));
            }
            if (request.DataConverted != null)
            {
                if (request.DataConverted == true)
                {
                    query = query.Where(i => i.IsConvertedFromData == true);
                }
                else
                {
                    query = query.Where(i => i.IsConvertedFromData != true);
                }
            }
            if (request.QualifiedByIds?.Any() ?? false)
            {
                query = query.Where(i => i.QualifiedBy.HasValue && request.QualifiedByIds.Contains(i.QualifiedBy.Value));
            }
            if (request.SubStatuses?.Any() ?? false)
            {
                query = query.Where(i => i.CustomLeadStatus != null && request.SubStatuses.Any(j => j == i.CustomLeadStatus.Id));
            }

            if (request.IsPicked != null && request.IsPicked != default)
            {
                query = query.Where(i => i.IsPicked == request.IsPicked.Value);
            }

            if (!string.IsNullOrWhiteSpace(request.ConfidentialNotes))
            {
                request.ConfidentialNotes = request.ConfidentialNotes.ToLower().Trim();
                query = query.Where(i => i.ConfidentialNotes.ToLower().Trim().Contains(request.ConfidentialNotes));
            }

            if (request.CarpetArea != null && request.CarpetAreaUnitId != default)
            {
                query = query.Where(i => i.Enquiries.Any(i => i.CarpetArea == request.CarpetArea && i.CarpetAreaUnitId == request.CarpetAreaUnitId));
            }
            else if (request.CarpetArea != null)
            {
                query = query.Where(i => i.Enquiries.Any(i => i.CarpetArea == request.CarpetArea || i.CarpetAreaInSqMtr == request.CarpetArea));
            }
            if (request.BuiltUpArea != null && request.BuiltUpAreaUnitId != default && request.BuiltUpAreaUnitId != Guid.Empty)
            {
                query = query.Where(i => i.Enquiries.Any(i => i.BuiltUpArea == request.BuiltUpArea && i.BuiltUpAreaUnitId == request.BuiltUpAreaUnitId));
            }
            else if (request.BuiltUpArea != null)
            {
                query = query.Where(i => i.Enquiries.Any(i => i.BuiltUpArea == request.BuiltUpArea || i.BuiltUpAreaInSqMtr == request.BuiltUpArea));
            }
            if (request.SaleableArea != null && request.SaleableAreaUnitId != default && request.SaleableAreaUnitId != Guid.Empty)
            {
                query = query.Where(i => i.Enquiries.Any(i => i.SaleableArea == request.SaleableArea && i.SaleableAreaUnitId == request.SaleableAreaUnitId));
            }
            else if (request.SaleableArea != null)
            {
                query = query.Where(i => i.Enquiries.Any(i => i.SaleableArea == request.SaleableArea || i.SaleableAreaInSqMtr == request.SaleableArea));
            }

            if (request.NetArea != null && request.NetAreaUnitId != default && request.NetAreaUnitId != Guid.Empty)
            {
                query = query.Where(i => i.Enquiries.Any(i => i.NetArea == request.NetArea && i.NetAreaUnitId == request.NetAreaUnitId));
            }
            else if (request.NetArea != null)
            {
                query = query.Where(i => i.Enquiries.Any(i => i.NetArea == request.NetArea || i.NetAreaInSqMtr == request.NetArea));
            }

            if (request.PropertyArea != null && request.PropertyAreaUnitId != default && request.PropertyAreaUnitId != Guid.Empty)
            {
                query = query.Where(i => i.Enquiries.Any(i => i.PropertyArea == request.PropertyArea && i.PropertyAreaUnitId == request.PropertyAreaUnitId));
            }
            else if (request.PropertyArea != null)
            {
                query = query.Where(i => i.Enquiries.Any(i => i.PropertyArea == request.PropertyArea || i.PropertyAreaInSqMtr == request.PropertyArea));
            }
            if ((request.MinCarpetArea != null || request.MaxCarpetArea != null) && request.CarpetAreaUnitId != default)
            {
                query = query.Where(i => i.Enquiries.Any(e =>
                    e.CarpetAreaUnitId == request.CarpetAreaUnitId &&
                    (request.MinCarpetArea == null || e.CarpetArea >= request.MinCarpetArea) &&
                    (request.MaxCarpetArea == null || e.CarpetArea <= request.MaxCarpetArea)));
            }
            else if (request.MinCarpetArea != null || request.MaxCarpetArea != null)
            {
                query = query.Where(i => i.Enquiries.Any(e =>
                    (request.MinCarpetArea == null || e.CarpetArea >= request.MinCarpetArea) &&
                    (request.MaxCarpetArea == null || e.CarpetArea <= request.MaxCarpetArea)));
            }
            if ((request.MinBuiltUpArea != null || request.MaxBuiltUpArea != null) && request.BuiltUpAreaUnitId != default && request.BuiltUpAreaUnitId != Guid.Empty)
            {
                query = query.Where(i => i.Enquiries.Any(e =>
                    e.BuiltUpAreaUnitId == request.BuiltUpAreaUnitId &&
                    (request.MinBuiltUpArea == null || e.BuiltUpArea >= request.MinBuiltUpArea) &&
                    (request.MaxBuiltUpArea == null || e.BuiltUpArea <= request.MaxBuiltUpArea)));
            }
            else if (request.MinBuiltUpArea != null || request.MaxBuiltUpArea != null)
            {
                query = query.Where(i => i.Enquiries.Any(e =>
                    (request.MinBuiltUpArea == null || e.BuiltUpArea >= request.MinBuiltUpArea) &&
                    (request.MaxBuiltUpArea == null || e.BuiltUpArea <= request.MaxBuiltUpArea)));
            }
            if ((request.MinSaleableArea != null || request.MaxSaleableArea != null) && request.SaleableAreaUnitId != default && request.SaleableAreaUnitId != Guid.Empty)
            {
                query = query.Where(i => i.Enquiries.Any(e =>
                    e.SaleableAreaUnitId == request.SaleableAreaUnitId &&
                    (request.MinSaleableArea == null || e.SaleableArea >= request.MinSaleableArea) &&
                    (request.MaxSaleableArea == null || e.SaleableArea <= request.MaxSaleableArea)));
            }
            else if (request.MinSaleableArea != null || request.MaxSaleableArea != null)
            {
                query = query.Where(i => i.Enquiries.Any(e =>
                    (request.MinSaleableArea == null || e.SaleableArea >= request.MinSaleableArea) &&
                    (request.MaxSaleableArea == null || e.SaleableArea <= request.MaxSaleableArea)));
            }
            if ((request.MinPropertyArea != null || request.MaxPropertyArea != null) && request.PropertyAreaUnitId != default && request.PropertyAreaUnitId != Guid.Empty)
            {
                query = query.Where(i => i.Enquiries.Any(e =>
                    e.PropertyAreaUnitId == request.PropertyAreaUnitId &&
                    (request.MinPropertyArea == null || e.PropertyArea >= request.MinPropertyArea) &&
                    (request.MaxPropertyArea == null || e.PropertyArea <= request.MaxPropertyArea)));
            }
            else if (request.MinPropertyArea != null || request.MaxPropertyArea != null)
            {
                query = query.Where(i => i.Enquiries.Any(e =>
                    (request.MinPropertyArea == null || e.PropertyArea >= request.MinPropertyArea) &&
                    (request.MaxPropertyArea == null || e.PropertyArea <= request.MaxPropertyArea)));
            }

            if ((request.MinNetArea != null || request.MaxNetArea != null) && request.NetAreaUnitId != default && request.NetAreaUnitId != Guid.Empty)
            {
                query = query.Where(i => i.Enquiries.Any(e =>
                    e.NetAreaUnitId == request.NetAreaUnitId &&
                    (request.MinNetArea == null || e.NetArea >= request.MinNetArea) &&
                    (request.MaxNetArea == null || e.NetArea <= request.MaxNetArea)));
            }
            else if (request.MinNetArea != null || request.MaxNetArea != null)
            {
                query = query.Where(i => i.Enquiries.Any(e =>
                    (request.MinNetArea == null || e.NetArea >= request.MinNetArea) &&
                    (request.MaxNetArea == null || e.NetArea <= request.MaxNetArea)));
            }
            if (request.FromMinBudget != null || request.ToMinBudget != null || request.FromMaxBudget != null || request.ToMaxBudget != null)
            {
                if (request.FromMinBudget != null && request.ToMinBudget != null)
                {
                    query = query.Where(i => i.Enquiries.Any(e =>
                        (e.LowerBudget >= request.FromMinBudget && e.LowerBudget <= request.ToMinBudget) ||
                        (e.LowerBudget >= request.FromMinBudget && e.LowerBudget <= request.ToMinBudget)
                    ));
                }
                else if (request.FromMinBudget != null && request.ToMinBudget == null)
                {
                    query = query.Where(i => i.Enquiries.Any(e =>
                        e.LowerBudget >= request.FromMinBudget ||
                        e.LowerBudget >= request.FromMinBudget
                    ));
                }
                else if (request.FromMinBudget == null && request.ToMinBudget != null)
                {
                    query = query.Where(i => i.Enquiries.Any(e =>
                        e.LowerBudget <= request.ToMinBudget ||
                        e.LowerBudget <= request.ToMinBudget
                    ));
                }

                if (request.FromMaxBudget != null && request.ToMaxBudget != null)
                {
                    query = query.Where(i => i.Enquiries.Any(e =>
                        (e.UpperBudget >= request.FromMaxBudget && e.UpperBudget <= request.ToMaxBudget) ||
                        (e.UpperBudget >= request.FromMaxBudget && e.UpperBudget <= request.ToMaxBudget)
                    ));
                }
                else if (request.FromMaxBudget != null && request.ToMaxBudget == null)
                {
                    query = query.Where(i => i.Enquiries.Any(e =>
                        e.UpperBudget >= request.FromMaxBudget ||
                        e.UpperBudget >= request.FromMaxBudget
                    ));
                }
                else if (request.FromMaxBudget == null && request.ToMaxBudget != null)
                {
                    query = query.Where(i => i.Enquiries.Any(e =>
                        e.UpperBudget <= request.ToMaxBudget ||
                        e.UpperBudget <= request.ToMaxBudget
                    ));
                }
            }
            if (!string.IsNullOrEmpty(request.UnitName))
            {
                query = query.Where(i => i.Enquiries.Any(e => e.UnitName != null && e.UnitName.ToLower().Trim() == request.UnitName.ToLower().Trim()));

            }
            if (request?.UnitNames?.Any() ?? false)
            {
                var unitnames = request.UnitNames.ConvertAll(i => i.ToLower());
                query = query.Where(i => i.Enquiries.Count > 0 && i.Enquiries.Any(i => unitnames.Contains(i.UnitName.ToLower()))).AsQueryable();
            }
            if (request?.Nationality?.Any() ?? false)
            {
                var nationality = request.Nationality.ConvertAll(i => i.ToLower());
                query = query.Where(i => i.Nationality != null && nationality.Contains(i.Nationality.ToLower())).AsQueryable();
            }

            if (request?.ClusterName?.Any() ?? false)
            {
                var ClusterName = request.ClusterName.ConvertAll(i => i.ToLower());
                query = query.Where(i => i.Enquiries.Count > 0 && i.Enquiries.Any(i => ClusterName.Contains(i.ClusterName.ToLower()))).AsQueryable();
            }
            if (!string.IsNullOrEmpty(request.ReferralName))
            {
                query = query.Where(i => (i.ReferralName.ToLower().Trim().Contains(request.ReferralName.ToLower().Trim())));
            }
            if (!string.IsNullOrEmpty(request.ReferralContactNo))
            {
                query = query.Where(i => !string.IsNullOrEmpty(i.ReferralContactNo) && request.ReferralContactNo.Contains(i.ReferralContactNo.Substring(i.ReferralContactNo.Length - 10)));
            }
            if (!string.IsNullOrEmpty(request.ReferralEmail))
            {
                query = query.Where(i => (i.ReferralEmail.ToLower().Trim().Contains(request.ReferralEmail.ToLower().Trim())));
            }

            if (request?.ShouldShowBookedDetails ?? false)
            {
                var customInvoiceStatus = customStatuses?.FirstOrDefault(i => i.Status == "invoiced") ?? new();
                query = query.Include(navigationPropertyPath: i => i.BookedDetails)
                             .ThenInclude(i => i.Properties)
                            .AsQueryable();
                query = query.Where(i => i.CustomLeadStatus != null && (i.CustomLeadStatus.BaseId == customInvoiceStatus.Id || i.CustomLeadStatus.Id == customInvoiceStatus.Id));
                query = query.Where(i => ((i.BookedDetails != null) && (i.BookedDetails.OrderByDescending(i => i.LastModifiedOn).FirstOrDefault().IsDeleted == false)));
                query = query.OrderByDescending(i => i.BookedDetails.FirstOrDefault().LastModifiedOn);

            }
            if ((request?.ShouldShowBookedDetails ?? false) && (request?.ShouldShowBrokerageInfo ?? false))
            {
                query = query.Include(navigationPropertyPath: i => i.BookedDetails)
                              .ThenInclude(i => i.BrokerageInfo)
                              .AsQueryable();
            }
            if (request?.TeamHead != null)
            {
                query = query.Where(i => (i.BookedDetails != null) && i.BookedDetails.FirstOrDefault().TeamHead == request.TeamHead);
            }
            if (request?.UpperAgreementLimit > 0)
            {
                query = query.Where(i => (i.BookedDetails != null) && i.BookedDetails.FirstOrDefault().AgreementValue <= request.UpperAgreementLimit);
            }
            if (request?.LowerAgreementLimit > 0)
            {
                query = query.Where(i => (i.BookedDetails != null) && i.BookedDetails.FirstOrDefault().AgreementValue >= request.LowerAgreementLimit);
            }
            if (request?.PaymentMode != null && request?.PaymentMode != TokenType.None)
            {
                switch (request.PaymentMode)
                {
                    case TokenType.Cheque:
                        query = query.Where(i => (i.BookedDetails != null) && i.BookedDetails.FirstOrDefault().PaymentMode == TokenType.Cheque);
                        break;
                    case TokenType.Cash:
                        query = query.Where(i => (i.BookedDetails != null) && i.BookedDetails.FirstOrDefault().PaymentMode == TokenType.Cash);
                        break;
                    case TokenType.NEFT:
                        query = query.Where(i => (i.BookedDetails != null) && i.BookedDetails.FirstOrDefault().PaymentMode == TokenType.NEFT);
                        break;
                    case TokenType.RTGS:
                        query = query.Where(i => (i.BookedDetails != null) && i.BookedDetails.FirstOrDefault().PaymentMode == TokenType.RTGS);
                        break;
                    case TokenType.UPI:
                        query = query.Where(i => (i.BookedDetails != null) && i.BookedDetails.FirstOrDefault().PaymentMode == TokenType.UPI);
                        break;
                    case TokenType.DD:
                        query = query.Where(i => (i.BookedDetails != null) && i.BookedDetails.FirstOrDefault().PaymentMode == TokenType.DD);
                        break;
                    case TokenType.IMPS:
                        query = query.Where(i => (i.BookedDetails != null) && i.BookedDetails.FirstOrDefault().PaymentMode == TokenType.IMPS);
                        break;
                }
            }
            if (request?.UpperDiscountLimit > 0)
            {
                query = query.Where(i => (i.BookedDetails != null) && i.BookedDetails.FirstOrDefault().Discount <= request.UpperDiscountLimit);
            }
            if (request?.LowerDiscountLimit > 0)
            {
                query = query.Where(i => (i.BookedDetails != null) && i.BookedDetails.FirstOrDefault().Discount >= request.UpperDiscountLimit);
            }
            if (request.DiscountMode != null && request?.DiscountMode != DiscountType.None)
            {
                switch (request.DiscountMode)
                {
                    case DiscountType.DirectAdjustment:
                        query = query.Where(i => (i.BookedDetails != null) && i.BookedDetails.FirstOrDefault().DiscountMode == DiscountType.DirectAdjustment);
                        break;
                    case DiscountType.Cashback:
                        query = query.Where(i => (i.BookedDetails != null) && i.BookedDetails.FirstOrDefault().DiscountMode == DiscountType.Cashback);
                        break;
                }
            }
            if (request?.LeadBrokerageInfoId != null)
            {
                query = query.Where(i => (i.BookedDetails != null) && i.BookedDetails.FirstOrDefault().LeadBrokerageInfoId == request.LeadBrokerageInfoId);
            }
            if (!string.IsNullOrEmpty(request.BookedUnderName))
            {
                query = query.Where(i => (i.BookedDetails != null) && i.BookedDetails.FirstOrDefault().BookedUnderName.ToLower() == request.BookedUnderName.ToLower());
            }
            if (!string.IsNullOrEmpty(request.DiscountUnit))
            {
                query = query.Where(i => (i.BookedDetails != null) && i.BookedDetails.FirstOrDefault().DiscountUnit == request.DiscountUnit);
            }
            if (request?.TotalBrokerage != null)
            {
                query = query.Where(i => (i.BookedDetails != null) && i.BookedDetails.FirstOrDefault().BrokerageInfo.TotalBrokerage == request.TotalBrokerage);

            }
            if (request?.SoldPrice != null)
            {
                query = query.Where(i => (i.BookedDetails != null) && i.BookedDetails.FirstOrDefault().BrokerageInfo.SoldPrice == request.SoldPrice);
            }
            if (request?.BrokerageCharges != null)
            {
                query = query.Where(i => (i.BookedDetails != null) && i.BookedDetails.FirstOrDefault().BrokerageInfo.BrokerageCharges == request.BrokerageCharges);
            }
            if (request?.NetBrokerageAmount != null)
            {
                query = query.Where(i => (i.BookedDetails != null) && i.BookedDetails.FirstOrDefault().BrokerageInfo.NetBrokerageAmount == request.NetBrokerageAmount);
            }
            if (request?.GST != null)
            {
                query = query.Where(i => (i.BookedDetails != null) && i.BookedDetails.FirstOrDefault().BrokerageInfo.GST == request.GST);
            }
            if (request?.Commission != null)
            {
                query = query.Where(i => (i.BookedDetails != null) && i.BookedDetails.FirstOrDefault().BrokerageInfo.Commission == request.Commission);
            }
            if (request?.EarnedBrokerage != null)
            {
                query = query.Where(i => (i.BookedDetails != null) && i.BookedDetails.FirstOrDefault().BrokerageInfo.EarnedBrokerage == request.EarnedBrokerage);
            }
            if (!string.IsNullOrEmpty(request.CommissionUnit))
            {
                query = query.Where(i => (i.BookedDetails != null) && i.BookedDetails.FirstOrDefault().BrokerageInfo.CommissionUnit == request.CommissionUnit);
            }
            if (!string.IsNullOrEmpty(request.BrokerageUnit))
            {
                query = query.Where(i => (i.BookedDetails != null) && i.BookedDetails.FirstOrDefault().BrokerageInfo.BrokerageUnit == request.BrokerageUnit);
            }
            if (!string.IsNullOrEmpty(request.GSTUnit))
            {
                query = query.Where(i => (i.BookedDetails != null) && i.BookedDetails.FirstOrDefault().BrokerageInfo.GSTUnit == request.GSTUnit);
            }
            if (request.BrokerageType != null && request?.BrokerageType != BrokerageType.None)
            {
                switch (request.BrokerageType)
                {
                    case BrokerageType.AgreementValue:
                        query = query.Where(i => (i.BookedDetails != null) && i.BookedDetails.FirstOrDefault().BrokerageInfo.BrokerageType == BrokerageType.AgreementValue);
                        break;
                    case BrokerageType.SoldPrice:
                        query = query.Where(i => (i.BookedDetails != null) && i.BookedDetails.FirstOrDefault().BrokerageInfo.BrokerageType == BrokerageType.AgreementValue);
                        break;
                }
            }
            if (!string.IsNullOrEmpty(request.ReferralNumber))
            {
                query = query.Where(i => (i.BookedDetails != null) && i.BookedDetails.FirstOrDefault().BrokerageInfo.ReferralNumber == request.ReferralNumber);
            }
            if (request?.ReferredBy != null)
            {
                query = query.Where(i => (i.BookedDetails != null) && i.BookedDetails.FirstOrDefault().BrokerageInfo.ReferredBy == request.ReferredBy);
            }

            if (request.BookedDate.HasValue && request.BookedDate.HasValue != default)
            {
                query = query.Where(i => i.BookedDate == request.BookedDate);
            }
            if (request?.UpperRemainingAmountLimit > 0)
            {
                query = query.Where(i => (i.BookedDetails != null) && i.BookedDetails.FirstOrDefault().RemainingAmount <= request.UpperRemainingAmountLimit);
            }
            if (request?.LowerRemainingAmountLimit > 0)
            {
                query = query.Where(i => (i.BookedDetails != null) && i.BookedDetails.FirstOrDefault().RemainingAmount >= request.LowerRemainingAmountLimit);
            }
            if (request.PaymentType != null && request?.PaymentType != PaymentType.None)
            {
                switch (request.PaymentType)
                {
                    case PaymentType.Cheque:
                        query = query.Where(i => (i.BookedDetails != null) && i.BookedDetails.FirstOrDefault().PaymentType == PaymentType.Cheque);
                        break;
                    case PaymentType.Cash:
                        query = query.Where(i => (i.BookedDetails != null) && i.BookedDetails.FirstOrDefault().PaymentType == PaymentType.Cash);
                        break;
                    case PaymentType.OnlineTransfer:
                        query = query.Where(i => (i.BookedDetails != null) && i.BookedDetails.FirstOrDefault().PaymentType == PaymentType.OnlineTransfer);
                        break;
                    case PaymentType.BankLoan:
                        query = query.Where(i => (i.BookedDetails != null) && i.BookedDetails.FirstOrDefault().PaymentType == PaymentType.BankLoan);
                        break;
                    case PaymentType.PartialLoanCash:
                        query = query.Where(i => (i.BookedDetails != null) && i.BookedDetails.FirstOrDefault().PaymentType == PaymentType.PartialLoanCash);
                        break;
                    case PaymentType.DD:
                        query = query.Where(i => (i.BookedDetails != null) && i.BookedDetails.FirstOrDefault().PaymentType == PaymentType.DD);
                        break;
                    case PaymentType.PendingLoanApproval:
                        query = query.Where(i => (i.BookedDetails != null) && i.BookedDetails.FirstOrDefault().PaymentType == PaymentType.PendingLoanApproval);
                        break;
                    case PaymentType.LoanApplied:
                        query = query.Where(i => (i.BookedDetails != null) && i.BookedDetails.FirstOrDefault().PaymentType == PaymentType.LoanApplied);
                        break;
                }
            }
            if (request?.CarParkingCharges != null)
            {
                query = query.Where(i => (i.BookedDetails != null) && i.BookedDetails.FirstOrDefault().CarParkingCharges == request.CarParkingCharges);
            }
            if (request?.AdditionalCharges != null)
            {
                query = query.Where(i => (i.BookedDetails != null) && i.BookedDetails.FirstOrDefault().AdditionalCharges == request.AdditionalCharges);
            }
            if (request?.RemainingAmount != null)
            {
                query = query.Where(i => (i.BookedDetails != null) && i.BookedDetails.FirstOrDefault().RemainingAmount == request.RemainingAmount);
            }
            if (request?.MeetingOrVisitStatuses?.Any() ?? false)
            {
                DateTime? fromDateForMeetingOrVisit = request.FromDateForMeetingOrVisit != null ? request.FromDateForMeetingOrVisit.Value.ConvertFromDateToUtc() : null;
                DateTime? toDateForMeetingOrVisit = request.ToDateForMeetingOrVisit != null ? request.ToDateForMeetingOrVisit.Value.ConvertToDateToUtc() : null;
                (List<AppointmentType> appTypes, List<bool> appDoneStatuses) = GetAppointmentTypes(request);
                if (request.AppointmentDoneByUserIds?.Any() ?? false)
                {
                    query = query.Where(i => i.Appointments.Any(app => request.AppointmentDoneByUserIds.Contains(app.CreatedBy) && app.Type != AppointmentType.None && appTypes.Contains(app.Type) && appDoneStatuses.Contains(app.IsDone)));
                }
                else if ((isAdmin != null) && !isAdmin.Value && (!request.AppointmentDoneByUserIds?.Any() ?? true))
                {
                    query = query.Where(i => i.Appointments.Any(j => subIds != null && subIds.Contains(j.UserId) && j.Type != AppointmentType.None));
                }
                if (fromDateForMeetingOrVisit != null && toDateForMeetingOrVisit != null)
                {
                    query = query.Where(i => (request.MeetingOrVisitStatuses.Contains(MeetingOrVisitCompletionStatus.IsMeetingDone)
                                         && i.Appointments.Any(i => i.Type == AppointmentType.Meeting && i.IsDone && (i.UserId != Guid.Empty) && (subIds != null) && subIds.Contains(i.UserId) && i.CreatedOn > fromDateForMeetingOrVisit && i.CreatedOn < toDateForMeetingOrVisit))
                                         ||
                                             (request.MeetingOrVisitStatuses.Contains(MeetingOrVisitCompletionStatus.IsMeetingNotDone)
                                             && i.Appointments.Any(i => i.Type == AppointmentType.Meeting && !i.IsDone && (i.UserId != Guid.Empty) && (subIds != null) && subIds.Contains(i.UserId) && i.CreatedOn > fromDateForMeetingOrVisit && i.CreatedOn < toDateForMeetingOrVisit))
                                         ||
                                             (request.MeetingOrVisitStatuses.Contains(MeetingOrVisitCompletionStatus.IsSiteVisitDone)
                                             && i.Appointments.Any(i => i.Type == AppointmentType.SiteVisit && i.IsDone && (i.UserId != Guid.Empty) && (subIds != null) && subIds.Contains(i.UserId) && i.CreatedOn > fromDateForMeetingOrVisit && i.CreatedOn < toDateForMeetingOrVisit))
                                         ||
                                             (request.MeetingOrVisitStatuses.Contains(MeetingOrVisitCompletionStatus.IsSiteVisitNotDone)
                                             && i.Appointments.Any(i => i.Type == AppointmentType.SiteVisit && !i.IsDone && (i.UserId != Guid.Empty) && (subIds != null) && subIds.Contains(i.UserId) && i.CreatedOn > fromDateForMeetingOrVisit && i.CreatedOn < toDateForMeetingOrVisit)));
                }
                else if (fromDateForMeetingOrVisit != null)
                {
                    query = query.Where(i => (request.MeetingOrVisitStatuses.Contains(MeetingOrVisitCompletionStatus.IsMeetingDone)
                                         && i.Appointments.Any(i => i.Type == AppointmentType.Meeting && i.IsDone && (i.UserId != Guid.Empty) && (subIds != null) && subIds.Contains(i.UserId) && i.CreatedOn > fromDateForMeetingOrVisit))
                                         ||
                                             (request.MeetingOrVisitStatuses.Contains(MeetingOrVisitCompletionStatus.IsMeetingNotDone)
                                             && i.Appointments.Any(i => i.Type == AppointmentType.Meeting && !i.IsDone && (i.UserId != Guid.Empty) && (subIds != null) && subIds.Contains(i.UserId) && i.CreatedOn > fromDateForMeetingOrVisit))
                                         ||
                                             (request.MeetingOrVisitStatuses.Contains(MeetingOrVisitCompletionStatus.IsSiteVisitDone)
                                             && i.Appointments.Any(i => i.Type == AppointmentType.SiteVisit && i.IsDone && (i.UserId != Guid.Empty) && (subIds != null) && subIds.Contains(i.UserId) && i.CreatedOn > fromDateForMeetingOrVisit))
                                         ||
                                             (request.MeetingOrVisitStatuses.Contains(MeetingOrVisitCompletionStatus.IsSiteVisitNotDone)
                                             && i.Appointments.Any(i => i.Type == AppointmentType.SiteVisit && !i.IsDone && (i.UserId != Guid.Empty) && (subIds != null) && subIds.Contains(i.UserId) && i.CreatedOn > fromDateForMeetingOrVisit)));
                }
                else if (toDateForMeetingOrVisit != null)
                {
                    query = query.Where(i => (request.MeetingOrVisitStatuses.Contains(MeetingOrVisitCompletionStatus.IsMeetingDone)
                                         && i.Appointments.Any(i => i.Type == AppointmentType.Meeting && i.IsDone && (i.UserId != Guid.Empty) && (subIds != null) && subIds.Contains(i.UserId) && i.CreatedOn < toDateForMeetingOrVisit.Value))
                                         ||
                                             (request.MeetingOrVisitStatuses.Contains(MeetingOrVisitCompletionStatus.IsMeetingNotDone)
                                             && i.Appointments.Any(i => i.Type == AppointmentType.Meeting && !i.IsDone && (i.UserId != Guid.Empty) && (subIds != null) && subIds.Contains(i.UserId) && i.CreatedOn < toDateForMeetingOrVisit.Value))
                                         ||
                                             (request.MeetingOrVisitStatuses.Contains(MeetingOrVisitCompletionStatus.IsSiteVisitDone)
                                             && i.Appointments.Any(i => i.Type == AppointmentType.SiteVisit && i.IsDone && (i.UserId != Guid.Empty) && (subIds != null) && subIds.Contains(i.UserId) && i.CreatedOn < toDateForMeetingOrVisit.Value))
                                         ||
                                             (request.MeetingOrVisitStatuses.Contains(MeetingOrVisitCompletionStatus.IsSiteVisitNotDone)
                                             && i.Appointments.Any(i => i.Type == AppointmentType.SiteVisit && !i.IsDone && (i.UserId != Guid.Empty) && (subIds != null) && subIds.Contains(i.UserId) && i.CreatedOn < toDateForMeetingOrVisit.Value)));
                }
                else if ((request.AssignToIds?.Any() ?? false) && (request.OwnerSelection == OwnerSelectionType.Both) && (isAdmin ?? false))
                {
                    query = query.Where(i => (request.MeetingOrVisitStatuses.Contains(MeetingOrVisitCompletionStatus.IsMeetingDone)
                                                && i.Appointments.Any(j => j.Type == AppointmentType.Meeting && j.IsDone && (j.UserId != Guid.Empty) && (subIds != null) && (subIds.Contains(i.AssignTo) || request.AssignToIds.Contains(i.SecondaryUserId.Value))))
                                           ||
                                                (request.MeetingOrVisitStatuses.Contains(MeetingOrVisitCompletionStatus.IsMeetingNotDone)
                                                && i.Appointments.Any(j => j.Type == AppointmentType.Meeting && !j.IsDone && (j.UserId != Guid.Empty) && (subIds != null) && (subIds.Contains(i.AssignTo) || request.AssignToIds.Contains(i.SecondaryUserId.Value))))
                                           ||
                                                (request.MeetingOrVisitStatuses.Contains(MeetingOrVisitCompletionStatus.IsSiteVisitDone)
                                                && i.Appointments.Any(j => j.Type == AppointmentType.SiteVisit && j.IsDone && (j.UserId != Guid.Empty) && (subIds != null) && (subIds.Contains(i.AssignTo) || request.AssignToIds.Contains(i.SecondaryUserId.Value))))
                                           ||
                                                (request.MeetingOrVisitStatuses.Contains(MeetingOrVisitCompletionStatus.IsSiteVisitNotDone)
                                                && i.Appointments.Any(j => j.Type == AppointmentType.SiteVisit && !j.IsDone && (j.UserId != Guid.Empty) && (subIds != null) && (subIds.Contains(i.AssignTo) || request.AssignToIds.Contains(i.SecondaryUserId.Value)))));

                }
                else if ((request.OwnerSelection == OwnerSelectionType.SecondaryOwner) && (request.AssignToIds?.Any() ?? false) && (isAdmin ?? false))
                {
                    query = query.Where(i => (request.MeetingOrVisitStatuses.Contains(MeetingOrVisitCompletionStatus.IsMeetingDone)
                                                && i.Appointments.Any(j => j.Type == AppointmentType.Meeting && j.IsDone && (j.UserId != Guid.Empty) && request.AssignToIds.Contains(i.SecondaryUserId.Value)))
                                           ||
                                                (request.MeetingOrVisitStatuses.Contains(MeetingOrVisitCompletionStatus.IsMeetingNotDone)
                                                && i.Appointments.Any(j => j.Type == AppointmentType.Meeting && !j.IsDone && (j.UserId != Guid.Empty) && request.AssignToIds.Contains(i.SecondaryUserId.Value)))
                                           ||
                                                (request.MeetingOrVisitStatuses.Contains(MeetingOrVisitCompletionStatus.IsSiteVisitDone)
                                                && i.Appointments.Any(j => j.Type == AppointmentType.SiteVisit && j.IsDone && (j.UserId != Guid.Empty) && request.AssignToIds.Contains(i.SecondaryUserId.Value)))
                                           ||
                                                (request.MeetingOrVisitStatuses.Contains(MeetingOrVisitCompletionStatus.IsSiteVisitNotDone)
                                                && i.Appointments.Any(j => j.Type == AppointmentType.SiteVisit && !j.IsDone && (j.UserId != Guid.Empty) && request.AssignToIds.Contains(i.SecondaryUserId.Value))));

                }
                else if ((request.AssignTo != null) && (isAdmin ?? false))
                {
                    query = query.Where(i => (request.MeetingOrVisitStatuses.Contains(MeetingOrVisitCompletionStatus.IsMeetingDone)
                                                && i.Appointments.Any(j => j.Type == AppointmentType.Meeting && j.IsDone && (j.UserId != Guid.Empty) && (subIds != null) && subIds.Contains(i.AssignTo)))
                                           ||
                                                (request.MeetingOrVisitStatuses.Contains(MeetingOrVisitCompletionStatus.IsMeetingNotDone)
                                                && i.Appointments.Any(j => j.Type == AppointmentType.Meeting && !j.IsDone && (j.UserId != Guid.Empty) && (subIds != null) && subIds.Contains(i.AssignTo)))
                                           ||
                                                (request.MeetingOrVisitStatuses.Contains(MeetingOrVisitCompletionStatus.IsSiteVisitDone)
                                                && i.Appointments.Any(j => j.Type == AppointmentType.SiteVisit && j.IsDone && (j.UserId != Guid.Empty) && (subIds != null) && subIds.Contains(i.AssignTo)))
                                           ||
                                                (request.MeetingOrVisitStatuses.Contains(MeetingOrVisitCompletionStatus.IsSiteVisitNotDone)
                                                && i.Appointments.Any(j => j.Type == AppointmentType.SiteVisit && !j.IsDone && (j.UserId != Guid.Empty) && (subIds != null) && subIds.Contains(i.AssignTo))));

                }
                else
                {
                    query = query.Where(i => (request.MeetingOrVisitStatuses.Contains(MeetingOrVisitCompletionStatus.IsMeetingDone)
                                                && i.Appointments.Any(i => i.Type == AppointmentType.Meeting && i.IsDone && (i.UserId != Guid.Empty) && (subIds != null) && subIds.Contains(i.UserId)))
                                           ||
                                                (request.MeetingOrVisitStatuses.Contains(MeetingOrVisitCompletionStatus.IsMeetingNotDone)
                                                && i.Appointments.Any(i => i.Type == AppointmentType.Meeting && !i.IsDone && (i.UserId != Guid.Empty) && (subIds != null) && subIds.Contains(i.UserId)))
                                           ||
                                                (request.MeetingOrVisitStatuses.Contains(MeetingOrVisitCompletionStatus.IsSiteVisitDone)
                                                && i.Appointments.Any(i => i.Type == AppointmentType.SiteVisit && i.IsDone && (i.UserId != Guid.Empty) && (subIds != null) && subIds.Contains(i.UserId)))
                                           ||
                                                (request.MeetingOrVisitStatuses.Contains(MeetingOrVisitCompletionStatus.IsSiteVisitNotDone)
                                                && i.Appointments.Any(i => i.Type == AppointmentType.SiteVisit && !i.IsDone && (i.UserId != Guid.Empty) && (subIds != null) && subIds.Contains(i.UserId))));
                }
            }
            if ((request?.IsDualOwnershipEnabled ?? false) && (request?.AssignToIds?.Any() ?? false) && (request.OwnerSelection == OwnerSelectionType.Both))
            {
                switch (request.LeadVisibility)
                {
                    case BaseLeadVisibility.SelfWithReportee:
                        query = query.Where(i => (selfWithReporteeIds.Contains(i.AssignTo) || selfWithReporteeIds.Contains(i.SecondaryUserId ?? Guid.Empty))).Where(i => !i.IsArchived);
                        break;
                    case BaseLeadVisibility.Self:
                        query = query.Where(i => (i.AssignTo == userId || i.SecondaryUserId == userId)).Where(i => !i.IsArchived);
                        break;
                    case BaseLeadVisibility.Reportee:
                        query = query.Where(i => (reporteeIds.Contains(i.AssignTo) || reporteeIds.Contains(i.SecondaryUserId ?? Guid.Empty))).Where(i => !i.IsArchived);
                        break;
                    case BaseLeadVisibility.UnassignLead:
                        query = query.Where(i => i.AssignTo == Guid.Empty).Where(i => !i.IsArchived);
                        break;
                    case BaseLeadVisibility.DeletedLeads:
                        query = query.Where(i => i.IsArchived && selfWithReporteeIds.Contains(i.AssignTo));
                        break;
                    default:
                        break;
                }
            }
            else if ((request?.IsDualOwnershipEnabled ?? false) && (request?.AssignToIds?.Any() ?? false) && (request.OwnerSelection == OwnerSelectionType.SecondaryOwner))
            {
                switch (request.LeadVisibility)
                {
                    case BaseLeadVisibility.SelfWithReportee:
                        query = query.Where(i =>  selfWithReporteeIds.Contains(i.SecondaryUserId.Value)).Where(i => !i.IsArchived);
                        break;
                    case BaseLeadVisibility.Self:
                        query = query.Where(i => i.SecondaryUserId == userId).Where(i => !i.IsArchived);
                        break;
                    case BaseLeadVisibility.Reportee:
                        query = query.Where(i => reporteeIds.Contains(i.SecondaryUserId.Value)).Where(i => !i.IsArchived);
                        break;
                    case BaseLeadVisibility.UnassignLead:
                        query = query.Where(i => i.AssignTo == Guid.Empty).Where(i => !i.IsArchived);
                        break;
                    case BaseLeadVisibility.DeletedLeads:
                        query = query.Where(i => i.IsArchived && selfWithReporteeIds.Contains(i.SecondaryUserId.Value));
                        break;
                    default:
                        break;
                }
            }
            else if ((request?.IsDualOwnershipEnabled ?? false) && (!request?.AssignToIds?.Any() ?? true))
            {
                switch (request.LeadVisibility)
                {
                    case BaseLeadVisibility.SelfWithReportee:
                        query = query.Where(i => (selfWithReporteeIds.Contains(i.AssignTo) || selfWithReporteeIds.Contains(i.SecondaryUserId ?? Guid.Empty))).Where(i => !i.IsArchived);
                        break;
                    case BaseLeadVisibility.Self:
                        query = query.Where(i => (i.AssignTo == userId || i.SecondaryUserId == userId)).Where(i => !i.IsArchived);
                        break;
                    case BaseLeadVisibility.Reportee:
                        query = query.Where(i => (reporteeIds.Contains(i.AssignTo) || reporteeIds.Contains(i.SecondaryUserId ?? Guid.Empty))).Where(i => !i.IsArchived);
                        break;
                    case BaseLeadVisibility.UnassignLead:
                        query = query.Where(i => i.AssignTo == Guid.Empty).Where(i => !i.IsArchived);
                        break;
                    case BaseLeadVisibility.DeletedLeads:
                        query = query.Where(i => i.IsArchived && selfWithReporteeIds.Contains(i.AssignTo));
                        break;
                    default:
                        break;
                }
            }
            else
            {
                switch (request.LeadVisibility)
                {
                    case BaseLeadVisibility.SelfWithReportee:
                        query = query.Where(i => selfWithReporteeIds.Contains(i.AssignTo)).Where(i => !i.IsArchived);
                        break;
                    case BaseLeadVisibility.Self:
                        query = query.Where(i => i.AssignTo == userId).Where(i => !i.IsArchived);
                        break;
                    case BaseLeadVisibility.Reportee:
                        query = query.Where(i => reporteeIds.Contains(i.AssignTo)).Where(i => !i.IsArchived);
                        break;
                    case BaseLeadVisibility.UnassignLead:
                        query = query.Where(i => i.AssignTo == Guid.Empty).Where(i => !i.IsArchived);
                        break;
                    case BaseLeadVisibility.DeletedLeads:
                        query = query.Where(i => i.IsArchived && selfWithReporteeIds.Contains(i.AssignTo));
                        break;
                    default:
                        break;
                }
            }
            if (request.EnquiredFor != null && request.EnquiredFor.Any())
            {
                //query = query.Where(i => i.Enquiries != null && i.Enquiries.Any(i => request.EnquiredFor.Contains(i.EnquiredFor)));
                query = query.Where(i => i.Enquiries != null && i.Enquiries.Any(i => i.EnquiryTypes != null && i.EnquiryTypes.Any(j => request.EnquiredFor.Contains(j))));
            }

            if (request.DateType.HasValue && ((request.FromDate.HasValue && request.FromDate.Value != default) || (request.ToDate.HasValue && request.ToDate.Value != default)))
            {
                DateTime? fromDate = request.FromDate.HasValue ? request.FromDate.Value.ConvertFromDateToUtc() : DateTime.MinValue;
                DateTime? toDate = request.ToDate.HasValue ? request.ToDate.Value.ConvertToDateToUtc() : DateTime.MaxValue;
                switch (request.DateType)
                {
                    case DateType.ReceivedDate:
                        query = query.Where(i => i.CreatedOn >= fromDate.Value && i.CreatedOn <= toDate.Value);
                        break;
                    case DateType.ScheduledDate:
                        query = query.Where(i => i.ScheduledDate != null && i.ScheduledDate.Value >= fromDate.Value && i.ScheduledDate.Value <= toDate.Value);
                        break;
                    case DateType.ModifiedDate:
                        query = query.Where(i => i.LastModifiedOn != null && i.LastModifiedOn.Value >= fromDate.Value && i.LastModifiedOn.Value <= toDate.Value);
                        break;
                    case DateType.DeletedDate:
                        query = query.Where(i => i.ArchivedOn >= fromDate.Value && i.ArchivedOn < toDate.Value);
                        break;
                    case DateType.PickedDate:
                        query = query.Where(i => i.PickedDate >= fromDate.Value && i.PickedDate <= toDate.Value);
                        break;
                    case DateType.BookedDate:
                        query = query.Where(item => item.BookedDetails.Any(detail => detail.BookedDate.Value >= fromDate.Value && detail.BookedDate.Value <= toDate.Value));
                        break;
                    case DateType.AssignedDate:
                        query = query.Where(i => i.Assignments != null && i.Assignments.Any(i => i.AssignmentDate != null && i.AssignmentDate.Value >= fromDate.Value && i.AssignmentDate.Value <= toDate.Value));
                        break;
                    case DateType.PossessionDate:
                        query = query.Where(i => i.Enquiries != null && i.Enquiries.Any(enquiry => enquiry.PossessionDate.Value >= request.FromDate.Value && enquiry.PossessionDate.Value <= request.ToDate.Value));
                        break;
                    default:
                        break;
                }
            }
            if (!string.IsNullOrEmpty(request?.DatesJsonFormattedString ?? string.Empty))
            {
                try
                {
                    var data = JsonConvert.DeserializeObject<List<Lrb.Application.Lead.Mobile.v2.Date>>(request?.DatesJsonFormattedString ?? string.Empty);
                    if (data?.Any() ?? false)
                    {
                        request.Dates = data;
                    }
                    else
                    {
                        request.Dates = null;
                    }
                }
                catch (Exception ex)
                {
                    request.Dates = null;
                    Console.WriteLine("Exception details while deserializing the date object " + ex.Serialize());
                }
            }
            if (request.Dates?.Any() ?? false)
            {
                foreach (var date in request.Dates)
                {
                    if (date.MultiDateType.HasValue && ((date.MultiFromDate.HasValue && date.MultiFromDate.Value != default) || (date.MultiToDate.HasValue && date.MultiToDate.Value != default)))
                    {
                        date.MultiFromDate = date.MultiFromDate.HasValue ? date.MultiFromDate.Value.ConvertFromDateToUtc() : DateTime.MinValue;
                        date.MultiToDate = date.MultiToDate.HasValue ? date.MultiToDate.Value.ConvertToDateToUtc() : DateTime.MaxValue;

                        switch (date.MultiDateType)
                        {
                            case DateType.ReceivedDate:
                                query = query.Where(i => i.CreatedOn >= date.MultiFromDate.Value && i.CreatedOn <= date.MultiToDate.Value);
                                break;
                            case DateType.ScheduledDate:
                                query = query.Where(i => i.ScheduledDate != null && i.ScheduledDate.Value >= date.MultiFromDate.Value && i.ScheduledDate.Value <= date.MultiToDate.Value);
                                break;
                            case DateType.ModifiedDate:
                                query = query.Where(i => i.LastModifiedOn != null && i.LastModifiedOn.Value >= date.MultiFromDate.Value && i.LastModifiedOn.Value <= date.MultiToDate.Value);
                                break;
                            case DateType.DeletedDate:
                                query = query.Where(i => i.ArchivedOn >= date.MultiFromDate.Value && i.ArchivedOn < date.MultiToDate.Value);
                                break;
                            case DateType.PickedDate:
                                query = query.Where(i => i.PickedDate >= date.MultiFromDate.Value && i.PickedDate <= date.MultiToDate.Value);
                                break;
                            case DateType.AssignedDate:
                                query = query.Where(i => i.Assignments != null && i.Assignments.Any(i => i.AssignmentDate != null && i.AssignmentDate.Value >= date.MultiFromDate.Value && i.AssignmentDate.Value <= date.MultiToDate.Value));
                                break;
                            default:
                                break;
                        }
                    }
                }
            }
            /*
                        if (!string.IsNullOrWhiteSpace(request.SearchByNameOrNumber))
                        {
                            query = query.Where(i => i.ContactNo.Contains(request.SearchByNameOrNumber.Replace(" ", "")) ||
                                                     i.Name.ToLower().Contains(request.SearchByNameOrNumber.ToLower()) ||
                                                     i.Email.ToLower().Contains(request.SearchByNameOrNumber.ToLower()) ||
                                                     i.AlternateContactNo.Contains(request.SearchByNameOrNumber.Replace(" ", "")) ||
                                                     i.SerialNumber.Contains(request.SearchByNameOrNumber.Replace(" ", "")));
                        }*/
            if (!string.IsNullOrWhiteSpace(request.SearchByNameOrNumber) && request.PropertyToSearch?.Any() == true)
            {
                var searchTerm = request.SearchByNameOrNumber.ToLower().Trim().Replace(" ", "");
                var isPurposeValid = TryParseEnum<Purpose>(searchTerm, out var parsedPurpose);
                bool isSourceValid = TryParseEnum<LeadSource>(searchTerm, out var parsedSource);
                var isEnquiryTypeValid = TryParseEnum<EnquiryType>(searchTerm, out var parsedEnquiryType);
                var statusId = customStatuses?.Where(i => i.DisplayName.ToLower().Trim().Replace(" ", "").Contains(searchTerm)).Select(i => i.Id).ToList();
                query = query.Where(i =>
                   (request.PropertyToSearch.Contains("LeadName") && i.Name != null && i.Name.ToLower().Trim().Replace(" ", "").Contains(searchTerm)) ||
                   (request.PropertyToSearch.Contains("ContactNo") && i.ContactNo != null && i.ContactNo.Replace(" ", "").Contains(searchTerm)) ||
                   (request.PropertyToSearch.Contains("SerialNumber") && i.SerialNumber != null && i.SerialNumber.Replace(" ", "").Contains(searchTerm)) ||
                   (request.PropertyToSearch.Contains("AlternateContactNo") && i.AlternateContactNo != null && i.AlternateContactNo.Replace(" ", "").Contains(searchTerm)) ||
                   (request.PropertyToSearch.Contains("Email") && i.Email != null && i.Email.ToLower().Contains(searchTerm)) ||
                   (request.PropertyToSearch.Contains("Nationality") && i.Nationality != null && i.Nationality.ToLower().Trim().Contains(searchTerm)) ||
                   (request.PropertyToSearch.Contains("PropertyName") && i.Properties.Any(a => a.Title.ToLower().Trim().Replace(" ", "").Contains(searchTerm))) ||
                   (request.PropertyToSearch.Contains("ProjectName") && i.Projects.Any(a => a.Name.ToLower().Trim().Replace(" ", "").Contains(searchTerm))) ||
                   (request.PropertyToSearch.Contains("SubSource") && i.Enquiries.Any(a => !string.IsNullOrEmpty(a.SubSource) && a.SubSource.ToLower().Trim().Replace(" ", "").Contains(searchTerm))) ||
                   (request.PropertyToSearch.Contains("Status") && (statusId.Contains(i.CustomLeadStatus.BaseId ?? Guid.Empty) || statusId.Contains(i.CustomLeadStatus.Id))) ||
                    (request.PropertyToSearch.Contains("Purpose") && isPurposeValid && i.Enquiries.Any(e => e.Purpose == parsedPurpose)) ||
                     (request.PropertyToSearch.Contains("Source") && isSourceValid && i.Enquiries.Any(e => e.LeadSource == parsedSource)) ||
                     (request.PropertyToSearch.Contains("EnquiredFor") && isEnquiryTypeValid && i.Enquiries.Any(e => e.EnquiryTypes.Contains(parsedEnquiryType))) ||
                   (request.PropertyToSearch.Contains("Location") && i.Enquiries.Any(e => e.Addresses.Any(j =>
                    (j.SubLocality +
                     j.Locality +
                     j.Community +
                     j.SubCommunity +
                     j.TowerName +
                     j.District +
                     j.City +
                     j.State +
                     j.Country +
                     j.PostalCode).Replace(",", "").ToLower().Trim().Replace(" ", "")
                    .Contains(searchTerm)))));
            }
            else if (!string.IsNullOrWhiteSpace(request.SearchByNameOrNumber))
            {
                query = query.Where(i => i.ContactNo.Contains(request.SearchByNameOrNumber.Replace(" ", "")) ||
                         i.Name.ToLower().Contains(request.SearchByNameOrNumber.ToLower())||
                         i.SerialNumber.ToLower().Contains(request.SearchByNameOrNumber.ToLower().Replace(" ", "")));
            }
            if (request.Source?.Any() ?? false)
            {
                query = query.Where(i => i.Enquiries != null && i.Enquiries.Any(i => request.Source.Contains(i.LeadSource)));
            }
            var Statuses = (customStatuses?.Where(i => i.Status is "not_interested" or "dropped" or "booked" or "booking_cancel" or "invoiced"))?.ToList() ?? new();
            var StatusesIds = Statuses.Select(i => i.Id).ToList();
            var ScheduledStatus = (customStatuses?.Where(i => i.Status is "site_visit_scheduled" or "callback" or "meeting_scheduled"))?.ToList() ?? new();
            var schuduledStatusIds = ScheduledStatus.Select(i => i.Id).ToList();
            TimeSpan userUtcOffset = request.BaseUTcOffset ?? TimeZoneInfo.Local.GetUtcOffset(DateTime.UtcNow);
            DateTime today = DateTime.UtcNow.Add(userUtcOffset).Date;
            switch (category)
            {
                case LeadFilterTypeMobile.New:
                    query = query.Where(i => i.CustomLeadStatus != null && i.CustomLeadStatus.Status == "new");
                    break;
                #region ScheduleDate based categories
                case LeadFilterTypeMobile.ScheduleToday:
                    query = query.Where(i => ((i.ScheduledDate ?? DateTime.MinValue) + userUtcOffset) >= today && ((i.ScheduledDate ?? DateTime.MinValue) + userUtcOffset) < today.AddDays(1))
                    .Where(i => i.CustomLeadStatus == null || schuduledStatusIds.Contains(i.CustomLeadStatus.BaseId ?? Guid.Empty) || schuduledStatusIds.Contains(i.CustomLeadStatus.Id)); ;
                    break;
                case LeadFilterTypeMobile.Overdue:
                    query = query.Where(i => ((i.ScheduledDate ?? DateTime.MinValue) + userUtcOffset) < today)
                    .Where(i => i.CustomLeadStatus != null && (schuduledStatusIds.Contains(i.CustomLeadStatus.BaseId ?? Guid.Empty) || schuduledStatusIds.Contains(i.CustomLeadStatus.Id)));
                    break;
                case LeadFilterTypeMobile.ScheduledTomorrow:
                    query = query.Where(i => ((i.ScheduledDate ?? DateTime.MinValue) + userUtcOffset) >= today.AddDays(1) && ((i.ScheduledDate ?? DateTime.MinValue) + userUtcOffset) < today.AddDays(2))
                    .Where(i => i.CustomLeadStatus != null && (schuduledStatusIds.Contains(i.CustomLeadStatus.BaseId ?? Guid.Empty) || schuduledStatusIds.Contains(i.CustomLeadStatus.Id)));
                    break;
                case LeadFilterTypeMobile.UpcomingSchedules:
                    query = query.Where(i => ((i.ScheduledDate ?? DateTime.MinValue) + userUtcOffset) >= today.AddDays(1))
                    .Where(i => i.CustomLeadStatus == null || schuduledStatusIds.Contains(i.CustomLeadStatus.BaseId ?? Guid.Empty) || schuduledStatusIds.Contains(i.CustomLeadStatus.Id));
                    break;
                #endregion
                case LeadFilterTypeMobile.Pending:
                    query = query.Where(i => i.CustomLeadStatus != null && i.CustomLeadStatus.Status == "pending");
                    break;
                case LeadFilterTypeMobile.NotInterested:
                    var notInterestedStatus = customStatuses?.FirstOrDefault(i => i.Status == "not_interested") ?? new();
                    query = query.Where(i => i.CustomLeadStatus != null && (i.CustomLeadStatus.BaseId == notInterestedStatus.Id || i.CustomLeadStatus.Id == notInterestedStatus.Id));
                    break;
                case LeadFilterTypeMobile.CallBack:
                    query = query.Where(i => i.CustomLeadStatus != null && i.CustomLeadStatus.MasterLeadStatusBaseId == Guid.Parse("5ae346bc-c695-4af4-8c3b-c8648587fbd6"));
                    break;
                case LeadFilterTypeMobile.SiteVisitScheduled:
                    var siteVisitScheduled = customStatuses?.FirstOrDefault(i => i.Status == "site_visit_scheduled") ?? new();
                    //query = query.Where(i => i.CustomLeadStatus != null && (i.CustomLeadStatus.Status == "site_visit_scheduled" || i.CustomLeadStatus.MasterLeadStatusBaseId.ToString() == "59647294-09d6-44a2-a346-9de5ba829e04"));
                    query = query.Where(i => i.CustomLeadStatus != null && (i.CustomLeadStatus.BaseId == siteVisitScheduled.Id || i.CustomLeadStatus.Id == siteVisitScheduled.Id));
                    break;
                case LeadFilterTypeMobile.ScheduledMeeting:
                    var meetingScheduled = customStatuses?.FirstOrDefault(i => i.Status == "meeting_scheduled") ?? new();
                    //query = query.Where(i => i.CustomLeadStatus != null && (i.CustomLeadStatus.Status == "meeting_scheduled" || i.CustomLeadStatus.MasterLeadStatusBaseId.ToString() == "99a7f794-9046-4a9d-b7e2-e0a2196b98dd"));
                    query = query.Where(i => i.CustomLeadStatus != null && (i.CustomLeadStatus.BaseId == meetingScheduled.Id || i.CustomLeadStatus.Id == meetingScheduled.Id));
                    break;
                case LeadFilterTypeMobile.UnassignLeads:
                    query = query.Where(i => i.CustomLeadStatus != null && i.AssignTo == Guid.Empty);
                    break;
                case LeadFilterTypeMobile.Booked:
                    var bookedStatus = customStatuses?.FirstOrDefault(i => i.Status == "booked") ?? new();
                    query = query.Where(i => i.CustomLeadStatus != null && (i.CustomLeadStatus.BaseId == bookedStatus.Id || i.CustomLeadStatus.Id == bookedStatus.Id));
                    break;
                case LeadFilterTypeMobile.Dropped:
                    var droppedStatus = customStatuses?.FirstOrDefault(i => i.Status == "dropped") ?? new();
                    query = query.Where(i => i.CustomLeadStatus != null && (i.CustomLeadStatus.BaseId == droppedStatus.Id || i.CustomLeadStatus.Id == droppedStatus.Id));
                    break;
                case LeadFilterTypeMobile.Escalated:
                    query = query.Where(i => i.TagInfo != null && i.TagInfo.IsEscalated)
                    .Where(i => i.CustomLeadStatus == null || i.CustomLeadStatus.MasterLeadStatusBaseId != Guid.Parse("023fcb89-037e-42f4-bcc2-647bb4e95c99"))
                    .Where(i => i.CustomLeadStatus == null || i.CustomLeadStatus.Status != "booked")
                    .Where(i => i.CustomLeadStatus == null || i.CustomLeadStatus.MasterLeadStatusBaseId != Guid.Parse("065fa6fd-a15e-4248-b469-e8596980c97a"));
                    break;
                case LeadFilterTypeMobile.HotLeads:
                    query = query.Where(i => i.TagInfo != null && i.TagInfo.IsHotLead)
                    .Where(i => i.CustomLeadStatus == null || i.CustomLeadStatus.MasterLeadStatusBaseId != Guid.Parse("023fcb89-037e-42f4-bcc2-647bb4e95c99"))
                    .Where(i => i.CustomLeadStatus == null || i.CustomLeadStatus.Status != "booked")
                    .Where(i => i.CustomLeadStatus == null || i.CustomLeadStatus.MasterLeadStatusBaseId != Guid.Parse("065fa6fd-a15e-4248-b469-e8596980c97a"));
                    break;
                case LeadFilterTypeMobile.AboutToConvert:
                    query = query.Where(i => i.TagInfo != null && i.TagInfo.IsAboutToConvert)
                    .Where(i => i.CustomLeadStatus == null || i.CustomLeadStatus.MasterLeadStatusBaseId != Guid.Parse("023fcb89-037e-42f4-bcc2-647bb4e95c99"))
                    .Where(i => i.CustomLeadStatus == null || i.CustomLeadStatus.Status != "booked")
                    .Where(i => i.CustomLeadStatus == null || i.CustomLeadStatus.MasterLeadStatusBaseId != Guid.Parse("065fa6fd-a15e-4248-b469-e8596980c97a"));
                    break;
                case LeadFilterTypeMobile.WarmLeads:
                    query = query.Where(i => i.TagInfo != null && i.TagInfo.IsWarmLead)
                    .Where(i => i.CustomLeadStatus == null || i.CustomLeadStatus.MasterLeadStatusBaseId != Guid.Parse("023fcb89-037e-42f4-bcc2-647bb4e95c99"))
                    .Where(i => i.CustomLeadStatus == null || i.CustomLeadStatus.Status != "booked")
                    .Where(i => i.CustomLeadStatus == null || i.CustomLeadStatus.MasterLeadStatusBaseId != Guid.Parse("065fa6fd-a15e-4248-b469-e8596980c97a"));
                    break;
                case LeadFilterTypeMobile.ColdLead:
                    query = query.Where(i => i.TagInfo != null && i.TagInfo.IsColdLead)
                    .Where(i => i.CustomLeadStatus == null || i.CustomLeadStatus.MasterLeadStatusBaseId != Guid.Parse("023fcb89-037e-42f4-bcc2-647bb4e95c99"))
                    .Where(i => i.CustomLeadStatus == null || i.CustomLeadStatus.Status != "booked")
                    .Where(i => i.CustomLeadStatus == null || i.CustomLeadStatus.MasterLeadStatusBaseId != Guid.Parse("065fa6fd-a15e-4248-b469-e8596980c97a"));
                    break;
                case LeadFilterTypeMobile.Highlighted:
                    query = query.Where(i => i.TagInfo != null && i.TagInfo.IsHighlighted)
                    .Where(i => i.CustomLeadStatus == null || i.CustomLeadStatus.MasterLeadStatusBaseId != Guid.Parse("023fcb89-037e-42f4-bcc2-647bb4e95c99"))
                    .Where(i => i.CustomLeadStatus == null || i.CustomLeadStatus.Status != "booked")
                    .Where(i => i.CustomLeadStatus == null || i.CustomLeadStatus.MasterLeadStatusBaseId != Guid.Parse("065fa6fd-a15e-4248-b469-e8596980c97a"));
                    break;
                case LeadFilterTypeMobile.Active:
                    query = query.Where(i => i.CustomLeadStatus != null && !StatusesIds.Contains(i.CustomLeadStatus.BaseId ?? Guid.Empty) && !StatusesIds.Contains(i.CustomLeadStatus.Id));
                    break;
                case LeadFilterTypeMobile.AllWithNID:
                case LeadFilterTypeMobile.All:
                    query = query.Where(i => i.CustomLeadStatus != null);
                    break;
                case LeadFilterTypeMobile.BookingCancel:
                    var bookingCancelStatus = customStatuses?.FirstOrDefault(i => i.Status == "booking_cancel") ?? new();
                    query = query.Where(i => i.CustomLeadStatus != null && (i.CustomLeadStatus.BaseId == bookingCancelStatus.Id || i.CustomLeadStatus.Id == bookingCancelStatus.Id));
                    break;
                case LeadFilterTypeMobile.Invoiced:
                    var invoicestatus = customStatuses?.FirstOrDefault(i => i.Status == "invoiced") ?? new();
                    query = query.Where(i => i.CustomLeadStatus != null && (i.CustomLeadStatus.BaseId == invoicestatus.Id || i.CustomLeadStatus.Id == invoicestatus.Id));
                    break;
                //case LeadFilterTypeMobile.Invoice:
                //    invoicestatuses = customStatuses?.FirstOrDefault(i => i.Status == "invoiced") ?? new();
                //    query = query.Where(i => i.CustomLeadStatus != null && (i.CustomLeadStatus.BaseId == invoicestatus.Id || i.CustomLeadStatus.Id == invoicestatus.Id));
                //    break;
                case LeadFilterTypeMobile.Untouched:
                    query = query.Where(i => !i.IsPicked);
                    break;
                case LeadFilterTypeMobile.ExpressionOfInterest:
                    var customStatus = customStatuses?.FirstOrDefault(i => i.Status == "expression_of_interest") ?? new();
                    query = query.Where(i => i.CustomLeadStatus != null && (i.CustomLeadStatus.BaseId == customStatus.Id || i.CustomLeadStatus.Id == customStatus.Id));
                    break;
                case LeadFilterTypeMobile.MeetingDone:
                    query = query.Where(i => i.Appointments.Any(i => i.Type == AppointmentType.Meeting && i.IsDone));
                    break;
                case LeadFilterTypeMobile.SiteVisitDone:
                    query = query.Where(i => i.Appointments.Any(i => i.Type == AppointmentType.SiteVisit && i.IsDone));
                    break;
                default:
                    break;
            }

            if (request.Properties?.Any() ?? false)
            {
                var propertyNames = request.Properties.Select(i => i.ToLower());
                query = query.Where(i => i.Properties.Count > 0 && i.Properties.Any(i => propertyNames.Contains(i.Title.ToLower())));
            }
            if (request.Projects?.Any() ?? false)
            {
                var projectNames = request.Projects.Select(i => i.ToLower());
                query = query.Where(i => i.Projects.Count > 0 && i.Projects.Any(i => projectNames.Contains(i.Name.ToLower()))).AsQueryable();
            }
            if (request.NoOfBHKs != null && request.NoOfBHKs.Any())
            {
                //query = query.Where(i => i.Enquiries != null && i.Enquiries.Any(i => request.NoOfBHKs.Contains(i.NoOfBHKs)));
                query = query.Where(i => i.Enquiries != null && i.Enquiries.Any(i => i.BHKs != null && i.BHKs.Any(j => request.NoOfBHKs.Contains(j))));
            }
            if (request.Beds?.Any() ?? false)
            {
                query = query.Where(i => i.Enquiries != null && i.Enquiries.Any(i => i.Beds != null && i.Beds.Any(j => request.Beds.Contains(j))));
            }
            if (request.Baths?.Any() ?? false)
            {
                query = query.Where(i => i.Enquiries != null && i.Enquiries.Any(i => i.Baths != null && i.Baths.Any(j => request.Baths.Contains(j))));
            }
            if (request.Floors?.Any() ?? false)
            {
                query = query.Where(i => i.Enquiries != null && i.Enquiries.Any(i => i.Floors != null && i.Floors.Any(j => request.Floors.Contains(j))));
            }
            if (request.OfferTypes?.Any() ?? false)
            {
                query = query.Where(i => i.Enquiries != null && i.Enquiries.Any(i => i.OfferType != null && request.OfferTypes.Contains(i.OfferType.Value)));
            }
            if (request.Purposes?.Any() ?? false)
            {
                query = query.Where(i => i.Enquiries != null && i.Enquiries.Any(i => i.Purpose != null && request.Purposes.Contains(i.Purpose.Value)));
            }
            if (request.Furnished?.Any() ?? false)
            {
                query = query.Where(i => i.Enquiries != null && i.Enquiries.Any(i => i.Furnished != null && request.Furnished.Contains(i.Furnished.Value)));
            }
            if (request.SubSources != null && request.SubSources.Any())
            {
                request.SubSources = request.SubSources.ConvertAll(i => i.ToLower().Trim());
                query = query.Where(i => i.Enquiries != null && i.Enquiries.Any(i => request.SubSources.Contains(i.SubSource.ToLower().Trim())));
            }
            if (request.BHKTypes != null && request.BHKTypes.Any())
            {
                //query = query.Where(i => i.Enquiries != null && i.Enquiries.Any(i => request.BHKTypes.Contains(i.BHKType)));
                query = query.Where(i => i.Enquiries != null && i.Enquiries.Any(i => i.BHKTypes != null && i.BHKTypes.Any(j => request.BHKTypes.Contains(j))));
            }
            if (request.PropertyType != null && request.PropertyType.Any())
            {
                query = query.Where(i => i.Enquiries.Any(e => e.PropertyTypes.Any(i => request.PropertyType.Contains(i.BaseId ?? Guid.Empty))));
            }
            if (request.PropertySubType != null && request.PropertySubType.Any())
            {
                query = query.Where(i => i.Enquiries.Any(e => e.PropertyTypes.Any(i => request.PropertySubType.Contains(i.Id))));
            }
            if (request.StatusIds?.Any() ?? false)
            {
                var statusIds = request.StatusIds;
                query = query.Where(i => i.CustomLeadStatus != null &&
                                          (statusIds.Contains(i.CustomLeadStatus.BaseId ?? Guid.Empty) ||
                                           statusIds.Contains(i.CustomLeadStatus.Id)));

            }
            if (request.Budget != null && request.Budget.Any())
            {
                foreach (var budget in request.Budget)
                {
                    switch (budget)
                    {
                        case Budget.UpToTenLakhs:
                            query = query.Where(i => i.Enquiries != null && i.Enquiries.Any(j => j.UpperBudget <= 1000000));
                            break;
                        case Budget.TenToTwentyLakhs:
                            query = query.Where(i => i.Enquiries != null && i.Enquiries.Any(j => j.UpperBudget > 1000000 && j.UpperBudget <= 2000000));
                            break;
                        case Budget.TwentyToThirtyLakhs:
                            query = query.Where(i => i.Enquiries != null && i.Enquiries.Any(j => j.UpperBudget > 2000000 && j.UpperBudget <= 3000000));
                            break;
                        case Budget.ThirtyToFourtyLakhs:
                            query = query.Where(i => i.Enquiries != null && i.Enquiries.Any(j => j.UpperBudget > 3000000 && j.UpperBudget <= 4000000));
                            break;
                        case Budget.FourtyToFiftyLakhs:
                            query = query.Where(i => i.Enquiries != null && i.Enquiries.Any(j => j.UpperBudget > 4000000 && j.UpperBudget <= 5000000));
                            break;
                        case Budget.FiftyToOneCrore:
                            query = query.Where(i => i.Enquiries != null && i.Enquiries.Any(j => j.UpperBudget > 5000000 && j.UpperBudget <= 10000000));
                            break;
                        case Budget.MoreThanOneCrore:
                            query = query.Where(i => i.Enquiries != null && i.Enquiries.Any(j => j.UpperBudget > 10000000));
                            break;
                    }
                }
            }
            if (request.Currency != null)
            {
                query = query.Where(i => i.Enquiries.Any(e => e.Currency == request.Currency));
            }
            if (request.MinBudget != null || request.MaxBudget != null)
            {
                if (request.MinBudget != null && request.MaxBudget != null)
                {
                    query = query.Where(i => i.Enquiries.Any(i => (i.UpperBudget >= request.MinBudget && i.UpperBudget <= request.MaxBudget) || (i.LowerBudget >= request.MinBudget && i.LowerBudget <= request.MaxBudget)));
                }
                else if (request.MinBudget != null && request.MaxBudget == null)
                {
                    query = query.Where(i => i.Enquiries.Any(i => (i.UpperBudget >= request.MinBudget) || (i.LowerBudget >= request.MinBudget)));
                }
                else if (request.MinBudget == null && request.MaxBudget != null)
                {
                    query = query.Where(i => i.Enquiries.Any(i => (i.UpperBudget <= request.MaxBudget) || (i.LowerBudget <= request.MaxBudget)));
                }
            }
            if (request.BudgetFilters != null && request.BudgetFilters.Any())
            {
                foreach (var budget in request.BudgetFilters)
                {
                    query = query.Where(i => i.Enquiries != null && i.Enquiries.Any(i => i.UpperBudget >= budget.MinBudget && i.UpperBudget <= budget.MaxBudget));
                }
            }
            if (request.Locations != null && request.Locations.Any())
            {
                request.Locations = request.Locations.ConvertAll(i => Uri.UnescapeDataString(i).Replace(",", "").ToLower().Trim().Replace(" ", "")).ToList();
                query = query.Where(i => i.Enquiries.Any(e => e.Addresses.Any(j =>
                request.Locations.Contains(
                    (j.SubLocality +
                    j.Locality +
                    j.Community +
                    j.SubCommunity +
                    j.TowerName +
                    j.District +
                    j.City +
                    j.State +
                    j.Country +
                    j.PostalCode).Replace(",", "").ToLower().Trim().Replace(" ", "")))));

            }
            if (request.IntegrationAccountIds != null && request.IntegrationAccountIds.Any())
            {
                query = query.Where(i => request.IntegrationAccountIds.Contains(i.AccountId));
            }
            if (request.AgencyNames != null && request.AgencyNames.Any())
            {
                request.AgencyNames = request.AgencyNames.ConvertAll(i => i.ToLower().Trim());
                query = query.Where(i => i.Agencies.Any(i => i.Name != null && request.AgencyNames.Contains(i.Name.ToLower().Trim())));
            }
            if (request.CampaignNames != null && request.CampaignNames.Any())
            {
                request.CampaignNames = request.CampaignNames.ConvertAll(i => i.ToLower().Trim());
                query = query.Where(i => i.Campaigns.Any(i => i.Name != null && request.CampaignNames.Contains(i.Name.ToLower().Trim())));
            }
            if (request.LeadIds != null)
            {
                query = query.Where(i => request.LeadIds.Contains(i.Id));
            }
            if (request?.SerialNumbers?.Any() ?? false)
            {
                query = query.Where(i => request.SerialNumbers.Contains(i.SerialNumber));
            }
            if (request?.IsUntouched != null && request.IsUntouched != default)
            {
                query = query.Where(i => !i.IsPicked == request.IsUntouched.Value);
            }

            if (request?.BookedByIds?.Any() ?? false)
            {
                query = query.Where(i => i.BookedDetails.Any(i => request.BookedByIds.Contains(i.BookedBy.Value)));
            }
            if ((request.AssignFromIds?.Any() ?? false) && !(request?.IsWithHistory ?? false))
            {
                query = query.Where(i => i.AssignedFrom != null && request.AssignFromIds.Contains(i.AssignedFrom.Value));
            }
            if ((request?.SecondaryUsers?.Any() ?? false) && (request?.IsDualOwnershipEnabled ?? false) && !(request?.IsWithHistory ?? false))
            {
                query = query.Where(i => i.SecondaryUserId != null && request.SecondaryUsers.Contains(i.SecondaryUserId.Value));
            }
            if (request?.OriginalOwnerIds?.Any() ?? false)
            {
                query = query.Where(i => i.OriginalOwner != null && request.OriginalOwnerIds.Contains(i.OriginalOwner.Value));
            }
            if ((request?.SecondaryFromIds?.Any() ?? false) && (request?.IsDualOwnershipEnabled ?? false) && !(request?.IsWithHistory ?? false))
            {
                query = query.Where(i => i.SecondaryFromUserId != null && request.SecondaryFromIds.Contains(i.SecondaryFromUserId.Value));
            }
            if (request?.IsWithHistory ?? false)
            {
                DateTime? fromDate = null;
                DateTime? toDate = null;

                if (request.DateType.HasValue && request.FromDate.HasValue && request.FromDate.Value != default && request.ToDate.HasValue && request.ToDate.Value != default)
                {
                    fromDate = request.FromDate.Value.ConvertFromDateToUtc();
                    toDate = request.ToDate.Value.ConvertToDateToUtc();
                }
                query = query.Where(i => i.Assignments != null && i.Assignments.Any(j =>
                    (request.HistoryAssignedToIds == null || !request.HistoryAssignedToIds.Any() || (j.AssignTo != null && request.HistoryAssignedToIds.Contains(j.AssignTo ?? Guid.Empty))) &&
                    (request.AssignFromIds == null || !request.AssignFromIds.Any() || (j.AssignedFrom != null && request.AssignFromIds.Contains(j.AssignedFrom ?? Guid.Empty))) &&
                    (request.SecondaryUsers == null || !request.SecondaryUsers.Any() || (j.SecondaryAssignTo != null && request.SecondaryUsers.Contains(j.SecondaryAssignTo ?? Guid.Empty))) &&
                    (request.SecondaryFromIds == null || !request.SecondaryFromIds.Any() || (j.SecondaryAssignFrom != null && request.SecondaryFromIds.Contains(j.SecondaryAssignFrom ?? Guid.Empty))) &&
                    (request.DoneBy == null || !request.DoneBy.Any() || request.DoneBy.Contains(j.LastModifiedBy)) &&
                    (fromDate == null || toDate == null ||
                    (j.AssignmentDate.HasValue && j.AssignmentDate.Value >= fromDate.Value && j.AssignmentDate.Value <= toDate.Value))
                ));
            }
            if (request?.DoneBy?.Any() ?? false)
            {
                DateTime? fromDate = null;
                DateTime? toDate = null;

                if (request.DateType.HasValue && request.FromDate.HasValue && request.FromDate.Value != default && request.ToDate.HasValue && request.ToDate.Value != default)
                {
                    fromDate = request.FromDate.Value.ConvertFromDateToUtc();
                    toDate = request.ToDate.Value.ConvertToDateToUtc();
                }

                query = query.Where(i => i.Assignments != null && i.Assignments.Any(j =>
                    request.DoneBy.Contains(j.LastModifiedBy) &&
                    (fromDate == null || toDate == null || (j.AssignmentDate.HasValue && j.AssignmentDate.Value >= fromDate.Value && j.AssignmentDate.Value <= toDate.Value))
                ));
            }
            if (request.Cities?.Any() ?? false)
            {
                var normalizedCityNames = request.Cities.ConvertAll(i => i.ToLower().Trim().Replace(" ", ""));
                query = query.Where(i => i.Enquiries.Any(e => e.Addresses.Where(i => normalizedCityNames.Contains(i.City.ToLower().Trim().Replace(" ", ""))).Any()));
            }

            if (request.States?.Any() ?? false)
            {
                var normalizedStateNames = request.States.ConvertAll(i => i.ToLower().Trim().Replace(" ", ""));
                query = query.Where(i => i.Enquiries.Any(e => e.Addresses.Where(i => normalizedStateNames.Contains(i.State.ToLower().Trim().Replace(" ", ""))).Any()));
            }
            if (request.Countries?.Any() ?? false)
            {
                var normalizedStateNames = request.Countries.ConvertAll(i => i.ToLower().Trim().Replace(" ", ""));
                query = query.Where(i => i.Enquiries.Any(e => e.Addresses.Where(i => normalizedStateNames.Contains(i.Country.ToLower().Trim().Replace(" ", ""))).Any()));
            }
            if (request.Communities?.Any() ?? false)
            {
                var normalizedStateNames = request.Communities.ConvertAll(i => i.ToLower().Trim().Replace(" ", ""));
                query = query.Where(i => i.Enquiries.Any(e => e.Addresses.Where(i => normalizedStateNames.Contains(i.Community.ToLower().Trim().Replace(" ", ""))).Any()));
            }
            if (request.SubCommunities?.Any() ?? false)
            {
                var normalizedStateNames = request.SubCommunities.ConvertAll(i => i.ToLower().Trim().Replace(" ", ""));
                query = query.Where(i => i.Enquiries.Any(e => e.Addresses.Where(i => normalizedStateNames.Contains(i.SubCommunity.ToLower().Trim().Replace(" ", ""))).Any()));
            }
            if (request.TowerNames?.Any() ?? false)
            {
                var normalizedStateNames = request.TowerNames.ConvertAll(i => i.ToLower().Trim().Replace(" ", ""));
                query = query.Where(i => i.Enquiries.Any(e => e.Addresses.Where(i => normalizedStateNames.Contains(i.TowerName.ToLower().Trim().Replace(" ", ""))).Any()));
            }
            if (request.PostalCodes?.Any() ?? false)
            {
                var normalizedStateNames = request.PostalCodes.ConvertAll(i => i.Replace(" ", ""));
                query = query.Where(i => i.Enquiries.Any(e => e.Addresses.Where(i => normalizedStateNames.Contains(i.PostalCode.ToLower().Trim().Replace(" ", ""))).Any()));
            }
            if (!string.IsNullOrEmpty(request?.AdditionalPropertiesKey))
            {
                var key = request.AdditionalPropertiesKey;

                if (!string.IsNullOrEmpty(request.AdditionalPropertiesValue))
                {
                    var value = request.AdditionalPropertiesValue;
                    query = query
                        .Where(lead => EF.Functions.JsonContains(
                            lead.AdditionalProperties,
                            JsonConvert.SerializeObject(new Dictionary<string, string> { { key, value } })));
                }
                else
                {
                    query = query
                        .Where(lead => EF.Functions.JsonContains(
                            lead.AdditionalProperties,
                            JsonConvert.SerializeObject(new Dictionary<string, string> { { key, "" } })));
                }
            }
            if (request.ChannelPartnerNames != null && request.ChannelPartnerNames.Any())
            {
                request.ChannelPartnerNames = request.ChannelPartnerNames.ConvertAll(i => i.ToLower().Trim());
                query = query.Where(i => i.ChannelPartners.Any(i => i.FirmName != null && request.ChannelPartnerNames.Contains(i.FirmName.ToLower().Trim())));
            }
            if (request.LastModifiedByIds?.Any() ?? false)
            {
                query = query.Where(i => request.LastModifiedByIds.Contains(i.LastModifiedBy));
            }
            if (request.CreatedByIds?.Any() ?? false)
            {
                query = query.Where(i => request.CreatedByIds.Contains(i.CreatedBy));
            }

            if (request.RestoredByIds?.Any() ?? false)
            {
                query = query.Where(i => request.RestoredByIds.Contains(i.RestoredBy.Value));
            }
            if (request.ArchivedByIds?.Any() ?? false)
            {
                query = query.Where(i => request.ArchivedByIds.Contains(i.ArchivedBy.Value));
            }
            if (request.SourcingManagers?.Any() ?? false)
            {
                query = query.Where(i => request.SourcingManagers.Contains(i.SourcingManager.Value));
            }
            if (request.ClosingManagers?.Any() ?? false)
            {
                query = query.Where(i => request.ClosingManagers.Contains(i.ClosingManager.Value));
            }
            if (request.Profession != null && request.Profession.Any())
            {
                query = query.Where(i => request.Profession.Contains(i.Profession));
            }
            if (!string.IsNullOrEmpty(request?.UploadTypeName))
            {
                query = query.Where(i => i.UploadTypeName != null && i.UploadTypeName.ToLower().Contains(request.UploadTypeName.ToLower().Trim()));
            }
            if (request?.LeadType != null && request.LeadType.Any())
            {
                var predicate = PredicateBuilder.New<Lead>(false);

                if (request.LeadType.Contains(LeadType.ShowPrimeLeads))
                {
                    predicate = predicate.Or(i => i.ChildLeadsCount == 0 && i.RootId == null && i.ParentLeadId == null);
                }

                if (request.LeadType.Contains(LeadType.ShowOnlyParentLeads))
                {
                    if (request.ChildLeadsCount != null && request.ChildLeadsCount > 0)
                    {
                        predicate = predicate.Or(i => i.ChildLeadsCount == request.ChildLeadsCount);
                    }
                    else
                    {
                        predicate = predicate.Or(i => i.ChildLeadsCount > 0);
                    }
                }

                if (request.LeadType.Contains(LeadType.ShowOnlyDuplicateLeads))
                {
                    predicate = predicate.Or(i => i.ChildLeadsCount == 0 && (i.RootId != null || i.ParentLeadId != null));
                }

                query = query.Where(predicate);
            }
            if (request?.PossesionType != null && request?.PossesionType != PossesionType.None)
            {
                switch (request?.PossesionType)
                {
                    case PossesionType.UnderConstruction:
                        query = query.Where(i => i.PossesionType == PossesionType.UnderConstruction);
                        break;

                    case PossesionType.SixMonth:
                        query = query.Where(i => i.PossesionType == PossesionType.SixMonth);
                        break;

                    case PossesionType.Year:
                        query = query.Where(i => i.PossesionType == PossesionType.Year);
                        break;

                    case PossesionType.TwoYears:
                        query = query.Where(i => i.PossesionType == PossesionType.TwoYears);
                        break;

                    case PossesionType.CustomDate:
                        DateTime? tempToPossesionDate = request?.ToPossesionDate?.ConvertToDateToUtc();
                        DateTime? tempFrompossesionDate = request?.FromPossesionDate?.ConvertFromDateToUtc();
                        query = query.Where(i => i.Enquiries.Any(j => j.PossessionDate != null && j.PossessionDate >= tempFrompossesionDate.Value && j.PossessionDate <= tempToPossesionDate.Value));

                        break;
                }
            }
            if (request?.LandLine != null && request.LandLine.Any())
            {
                query = query.Where(i => i.LandLine != null && request.LandLine.Contains(i.LandLine.Trim()));
            }
            if (request?.CountryCode?.Any() ?? false)
            {
                var codes = request.CountryCode
        .Where(c => !string.IsNullOrWhiteSpace(c))
        .Select(c => c.Trim().TrimStart('+'))
        .Distinct()
        .ToList();

                Expression<Func<Lead, bool>> filter = lead => false;

                foreach (var code in codes)
                {
                    var local = code;

                    filter = filter.Or(lead =>
                        (lead.CountryCode != null && lead.CountryCode.Trim().TrimStart('+') == local) ||
                        (lead.ContactNo != null &&
                            (lead.ContactNo.StartsWith("+" + local) || lead.ContactNo.StartsWith(local)))
                    );
                }

                query = query.Where(filter);
            }
            if (request?.AltCountryCode?.Any() ?? false)
            {
                query = query.Where(i => i.AltCountryCode != null && request.AltCountryCode.Contains(i.AltCountryCode));
            }
            if (request?.GenderTypes?.Any() ?? false)
            {
                query = query.Where(i => i.Gender != null && request.GenderTypes.Contains(i.Gender.Value));
            }
            if (request?.MaritalStatuses?.Any() ?? false)
            {
                query = query.Where(i => i.MaritalStatus != null && request.MaritalStatuses.Contains(i.MaritalStatus.Value));
            }
            if (request?.DateOfBirth != null)
            {
                query = query.Where(i => i.DateOfBirth != null && i.DateOfBirth == request.DateOfBirth);
            }

            if (request?.CallDirections != null && request.CallDirections.Any() && !request.CallDirections.Contains(CallDirection.None))
            {
                query = query.Where(i =>
                    i.LeadCallLogs != null && i.LeadCallLogs.Any(j => request.CallDirections.Contains(j.CallDirection)));
            }

            if (request?.CallStatuses != null && request.CallStatuses.Any() && !request.CallStatuses.Contains(CallStatus.None))
            {
                query = query.Where(i =>
                    i.LeadCallLogs != null && i.LeadCallLogs.Any(j => request.CallStatuses.Contains(j.CallStatus)));
            }

            if (request?.UserIds != null && request.UserIds.Any())
            {
                query = query.Where(i =>
                    i.LeadCallLogs != null && i.LeadCallLogs.Any(j => request.UserIds.Contains(j.UserId)));
            }

            return query.AsQueryable();
        }
        private IQueryable<Lead> BuildQuery(GetLeadCategoryRequest request, Guid userId, IList<Guid> subIds, List<Guid>? leadHistoryIds, List<Guid>? scheduledMeetingLeadHistoryIds, List<Guid>? scheduledVisitLeadHistoryIds, List<CustomMasterLeadStatus>? customMasterLeadStatuses)
        {
            //var tenantId = _currentUser.GetTenant();
            //var isAdmin = _dapperRepository.IsAdminAsync(userId, tenantId ?? string.Empty).Result;
            var scope = _provider.CreateScope();
            var context = scope.ServiceProvider.GetRequiredService<ApplicationDbContext>();
            var reporteeIds = subIds.Where(i => i != userId && i != Guid.Empty);
            var selfWithReporteeIds = subIds.Where(i => i != Guid.Empty);
            var query = context.Leads.Where(i => !i.IsDeleted)
                .Include(i => i.TagInfo)
                .Include(i => i.CustomLeadStatus)
                .Include(i => i.Enquiries)
                    //.ThenInclude(i => i.Address)
                    .ThenInclude(i => i.Addresses)
                .Include(i => i.Enquiries)
                    .ThenInclude(i => i.PropertyType)
                .Include(i => i.Appointments)
                    .ThenInclude(i => i.Location)
                .Include(i => i.Projects)
                .Include(i => i.Properties)
                .Include(i => i.Enquiries)
                .ThenInclude(i => i.PropertyTypes)
                .AsQueryable();

            if (leadHistoryIds?.Any() ?? false)
            {
                query = query.Where(i => leadHistoryIds.Contains(i.Id));
            }
            //if (request?.MeetingOrVisitStatuses?.Any() ?? false)
            //{
            //    query = query.Where(i => (request.MeetingOrVisitStatuses.Contains(MeetingOrVisitCompletionStatus.IsMeetingDone) && i.Appointments.Any(i => i.Type == AppointmentType.Meeting && i.IsDone))
            //            || (request.MeetingOrVisitStatuses.Contains(MeetingOrVisitCompletionStatus.IsMeetingNotDone) && i.Appointments.Any(i => i.Type == AppointmentType.Meeting && !i.IsDone))
            //            || (request.MeetingOrVisitStatuses.Contains(MeetingOrVisitCompletionStatus.IsSiteVisitDone) && i.Appointments.Any(i => i.Type == AppointmentType.SiteVisit && i.IsDone))
            //            || (request.MeetingOrVisitStatuses.Contains(MeetingOrVisitCompletionStatus.IsSiteVisitNotDone) && i.Appointments.Any(i => i.Type == AppointmentType.SiteVisit && !i.IsDone)));
            //}

            //request.FromDateForMeetingOrVisit = request.FromDateForMeetingOrVisit != null ? request.FromDateForMeetingOrVisit.Value.ConvertFromDateToUtc() : null;
            //request.ToDateForMeetingOrVisit = request.ToDateForMeetingOrVisit != null ? request.ToDateForMeetingOrVisit.Value.ConvertToDateToUtc() : null;

            //if (request.FromDateForMeetingOrVisit != null && request.ToDateForMeetingOrVisit != null)
            //{
            //    query = query.Where(i => i.Appointments.Any(i => i.LastModifiedOn > request.FromDateForMeetingOrVisit.Value && i.LastModifiedOn < request.ToDateForMeetingOrVisit.Value));
            //}
            //else if (request.FromDateForMeetingOrVisit != null)
            //{
            //    query = query.Where(i => i.Appointments.Any(i => i.LastModifiedOn > request.FromDateForMeetingOrVisit.Value));
            //}
            //else if (request.ToDateForMeetingOrVisit != null)
            //{
            //    query = query.Where(i => i.Appointments.Any(i => i.LastModifiedOn < request.ToDateForMeetingOrVisit.Value));
            //}
            if (request?.MeetingOrVisitStatuses?.Any() ?? false)
            {
                DateTime? fromDateForMeetingOrVisit = request.FromDateForMeetingOrVisit != null ? request.FromDateForMeetingOrVisit.Value.ConvertFromDateToUtc() : DateTime.MinValue;
                DateTime? toDateForMeetingOrVisit = request.ToDateForMeetingOrVisit != null ? request.ToDateForMeetingOrVisit.Value.ConvertToDateToUtc() : DateTime.MaxValue;

                if (fromDateForMeetingOrVisit != null && toDateForMeetingOrVisit != null)
                {
                    query = query.Where(i => (request.MeetingOrVisitStatuses.Contains(MeetingOrVisitCompletionStatus.IsMeetingDone)
                                         && i.Appointments.Any(i => i.Type == AppointmentType.Meeting && i.IsDone && i.CreatedOn > fromDateForMeetingOrVisit && i.CreatedOn < toDateForMeetingOrVisit))
                                         ||
                                             (request.MeetingOrVisitStatuses.Contains(MeetingOrVisitCompletionStatus.IsMeetingNotDone)
                                             && i.Appointments.Any(i => i.Type == AppointmentType.Meeting && !i.IsDone && i.CreatedOn > fromDateForMeetingOrVisit && i.CreatedOn < toDateForMeetingOrVisit))
                                         ||
                                             (request.MeetingOrVisitStatuses.Contains(MeetingOrVisitCompletionStatus.IsSiteVisitDone)
                                             && i.Appointments.Any(i => i.Type == AppointmentType.SiteVisit && i.IsDone && i.CreatedOn > fromDateForMeetingOrVisit && i.CreatedOn < toDateForMeetingOrVisit))
                                         ||
                                             (request.MeetingOrVisitStatuses.Contains(MeetingOrVisitCompletionStatus.IsSiteVisitNotDone)
                                             && i.Appointments.Any(i => i.Type == AppointmentType.SiteVisit && !i.IsDone && i.CreatedOn > fromDateForMeetingOrVisit && i.CreatedOn < toDateForMeetingOrVisit)));
                }
                else if (fromDateForMeetingOrVisit != null)
                {
                    query = query.Where(i => (request.MeetingOrVisitStatuses.Contains(MeetingOrVisitCompletionStatus.IsMeetingDone)
                                         && i.Appointments.Any(i => i.Type == AppointmentType.Meeting && i.IsDone && i.CreatedOn > fromDateForMeetingOrVisit))
                                         ||
                                             (request.MeetingOrVisitStatuses.Contains(MeetingOrVisitCompletionStatus.IsMeetingNotDone)
                                             && i.Appointments.Any(i => i.Type == AppointmentType.Meeting && !i.IsDone && i.CreatedOn > fromDateForMeetingOrVisit))
                                         ||
                                             (request.MeetingOrVisitStatuses.Contains(MeetingOrVisitCompletionStatus.IsSiteVisitDone)
                                             && i.Appointments.Any(i => i.Type == AppointmentType.SiteVisit && i.IsDone && i.CreatedOn > fromDateForMeetingOrVisit))
                                         ||
                                             (request.MeetingOrVisitStatuses.Contains(MeetingOrVisitCompletionStatus.IsSiteVisitNotDone)
                                             && i.Appointments.Any(i => i.Type == AppointmentType.SiteVisit && !i.IsDone && i.CreatedOn > fromDateForMeetingOrVisit)));
                }
                else if (toDateForMeetingOrVisit != null)
                {
                    query = query.Where(i => (request.MeetingOrVisitStatuses.Contains(MeetingOrVisitCompletionStatus.IsMeetingDone)
                                         && i.Appointments.Any(i => i.Type == AppointmentType.Meeting && i.IsDone && i.CreatedOn < toDateForMeetingOrVisit.Value))
                                         ||
                                             (request.MeetingOrVisitStatuses.Contains(MeetingOrVisitCompletionStatus.IsMeetingNotDone)
                                             && i.Appointments.Any(i => i.Type == AppointmentType.Meeting && !i.IsDone && i.CreatedOn < toDateForMeetingOrVisit.Value))
                                         ||
                                             (request.MeetingOrVisitStatuses.Contains(MeetingOrVisitCompletionStatus.IsSiteVisitDone)
                                             && i.Appointments.Any(i => i.Type == AppointmentType.SiteVisit && i.IsDone && i.CreatedOn < toDateForMeetingOrVisit.Value))
                                         ||
                                             (request.MeetingOrVisitStatuses.Contains(MeetingOrVisitCompletionStatus.IsSiteVisitNotDone)
                                             && i.Appointments.Any(i => i.Type == AppointmentType.SiteVisit && !i.IsDone && i.CreatedOn < toDateForMeetingOrVisit.Value)));
                }
                else if ((request.AssignToIds?.Any() ?? false) && (request.OwnerSelection == OwnerSelectionType.Both))
                {
                    query = query.Where(i => (request.MeetingOrVisitStatuses.Contains(MeetingOrVisitCompletionStatus.IsMeetingDone)
                                                 && i.Appointments.Any(j => j.Type == AppointmentType.Meeting && j.IsDone && (j.UserId != Guid.Empty) && (subIds != null) && (subIds.Contains(i.AssignTo) || request.AssignToIds.Contains(i.SecondaryUserId.Value))))
                                            ||
                                                 (request.MeetingOrVisitStatuses.Contains(MeetingOrVisitCompletionStatus.IsMeetingNotDone)
                                                 && i.Appointments.Any(j => j.Type == AppointmentType.Meeting && !j.IsDone && (j.UserId != Guid.Empty) && (subIds != null) && (subIds.Contains(i.AssignTo) || request.AssignToIds.Contains(i.SecondaryUserId.Value))))
                                            ||
                                                 (request.MeetingOrVisitStatuses.Contains(MeetingOrVisitCompletionStatus.IsSiteVisitDone)
                                                 && i.Appointments.Any(j => j.Type == AppointmentType.SiteVisit && j.IsDone && (j.UserId != Guid.Empty) && (subIds != null) && (subIds.Contains(i.AssignTo) || request.AssignToIds.Contains(i.SecondaryUserId.Value))))
                                            ||
                                                 (request.MeetingOrVisitStatuses.Contains(MeetingOrVisitCompletionStatus.IsSiteVisitNotDone)
                                                 && i.Appointments.Any(j => j.Type == AppointmentType.SiteVisit && !j.IsDone && (j.UserId != Guid.Empty) && (subIds != null) && (subIds.Contains(i.AssignTo) || request.AssignToIds.Contains(i.SecondaryUserId.Value)))));

                }
                else if ((request.OwnerSelection == OwnerSelectionType.SecondaryOwner) && (request.AssignToIds?.Any() ?? false) )
                {
                    query = query.Where(i => (request.MeetingOrVisitStatuses.Contains(MeetingOrVisitCompletionStatus.IsMeetingDone)
                                                && i.Appointments.Any(j => j.Type == AppointmentType.Meeting && j.IsDone && (j.UserId != Guid.Empty) && request.AssignToIds.Contains(i.SecondaryUserId.Value)))
                                           ||
                                                (request.MeetingOrVisitStatuses.Contains(MeetingOrVisitCompletionStatus.IsMeetingNotDone)
                                                && i.Appointments.Any(j => j.Type == AppointmentType.Meeting && !j.IsDone && (j.UserId != Guid.Empty) && request.AssignToIds.Contains(i.SecondaryUserId.Value)))
                                           ||
                                                (request.MeetingOrVisitStatuses.Contains(MeetingOrVisitCompletionStatus.IsSiteVisitDone)
                                                && i.Appointments.Any(j => j.Type == AppointmentType.SiteVisit && j.IsDone && (j.UserId != Guid.Empty) && request.AssignToIds.Contains(i.SecondaryUserId.Value)))
                                           ||
                                                (request.MeetingOrVisitStatuses.Contains(MeetingOrVisitCompletionStatus.IsSiteVisitNotDone)
                                                && i.Appointments.Any(j => j.Type == AppointmentType.SiteVisit && !j.IsDone && (j.UserId != Guid.Empty) && request.AssignToIds.Contains(i.SecondaryUserId.Value))));

                }
                else if ((request.AssignToIds?.Any() ?? false) )
                {
                    query = query.Where(i => (request.MeetingOrVisitStatuses.Contains(MeetingOrVisitCompletionStatus.IsMeetingDone)
                                                && i.Appointments.Any(j => j.Type == AppointmentType.Meeting && j.IsDone && (j.UserId != Guid.Empty) && (subIds != null) && subIds.Contains(i.AssignTo)))
                                           ||
                                                (request.MeetingOrVisitStatuses.Contains(MeetingOrVisitCompletionStatus.IsMeetingNotDone)
                                                && i.Appointments.Any(j => j.Type == AppointmentType.Meeting && !j.IsDone && (j.UserId != Guid.Empty) && (subIds != null) && subIds.Contains(i.AssignTo)))
                                           ||
                                                (request.MeetingOrVisitStatuses.Contains(MeetingOrVisitCompletionStatus.IsSiteVisitDone)
                                                && i.Appointments.Any(j => j.Type == AppointmentType.SiteVisit && j.IsDone && (j.UserId != Guid.Empty) && (subIds != null) && subIds.Contains(i.AssignTo)))
                                           ||
                                                (request.MeetingOrVisitStatuses.Contains(MeetingOrVisitCompletionStatus.IsSiteVisitNotDone)
                                                && i.Appointments.Any(j => j.Type == AppointmentType.SiteVisit && !j.IsDone && (j.UserId != Guid.Empty) && (subIds != null) && subIds.Contains(i.AssignTo))));

                }

                else
                {
                    query = query.Where(i => (request.MeetingOrVisitStatuses.Contains(MeetingOrVisitCompletionStatus.IsMeetingDone)
                                                && i.Appointments.Any(j => j.Type == AppointmentType.Meeting && j.IsDone && (j.UserId != Guid.Empty) && (subIds != null) && subIds.Contains(j.UserId)))
                                           ||
                                                (request.MeetingOrVisitStatuses.Contains(MeetingOrVisitCompletionStatus.IsMeetingNotDone)
                                                && i.Appointments.Any(j => j.Type == AppointmentType.Meeting && !j.IsDone && (j.UserId != Guid.Empty) && (subIds != null) && subIds.Contains(j.UserId)))
                                           ||
                                                (request.MeetingOrVisitStatuses.Contains(MeetingOrVisitCompletionStatus.IsSiteVisitDone)
                                                && i.Appointments.Any(j => j.Type == AppointmentType.SiteVisit && j.IsDone && (j.UserId != Guid.Empty) && (subIds != null) && subIds.Contains(j.UserId)))
                                           ||
                                                (request.MeetingOrVisitStatuses.Contains(MeetingOrVisitCompletionStatus.IsSiteVisitNotDone)
                                                && i.Appointments.Any(j => j.Type == AppointmentType.SiteVisit && !j.IsDone && (j.UserId != Guid.Empty) && (subIds != null) && subIds.Contains(j.UserId))));
                }

            }
            switch (request.LeadVisibility)
            {
                case BaseLeadVisibility.SelfWithReportee:
                    query = query.Where(i => (selfWithReporteeIds.Contains(i.AssignTo) || selfWithReporteeIds.Contains(i.SecondaryUserId ?? Guid.Empty))).Where(i => !i.IsArchived);
                    break;
                case BaseLeadVisibility.Self:
                    query = query.Where(i => (i.AssignTo == userId || i.SecondaryUserId == userId)).Where(i => !i.IsArchived);
                    break;
                case BaseLeadVisibility.Reportee:
                    query = query.Where(i => (reporteeIds.Contains(i.AssignTo) || reporteeIds.Contains(i.SecondaryUserId ?? Guid.Empty))).Where(i => !i.IsArchived);
                    break;
                case BaseLeadVisibility.UnassignLead:
                    query = query.Where(i => i.AssignTo == Guid.Empty).Where(i => !i.IsArchived);
                    break;
                case BaseLeadVisibility.DeletedLeads:
                    query = query.Where(i => i.IsArchived && selfWithReporteeIds.Contains(i.AssignTo));
                    break;
                default:
                    break;
            }
            if (request.EnquiredFor != null && request.EnquiredFor.Any())
            {
                //query = query.Where(i => i.Enquiries != null && i.Enquiries.Any(i => request.EnquiredFor.Contains(i.EnquiredFor)));
                query = query.Where(i => i.Enquiries != null && i.Enquiries.Any(i => i.EnquiryTypes != null && i.EnquiryTypes.Any(j => request.EnquiredFor.Contains(j))));
            }
            //if (request.EnquiredFor.HasValue)
            //{
            //    query = query.Where(i => i.Enquiries != null && i.Enquiries.Count > 0 && i.Enquiries.Any(i => i.IsPrimary && i.EnquiredFor == request.EnquiredFor.Value));
            //}
            if (request.DateType.HasValue && ((request.FromDate.HasValue && request.FromDate.Value != default) || (request.ToDate.HasValue && request.ToDate.Value != default)))
            {
                DateTime? fromDate = request.FromDate.HasValue ? request.FromDate.Value.ConvertFromDateToUtc() : DateTime.MinValue;
                DateTime? toDate = request.ToDate.HasValue ? request.ToDate.Value.ConvertToDateToUtc() : DateTime.MaxValue;
                switch (request.DateType)
                {
                    case DateType.ReceivedDate:
                        query = query.Where(i => i.CreatedOn >= fromDate && i.CreatedOn <= toDate);
                        break;
                    case DateType.ScheduledDate:
                        query = query.Where(i => i.ScheduledDate != null && i.ScheduledDate >= fromDate && i.ScheduledDate <= toDate);
                        break;
                    case DateType.ModifiedDate:
                        query = query.Where(i => i.LastModifiedOn != null && i.LastModifiedOn >= fromDate && i.LastModifiedOn <= toDate);
                        break;
                    case DateType.DeletedDate:
                        query = query.Where(i => i.ArchivedOn >= fromDate && i.ArchivedOn < toDate.Value);
                        break;
                    default:
                        break;
                }
            }

            if (request.Source?.Any() ?? false)
            {
                query = query.Where(i => i.Enquiries != null && i.Enquiries.Any(i => request.Source.Contains(i.LeadSource)));
            }
            var ScheduledStatus = (customMasterLeadStatuses?.Where(i => i.Status is "site_visit_scheduled" or "callback" or "meeting_scheduled"))?.ToList() ?? new();
            var schuduledStatusIds = ScheduledStatus.Select(i => i.Id).ToList();
            TimeSpan userUtcOffset = request.BaseUTcOffset ?? TimeZoneInfo.Local.GetUtcOffset(DateTime.UtcNow);
            DateTime today = DateTime.UtcNow.Add(userUtcOffset).Date;
            switch (request.FilterType)
            {
                case LeadFilterTypeMobile.New:
                    query = query.Where(i => i.CustomLeadStatus != null && i.CustomLeadStatus.Status == "new");
                    break;
                #region ScheduleDate based categories
                case LeadFilterTypeMobile.ScheduleToday:
                    query = query.Where(i => ((i.ScheduledDate ?? DateTime.MinValue) + userUtcOffset) >= today && ((i.ScheduledDate ?? DateTime.MinValue) + userUtcOffset) < today.AddDays(1))
                    .Where(i => i.CustomLeadStatus == null || schuduledStatusIds.Contains(i.CustomLeadStatus.BaseId ?? Guid.Empty) || schuduledStatusIds.Contains(i.CustomLeadStatus.Id)); ;
                    break;
                case LeadFilterTypeMobile.Overdue:
                    query = query.Where(i => ((i.ScheduledDate ?? DateTime.MinValue) + userUtcOffset) < today)
                    .Where(i => i.CustomLeadStatus != null && (schuduledStatusIds.Contains(i.CustomLeadStatus.BaseId ?? Guid.Empty) || schuduledStatusIds.Contains(i.CustomLeadStatus.Id)));
                    break;
                case LeadFilterTypeMobile.ScheduledTomorrow:
                    query = query.Where(i => ((i.ScheduledDate ?? DateTime.MinValue) + userUtcOffset) >= today.AddDays(1) && ((i.ScheduledDate ?? DateTime.MinValue) + userUtcOffset) < today.AddDays(2))
                    .Where(i => i.CustomLeadStatus != null && (schuduledStatusIds.Contains(i.CustomLeadStatus.BaseId ?? Guid.Empty) || schuduledStatusIds.Contains(i.CustomLeadStatus.Id)));
                    break;
                case LeadFilterTypeMobile.UpcomingSchedules:
                    query = query.Where(i => ((i.ScheduledDate ?? DateTime.MinValue) + userUtcOffset) >= today.AddDays(1))
                    .Where(i => i.CustomLeadStatus == null || schuduledStatusIds.Contains(i.CustomLeadStatus.BaseId ?? Guid.Empty) || schuduledStatusIds.Contains(i.CustomLeadStatus.Id));
                    break;
                #endregion
                case LeadFilterTypeMobile.Pending:
                    query = query.Where(i => i.CustomLeadStatus != null && i.CustomLeadStatus.Status == "pending");
                    break;
                case LeadFilterTypeMobile.NotInterested:
                    var notIntrestedId = customMasterLeadStatuses?.FirstOrDefault(i => i.Status == "not_interested")?.Id ?? Guid.Empty;
                    query = query.Where(i => i.CustomLeadStatus != null && i.CustomLeadStatus.BaseId == notIntrestedId);
                    break;
                case LeadFilterTypeMobile.CallBack:
                    var callbackId = customMasterLeadStatuses?.FirstOrDefault(i => i.Status == "callback")?.Id ?? Guid.Empty;
                    query = query.Where(i => i.CustomLeadStatus != null && i.CustomLeadStatus.BaseId == callbackId);
                    break;
                case LeadFilterTypeMobile.SiteVisitScheduled:
                    query = query.Where(i => i.CustomLeadStatus != null && (i.CustomLeadStatus.Status == "site_visit_scheduled" || i.CustomLeadStatus.MasterLeadStatusBaseId.ToString() == "59647294-09d6-44a2-a346-9de5ba829e04"));
                    //query = query.Where(i => i.Status != null && i.Status.Status == "site_visit_scheduled");
                    //if (scheduledVisitLeadHistoryIds?.Any() ?? false)
                    //{
                    //    var dNStatuses = new List<string>() { "not_interested", "dropped" };
                    //    var dNStatusesIds = new List<Guid?>() { Guid.Parse("065fa6fd-a15e-4248-b469-e8596980c97a"), Guid.Parse("023fcb89-037e-42f4-bcc2-647bb4e95c99") };
                    //    query = query.Where(i => scheduledVisitLeadHistoryIds.Contains(i.Id) && i.Status != null && !dNStatuses.Contains(i.Status.Status) && !dNStatusesIds.Contains(i.Status.BaseId));
                    //}
                    break;
                case LeadFilterTypeMobile.ScheduledMeeting:
                    query = query.Where(i => i.CustomLeadStatus != null && (i.CustomLeadStatus.Status == "meeting_scheduled" || i.CustomLeadStatus.MasterLeadStatusBaseId.ToString() == "99a7f794-9046-4a9d-b7e2-e0a2196b98dd"));
                    //query = query.Where(i => i.Status != null && i.Status.Status == "meeting_scheduled");
                    //if (scheduledMeetingLeadHistoryIds?.Any() ?? false)
                    //{
                    //    var dNStatuses = new List<string>() { "not_interested", "dropped" };
                    //    var dNStatusesIds = new List<Guid?>() { Guid.Parse("065fa6fd-a15e-4248-b469-e8596980c97a"), Guid.Parse("023fcb89-037e-42f4-bcc2-647bb4e95c99") };
                    //    query = query.Where(i => scheduledMeetingLeadHistoryIds.Contains(i.Id) && i.Status != null && !dNStatuses.Contains(i.Status.Status) && !dNStatusesIds.Contains(i.Status.BaseId));
                    //}
                    break;
                case LeadFilterTypeMobile.UnassignLeads:
                    query = query.Where(i => i.AssignTo == Guid.Empty);
                    break;
                case LeadFilterTypeMobile.Booked:
                    query = query.Where(i => i.CustomLeadStatus != null && i.CustomLeadStatus.Status == "booked");
                    break;
                case LeadFilterTypeMobile.Dropped:
                    var droppedId = customMasterLeadStatuses?.FirstOrDefault(i => i.Status == "dropped")?.Id ?? Guid.Empty;
                    query = query.Where(i => i.CustomLeadStatus != null && i.CustomLeadStatus.BaseId == droppedId);
                    break;
                case LeadFilterTypeMobile.Escalated:
                    query = query.Where(i => i.TagInfo != null && i.TagInfo.IsEscalated)
                    .Where(i => i.CustomLeadStatus == null || i.CustomLeadStatus.MasterLeadStatusBaseId != Guid.Parse("023fcb89-037e-42f4-bcc2-647bb4e95c99"))
                    .Where(i => i.CustomLeadStatus == null || i.CustomLeadStatus.Status != "booked")
                    .Where(i => i.CustomLeadStatus == null || i.CustomLeadStatus.MasterLeadStatusBaseId != Guid.Parse("065fa6fd-a15e-4248-b469-e8596980c97a"));

                    break;
                case LeadFilterTypeMobile.HotLeads:
                    query = query.Where(i => i.TagInfo != null && i.TagInfo.IsHotLead)
                    .Where(i => i.CustomLeadStatus == null || i.CustomLeadStatus.MasterLeadStatusBaseId != Guid.Parse("023fcb89-037e-42f4-bcc2-647bb4e95c99"))
                    .Where(i => i.CustomLeadStatus == null || i.CustomLeadStatus.Status != "booked")
                    .Where(i => i.CustomLeadStatus == null || i.CustomLeadStatus.MasterLeadStatusBaseId != Guid.Parse("065fa6fd-a15e-4248-b469-e8596980c97a"));
                    break;
                case LeadFilterTypeMobile.AboutToConvert:
                    query = query.Where(i => i.TagInfo != null && i.TagInfo.IsAboutToConvert)
                    .Where(i => i.CustomLeadStatus == null || i.CustomLeadStatus.MasterLeadStatusBaseId != Guid.Parse("023fcb89-037e-42f4-bcc2-647bb4e95c99"))
                    .Where(i => i.CustomLeadStatus == null || i.CustomLeadStatus.Status != "booked")
                    .Where(i => i.CustomLeadStatus == null || i.CustomLeadStatus.MasterLeadStatusBaseId != Guid.Parse("065fa6fd-a15e-4248-b469-e8596980c97a"));
                    break;
                case LeadFilterTypeMobile.WarmLeads:
                    query = query.Where(i => i.TagInfo != null && i.TagInfo.IsWarmLead)
                    .Where(i => i.CustomLeadStatus == null || i.CustomLeadStatus.MasterLeadStatusBaseId != Guid.Parse("023fcb89-037e-42f4-bcc2-647bb4e95c99"))
                    .Where(i => i.CustomLeadStatus == null || i.CustomLeadStatus.Status != "booked")
                    .Where(i => i.CustomLeadStatus == null || i.CustomLeadStatus.MasterLeadStatusBaseId != Guid.Parse("065fa6fd-a15e-4248-b469-e8596980c97a"));
                    break;
                case LeadFilterTypeMobile.ColdLead:
                    query = query.Where(i => i.TagInfo != null && i.TagInfo.IsColdLead)
                    .Where(i => i.CustomLeadStatus == null || i.CustomLeadStatus.MasterLeadStatusBaseId != Guid.Parse("023fcb89-037e-42f4-bcc2-647bb4e95c99"))
                    .Where(i => i.CustomLeadStatus == null || i.CustomLeadStatus.Status != "booked")
                    .Where(i => i.CustomLeadStatus == null || i.CustomLeadStatus.MasterLeadStatusBaseId != Guid.Parse("065fa6fd-a15e-4248-b469-e8596980c97a"));
                    break;
                case LeadFilterTypeMobile.Highlighted:
                    query = query.Where(i => i.TagInfo != null && i.TagInfo.IsHighlighted)
                    .Where(i => i.CustomLeadStatus == null || i.CustomLeadStatus.MasterLeadStatusBaseId != Guid.Parse("023fcb89-037e-42f4-bcc2-647bb4e95c99"))
                    .Where(i => i.CustomLeadStatus == null || i.CustomLeadStatus.Status != "booked")
                    .Where(i => i.CustomLeadStatus == null || i.CustomLeadStatus.MasterLeadStatusBaseId != Guid.Parse("065fa6fd-a15e-4248-b469-e8596980c97a"));
                    break;
                case LeadFilterTypeMobile.All:
                    var statuses = new List<string>() { "not_interested", "dropped", "booked" };
                    var StatusesIds = new List<Guid?>() { Guid.Parse("065fa6fd-a15e-4248-b469-e8596980c97a"), Guid.Parse("023fcb89-037e-42f4-bcc2-647bb4e95c99") };
                    query = query.Where(i => i.CustomLeadStatus != null && !statuses.Contains(i.CustomLeadStatus.Status) && !StatusesIds.Contains(i.CustomLeadStatus.MasterLeadStatusBaseId));
                    break;
                case LeadFilterTypeMobile.ExpressionOfInterest:
                    query = query.Where(i => i.CustomLeadStatus != null && i.CustomLeadStatus.Status == "expression_of_interest");
                    break;
                case LeadFilterTypeMobile.MeetingDone:
                    query = query.Where(i => i.Appointments.Any(i => i.Type == AppointmentType.Meeting && i.IsDone));
                    break;
                case LeadFilterTypeMobile.SiteVisitDone:
                    query = query.Where(i => i.Appointments.Any(i => i.Type == AppointmentType.SiteVisit && i.IsDone));
                    break;
                default:
                    break;
            }
            //if ((request?.MinBudgets != null && request?.MaxBudgets != null))
            //{
            //    query = query.Where(i => i.Enquiries != null && i.Enquiries.Any(i => i.UpperBudget >= request.MinBudgets && i.UpperBudget <= request.MaxBudgets));
            //}
            //else if (request?.MinBudgets != null)
            //{
            //    query = query.Where(i => i.Enquiries != null && i.Enquiries.Any(i => i.UpperBudget >= request.MinBudgets));
            //}
            //else if (request?.MaxBudgets != null)
            //{
            //    request.MinBudgets ??= 0;
            //    query = query.Where(i => i.Enquiries != null && i.Enquiries.Any(i => i.UpperBudget >= request.MinBudgets && i.UpperBudget <= request.MaxBudgets));
            //}

            if (request.AssignToIds?.Any() ?? false)
            {
                query = query.Where(i => request.AssignToIds.Contains(i.AssignTo));
            }
            if (request.Properties?.Any() ?? false)
            {
                var propertyNames = request.Properties.Select(i => i.ToLower());
                query = query.Where(i => i.Properties.Count > 0 && i.Properties.Any(i => propertyNames.Contains(i.Title.ToLower())));
            }
            if (request.Projects?.Any() ?? false)
            {
                var projectNames = request.Projects.Select(i => i.ToLower());
                query = query.Where(i => i.Projects.Count > 0 && i.Projects.Any(i => projectNames.Contains(i.Name.ToLower()))).AsQueryable();
            }
            if (request.NoOfBHKs != null && request.NoOfBHKs.Any())
            {
                //query = query.Where(i => i.Enquiries != null && i.Enquiries.Any(i => request.NoOfBHKs.Contains(i.NoOfBHKs)));
                query = query.Where(i => i.Enquiries != null && i.Enquiries.Any(i => i.BHKs != null && i.BHKs.Any(j => request.NoOfBHKs.Contains(j))));
            }
            if (request.Beds?.Any() ?? false)
            {
                query = query.Where(i => i.Enquiries != null && i.Enquiries.Any(i => i.Beds != null && i.Beds.Any(j => request.Beds.Contains(j))));
            }
            if (request.Baths?.Any() ?? false)
            {
                query = query.Where(i => i.Enquiries != null && i.Enquiries.Any(i => i.Baths != null && i.Baths.Any(j => request.Baths.Contains(j))));
            }
            if (request.Floors?.Any() ?? false)
            {
                query = query.Where(i => i.Enquiries != null && i.Enquiries.Any(i => i.Floors != null && i.Floors.Any(j => request.Floors.Contains(j))));
            }
            if (request.OfferTypes?.Any() ?? false)
            {
                query = query.Where(i => i.Enquiries != null && i.Enquiries.Any(i => i.OfferType != null && request.OfferTypes.Contains(i.OfferType.Value)));
            }
            if (request.Purposes?.Any() ?? false)
            {
                query = query.Where(i => i.Enquiries != null && i.Enquiries.Any(i => i.Purpose != null && request.Purposes.Contains(i.Purpose.Value)));
            }
            if (request.Furnished?.Any() ?? false)
            {
                query = query.Where(i => i.Enquiries != null && i.Enquiries.Any(i => i.Furnished != null && request.Furnished.Contains(i.Furnished.Value)));
            }
            if (request.BHKTypes != null && request.BHKTypes.Any())
            {
                //query = query.Where(i => i.Enquiries != null && i.Enquiries.Any(i => request.BHKTypes.Contains(i.BHKType)));
                query = query.Where(i => i.Enquiries != null && i.Enquiries.Any(i => i.BHKTypes != null && i.BHKTypes.Any(j => request.BHKTypes.Contains(j))));
            }
            if (request.PropertyType != null && request.PropertyType.Any())
            {
                query = query.Where(i => i.Enquiries.Any(e => e.PropertyTypes.Any(i => request.PropertyType.Contains(i.BaseId ?? Guid.Empty))));
            }
            if (request.PropertySubType != null && request.PropertySubType.Any())
            {
                query = query.Where(i => i.Enquiries.Any(e => e.PropertyTypes.Any(i => request.PropertySubType.Contains(i.Id))));
            }
            //if (request.StatusIds != null && request.StatusIds.Any())
            //{
            //    query = query.Where(i => i.Status != null && request.StatusIds.Contains(i.Status.Id));
            //}
            if (request.Budget != null && request.Budget.Any())
            {
                foreach (var budget in request.Budget)
                {
                    switch (budget)
                    {
                        case Budget.UpToTenLakhs:
                            query = query.Where(i => i.Enquiries != null && i.Enquiries.Any(j => j.UpperBudget <= 1000000));
                            break;
                        case Budget.TenToTwentyLakhs:
                            query = query.Where(i => i.Enquiries != null && i.Enquiries.Any(j => j.UpperBudget > 1000000 && j.UpperBudget <= 2000000));
                            break;
                        case Budget.TwentyToThirtyLakhs:
                            query = query.Where(i => i.Enquiries != null && i.Enquiries.Any(j => j.UpperBudget > 2000000 && j.UpperBudget <= 3000000));
                            break;
                        case Budget.ThirtyToFourtyLakhs:
                            query = query.Where(i => i.Enquiries != null && i.Enquiries.Any(j => j.UpperBudget > 3000000 && j.UpperBudget <= 4000000));
                            break;
                        case Budget.FourtyToFiftyLakhs:
                            query = query.Where(i => i.Enquiries != null && i.Enquiries.Any(j => j.UpperBudget > 4000000 && j.UpperBudget <= 5000000));
                            break;
                        case Budget.FiftyToOneCrore:
                            query = query.Where(i => i.Enquiries != null && i.Enquiries.Any(j => j.UpperBudget > 5000000 && j.UpperBudget <= 10000000));
                            break;
                        case Budget.MoreThanOneCrore:
                            query = query.Where(i => i.Enquiries != null && i.Enquiries.Any(j => j.UpperBudget > 10000000));
                            break;
                    }
                }
            }
            //if ((request.MinBudgets != null && request.MinBudgets.Any()) && (request.MaxBudgets != null && request.MaxBudgets.Any()))
            //{
            //    var index = 0;
            //    if (request.MinBudgets.Count == request.MaxBudgets.Count)
            //    {
            //        foreach (var minBudget in request.MinBudgets)
            //        {
            //            var maxbudget = request.MaxBudgets[index];
            //            query = query.Where(i => i.Enquiries != null && i.Enquiries.Any(i => i.UpperBudget >= minBudget && i.UpperBudget <= maxbudget));
            //            index++;
            //        }
            //    }
            //}
            if (request.BudgetFilters != null && request.BudgetFilters.Any())
            {
                foreach (var budget in request.BudgetFilters)
                {
                    query = query.Where(i => i.Enquiries != null && i.Enquiries.Any(i => i.UpperBudget >= budget.MinBudget && i.UpperBudget <= budget.MaxBudget));

                }
            }
            if (request.Locations != null && request.Locations.Any())
            {
                request.Locations = request.Locations.ConvertAll(i => Uri.UnescapeDataString(i).Replace(",", "").ToLower().Trim().Replace(" ", "")).ToList();
                query = query.Where(i => i.Enquiries.Any(e => e.Addresses.Any(j =>
                request.Locations.Contains(
                    (j.SubLocality +
                    j.Locality +
                    j.Community +
                    j.SubCommunity +
                    j.TowerName +
                    j.District +
                    j.City +
                    j.State +
                    j.Country +
                    j.PostalCode).Replace(",", "").ToLower().Trim().Replace(" ", "")))));

            }
            if (request.Designations?.Any() ?? false)
            {
                request.Designations = request.Designations.ConvertAll(i => i.ToLower().Trim());
                query = query.Where(i => request.Designations.Contains(i.Designation.ToLower().Trim()));
            }
            if (!string.IsNullOrEmpty(request?.Designation))
            {
                query = query.Where(i => i.Designation.ToLower().Contains(request.Designation.ToLower().Trim()));
            }
            if (request.Cities?.Any() ?? false)
            {
                var normalizedCityNames = request.Cities.ConvertAll(i => i.ToLower().Trim().Replace(" ", ""));
                query = query.Where(i => i.Enquiries.Any(e => e.Addresses.Where(i => normalizedCityNames.Contains(i.City.ToLower().Trim().Replace(" ", ""))).Any()));
            }

            if (request.States?.Any() ?? false)
            {
                var normalizedStateNames = request.States.ConvertAll(i => i.ToLower().Trim().Replace(" ", ""));
                query = query.Where(i => i.Enquiries.Any(e => e.Addresses.Where(i => normalizedStateNames.Contains(i.State.ToLower().Trim().Replace(" ", ""))).Any()));
            }
            if (request.Countries?.Any() ?? false)
            {
                var normalizedStateNames = request.Countries.ConvertAll(i => i.ToLower().Trim().Replace(" ", ""));
                query = query.Where(i => i.Enquiries.Any(e => e.Addresses.Where(i => normalizedStateNames.Contains(i.Country.ToLower().Trim().Replace(" ", ""))).Any()));
            }
            if (request.Communities?.Any() ?? false)
            {
                var normalizedStateNames = request.Communities.ConvertAll(i => i.ToLower().Trim().Replace(" ", ""));
                query = query.Where(i => i.Enquiries.Any(e => e.Addresses.Where(i => normalizedStateNames.Contains(i.Community.ToLower().Trim().Replace(" ", ""))).Any()));
            }
            if (request.SubCommunities?.Any() ?? false)
            {
                var normalizedStateNames = request.SubCommunities.ConvertAll(i => i.ToLower().Trim().Replace(" ", ""));
                query = query.Where(i => i.Enquiries.Any(e => e.Addresses.Where(i => normalizedStateNames.Contains(i.SubCommunity.ToLower().Trim().Replace(" ", ""))).Any()));
            }
            if (request.TowerNames?.Any() ?? false)
            {
                var normalizedStateNames = request.TowerNames.ConvertAll(i => i.ToLower().Trim().Replace(" ", ""));
                query = query.Where(i => i.Enquiries.Any(e => e.Addresses.Where(i => normalizedStateNames.Contains(i.TowerName.ToLower().Trim().Replace(" ", ""))).Any()));
            }
            if (request.PostalCodes?.Any() ?? false)
            {
                var normalizedStateNames = request.PostalCodes.ConvertAll(i => i.Replace(" ", ""));
                query = query.Where(i => i.Enquiries.Any(e => e.Addresses.Where(i => normalizedStateNames.Contains(i.PostalCode.ToLower().Trim().Replace(" ", ""))).Any()));
            }
            if (!string.IsNullOrEmpty(request.ReferralEmail))
            {
                query = query.Where(i => (i.ReferralEmail.ToLower().Trim().Contains(request.ReferralEmail.ToLower().Trim())));
            }
            if (request.BuiltUpArea != null && request.BuiltUpAreaUnitId != default && request.BuiltUpAreaUnitId != Guid.Empty)
            {
                query = query.Where(i => i.Enquiries.Any(i => i.BuiltUpArea == request.BuiltUpArea && i.BuiltUpAreaUnitId == request.BuiltUpAreaUnitId));
            }
            else if (request.BuiltUpArea != null)
            {
                query = query.Where(i => i.Enquiries.Any(i => i.BuiltUpArea == request.BuiltUpArea || i.BuiltUpAreaInSqMtr == request.BuiltUpArea));
            }
            if (request.SaleableArea != null && request.SaleableAreaUnitId != default && request.SaleableAreaUnitId != Guid.Empty)
            {
                query = query.Where(i => i.Enquiries.Any(i => i.SaleableArea == request.SaleableArea && i.SaleableAreaUnitId == request.SaleableAreaUnitId));
            }
            else if (request.SaleableArea != null)
            {
                query = query.Where(i => i.Enquiries.Any(i => i.SaleableArea == request.SaleableArea || i.SaleableAreaInSqMtr == request.SaleableArea));
            }
            if (request.NetArea != null && request.NetAreaUnitId != default && request.NetAreaUnitId != Guid.Empty)
            {
                query = query.Where(i => i.Enquiries.Any(i => i.NetArea == request.NetArea && i.NetAreaUnitId == request.NetAreaUnitId));
            }
            else if (request.NetArea != null)
            {
                query = query.Where(i => i.Enquiries.Any(i => i.NetArea == request.NetArea || i.NetAreaInSqMtr == request.NetArea));
            }

            if (request.PropertyArea != null && request.PropertyAreaUnitId != default && request.PropertyAreaUnitId != Guid.Empty)
            {
                query = query.Where(i => i.Enquiries.Any(i => i.PropertyArea == request.PropertyArea && i.PropertyAreaUnitId == request.PropertyAreaUnitId));
            }
            else if (request.PropertyArea != null)
            {
                query = query.Where(i => i.Enquiries.Any(i => i.PropertyArea == request.PropertyArea || i.PropertyAreaInSqMtr == request.PropertyArea));
            }
            if ((request.MinCarpetArea != null || request.MaxCarpetArea != null) && request.CarpetAreaUnitId != default)
            {
                query = query.Where(i => i.Enquiries.Any(e =>
                    e.CarpetAreaUnitId == request.CarpetAreaUnitId &&
                    (request.MinCarpetArea == null || e.CarpetArea >= request.MinCarpetArea) &&
                    (request.MaxCarpetArea == null || e.CarpetArea <= request.MaxCarpetArea)));
            }
            else if (request.MinCarpetArea != null || request.MaxCarpetArea != null)
            {
                query = query.Where(i => i.Enquiries.Any(e =>
                    (request.MinCarpetArea == null || e.CarpetArea >= request.MinCarpetArea ) &&
                    (request.MaxCarpetArea == null || e.CarpetArea <= request.MaxCarpetArea )));
            }
            if ((request.MinBuiltUpArea != null || request.MaxBuiltUpArea != null) && request.BuiltUpAreaUnitId != default && request.BuiltUpAreaUnitId != Guid.Empty)
            {
                query = query.Where(i => i.Enquiries.Any(e =>
                    e.BuiltUpAreaUnitId == request.BuiltUpAreaUnitId &&
                    (request.MinBuiltUpArea == null || e.BuiltUpArea >= request.MinBuiltUpArea) &&
                    (request.MaxBuiltUpArea == null || e.BuiltUpArea <= request.MaxBuiltUpArea)));
            }
            else if (request.MinBuiltUpArea != null || request.MaxBuiltUpArea != null)
            {
                query = query.Where(i => i.Enquiries.Any(e =>
                    (request.MinBuiltUpArea == null || e.BuiltUpArea >= request.MinBuiltUpArea ) &&
                    (request.MaxBuiltUpArea == null || e.BuiltUpArea <= request.MaxBuiltUpArea )));
            }
            if ((request.MinSaleableArea != null || request.MaxSaleableArea != null) && request.SaleableAreaUnitId != default && request.SaleableAreaUnitId != Guid.Empty)
            {
                query = query.Where(i => i.Enquiries.Any(e =>
                    e.SaleableAreaUnitId == request.SaleableAreaUnitId &&
                    (request.MinSaleableArea == null || e.SaleableArea >= request.MinSaleableArea) &&
                    (request.MaxSaleableArea == null || e.SaleableArea <= request.MaxSaleableArea)));
            }
            else if (request.MinSaleableArea != null || request.MaxSaleableArea != null)
            {
                query = query.Where(i => i.Enquiries.Any(e =>
                    (request.MinSaleableArea == null || e.SaleableArea >= request.MinSaleableArea ) &&
                    (request.MaxSaleableArea == null || e.SaleableArea <= request.MaxSaleableArea )));
            }
            if ((request.MinPropertyArea != null || request.MaxPropertyArea != null) && request.PropertyAreaUnitId != default && request.PropertyAreaUnitId != Guid.Empty)
            {
                query = query.Where(i => i.Enquiries.Any(e =>
                    e.PropertyAreaUnitId == request.PropertyAreaUnitId &&
                    (request.MinPropertyArea == null || e.PropertyArea >= request.MinPropertyArea) &&
                    (request.MaxPropertyArea == null || e.PropertyArea <= request.MaxPropertyArea)));
            }
            else if (request.MinPropertyArea != null || request.MaxPropertyArea != null)
            {
                query = query.Where(i => i.Enquiries.Any(e =>
                    (request.MinPropertyArea == null || e.PropertyArea >= request.MinPropertyArea ) &&
                    (request.MaxPropertyArea == null || e.PropertyArea <= request.MaxPropertyArea )));
            }

            if ((request.MinNetArea != null || request.MaxNetArea != null) && request.NetAreaUnitId != default && request.NetAreaUnitId != Guid.Empty)
            {
                query = query.Where(i => i.Enquiries.Any(e =>
                    e.NetAreaUnitId == request.NetAreaUnitId &&
                    (request.MinNetArea == null || e.NetArea >= request.MinNetArea) &&
                    (request.MaxNetArea == null || e.NetArea <= request.MaxNetArea)));
            }
            else if (request.MinNetArea != null || request.MaxNetArea != null)
            {
                query = query.Where(i => i.Enquiries.Any(e =>
                    (request.MinNetArea == null || e.NetArea >= request.MinNetArea ) &&
                    (request.MaxNetArea == null || e.NetArea <= request.MaxNetArea )));
            }
            if (!string.IsNullOrEmpty(request.UnitName))
            {
                query = query.Where(i => i.Enquiries.Any(e => e.UnitName != null && e.UnitName.ToLower().Trim() == request.UnitName.ToLower().Trim()));

            }
            if (request?.UnitNames?.Any() ?? false)
            {
                var unitnames = request.UnitNames.ConvertAll(i => i.ToLower());
                query = query.Where(i => i.Enquiries.Count > 0 && i.Enquiries.Any(i => unitnames.Contains(i.UnitName.ToLower()))).AsQueryable();
            }
            if (request?.Nationality?.Any() ?? false)
            {
                var nationality = request.Nationality.ConvertAll(i => i.ToLower());
                query = query.Where(i => i.Nationality != null && nationality.Contains(i.Nationality.ToLower())).AsQueryable();
            }

            if (request?.ClusterName?.Any() ?? false)
            {
                var ClusterName = request.ClusterName.ConvertAll(i => i.ToLower());
                query = query.Where(i => i.Enquiries.Count > 0 && i.Enquiries.Any(i => ClusterName.Contains(i.ClusterName.ToLower()))).AsQueryable();
            }
            
            if (request?.PossesionType != null && request?.PossesionType != PossesionType.None)
            {

                switch (request?.PossesionType)
                {
                    case PossesionType.UnderConstruction:
                        query=query.Where(i => i.PossesionType == PossesionType.UnderConstruction);
                        break;

                    case PossesionType.SixMonth:
                        query = query.Where(i => i.PossesionType == PossesionType.SixMonth);
                        break;

                    case PossesionType.Year:
                        query = query.Where(i => i.PossesionType == PossesionType.Year);
                        break;

                    case PossesionType.TwoYears:
                        query = query.Where(i => i.PossesionType == PossesionType.TwoYears);
                        break;

                    case PossesionType.CustomDate:
                        DateTime? tempToPossesionDate = request?.ToPossesionDate?.ConvertToDateToUtc();
                        DateTime? tempFrompossesionDate = request?.FromPossesionDate?.ConvertFromDateToUtc();
                        query = query.Where(i => i.Enquiries.Any(j => j.PossessionDate != null && j.PossessionDate >= tempFrompossesionDate.Value && j.PossessionDate <= tempToPossesionDate.Value));

                        break;
                }

            }
            if (request?.LandLine != null && request.LandLine.Any())
            {
                query = query.Where(i => i.LandLine != null && request.LandLine.Contains(i.LandLine.Trim()));
            }
            if (request?.CountryCode?.Any() ?? false)
            {
                var codes = request.CountryCode
        .Where(c => !string.IsNullOrWhiteSpace(c))
        .Select(c => c.Trim().TrimStart('+'))
        .Distinct()
        .ToList();

                Expression<Func<Lead, bool>> filter = lead => false;

                foreach (var code in codes)
                {
                    var local = code;

                    filter = filter.Or(lead =>
                        (lead.CountryCode != null && lead.CountryCode.Trim().TrimStart('+') == local) ||
                        (lead.ContactNo != null &&
                            (lead.ContactNo.StartsWith("+" + local) || lead.ContactNo.StartsWith(local)))
                    );
                }

                query = query.Where(filter);
            }
            if (request?.AltCountryCode?.Any() ?? false)
            {
                query = query.Where(i => i.AltCountryCode != null && request.AltCountryCode.Contains(i.AltCountryCode));
            }
            if (request?.GenderTypes?.Any() ?? false)
            {
                query = query.Where(i => i.Gender != null && request.GenderTypes.Contains(i.Gender.Value));
            }
            if (request?.MaritalStatuses?.Any() ?? false)
            {
                query = query.Where(i => i.MaritalStatus != null && request.MaritalStatuses.Contains(i.MaritalStatus.Value));
            }
            if (request?.DateOfBirth != null)
            {
                query = query.Where(i => i.DateOfBirth != null && i.DateOfBirth == request.DateOfBirth);
            }
            return query.AsQueryable();
            //}
        }
        private IQueryable<Lead> BuildQuery(SearchLeadRequest request, Guid userId, IList<Guid> subIds, List<CustomMasterLeadStatus>? customMasterLeadStatus = null)
        {
            var scope = _provider.CreateScope();
            var context = scope.ServiceProvider.GetRequiredService<ApplicationDbContext>();
            //using (context)
            //{

            var reporteeIds = subIds.Where(i => i != userId && i != Guid.Empty);
            var selfWithReporteeIds = subIds.Where(i => i != Guid.Empty);
            var query = context.Leads.Where(i => !i.IsDeleted).Where(i => !i.IsArchived)
                .Include(i => i.TagInfo)
                .Include(i => i.CustomLeadStatus)
                .Include(i => i.Enquiries)
                    //.ThenInclude(i => i.Address)
                    .ThenInclude(i => i.Addresses)
                .Include(i => i.Enquiries)
                    .ThenInclude(i => i.PropertyType)
                .Include(i => i.Appointments)
                    .ThenInclude(i => i.Location)
                .Include(i => i.Projects)
                .Include(i => i.Properties)
                .Include(i => i.Enquiries)
                .ThenInclude(i => i.PropertyTypes)
                .AsQueryable();

            /* if (!string.IsNullOrWhiteSpace(request.SearchByNameOrNumber))
                 var trimmedString = request.SearchByNameOrNumber.Trim();
                 query = query.Where(i => i.ContactNo.Contains(trimmedString.Replace(" ", "")) ||
                                          i.Name.ToLower().Contains(trimmedString.ToLower()) ||
                                          i.Email.ToLower().Contains(trimmedString.ToLower()) ||
                                          i.AlternateContactNo.Contains(trimmedString.Replace(" ", "")) ||
                                          i.SerialNumber.Contains(request.SearchByNameOrNumber.Replace(" ", "")));
             }*/
            if (!string.IsNullOrWhiteSpace(request.SearchByNameOrNumber) && request.PropertyToSearch?.Any() == true)
            {
                var searchTerm = request.SearchByNameOrNumber.ToLower().Trim().Replace(" ", "");
                var isPurposeValid = TryParseEnum<Purpose>(searchTerm, out var parsedPurpose);
                bool isSourceValid = TryParseEnum<LeadSource>(searchTerm, out var parsedSource);
                var isEnquiryTypeValid = TryParseEnum<EnquiryType>(searchTerm, out var parsedEnquiryType);
                var statusId = customMasterLeadStatus?.Where(i => i.DisplayName.ToLower().Trim().Replace(" ", "").Contains(searchTerm)).Select(i => i.Id).ToList();
                query = query.Where(i =>
                   (request.PropertyToSearch.Contains("LeadName") && i.Name != null && i.Name.ToLower().Trim().Replace(" ", "").Contains(searchTerm)) ||
                   (request.PropertyToSearch.Contains("ContactNo") && i.ContactNo != null && i.ContactNo.Replace(" ", "").Contains(searchTerm)) ||
                   (request.PropertyToSearch.Contains("SerialNumber") && i.SerialNumber != null && i.SerialNumber.Replace(" ", "").Contains(searchTerm)) ||
                   (request.PropertyToSearch.Contains("AlternateContactNo") && i.AlternateContactNo != null && i.AlternateContactNo.Replace(" ", "").Contains(searchTerm)) ||
                   (request.PropertyToSearch.Contains("Email") && i.Email != null && i.Email.ToLower().Contains(searchTerm)) ||
                   (request.PropertyToSearch.Contains("Nationality") && i.Nationality != null && i.Nationality.ToLower().Trim().Contains(searchTerm)) ||
                   (request.PropertyToSearch.Contains("PropertyName") && i.Properties.Any(a => a.Title.ToLower().Trim().Replace(" ", "").Contains(searchTerm))) ||
                   (request.PropertyToSearch.Contains("ProjectName") && i.Projects.Any(a => a.Name.ToLower().Trim().Replace(" ", "").Contains(searchTerm))) ||
                   (request.PropertyToSearch.Contains("SubSource") && i.Enquiries.Any(a => !string.IsNullOrEmpty(a.SubSource) && a.SubSource.ToLower().Trim().Replace(" ", "").Contains(searchTerm))) ||
                   (request.PropertyToSearch.Contains("Status") && (statusId.Contains(i.CustomLeadStatus.BaseId ?? Guid.Empty) || statusId.Contains(i.CustomLeadStatus.Id))) ||
                    (request.PropertyToSearch.Contains("Purpose") && isPurposeValid && i.Enquiries.Any(e => e.Purpose == parsedPurpose)) ||
                     (request.PropertyToSearch.Contains("Source") && isSourceValid && i.Enquiries.Any(e => e.LeadSource == parsedSource)) ||
                     (request.PropertyToSearch.Contains("EnquiredFor") && isEnquiryTypeValid && i.Enquiries.Any(e => e.EnquiryTypes.Contains(parsedEnquiryType))) ||
                   (request.PropertyToSearch.Contains("Location") && i.Enquiries.Any(e => e.Addresses.Any(j =>
                    (j.SubLocality +
                     j.Locality +
                     j.Community +
                     j.SubCommunity +
                     j.TowerName +
                     j.District +
                     j.City +
                     j.State +
                     j.Country +
                     j.PostalCode).Replace(",", "").ToLower().Trim().Replace(" ", "")
                    .Contains(searchTerm)))));
            }
            else if (!string.IsNullOrWhiteSpace(request.SearchByNameOrNumber))
            {
                query = query.Where(i => i.ContactNo.Contains(request.SearchByNameOrNumber.Replace(" ", "")) ||
                         i.Name.ToLower().Contains(request.SearchByNameOrNumber.ToLower()) ||
                         i.SerialNumber.ToLower().Contains(request.SearchByNameOrNumber.ToLower().Replace(" ", "")));
            }
            return query;
            //}
        }
        public (List<AppointmentType>, List<bool>) GetAppointmentTypes(GetAllLeadsRequest request)
        {
            List<AppointmentType> appTypes = new();
            List<bool> appDoneStatuses = new();
            if (request.MeetingOrVisitStatuses == null)
            {
                return (appTypes, appDoneStatuses);
            }
            request.MeetingOrVisitStatuses?.ForEach(appType =>
            {
                switch (appType)
                {
                    case MeetingOrVisitCompletionStatus.IsMeetingDone:
                        appTypes.Add(AppointmentType.Meeting);
                        appDoneStatuses.Add(true);
                        break;
                    case MeetingOrVisitCompletionStatus.IsMeetingNotDone:
                        appTypes.Add(AppointmentType.Meeting);
                        appDoneStatuses.Add(false);
                        break;
                    case MeetingOrVisitCompletionStatus.IsSiteVisitDone:
                        appTypes.Add(AppointmentType.SiteVisit);
                        appDoneStatuses.Add(true);
                        break;
                    case MeetingOrVisitCompletionStatus.IsSiteVisitNotDone:
                        appTypes.Add(AppointmentType.SiteVisit);
                        appDoneStatuses.Add(false);
                        break;
                }
            });
            appTypes = appTypes.Distinct().ToList();
            appDoneStatuses = appDoneStatuses.Distinct().ToList();
            return (appTypes, appDoneStatuses);
        }
        #endregion
        #region v2 methods for mobile
        public async Task<IEnumerable<Lead>> GetAllCategoryLeadsForMobileAsync(Application.Lead.Mobile.v2.V2GetAllLeadsRequest request, Guid userId, IList<Guid> subIds, LeadFilterTypeMobile category, List<Guid>? leadHistoryIds, List<Guid>? scheduledMeetingLeadHistoryIds, List<Guid>? scheduledVisitLeadHistoryIds, List<CustomMasterLeadStatus> custumStatuses, bool? isAdmin = null)
        {
            var query = BuildQuery(request, userId, subIds, category, leadHistoryIds, scheduledMeetingLeadHistoryIds, scheduledVisitLeadHistoryIds, custumStatuses);

            List<LeadFilterTypeMobile> scheduledFilterTypes = new()
            {
                LeadFilterTypeMobile.ScheduledMeeting, LeadFilterTypeMobile.ScheduleToday, LeadFilterTypeMobile.Overdue, LeadFilterTypeMobile.SiteVisitScheduled, LeadFilterTypeMobile.ScheduledTomorrow, LeadFilterTypeMobile.UpcomingSchedules, LeadFilterTypeMobile.CallBack
            };
            if (scheduledFilterTypes.Contains(category))
            {
                query = query.OrderBy(i => (i.ScheduledDate ?? DateTime.MinValue)).ThenBy(i => i.Name);
            }
            else
            {
                query = query.OrderByDescending(i => (i.LastModifiedOn ?? DateTime.MaxValue)).ThenBy(i => i.Name);
            }

            query = query
                .Skip(request.PageSize * (request.PageNumber - 1))
                .Take(request.PageSize)
                .AsQueryable();

            var leads = await query.ToListAsync().ConfigureAwait(false);
            return leads;
        }

        public async Task<int> GetAllCategoryLeadsCountForMobileAsync(Application.Lead.Mobile.v2.V2GetAllLeadsRequest request, Guid userId, IList<Guid> subIds, LeadFilterTypeMobile category, List<Guid>? leadHistoryIds, List<Guid>? scheduledMeetingLeadHistoryIds, List<Guid>? scheduledVisitLeadHistoryIds, List<CustomMasterLeadStatus> custumStatuses = null, bool? isAdmin = null)
        {
            var query = BuildQuery(request, userId, subIds, category, leadHistoryIds, scheduledMeetingLeadHistoryIds, scheduledVisitLeadHistoryIds, custumStatuses, isAdmin);
            var count = await query.CountAsync();
            return count;
        }
        public async Task<IEnumerable<Lead>> GetLeadsByCategoryForMobileAsync(Application.Lead.Mobile.v2.V2GetLeadCategoryRequest request, Guid userId, IList<Guid> subIds, List<Guid>? leadHistoryIds, List<Guid>? scheduledMeetingLeadHistoryIds, List<Guid>? scheduledVisitLeadHistoryIds, List<CustomMasterLeadStatus> customLeadStatuses = null, bool? isAdmin = null)
        {
            var query = BuildQuery(request, userId, subIds, leadHistoryIds, scheduledMeetingLeadHistoryIds, scheduledVisitLeadHistoryIds, customLeadStatuses);
            if (request?.FilterType == LeadFilterTypeMobile.ScheduledMeeting || request?.FilterType == LeadFilterTypeMobile.ScheduleToday ||
                       request?.FilterType == LeadFilterTypeMobile.Overdue || request?.FilterType == LeadFilterTypeMobile.SiteVisitScheduled ||
                       request?.FilterType == LeadFilterTypeMobile.CallBack || request?.FilterType == LeadFilterTypeMobile.ScheduledTomorrow ||
                       request?.FilterType == LeadFilterTypeMobile.UpcomingSchedules)
            {
                //query = query.OrderBy(i => (i.ScheduledDate ?? DateTime.MinValue) - new DateTime(2000, 01, 01, 00, 00, 00, DateTimeKind.Utc));
                query = query.OrderBy(i => (i.ScheduledDate ?? DateTime.MinValue)).ThenBy(i => i.Name);
            }
            else
            {
                //query = query.OrderByDescending(i => (i.LastModifiedOn ?? DateTime.MaxValue) - new DateTime(2000, 01, 01, 00, 00, 00, DateTimeKind.Utc));
                query = query.OrderByDescending(i => (i.LastModifiedOn ?? DateTime.MaxValue)).ThenBy(i => i.Name);
            }
            query = query.Skip(request.PageSize * (request.PageNumber - 1))
                .Take(request.PageSize)
                .AsQueryable();
            return await query.ToListAsync();
        }
        public async Task<int> GetLeadsCountByCategoryForMobileAsync(Application.Lead.Mobile.v2.V2GetLeadCategoryRequest request, Guid userId, IList<Guid> subIds, List<Guid>? leadHistoryIds, List<Guid>? scheduledMeetingLeadHistoryIds, List<Guid>? scheduledVisitLeadHistoryIds, List<CustomMasterLeadStatus> customMasterLeadStatuses, bool? isAdmin = null)
        {
            var query = BuildQuery(request, userId, subIds, leadHistoryIds, scheduledMeetingLeadHistoryIds, scheduledVisitLeadHistoryIds, customMasterLeadStatuses,isAdmin : isAdmin);
            return await query.CountAsync();
        }
        public async Task<IEnumerable<Lead>> SearchLeadForMobileAsync(Application.Lead.Mobile.v2.V2SearchLeadRequest request, Guid userId, KeyValuePair<bool, List<Guid>> adminWithSubIds, List<CustomMasterLeadStatus>? customMasterLeadStatus = null)
        {
            var query = BuildQuery(request, userId, adminWithSubIds, customMasterLeadStatus);
            query = query.OrderByDescending(i => i.LastModifiedOn - new DateTime(2000, 01, 01, 00, 00, 00, DateTimeKind.Utc))
                .Skip(request.PageSize * (request.PageNumber - 1))
                .Take(request.PageSize)
                .AsQueryable();
            return await query.ToListAsync();
        }
        public async Task<int> SearchLeadCountForMobileAsync(Application.Lead.Mobile.v2.V2SearchLeadRequest request, Guid userId, KeyValuePair<bool, List<Guid>> adminWithSubIds, List<CustomMasterLeadStatus>? customMasterLeadStatus = null)
        {
            var query = BuildQuery(request, userId, adminWithSubIds, customMasterLeadStatus);
            return await query.CountAsync();
        }
        private IQueryable<Lead> BuildQuery(Application.Lead.Mobile.v2.V2GetAllLeadsRequest request, Guid userId, IList<Guid> subIds, LeadFilterTypeMobile category, List<Guid>? leadHistoryIds, List<Guid>? scheduledMeetingLeadHistoryIds, List<Guid>? scheduledVisitLeadHistoryIds = null, List<CustomMasterLeadStatus> customStatuses = null, bool? isAdmin = null)
        {
            var scope = _provider.CreateScope();
            var context = scope.ServiceProvider.GetRequiredService<ApplicationDbContext>();
            var reporteeIds = subIds.Where(i => i != userId && i != Guid.Empty);
            var selfWithReporteeIds = subIds.Where(i => i != Guid.Empty);
            var query = context.Leads.Where(i => !i.IsDeleted)
                .Include(i => i.CustomLeadStatus)
                .Include(i => i.Enquiries)
                    //.ThenInclude(i => i.Address)
                    .ThenInclude(i => i.Addresses)
                .Include(i => i.Enquiries)
                    .ThenInclude(i => i.PropertyType)
                .Include(i => i.Appointments)
                    .ThenInclude(i => i.Location)
                .Include(i => i.Projects)
                .Include(i => i.Properties)
                .Include(i => i.CustomFlags)
                .ThenInclude(i => i.Flag)
                .Include(i => i.Agencies)
                .Include(i => i.Campaigns)
                .Include(i => i.Enquiries)
                .ThenInclude(i => i.PropertyTypes)
                .AsQueryable();
            if (request?.ShouldShowBookedDetails ?? false)
            {
                query = query.Include(navigationPropertyPath: i => i.BookedDetails)
                             .ThenInclude(i => i.Properties)
                            .AsQueryable();
                query = query.Where(i => i.BookedDetails.OrderByDescending(i => i.LastModifiedOn).FirstOrDefault().IsDeleted == false);
                query = query.OrderByDescending(i => i.BookedDetails.FirstOrDefault().LastModifiedOn);
            }
            if ((request?.ShouldShowBookedDetails ?? false) && (request?.ShouldShowBrokerageInfo ?? false))
            {
                query = query.Include(navigationPropertyPath: i => i.BookedDetails)
                              .ThenInclude(i => i.BrokerageInfo)
                              .AsQueryable();
            }
            if (request?.TeamHead != null)
            {
                query = query.Where(i => i.BookedDetails.FirstOrDefault().TeamHead == request.TeamHead);
            }
            if (request?.UpperAgreementLimit > 0)
            {
                query = query.Where(i => i.BookedDetails.FirstOrDefault().AgreementValue <= request.UpperAgreementLimit);
            }
            if (request?.LowerAgreementLimit > 0)
            {
                query = query.Where(i => i.BookedDetails.FirstOrDefault().AgreementValue >= request.LowerAgreementLimit);
            }
            if (request?.PaymentMode != TokenType.None)
            {
                switch (request.PaymentMode)
                {
                    case TokenType.Cheque:
                        query = query.Where(i => i.BookedDetails.FirstOrDefault().PaymentMode == TokenType.Cheque);
                        break;
                    case TokenType.Cash:
                        query = query.Where(i => i.BookedDetails.FirstOrDefault().PaymentMode == TokenType.Cash);
                        break;
                    case TokenType.NEFT:
                        query = query.Where(i => i.BookedDetails.FirstOrDefault().PaymentMode == TokenType.NEFT);
                        break;
                    case TokenType.RTGS:
                        query = query.Where(i => i.BookedDetails.FirstOrDefault().PaymentMode == TokenType.RTGS);
                        break;
                    case TokenType.UPI:
                        query = query.Where(i => i.BookedDetails.FirstOrDefault().PaymentMode == TokenType.UPI);
                        break;
                    case TokenType.DD:
                        query = query.Where(i => i.BookedDetails.FirstOrDefault().PaymentMode == TokenType.DD);
                        break;
                    case TokenType.IMPS:
                        query = query.Where(i => i.BookedDetails.FirstOrDefault().PaymentMode == TokenType.IMPS);
                        break;
                }
            }
            if (request?.UpperDiscountLimit > 0)
            {
                query = query.Where(i => i.BookedDetails.FirstOrDefault().Discount <= request.UpperDiscountLimit);
            }
            if (request?.LowerDiscountLimit > 0)
            {
                query = query.Where(i => i.BookedDetails.FirstOrDefault().Discount >= request.UpperDiscountLimit);
            }
            if (request?.DiscountMode != DiscountType.None)
            {
                switch (request.DiscountMode)
                {
                    case DiscountType.DirectAdjustment:
                        query = query.Where(i => i.BookedDetails.FirstOrDefault().DiscountMode == DiscountType.DirectAdjustment);
                        break;
                    case DiscountType.Cashback:
                        query = query.Where(i => i.BookedDetails.FirstOrDefault().DiscountMode == DiscountType.Cashback);
                        break;
                }
            }
            if (request?.LeadBrokerageInfoId != null)
            {
                query = query.Where(i => i.BookedDetails.FirstOrDefault().LeadBrokerageInfoId == request.LeadBrokerageInfoId);
            }

            if (!string.IsNullOrEmpty(request.DiscountUnit))
            {
                query = query.Where(i => i.BookedDetails.FirstOrDefault().DiscountUnit == request.DiscountUnit);
            }
            if (request?.TotalBrokerage != null)
            {
                query = query.Where(i => i.BookedDetails.FirstOrDefault().BrokerageInfo.TotalBrokerage == request.TotalBrokerage);

            }
            if (request?.SoldPrice != null)
            {
                query = query.Where(i => i.BookedDetails.FirstOrDefault().BrokerageInfo.SoldPrice == request.SoldPrice);
            }
            if (request?.BrokerageCharges != null)
            {
                query = query.Where(i => i.BookedDetails.FirstOrDefault().BrokerageInfo.BrokerageCharges == request.BrokerageCharges);
            }
            if (request?.NetBrokerageAmount != null)
            {
                query = query.Where(i => i.BookedDetails.FirstOrDefault().BrokerageInfo.NetBrokerageAmount == request.NetBrokerageAmount);
            }
            if (request?.GST != null)
            {
                query = query.Where(i => i.BookedDetails.FirstOrDefault().BrokerageInfo.GST == request.GST);
            }
            if (request?.Commission != null)
            {
                query = query.Where(i => i.BookedDetails.FirstOrDefault().BrokerageInfo.Commission == request.Commission);
            }
            if (request?.EarnedBrokerage != null)
            {
                query = query.Where(i => i.BookedDetails.FirstOrDefault().BrokerageInfo.EarnedBrokerage == request.EarnedBrokerage);
            }
            if (!string.IsNullOrEmpty(request.CommissionUnit))
            {
                query = query.Where(i => i.BookedDetails.FirstOrDefault().BrokerageInfo.CommissionUnit == request.CommissionUnit);
            }
            if (!string.IsNullOrEmpty(request.BrokerageUnit))
            {
                query = query.Where(i => i.BookedDetails.FirstOrDefault().BrokerageInfo.BrokerageUnit == request.BrokerageUnit);
            }
            if (!string.IsNullOrEmpty(request.GSTUnit))
            {
                query = query.Where(i => i.BookedDetails.FirstOrDefault().BrokerageInfo.GSTUnit == request.GSTUnit);
            }
            if (request?.BrokerageType != BrokerageType.None)
            {
                switch (request.BrokerageType)
                {
                    case BrokerageType.AgreementValue:
                        query = query.Where(i => i.BookedDetails.FirstOrDefault().BrokerageInfo.BrokerageType == BrokerageType.AgreementValue);
                        break;
                    case BrokerageType.SoldPrice:
                        query = query.Where(i => i.BookedDetails.FirstOrDefault().BrokerageInfo.BrokerageType == BrokerageType.AgreementValue);
                        break;
                }
            }
            if (!string.IsNullOrEmpty(request.ReferralNumber))
            {
                query = query.Where(i => i.BookedDetails.FirstOrDefault().BrokerageInfo.ReferralNumber == request.ReferralNumber);
            }
            if (request?.ReferredBy != null)
            {
                query = query.Where(i => i.BookedDetails.FirstOrDefault().BrokerageInfo.ReferredBy == request.ReferredBy);
            }

            if (request.BookedDate.HasValue && request.BookedDate.HasValue != default)
            {
                query = query.Where(i => i.BookedDate == request.BookedDate);
            }
            if (request?.UpperRemainingAmountLimit > 0)
            {
                query = query.Where(i => i.BookedDetails.FirstOrDefault().RemainingAmount <= request.UpperRemainingAmountLimit);
            }
            if (request?.LowerRemainingAmountLimit > 0)
            {
                query = query.Where(i => i.BookedDetails.FirstOrDefault().RemainingAmount >= request.LowerRemainingAmountLimit);
            }
            if (request?.PaymentType != PaymentType.None)
            {
                switch (request.PaymentType)
                {
                    case PaymentType.Cheque:
                        query = query.Where(i => i.BookedDetails.FirstOrDefault().PaymentType == PaymentType.Cheque);
                        break;
                    case PaymentType.Cash:
                        query = query.Where(i => i.BookedDetails.FirstOrDefault().PaymentType == PaymentType.Cash);
                        break;
                    case PaymentType.OnlineTransfer:
                        query = query.Where(i => i.BookedDetails.FirstOrDefault().PaymentType == PaymentType.OnlineTransfer);
                        break;
                    case PaymentType.BankLoan:
                        query = query.Where(i => i.BookedDetails.FirstOrDefault().PaymentType == PaymentType.BankLoan);
                        break;
                    case PaymentType.PartialLoanCash:
                        query = query.Where(i => i.BookedDetails.FirstOrDefault().PaymentType == PaymentType.PartialLoanCash);
                        break;
                    case PaymentType.DD:
                        query = query.Where(i => i.BookedDetails.FirstOrDefault().PaymentType == PaymentType.DD);
                        break;
                    case PaymentType.PendingLoanApproval:
                        query = query.Where(i => i.BookedDetails.FirstOrDefault().PaymentType == PaymentType.PendingLoanApproval);
                        break;
                    case PaymentType.LoanApplied:
                        query = query.Where(i => i.BookedDetails.FirstOrDefault().PaymentType == PaymentType.LoanApplied);
                        break;
                }
            }
            if (request.CustomFlags?.Any() ?? false)
            {
                query = query.Where(i => i.CustomFlags != null && i.CustomFlags.Any(j => j.Flag != null && request.CustomFlags.Contains(j.Flag.Name ?? string.Empty)));
            }
            if (request?.IsDualOwnershipEnabled ?? false)
            {
                if (request.SecondaryUsers?.Any() ?? false)
                {
                    query = query.Where(i => request.SecondaryUsers.Contains(i.SecondaryUserId ?? Guid.Empty));
                }
            }

            if (request.IsPicked != null && request.IsPicked != default)
            {
                query = query.Where(i => i.IsPicked == request.IsPicked.Value);
            }


            if (request.CallLogFromDate != null && request.CallLogToDate != null)
            {
                request.CallLogFromDate = request.CallLogFromDate?.ConvertFromDateToUtc();
                request.CallLogToDate = request.CallLogToDate?.ConvertToDateToUtc();
                query = query.Where(i => i.LeadCallLogs.Any(i => i.CreatedOn > request.CallLogFromDate.Value && i.CreatedOn < request.CallLogToDate.Value));
            }
            else if (request.CallLogFromDate != null)
            {
                request.CallLogFromDate = request.CallLogFromDate?.ConvertFromDateToUtc();
                query = query.Where(i => i.LeadCallLogs.Any(i => i.CreatedOn > request.CallLogFromDate.Value));
            }
            else if (request.CallLogToDate != null)
            {
                request.CallLogToDate = request.CallLogToDate?.ConvertToDateToUtc();
                query = query.Where(i => i.LeadCallLogs.Any(i => i.CreatedOn < request.CallLogToDate.Value));
            }

            if (!string.IsNullOrWhiteSpace(request.ConfidentialNotes))
            {
                request.ConfidentialNotes = request.ConfidentialNotes.ToLower().Trim();
                query = query.Where(i => i.ConfidentialNotes.ToLower().Trim().Contains(request.ConfidentialNotes));
            }

            if (request.CarpetArea != null && request.CarpetAreaUnitId != default)
            {
                query = query.Where(i => i.Enquiries.Any(i => i.CarpetArea == request.CarpetArea && i.CarpetAreaUnitId == request.CarpetAreaUnitId));
            }
            else if (request.CarpetArea != null)
            {
                query = query.Where(i => i.Enquiries.Any(i => i.CarpetArea == request.CarpetArea || i.CarpetAreaInSqMtr == request.CarpetArea));
            }

            if (!string.IsNullOrEmpty(request.ReferralName))
            {
                query = query.Where(i => (i.ReferralName.ToLower().Trim().Contains(request.ReferralName.ToLower().Trim())));
            }
            if (!string.IsNullOrEmpty(request.ReferralContactNo))
            {
                query = query.Where(i => !string.IsNullOrEmpty(i.ReferralContactNo) && request.ReferralContactNo.Contains(i.ReferralContactNo.Substring(i.ReferralContactNo.Length - 10)));
            }
            if (!string.IsNullOrEmpty(request.ReferralEmail))
            {
                query = query.Where(i => (i.ReferralEmail.ToLower().Trim().Contains(request.ReferralEmail.ToLower().Trim())));
            }

            if (leadHistoryIds?.Any() ?? false)
            {
                query = query.Where(i => leadHistoryIds.Contains(i.Id));
            }

            if (request?.MeetingOrVisitStatuses?.Any() ?? false)
            {
                DateTime? fromDate = request.FromDateForMeetingOrVisit != null ? request.FromDateForMeetingOrVisit.Value.ConvertFromDateToUtc() : DateTime.MinValue;
                DateTime? toDate = request.ToDateForMeetingOrVisit != null ? request.ToDateForMeetingOrVisit.Value.ConvertToDateToUtc() : DateTime.MaxValue;
                (List<AppointmentType> appTypes, List<bool> appDoneStatuses) = GetAppointmentTypes(request);
                if (request.AppointmentDoneByUserIds?.Any() ?? false)
                {
                    query = query.Where(i => i.Appointments.Any(app => request.AppointmentDoneByUserIds.Contains(app.CreatedBy) && app.Type != AppointmentType.None && appTypes.Contains(app.Type) && appDoneStatuses.Contains(app.IsDone)));
                }
                else if ((isAdmin != null) && !isAdmin.Value && (!request.AppointmentDoneByUserIds?.Any() ?? true))
                {
                    query = query.Where(i => i.Appointments.Any(j => j.UserId == i.AssignTo && j.Type != AppointmentType.None));
                }
                if (fromDate != null && toDate != null)
                {
                    query = query.Where(i => (request.MeetingOrVisitStatuses.Contains(MeetingOrVisitCompletionStatus.IsMeetingDone)
                                         && i.Appointments.Any(i => i.Type == AppointmentType.Meeting && i.IsDone && (i.UserId != Guid.Empty) && (subIds != null) && subIds.Contains(i.UserId) && i.CreatedOn > fromDate && i.CreatedOn < toDate))
                                         ||
                                             (request.MeetingOrVisitStatuses.Contains(MeetingOrVisitCompletionStatus.IsMeetingNotDone)
                                             && i.Appointments.Any(i => i.Type == AppointmentType.Meeting && !i.IsDone && (i.UserId != Guid.Empty) && (subIds != null) && subIds.Contains(i.UserId) && i.CreatedOn > fromDate && i.CreatedOn < toDate))
                                         ||
                                             (request.MeetingOrVisitStatuses.Contains(MeetingOrVisitCompletionStatus.IsSiteVisitDone)
                                             && i.Appointments.Any(i => i.Type == AppointmentType.SiteVisit && i.IsDone && (i.UserId != Guid.Empty) && (subIds != null) && subIds.Contains(i.UserId) && i.CreatedOn > fromDate && i.CreatedOn < toDate))
                                         ||
                                             (request.MeetingOrVisitStatuses.Contains(MeetingOrVisitCompletionStatus.IsSiteVisitNotDone)
                                             && i.Appointments.Any(i => i.Type == AppointmentType.SiteVisit && !i.IsDone && (i.UserId != Guid.Empty) && (subIds != null) && subIds.Contains(i.UserId) && i.CreatedOn > fromDate && i.CreatedOn < toDate)));
                }
                else if (fromDate != null)
                {
                    query = query.Where(i => (request.MeetingOrVisitStatuses.Contains(MeetingOrVisitCompletionStatus.IsMeetingDone)
                                         && i.Appointments.Any(i => i.Type == AppointmentType.Meeting && i.IsDone && (i.UserId != Guid.Empty) && (subIds != null) && subIds.Contains(i.UserId) && i.CreatedOn > fromDate))
                                         ||
                                             (request.MeetingOrVisitStatuses.Contains(MeetingOrVisitCompletionStatus.IsMeetingNotDone)
                                             && i.Appointments.Any(i => i.Type == AppointmentType.Meeting && !i.IsDone && (i.UserId != Guid.Empty) && (subIds != null) && subIds.Contains(i.UserId) && i.CreatedOn > fromDate))
                                         ||
                                             (request.MeetingOrVisitStatuses.Contains(MeetingOrVisitCompletionStatus.IsSiteVisitDone)
                                             && i.Appointments.Any(i => i.Type == AppointmentType.SiteVisit && i.IsDone && (i.UserId != Guid.Empty) && (subIds != null) && subIds.Contains(i.UserId) && i.CreatedOn > fromDate))
                                         ||
                                             (request.MeetingOrVisitStatuses.Contains(MeetingOrVisitCompletionStatus.IsSiteVisitNotDone)
                                             && i.Appointments.Any(i => i.Type == AppointmentType.SiteVisit && !i.IsDone && (i.UserId != Guid.Empty) && (subIds != null) && subIds.Contains(i.UserId) && i.CreatedOn > fromDate)));
                }
                else if (request.ToDateForMeetingOrVisit != null)
                {
                    query = query.Where(i => (request.MeetingOrVisitStatuses.Contains(MeetingOrVisitCompletionStatus.IsMeetingDone)
                                         && i.Appointments.Any(i => i.Type == AppointmentType.Meeting && i.IsDone && i.CreatedOn < toDate.Value))
                                         ||
                                             (request.MeetingOrVisitStatuses.Contains(MeetingOrVisitCompletionStatus.IsMeetingNotDone)
                                             && i.Appointments.Any(i => i.Type == AppointmentType.Meeting && !i.IsDone && (i.UserId != Guid.Empty) && (subIds != null) && subIds.Contains(i.UserId) && i.CreatedOn < toDate.Value))
                                         ||
                                             (request.MeetingOrVisitStatuses.Contains(MeetingOrVisitCompletionStatus.IsSiteVisitDone)
                                             && i.Appointments.Any(i => i.Type == AppointmentType.SiteVisit && i.IsDone && (i.UserId != Guid.Empty) && (subIds != null) && subIds.Contains(i.UserId) && i.CreatedOn < toDate.Value))
                                         ||
                                             (request.MeetingOrVisitStatuses.Contains(MeetingOrVisitCompletionStatus.IsSiteVisitNotDone)
                                             && i.Appointments.Any(i => i.Type == AppointmentType.SiteVisit && !i.IsDone && (i.UserId != Guid.Empty) && (subIds != null) && subIds.Contains(i.UserId) && i.CreatedOn < toDate.Value)));
                }
                else if ((request.AssignToIds?.Any() ?? false) && (request.OwnerSelection == OwnerSelectionType.Both) && (isAdmin ?? false))
                {
                    query = query.Where(i => (request.MeetingOrVisitStatuses.Contains(MeetingOrVisitCompletionStatus.IsMeetingDone)
                                               && i.Appointments.Any(j => j.Type == AppointmentType.Meeting && j.IsDone && (j.UserId != Guid.Empty) && (subIds != null) && (subIds.Contains(i.AssignTo) || request.AssignToIds.Contains(i.SecondaryUserId.Value))))
                                          ||
                                               (request.MeetingOrVisitStatuses.Contains(MeetingOrVisitCompletionStatus.IsMeetingNotDone)
                                               && i.Appointments.Any(j => j.Type == AppointmentType.Meeting && !j.IsDone && (j.UserId != Guid.Empty) && (subIds != null) && (subIds.Contains(i.AssignTo) || request.AssignToIds.Contains(i.SecondaryUserId.Value))))
                                          ||
                                               (request.MeetingOrVisitStatuses.Contains(MeetingOrVisitCompletionStatus.IsSiteVisitDone)
                                               && i.Appointments.Any(j => j.Type == AppointmentType.SiteVisit && j.IsDone && (j.UserId != Guid.Empty) && (subIds != null) && (subIds.Contains(i.AssignTo) || request.AssignToIds.Contains(i.SecondaryUserId.Value))))
                                          ||
                                               (request.MeetingOrVisitStatuses.Contains(MeetingOrVisitCompletionStatus.IsSiteVisitNotDone)
                                               && i.Appointments.Any(j => j.Type == AppointmentType.SiteVisit && !j.IsDone && (j.UserId != Guid.Empty) && (subIds != null) && (subIds.Contains(i.AssignTo) || request.AssignToIds.Contains(i.SecondaryUserId.Value)))));

                }
                else if ((request.OwnerSelection == OwnerSelectionType.SecondaryOwner) && (request.AssignToIds?.Any() ?? false) && (isAdmin ?? false))
                {
                    query = query.Where(i => (request.MeetingOrVisitStatuses.Contains(MeetingOrVisitCompletionStatus.IsMeetingDone)
                                                && i.Appointments.Any(j => j.Type == AppointmentType.Meeting && j.IsDone && (j.UserId != Guid.Empty) && request.AssignToIds.Contains(i.SecondaryUserId.Value)))
                                           ||
                                                (request.MeetingOrVisitStatuses.Contains(MeetingOrVisitCompletionStatus.IsMeetingNotDone)
                                                && i.Appointments.Any(j => j.Type == AppointmentType.Meeting && !j.IsDone && (j.UserId != Guid.Empty) && request.AssignToIds.Contains(i.SecondaryUserId.Value)))
                                           ||
                                                (request.MeetingOrVisitStatuses.Contains(MeetingOrVisitCompletionStatus.IsSiteVisitDone)
                                                && i.Appointments.Any(j => j.Type == AppointmentType.SiteVisit && j.IsDone && (j.UserId != Guid.Empty) && request.AssignToIds.Contains(i.SecondaryUserId.Value)))
                                           ||
                                                (request.MeetingOrVisitStatuses.Contains(MeetingOrVisitCompletionStatus.IsSiteVisitNotDone)
                                                && i.Appointments.Any(j => j.Type == AppointmentType.SiteVisit && !j.IsDone && (j.UserId != Guid.Empty) && request.AssignToIds.Contains(i.SecondaryUserId.Value))));

                }
                else if ((request.AssignToIds?.Any() ?? false) && (isAdmin ?? false))
                {
                    query = query.Where(i => (request.MeetingOrVisitStatuses.Contains(MeetingOrVisitCompletionStatus.IsMeetingDone)
                                                && i.Appointments.Any(j => j.Type == AppointmentType.Meeting && j.IsDone && (j.UserId != Guid.Empty) && (subIds != null) && subIds.Contains(i.AssignTo)))
                                           ||
                                                (request.MeetingOrVisitStatuses.Contains(MeetingOrVisitCompletionStatus.IsMeetingNotDone)
                                                && i.Appointments.Any(j => j.Type == AppointmentType.Meeting && !j.IsDone && (j.UserId != Guid.Empty) && (subIds != null) && subIds.Contains(i.AssignTo)))
                                           ||
                                                (request.MeetingOrVisitStatuses.Contains(MeetingOrVisitCompletionStatus.IsSiteVisitDone)
                                                && i.Appointments.Any(j => j.Type == AppointmentType.SiteVisit && j.IsDone && (j.UserId != Guid.Empty) && (subIds != null) && subIds.Contains(i.AssignTo)))
                                           ||
                                                (request.MeetingOrVisitStatuses.Contains(MeetingOrVisitCompletionStatus.IsSiteVisitNotDone)
                                                && i.Appointments.Any(j => j.Type == AppointmentType.SiteVisit && !j.IsDone && (j.UserId != Guid.Empty) && (subIds != null) && subIds.Contains(i.AssignTo))));

                }

                else
                {
                    query = query.Where(i => (request.MeetingOrVisitStatuses.Contains(MeetingOrVisitCompletionStatus.IsMeetingDone)
                                                && i.Appointments.Any(i => i.Type == AppointmentType.Meeting && i.IsDone && (i.UserId != Guid.Empty) && (subIds != null) && subIds.Contains(i.UserId)))
                                           ||
                                                (request.MeetingOrVisitStatuses.Contains(MeetingOrVisitCompletionStatus.IsMeetingNotDone)
                                                && i.Appointments.Any(i => i.Type == AppointmentType.Meeting && !i.IsDone && (i.UserId != Guid.Empty) && (subIds != null) && subIds.Contains(i.UserId)))
                                           ||
                                                (request.MeetingOrVisitStatuses.Contains(MeetingOrVisitCompletionStatus.IsSiteVisitDone)
                                                && i.Appointments.Any(i => i.Type == AppointmentType.SiteVisit && i.IsDone && (i.UserId != Guid.Empty) && (subIds != null) && subIds.Contains(i.UserId)))
                                           ||
                                                (request.MeetingOrVisitStatuses.Contains(MeetingOrVisitCompletionStatus.IsSiteVisitNotDone)
                                                && i.Appointments.Any(i => i.Type == AppointmentType.SiteVisit && !i.IsDone && (i.UserId != Guid.Empty) && (subIds != null) && subIds.Contains(i.UserId))));
                }
            }
            if ((request?.IsDualOwnershipEnabled ?? false) && (request?.AssignToIds?.Any() ?? false) && (request.OwnerSelection == OwnerSelectionType.Both))
            {
                switch (request.LeadVisibility)
                {
                    case BaseLeadVisibility.SelfWithReportee:
                        query = query.Where(i => (selfWithReporteeIds.Contains(i.AssignTo) || selfWithReporteeIds.Contains(i.SecondaryUserId ?? Guid.Empty))).Where(i => !i.IsArchived);
                        break;
                    case BaseLeadVisibility.Self:
                        query = query.Where(i => (i.AssignTo == userId || i.SecondaryUserId == userId)).Where(i => !i.IsArchived);
                        break;
                    case BaseLeadVisibility.Reportee:
                        query = query.Where(i => (reporteeIds.Contains(i.AssignTo) || reporteeIds.Contains(i.SecondaryUserId ?? Guid.Empty))).Where(i => !i.IsArchived);
                        break;
                    case BaseLeadVisibility.UnassignLead:
                        query = query.Where(i => i.AssignTo == Guid.Empty).Where(i => !i.IsArchived);
                        break;
                    case BaseLeadVisibility.DeletedLeads:
                        query = query.Where(i => i.IsArchived && selfWithReporteeIds.Contains(i.AssignTo));
                        break;
                    default:
                        break;
                }
            }
            else if ((request?.IsDualOwnershipEnabled ?? false) && (request?.AssignToIds?.Any() ?? false) && (request.OwnerSelection == OwnerSelectionType.SecondaryOwner))
            {
                switch (request.LeadVisibility)
                {
                    case BaseLeadVisibility.SelfWithReportee:
                        query = query.Where(i => selfWithReporteeIds.Contains(i.SecondaryUserId.Value)).Where(i => !i.IsArchived);
                        break;
                    case BaseLeadVisibility.Self:
                        query = query.Where(i => i.SecondaryUserId == userId).Where(i => !i.IsArchived);
                        break;
                    case BaseLeadVisibility.Reportee:
                        query = query.Where(i => reporteeIds.Contains(i.SecondaryUserId.Value)).Where(i => !i.IsArchived);
                        break;
                    case BaseLeadVisibility.UnassignLead:
                        query = query.Where(i => i.AssignTo == Guid.Empty).Where(i => !i.IsArchived);
                        break;
                    case BaseLeadVisibility.DeletedLeads:
                        query = query.Where(i => i.IsArchived && selfWithReporteeIds.Contains(i.SecondaryUserId.Value));
                        break;
                    default:
                        break;
                }
            }
            else if ((request?.IsDualOwnershipEnabled ?? false) && (!request?.AssignToIds?.Any() ?? true))
            {
                switch (request.LeadVisibility)
                {
                    case BaseLeadVisibility.SelfWithReportee:
                        query = query.Where(i => (selfWithReporteeIds.Contains(i.AssignTo) || selfWithReporteeIds.Contains(i.SecondaryUserId ?? Guid.Empty))).Where(i => !i.IsArchived);
                        break;
                    case BaseLeadVisibility.Self:
                        query = query.Where(i => (i.AssignTo == userId || i.SecondaryUserId == userId)).Where(i => !i.IsArchived);
                        break;
                    case BaseLeadVisibility.Reportee:
                        query = query.Where(i => (reporteeIds.Contains(i.AssignTo) || reporteeIds.Contains(i.SecondaryUserId ?? Guid.Empty))).Where(i => !i.IsArchived);
                        break;
                    case BaseLeadVisibility.UnassignLead:
                        query = query.Where(i => i.AssignTo == Guid.Empty).Where(i => !i.IsArchived);
                        break;
                    case BaseLeadVisibility.DeletedLeads:
                        query = query.Where(i => i.IsArchived && selfWithReporteeIds.Contains(i.AssignTo));
                        break;
                    default:
                        break;
                }
            }
            else
            {
                switch (request.LeadVisibility)
                {
                    case BaseLeadVisibility.SelfWithReportee:
                        query = query.Where(i => selfWithReporteeIds.Contains(i.AssignTo)).Where(i => !i.IsArchived);
                        break;
                    case BaseLeadVisibility.Self:
                        query = query.Where(i => i.AssignTo == userId).Where(i => !i.IsArchived);
                        break;
                    case BaseLeadVisibility.Reportee:
                        query = query.Where(i => reporteeIds.Contains(i.AssignTo)).Where(i => !i.IsArchived);
                        break;
                    case BaseLeadVisibility.UnassignLead:
                        query = query.Where(i => i.AssignTo == Guid.Empty).Where(i => !i.IsArchived);
                        break;
                    case BaseLeadVisibility.DeletedLeads:
                        query = query.Where(i => i.IsArchived && selfWithReporteeIds.Contains(i.AssignTo));
                        break;
                    default:
                        break;
                }
            }
            if (request.EnquiredFor != null && request.EnquiredFor.Any())
            {
                //query = query.Where(i => i.Enquiries != null && i.Enquiries.Any(i => request.EnquiredFor.Contains(i.EnquiredFor)));
                query = query.Where(i => i.Enquiries != null && i.Enquiries.Any(i => i.EnquiryTypes != null && i.EnquiryTypes.Any(j => request.EnquiredFor.Contains(j))));
            }

            if (request.DateType.HasValue && ((request.FromDate.HasValue && request.FromDate.Value != default) || (request.ToDate.HasValue && request.ToDate.Value != default)))
            {
                DateTime? fromDate = request.FromDate.HasValue ? request.FromDate.Value.ConvertFromDateToUtc() : DateTime.MinValue;
                DateTime? toDate = request.ToDate.HasValue ? request.ToDate.Value.ConvertToDateToUtc() : DateTime.MaxValue;
                switch (request.DateType)
                {
                    case DateType.ReceivedDate:
                        query = query.Where(i => i.CreatedOn >= fromDate.Value && i.CreatedOn <= toDate.Value);
                        break;
                    case DateType.ScheduledDate:
                        query = query.Where(i => i.ScheduledDate != null && i.ScheduledDate.Value >= fromDate.Value && i.ScheduledDate.Value <= toDate.Value);
                        break;
                    case DateType.ModifiedDate:
                        query = query.Where(i => i.LastModifiedOn != null && i.LastModifiedOn.Value >= fromDate.Value && i.LastModifiedOn.Value <= toDate.Value);
                        break;
                    case DateType.DeletedDate:
                        query = query.Where(i => i.ArchivedOn >= fromDate.Value && i.ArchivedOn < toDate.Value);
                        break;
                    case DateType.PickedDate:
                        query = query.Where(i => i.PickedDate >= fromDate.Value && i.PickedDate <= toDate.Value);
                        break;
                    case DateType.BookedDate:
                        query = query.Where(item => item.BookedDetails.Any(detail => detail.BookedDate.Value >= fromDate.Value && detail.BookedDate.Value <= toDate.Value));
                        break;
                    default:
                        break;
                }
            }
            if (!string.IsNullOrEmpty(request?.DatesJsonFormattedString ?? string.Empty))
            {
                try
                {
                    var data = JsonConvert.DeserializeObject<List<Lrb.Application.Lead.Mobile.v2.Date>>(request?.DatesJsonFormattedString ?? string.Empty);
                    if (data?.Any() ?? false)
                    {
                        request.Dates = data;
                    }
                    else
                    {
                        request.Dates = null;
                    }
                }
                catch (Exception ex)
                {
                    request.Dates = null;
                    Console.WriteLine("Exception details while deserializing the date object " + ex.Serialize());
                }
            }
            if (request.Dates?.Any() ?? false)
            {
                foreach (var date in request.Dates)
                {
                    if (date.MultiDateType.HasValue && ((date.MultiFromDate.HasValue && date.MultiFromDate.Value != default) || (date.MultiToDate.HasValue && date.MultiToDate.Value != default)))
                    {
                        DateTime? multiFromDate = date.MultiFromDate.HasValue ? date.MultiFromDate.Value.ConvertFromDateToUtc() : DateTime.MinValue;
                        DateTime? multiToDate = date.MultiToDate.HasValue ? date.MultiToDate.Value.ConvertToDateToUtc() : DateTime.MaxValue;

                        switch (date.MultiDateType)
                        {
                            case DateType.ReceivedDate:
                                query = query.Where(i => i.CreatedOn >= multiFromDate.Value && i.CreatedOn <= multiToDate.Value);
                                break;
                            case DateType.ScheduledDate:
                                query = query.Where(i => i.ScheduledDate != null && i.ScheduledDate.Value >= multiFromDate.Value && i.ScheduledDate.Value <= multiToDate.Value);
                                break;
                            case DateType.ModifiedDate:
                                query = query.Where(i => i.LastModifiedOn != null && i.LastModifiedOn.Value >= multiFromDate.Value && i.LastModifiedOn.Value <= multiToDate.Value);
                                break;
                            case DateType.DeletedDate:
                                query = query.Where(i => i.ArchivedOn >= multiFromDate.Value && i.ArchivedOn < multiToDate.Value);
                                break;
                            case DateType.PickedDate:
                                query = query.Where(i => i.PickedDate >= multiFromDate.Value && i.PickedDate <= multiToDate.Value);
                                break;
                            default:
                                break;
                        }
                    }
                }
            }

            /* if (!string.IsNullOrWhiteSpace(request.SearchByNameOrNumber))
             {
                 query = query.Where(i => i.ContactNo.Contains(request.SearchByNameOrNumber.Replace(" ", "")) ||
                                          i.Name.ToLower().Contains(request.SearchByNameOrNumber.ToLower()) ||
                                          i.Email.ToLower().Contains(request.SearchByNameOrNumber.ToLower()) ||
                                          i.AlternateContactNo.Contains(request.SearchByNameOrNumber.Replace(" ", "")) ||
                                          i.SerialNumber.Contains(request.SearchByNameOrNumber.Replace(" ", "")));
             }*/
            if (!string.IsNullOrWhiteSpace(request.SearchByNameOrNumber) && request.PropertyToSearch?.Any() == true)
            {
                var searchTerm = request.SearchByNameOrNumber.ToLower().Trim().Replace(" ", "");
                var isPurposeValid = TryParseEnum<Purpose>(searchTerm, out var parsedPurpose);
                bool isSourceValid = TryParseEnum<LeadSource>(searchTerm, out var parsedSource);
                var isEnquiryTypeValid = TryParseEnum<EnquiryType>(searchTerm, out var parsedEnquiryType); ;
                var statusId = customStatuses?.Where(i => i.DisplayName.ToLower().Trim().Replace(" ", "").Contains(searchTerm)).Select(i => i.Id).ToList();
                query = query.Where(i =>
                   (request.PropertyToSearch.Contains("LeadName") && i.Name != null && i.Name.ToLower().Trim().Replace(" ", "").Contains(searchTerm)) ||
                   (request.PropertyToSearch.Contains("ContactNo") && i.ContactNo != null && i.ContactNo.Replace(" ", "").Contains(searchTerm)) ||
                   (request.PropertyToSearch.Contains("SerialNumber") && i.SerialNumber != null && i.SerialNumber.Replace(" ", "").Contains(searchTerm)) ||
                   (request.PropertyToSearch.Contains("AlternateContactNo") && i.AlternateContactNo != null && i.AlternateContactNo.Replace(" ", "").Contains(searchTerm)) ||
                   (request.PropertyToSearch.Contains("Email") && i.Email != null && i.Email.ToLower().Contains(searchTerm)) ||
                   (request.PropertyToSearch.Contains("Nationality") && i.Nationality != null && i.Nationality.ToLower().Trim().Contains(searchTerm)) ||
                   (request.PropertyToSearch.Contains("PropertyName") && i.Properties.Any(a => a.Title.ToLower().Trim().Replace(" ", "").Contains(searchTerm))) ||
                   (request.PropertyToSearch.Contains("ProjectName") && i.Projects.Any(a => a.Name.ToLower().Trim().Replace(" ", "").Contains(searchTerm))) ||
                   (request.PropertyToSearch.Contains("SubSource") && i.Enquiries.Any(a => !string.IsNullOrEmpty(a.SubSource) && a.SubSource.ToLower().Trim().Replace(" ", "").Contains(searchTerm))) ||
                   (request.PropertyToSearch.Contains("Status") && (statusId.Contains(i.CustomLeadStatus.BaseId ?? Guid.Empty) || statusId.Contains(i.CustomLeadStatus.Id))) ||
                    (request.PropertyToSearch.Contains("Purpose") && isPurposeValid && i.Enquiries.Any(e => e.Purpose == parsedPurpose)) ||
                     (request.PropertyToSearch.Contains("Source") && isSourceValid && i.Enquiries.Any(e => e.LeadSource == parsedSource)) ||
                     (request.PropertyToSearch.Contains("EnquiredFor") && isEnquiryTypeValid && i.Enquiries.Any(e => e.EnquiryTypes.Contains(parsedEnquiryType))) ||
                   (request.PropertyToSearch.Contains("Location") && i.Enquiries.Any(e => e.Addresses.Any(j =>
                    (j.SubLocality +
                     j.Locality +
                     j.Community +
                     j.SubCommunity +
                     j.TowerName +
                     j.District +
                     j.City +
                     j.State +
                     j.Country +
                     j.PostalCode).Replace(",", "").ToLower().Trim().Replace(" ", "")
                    .Contains(searchTerm)))));
            }
            else if (!string.IsNullOrWhiteSpace(request.SearchByNameOrNumber))
            {
                query = query.Where(i => i.ContactNo.Contains(request.SearchByNameOrNumber.Replace(" ", "")) ||
                         i.Name.ToLower().Contains(request.SearchByNameOrNumber.ToLower()) ||
                         i.SerialNumber.ToLower().Contains(request.SearchByNameOrNumber.ToLower().Replace(" ", "")));
            }
            if (request.CallStatus != null)
            {
                query = query.Where(i => i.LeadCallLogs != null && i.LeadCallLogs.Any(j => j.CallStatus == request.CallStatus));
            }
            if (request.CallDirection != null)
            {
                query = query.Where(i => i.LeadCallLogs != null && i.LeadCallLogs.Any(j => j.CallDirection == request.CallDirection));
            }
            if (request.Source?.Any() ?? false)
            {
                query = query.Where(i => i.Enquiries != null && i.Enquiries.Any(i => request.Source.Contains(i.LeadSource)));
            }
            var Statuses = (customStatuses?.Where(i => i.Status is "not_interested" or "dropped" or "booked" or "booking_cancel"))?.ToList() ?? new();
            var StatusesIds = Statuses.Select(i => i.Id).ToList();
            var ScheduledStatus = (customStatuses?.Where(i => i.Status is "site_visit_scheduled" or "callback" or "meeting_scheduled"))?.ToList() ?? new();
            var schuduledStatusIds = ScheduledStatus.Select(i => i.Id).ToList();
            TimeSpan userUtcOffset = request.BaseUTcOffset ?? TimeZoneInfo.Local.GetUtcOffset(DateTime.UtcNow);
            DateTime today = DateTime.UtcNow.Add(userUtcOffset).Date;
            switch (category)
            {
                case LeadFilterTypeMobile.New:
                    query = query.Where(i => i.CustomLeadStatus != null && i.CustomLeadStatus.Status == "new");
                    break;
                #region ScheduleDate based categories
                case LeadFilterTypeMobile.ScheduleToday:
                    query = query.Where(i => ((i.ScheduledDate ?? DateTime.MinValue) + userUtcOffset) >= today && ((i.ScheduledDate ?? DateTime.MinValue) + userUtcOffset) < today.AddDays(1))
                    .Where(i => i.CustomLeadStatus == null || schuduledStatusIds.Contains(i.CustomLeadStatus.BaseId ?? Guid.Empty) || schuduledStatusIds.Contains(i.CustomLeadStatus.Id)); ;
                    break;
                case LeadFilterTypeMobile.Overdue:
                    query = query.Where(i => ((i.ScheduledDate ?? DateTime.MinValue) + userUtcOffset) < today)
                    .Where(i => i.CustomLeadStatus != null && (schuduledStatusIds.Contains(i.CustomLeadStatus.BaseId ?? Guid.Empty) || schuduledStatusIds.Contains(i.CustomLeadStatus.Id)));
                    break;
                case LeadFilterTypeMobile.ScheduledTomorrow:
                    query = query.Where(i => ((i.ScheduledDate ?? DateTime.MinValue) + userUtcOffset) >= today.AddDays(1) && ((i.ScheduledDate ?? DateTime.MinValue) + userUtcOffset) < today.AddDays(2))
                    .Where(i => i.CustomLeadStatus != null && (schuduledStatusIds.Contains(i.CustomLeadStatus.BaseId ?? Guid.Empty) || schuduledStatusIds.Contains(i.CustomLeadStatus.Id)));
                    break;
                case LeadFilterTypeMobile.UpcomingSchedules:
                    query = query.Where(i => ((i.ScheduledDate ?? DateTime.MinValue) + userUtcOffset) >= today.AddDays(1))
                    .Where(i => i.CustomLeadStatus == null || schuduledStatusIds.Contains(i.CustomLeadStatus.BaseId ?? Guid.Empty) || schuduledStatusIds.Contains(i.CustomLeadStatus.Id));
                    break;
                #endregion
                case LeadFilterTypeMobile.Pending:
                    query = query.Where(i => i.CustomLeadStatus != null && i.CustomLeadStatus.Status == "pending");
                    break;
                case LeadFilterTypeMobile.NotInterested:
                    var notInterestedStatus = customStatuses?.FirstOrDefault(i => i.Status == "not_interested") ?? new();
                    query = query.Where(i => i.CustomLeadStatus != null && (i.CustomLeadStatus.BaseId == notInterestedStatus.Id || i.CustomLeadStatus.Id == notInterestedStatus.Id));
                    break;
                case LeadFilterTypeMobile.CallBack:
                    query = query.Where(i => i.CustomLeadStatus != null && i.CustomLeadStatus.MasterLeadStatusBaseId == Guid.Parse("5ae346bc-c695-4af4-8c3b-c8648587fbd6"));
                    break;
                case LeadFilterTypeMobile.SiteVisitScheduled:
                    var siteVisitScheduled = customStatuses?.FirstOrDefault(i => i.Status == "site_visit_scheduled") ?? new();
                    //query = query.Where(i => i.CustomLeadStatus != null && (i.CustomLeadStatus.Status == "site_visit_scheduled" || i.CustomLeadStatus.MasterLeadStatusBaseId.ToString() == "59647294-09d6-44a2-a346-9de5ba829e04"));
                    query = query.Where(i => i.CustomLeadStatus != null && (i.CustomLeadStatus.BaseId == siteVisitScheduled.Id || i.CustomLeadStatus.Id == siteVisitScheduled.Id));
                    break;
                case LeadFilterTypeMobile.ScheduledMeeting:
                    var meetingScheduled = customStatuses?.FirstOrDefault(i => i.Status == "meeting_scheduled") ?? new();
                    //query = query.Where(i => i.CustomLeadStatus != null && (i.CustomLeadStatus.Status == "meeting_scheduled" || i.CustomLeadStatus.MasterLeadStatusBaseId.ToString() == "99a7f794-9046-4a9d-b7e2-e0a2196b98dd"));
                    query = query.Where(i => i.CustomLeadStatus != null && (i.CustomLeadStatus.BaseId == meetingScheduled.Id || i.CustomLeadStatus.Id == meetingScheduled.Id));
                    break;
                case LeadFilterTypeMobile.UnassignLeads:
                    query = query.Where(i => i.CustomLeadStatus != null && i.AssignTo == Guid.Empty);
                    break;
                case LeadFilterTypeMobile.Booked:
                    var bookedStatus = customStatuses?.FirstOrDefault(i => i.Status == "booked") ?? new();
                    query = query.Where(i => i.CustomLeadStatus != null && (i.CustomLeadStatus.BaseId == bookedStatus.Id || i.CustomLeadStatus.Id == bookedStatus.Id));
                    break;
                case LeadFilterTypeMobile.Dropped:
                    var droppedStatus = customStatuses?.FirstOrDefault(i => i.Status == "dropped") ?? new();
                    query = query.Where(i => i.CustomLeadStatus != null && (i.CustomLeadStatus.BaseId == droppedStatus.Id || i.CustomLeadStatus.Id == droppedStatus.Id));
                    break;
                case LeadFilterTypeMobile.Escalated:
                    query = query.Where(i => i.TagInfo != null && i.TagInfo.IsEscalated)
                    .Where(i => i.CustomLeadStatus == null || i.CustomLeadStatus.MasterLeadStatusBaseId != Guid.Parse("023fcb89-037e-42f4-bcc2-647bb4e95c99"))
                    .Where(i => i.CustomLeadStatus == null || i.CustomLeadStatus.Status != "booked")
                    .Where(i => i.CustomLeadStatus == null || i.CustomLeadStatus.MasterLeadStatusBaseId != Guid.Parse("065fa6fd-a15e-4248-b469-e8596980c97a"));
                    break;
                case LeadFilterTypeMobile.HotLeads:
                    query = query.Where(i => i.TagInfo != null && i.TagInfo.IsHotLead)
                    .Where(i => i.CustomLeadStatus == null || i.CustomLeadStatus.MasterLeadStatusBaseId != Guid.Parse("023fcb89-037e-42f4-bcc2-647bb4e95c99"))
                    .Where(i => i.CustomLeadStatus == null || i.CustomLeadStatus.Status != "booked")
                    .Where(i => i.CustomLeadStatus == null || i.CustomLeadStatus.MasterLeadStatusBaseId != Guid.Parse("065fa6fd-a15e-4248-b469-e8596980c97a"));
                    break;
                case LeadFilterTypeMobile.AboutToConvert:
                    query = query.Where(i => i.TagInfo != null && i.TagInfo.IsAboutToConvert)
                    .Where(i => i.CustomLeadStatus == null || i.CustomLeadStatus.MasterLeadStatusBaseId != Guid.Parse("023fcb89-037e-42f4-bcc2-647bb4e95c99"))
                    .Where(i => i.CustomLeadStatus == null || i.CustomLeadStatus.Status != "booked")
                    .Where(i => i.CustomLeadStatus == null || i.CustomLeadStatus.MasterLeadStatusBaseId != Guid.Parse("065fa6fd-a15e-4248-b469-e8596980c97a"));
                    break;
                case LeadFilterTypeMobile.WarmLeads:
                    query = query.Where(i => i.TagInfo != null && i.TagInfo.IsWarmLead)
                    .Where(i => i.CustomLeadStatus == null || i.CustomLeadStatus.MasterLeadStatusBaseId != Guid.Parse("023fcb89-037e-42f4-bcc2-647bb4e95c99"))
                    .Where(i => i.CustomLeadStatus == null || i.CustomLeadStatus.Status != "booked")
                    .Where(i => i.CustomLeadStatus == null || i.CustomLeadStatus.MasterLeadStatusBaseId != Guid.Parse("065fa6fd-a15e-4248-b469-e8596980c97a"));
                    break;
                case LeadFilterTypeMobile.ColdLead:
                    query = query.Where(i => i.TagInfo != null && i.TagInfo.IsColdLead)
                    .Where(i => i.CustomLeadStatus == null || i.CustomLeadStatus.MasterLeadStatusBaseId != Guid.Parse("023fcb89-037e-42f4-bcc2-647bb4e95c99"))
                    .Where(i => i.CustomLeadStatus == null || i.CustomLeadStatus.Status != "booked")
                    .Where(i => i.CustomLeadStatus == null || i.CustomLeadStatus.MasterLeadStatusBaseId != Guid.Parse("065fa6fd-a15e-4248-b469-e8596980c97a"));
                    break;
                case LeadFilterTypeMobile.Highlighted:
                    query = query.Where(i => i.TagInfo != null && i.TagInfo.IsHighlighted)
                    .Where(i => i.CustomLeadStatus == null || i.CustomLeadStatus.MasterLeadStatusBaseId != Guid.Parse("023fcb89-037e-42f4-bcc2-647bb4e95c99"))
                    .Where(i => i.CustomLeadStatus == null || i.CustomLeadStatus.Status != "booked")
                    .Where(i => i.CustomLeadStatus == null || i.CustomLeadStatus.MasterLeadStatusBaseId != Guid.Parse("065fa6fd-a15e-4248-b469-e8596980c97a"));
                    break;
                case LeadFilterTypeMobile.Active:
                    query = query.Where(i => i.CustomLeadStatus != null && !StatusesIds.Contains(i.CustomLeadStatus.BaseId ?? Guid.Empty) && !StatusesIds.Contains(i.CustomLeadStatus.Id));
                    break;
                case LeadFilterTypeMobile.AllWithNID:
                case LeadFilterTypeMobile.All:
                    query = query.Where(i => i.CustomLeadStatus != null);
                    break;
                case LeadFilterTypeMobile.BookingCancel:
                    var bookingCancelStatus = customStatuses?.FirstOrDefault(i => i.Status == "booking_cancel") ?? new();
                    query = query.Where(i => i.CustomLeadStatus != null && (i.CustomLeadStatus.BaseId == bookingCancelStatus.Id || i.CustomLeadStatus.Id == bookingCancelStatus.Id));
                    break;
                case LeadFilterTypeMobile.Untouched:
                    query = query.Where(i => !i.IsPicked);
                    break;
                case LeadFilterTypeMobile.ExpressionOfInterest:
                    var customStatus = customStatuses?.FirstOrDefault(i => i.Status == "expression_of_interest") ?? new();
                    query = query.Where(i => i.CustomLeadStatus != null && (i.CustomLeadStatus.BaseId == customStatus.Id || i.CustomLeadStatus.Id == customStatus.Id));
                    break;
                case LeadFilterTypeMobile.MeetingDone:
                    query = query.Where(i => i.Appointments.Any(i => i.Type == AppointmentType.Meeting && i.IsDone));
                    break;
                case LeadFilterTypeMobile.SiteVisitDone:
                    query = query.Where(i => i.Appointments.Any(i => i.Type == AppointmentType.SiteVisit && i.IsDone));
                    break;
                default:
                    break;
            }

            if (request.Properties?.Any() ?? false)
            {
                var propertyNames = request.Properties.Select(i => i.ToLower());
                query = query.Where(i => i.Properties.Count > 0 && i.Properties.Any(i => propertyNames.Contains(i.Title.ToLower())));
            }
            if (request.Projects?.Any() ?? false)
            {
                var projectNames = request.Projects.Select(i => i.ToLower());
                query = query.Where(i => i.Projects.Count > 0 && i.Projects.Any(i => projectNames.Contains(i.Name.ToLower()))).AsQueryable();
            }
            if (request.NoOfBHKs != null && request.NoOfBHKs.Any())
            {
                //query = query.Where(i => i.Enquiries != null && i.Enquiries.Any(i => request.NoOfBHKs.Contains(i.NoOfBHKs)));
                query = query.Where(i => i.Enquiries != null && i.Enquiries.Any(i => i.BHKs != null && i.BHKs.Any(j => request.NoOfBHKs.Contains(j))));
            }
            if (request.Beds?.Any() ?? false)
            {
                query = query.Where(i => i.Enquiries != null && i.Enquiries.Any(i => i.Beds != null && i.Beds.Any(j => request.Beds.Contains(j))));
            }
            if (request.Baths?.Any() ?? false)
            {
                query = query.Where(i => i.Enquiries != null && i.Enquiries.Any(i => i.Baths != null && i.Baths.Any(j => request.Baths.Contains(j))));
            }
            if (request.Floors?.Any() ?? false)
            {
                query = query.Where(i => i.Enquiries != null && i.Enquiries.Any(i => i.Floors != null && i.Floors.Any(j => request.Floors.Contains(j))));
            }
            if (request.OfferTypes?.Any() ?? false)
            {
                query = query.Where(i => i.Enquiries != null && i.Enquiries.Any(i => i.OfferType != null && request.OfferTypes.Contains(i.OfferType.Value)));
            }
            if (request.Purposes?.Any() ?? false)
            {
                query = query.Where(i => i.Enquiries != null && i.Enquiries.Any(i => i.Purpose != null && request.Purposes.Contains(i.Purpose.Value)));
            }
            if (request.Furnished?.Any() ?? false)
            {
                query = query.Where(i => i.Enquiries != null && i.Enquiries.Any(i => i.Furnished != null && request.Furnished.Contains(i.Furnished.Value)));
            }
            if (request.SubSources != null && request.SubSources.Any())
            {
                request.SubSources = request.SubSources.ConvertAll(i => i.ToLower().Trim());
                query = query.Where(i => i.Enquiries != null && i.Enquiries.Any(i => request.SubSources.Contains(i.SubSource.ToLower().Trim())));
            }
            if (request.BHKTypes != null && request.BHKTypes.Any())
            {
                //query = query.Where(i => i.Enquiries != null && i.Enquiries.Any(i => request.BHKTypes.Contains(i.BHKType)));
                query = query.Where(i => i.Enquiries != null && i.Enquiries.Any(i => i.BHKTypes != null && i.BHKTypes.Any(j => request.BHKTypes.Contains(j))));
            }
            if (request.PropertyType != null && request.PropertyType.Any())
            {
                query = query.Where(i => i.Enquiries.Any(e => e.PropertyTypes.Any(i => request.PropertyType.Contains(i.BaseId ?? Guid.Empty))));
            }
            if (request.PropertySubType != null && request.PropertySubType.Any())
            {
                query = query.Where(i => i.Enquiries.Any(e => e.PropertyTypes.Any(i => request.PropertySubType.Contains(i.Id))));
            }
            if (request.StatusIds != null && request.StatusIds.Any())
            {
                query = query.Where(i => i.CustomLeadStatus != null && request.StatusIds.Contains(i.CustomLeadStatus.Id));
            }
            if (request.Budget != null && request.Budget.Any())
            {
                foreach (var budget in request.Budget)
                {
                    switch (budget)
                    {
                        case Budget.UpToTenLakhs:
                            query = query.Where(i => i.Enquiries != null && i.Enquiries.Any(j => j.UpperBudget <= 1000000));
                            break;
                        case Budget.TenToTwentyLakhs:
                            query = query.Where(i => i.Enquiries != null && i.Enquiries.Any(j => j.UpperBudget > 1000000 && j.UpperBudget <= 2000000));
                            break;
                        case Budget.TwentyToThirtyLakhs:
                            query = query.Where(i => i.Enquiries != null && i.Enquiries.Any(j => j.UpperBudget > 2000000 && j.UpperBudget <= 3000000));
                            break;
                        case Budget.ThirtyToFourtyLakhs:
                            query = query.Where(i => i.Enquiries != null && i.Enquiries.Any(j => j.UpperBudget > 3000000 && j.UpperBudget <= 4000000));
                            break;
                        case Budget.FourtyToFiftyLakhs:
                            query = query.Where(i => i.Enquiries != null && i.Enquiries.Any(j => j.UpperBudget > 4000000 && j.UpperBudget <= 5000000));
                            break;
                        case Budget.FiftyToOneCrore:
                            query = query.Where(i => i.Enquiries != null && i.Enquiries.Any(j => j.UpperBudget > 5000000 && j.UpperBudget <= 10000000));
                            break;
                        case Budget.MoreThanOneCrore:
                            query = query.Where(i => i.Enquiries != null && i.Enquiries.Any(j => j.UpperBudget > 10000000));
                            break;
                    }
                }
            }
            if (request.Currency != null)
            {
                query = query.Where(i => i.Enquiries.Any(e => e.Currency == request.Currency));
            }
            if (request.MinBudget != null || request.MaxBudget != null)
            {
                if (request.MinBudget != null && request.MaxBudget != null)
                {
                    query = query.Where(i => i.Enquiries.Any(i => (i.UpperBudget >= request.MinBudget && i.UpperBudget <= request.MaxBudget) || (i.LowerBudget >= request.MinBudget && i.LowerBudget <= request.MaxBudget)));
                }
                else if (request.MinBudget != null && request.MaxBudget == null)
                {
                    query = query.Where(i => i.Enquiries.Any(i => (i.UpperBudget >= request.MinBudget) || (i.LowerBudget >= request.MinBudget)));
                }
                else if (request.MinBudget == null && request.MaxBudget != null)
                {
                    query = query.Where(i => i.Enquiries.Any(i => (i.UpperBudget <= request.MaxBudget) || (i.LowerBudget <= request.MaxBudget)));
                }
            }
            if (request.BudgetFilters != null && request.BudgetFilters.Any())
            {
                foreach (var budget in request.BudgetFilters)
                {
                    query = query.Where(i => i.Enquiries != null && i.Enquiries.Any(i => i.UpperBudget >= budget.MinBudget && i.UpperBudget <= budget.MaxBudget));
                }
            }
            if (request.Locations != null && request.Locations.Any())
            {
                request.Locations = request.Locations.ConvertAll(i => Uri.UnescapeDataString(i).Replace(",", "").ToLower().Trim().Replace(" ", "")).ToList();
                query = query.Where(i => i.Enquiries.Any(e => e.Addresses.Any(j =>
                request.Locations.Contains(
                    (j.SubLocality +
                    j.Locality +
                    j.Community +
                    j.SubCommunity +
                    j.TowerName +
                    j.District +
                    j.City +
                    j.State +
                    j.Country +
                    j.PostalCode).Replace(",", "").ToLower().Trim().Replace(" ", "")))));

            }
            if (request.IntegrationAccountIds != null && request.IntegrationAccountIds.Any())
            {
                query = query.Where(i => request.IntegrationAccountIds.Contains(i.AccountId));
            }
            if (request?.OriginalOwnerIds?.Any() ?? false)
            {
                query = query.Where(i => i.OriginalOwner != null && request.OriginalOwnerIds.Contains(i.OriginalOwner.Value));
            }
            if (request.AgencyNames != null && request.AgencyNames.Any())
            {
                request.AgencyNames = request.AgencyNames.ConvertAll(i => i.ToLower().Trim());
                query = query.Where(i => i.Agencies.Any(i => i.Name != null && request.AgencyNames.Contains(i.Name.ToLower().Trim())));
            }
            if (request.CampaignNames != null && request.CampaignNames.Any())
            {
                request.CampaignNames = request.CampaignNames.ConvertAll(i => i.ToLower().Trim());
                query = query.Where(i => i.Campaigns.Any(i => i.Name != null && request.CampaignNames.Contains(i.Name.ToLower().Trim())));
            }
            if (request.LeadIds != null)
            {
                query = query.Where(i => request.LeadIds.Contains(i.Id));
            }
            if (request?.SerialNumbers?.Any() ?? false)
            {
                query = query.Where(i => request.SerialNumbers.Contains(i.SerialNumber));
            }
            /*if (request?.BookedByIds?.Any() ?? false)
            {
                query = query.Where(i => i.BookedBy != null && request.BookedByIds.Contains(i.BookedBy.Value));
            }*/

            if (request?.IsUntouched != null && request.IsUntouched != default)
            {
                query = query.Where(i => !i.IsPicked == request.IsUntouched.Value);
            }
            if (request?.BookedByIds?.Any() ?? false)
            {
                query = query.Where(i => i.BookedDetails.Any(i => request.BookedByIds.Contains(i.BookedBy.Value)));
            }
            if (request.BuiltUpArea != null && request.BuiltUpAreaUnitId != default && request.BuiltUpAreaUnitId != Guid.Empty)
            {
                query = query.Where(i => i.Enquiries.Any(i => i.BuiltUpArea == request.BuiltUpArea && i.BuiltUpAreaUnitId == request.BuiltUpAreaUnitId));
            }
            else if (request.BuiltUpArea != null)
            {
                query = query.Where(i => i.Enquiries.Any(i => i.BuiltUpArea == request.BuiltUpArea || i.BuiltUpAreaInSqMtr == request.BuiltUpArea));
            }
            if (request.SaleableArea != null && request.SaleableAreaUnitId != default && request.SaleableAreaUnitId != Guid.Empty)
            {
                query = query.Where(i => i.Enquiries.Any(i => i.SaleableArea == request.SaleableArea && i.SaleableAreaUnitId == request.SaleableAreaUnitId));
            }
            else if (request.SaleableArea != null)
            {
                query = query.Where(i => i.Enquiries.Any(i => i.SaleableArea == request.SaleableArea || i.SaleableAreaInSqMtr == request.SaleableArea));
            }
            if (request.NetArea != null && request.NetAreaUnitId != default && request.NetAreaUnitId != Guid.Empty)
            {
                query = query.Where(i => i.Enquiries.Any(i => i.NetArea == request.NetArea && i.NetAreaUnitId == request.NetAreaUnitId));
            }
            else if (request.NetArea != null)
            {
                query = query.Where(i => i.Enquiries.Any(i => i.NetArea == request.NetArea || i.NetAreaInSqMtr == request.NetArea));
            }

            if (request.PropertyArea != null && request.PropertyAreaUnitId != default && request.PropertyAreaUnitId != Guid.Empty)
            {
                query = query.Where(i => i.Enquiries.Any(i => i.PropertyArea == request.PropertyArea && i.PropertyAreaUnitId == request.PropertyAreaUnitId));
            }
            else if (request.PropertyArea != null)
            {
                query = query.Where(i => i.Enquiries.Any(i => i.PropertyArea == request.PropertyArea || i.PropertyAreaInSqMtr == request.PropertyArea));
            }
            if ((request.MinCarpetArea != null || request.MaxCarpetArea != null) && request.CarpetAreaUnitId != default)
            {
                query = query.Where(i => i.Enquiries.Any(e =>
                    e.CarpetAreaUnitId == request.CarpetAreaUnitId &&
                    (request.MinCarpetArea == null || e.CarpetArea >= request.MinCarpetArea) &&
                    (request.MaxCarpetArea == null || e.CarpetArea <= request.MaxCarpetArea)));
            }
            else if (request.MinCarpetArea != null || request.MaxCarpetArea != null)
            {
                query = query.Where(i => i.Enquiries.Any(e =>
                    (request.MinCarpetArea == null || e.CarpetArea >= request.MinCarpetArea) &&
                    (request.MaxCarpetArea == null || e.CarpetArea <= request.MaxCarpetArea)));
            }
            if ((request.MinBuiltUpArea != null || request.MaxBuiltUpArea != null) && request.BuiltUpAreaUnitId != default && request.BuiltUpAreaUnitId != Guid.Empty)
            {
                query = query.Where(i => i.Enquiries.Any(e =>
                    e.BuiltUpAreaUnitId == request.BuiltUpAreaUnitId &&
                    (request.MinBuiltUpArea == null || e.BuiltUpArea >= request.MinBuiltUpArea) &&
                    (request.MaxBuiltUpArea == null || e.BuiltUpArea <= request.MaxBuiltUpArea)));
            }
            else if (request.MinBuiltUpArea != null || request.MaxBuiltUpArea != null)
            {
                query = query.Where(i => i.Enquiries.Any(e =>
                    (request.MinBuiltUpArea == null || e.BuiltUpArea >= request.MinBuiltUpArea) &&
                    (request.MaxBuiltUpArea == null || e.BuiltUpArea <= request.MaxBuiltUpArea)));
            }
            if ((request.MinSaleableArea != null || request.MaxSaleableArea != null) && request.SaleableAreaUnitId != default && request.SaleableAreaUnitId != Guid.Empty)
            {
                query = query.Where(i => i.Enquiries.Any(e =>
                    e.SaleableAreaUnitId == request.SaleableAreaUnitId &&
                    (request.MinSaleableArea == null || e.SaleableArea >= request.MinSaleableArea) &&
                    (request.MaxSaleableArea == null || e.SaleableArea <= request.MaxSaleableArea)));
            }
            else if (request.MinSaleableArea != null || request.MaxSaleableArea != null)
            {
                query = query.Where(i => i.Enquiries.Any(e =>
                    (request.MinSaleableArea == null || e.SaleableArea >= request.MinSaleableArea) &&
                    (request.MaxSaleableArea == null || e.SaleableArea <= request.MaxSaleableArea)));
            }
            if ((request.MinPropertyArea != null || request.MaxPropertyArea != null) && request.PropertyAreaUnitId != default && request.PropertyAreaUnitId != Guid.Empty)
            {
                query = query.Where(i => i.Enquiries.Any(e =>
                    e.PropertyAreaUnitId == request.PropertyAreaUnitId &&
                    (request.MinPropertyArea == null || e.PropertyArea >= request.MinPropertyArea) &&
                    (request.MaxPropertyArea == null || e.PropertyArea <= request.MaxPropertyArea)));
            }
            else if (request.MinPropertyArea != null || request.MaxPropertyArea != null)
            {
                query = query.Where(i => i.Enquiries.Any(e =>
                    (request.MinPropertyArea == null || e.PropertyArea >= request.MinPropertyArea) &&
                    (request.MaxPropertyArea == null || e.PropertyArea <= request.MaxPropertyArea)));
            }

            if ((request.MinNetArea != null || request.MaxNetArea != null) && request.NetAreaUnitId != default && request.NetAreaUnitId != Guid.Empty)
            {
                query = query.Where(i => i.Enquiries.Any(e =>
                    e.NetAreaUnitId == request.NetAreaUnitId &&
                    (request.MinNetArea == null || e.NetArea >= request.MinNetArea) &&
                    (request.MaxNetArea == null || e.NetArea <= request.MaxNetArea)));
            }
            else if (request.MinNetArea != null || request.MaxNetArea != null)
            {
                query = query.Where(i => i.Enquiries.Any(e =>
                    (request.MinNetArea == null || e.NetArea >= request.MinNetArea) &&
                    (request.MaxNetArea == null || e.NetArea <= request.MaxNetArea)));
            }
            if (!string.IsNullOrEmpty(request.UnitName))
            {
                query = query.Where(i => i.Enquiries.Any(e => e.UnitName != null && e.UnitName.ToLower().Trim() == request.UnitName.ToLower().Trim()));

            }

            if (request.Cities?.Any() ?? false)
            {
                var normalizedCityNames = request.Cities.ConvertAll(i => i.ToLower().Trim().Replace(" ", ""));
                query = query.Where(i => i.Enquiries.Any(e => e.Addresses.Where(i => normalizedCityNames.Contains(i.City.ToLower().Trim().Replace(" ", ""))).Any()));
            }

            if (request.States?.Any() ?? false)
            {
                var normalizedStateNames = request.States.ConvertAll(i => i.ToLower().Trim().Replace(" ", ""));
                query = query.Where(i => i.Enquiries.Any(e => e.Addresses.Where(i => normalizedStateNames.Contains(i.State.ToLower().Trim().Replace(" ", ""))).Any()));
            }
            if (request.Countries?.Any() ?? false)
            {
                var normalizedStateNames = request.Countries.ConvertAll(i => i.ToLower().Trim().Replace(" ", ""));
                query = query.Where(i => i.Enquiries.Any(e => e.Addresses.Where(i => normalizedStateNames.Contains(i.Country.ToLower().Trim().Replace(" ", ""))).Any()));
            }
            if (request.Communities?.Any() ?? false)
            {
                var normalizedStateNames = request.Communities.ConvertAll(i => i.ToLower().Trim().Replace(" ", ""));
                query = query.Where(i => i.Enquiries.Any(e => e.Addresses.Where(i => normalizedStateNames.Contains(i.Community.ToLower().Trim().Replace(" ", ""))).Any()));
            }
            if (request.SubCommunities?.Any() ?? false)
            {
                var normalizedStateNames = request.SubCommunities.ConvertAll(i => i.ToLower().Trim().Replace(" ", ""));
                query = query.Where(i => i.Enquiries.Any(e => e.Addresses.Where(i => normalizedStateNames.Contains(i.SubCommunity.ToLower().Trim().Replace(" ", ""))).Any()));
            }
            if (request.TowerNames?.Any() ?? false)
            {
                var normalizedStateNames = request.TowerNames.ConvertAll(i => i.ToLower().Trim().Replace(" ", ""));
                query = query.Where(i => i.Enquiries.Any(e => e.Addresses.Where(i => normalizedStateNames.Contains(i.TowerName.ToLower().Trim().Replace(" ", ""))).Any()));
            }
            if (request.PostalCodes?.Any() ?? false)
            {
                var normalizedStateNames = request.PostalCodes.ConvertAll(i => i.Replace(" ", ""));
                query = query.Where(i => i.Enquiries.Any(e => e.Addresses.Where(i => normalizedStateNames.Contains(i.PostalCode.ToLower().Trim().Replace(" ", ""))).Any()));
            }
            if (request.NetArea != null && request.NetAreaUnitId != default && request.NetAreaUnitId != Guid.Empty)
            {
                query = query.Where(i => i.Enquiries.Any(i => i.NetArea == request.NetArea && i.NetAreaUnitId == request.NetAreaUnitId));
            }
            else if (request.NetArea != null)
            {
                query = query.Where(i => i.Enquiries.Any(i => i.NetArea == request.NetArea || i.NetAreaInSqMtr == request.NetArea));
            }
            if (request?.PossesionType != null && request?.PossesionType != PossesionType.None)
            {

                switch (request?.PossesionType)
                {
                    case PossesionType.UnderConstruction:
                        query = query.Where(i => i.PossesionType == PossesionType.UnderConstruction);
                        break;

                    case PossesionType.SixMonth:
                        query = query.Where(i => i.PossesionType == PossesionType.SixMonth);
                        break;

                    case PossesionType.Year:
                        query = query.Where(i => i.PossesionType == PossesionType.Year);
                        break;

                    case PossesionType.TwoYears:
                        query = query.Where(i => i.PossesionType == PossesionType.TwoYears);
                        break;

                    case PossesionType.CustomDate:
                        DateTime? tempToPossesionDate = request?.ToPossesionDate?.ConvertToDateToUtc();
                        DateTime? tempFrompossesionDate = request?.FromPossesionDate?.ConvertFromDateToUtc();
                        query = query.Where(i => i.Enquiries.Any(j => j.PossessionDate != null && j.PossessionDate >= tempFrompossesionDate.Value && j.PossessionDate <= tempToPossesionDate.Value));

                        break;
                }

            }
            if (request?.LandLine != null && request.LandLine.Any())
            {
                query = query.Where(i => i.LandLine != null && request.LandLine.Contains(i.LandLine.Trim()));
            }
            if (request.PropertyArea != null && request.PropertyAreaUnitId != default && request.PropertyAreaUnitId != Guid.Empty)
            {
                query = query.Where(i => i.Enquiries.Any(i => i.PropertyArea == request.PropertyArea && i.PropertyAreaUnitId == request.PropertyAreaUnitId));
            }
            else if (request.PropertyArea != null)
            {
                query = query.Where(i => i.Enquiries.Any(i => i.PropertyArea == request.PropertyArea || i.PropertyAreaInSqMtr == request.PropertyArea));
            }

            if (request?.UnitNames?.Any() ?? false)
            {
                var unitnames = request.UnitNames.ConvertAll(i => i.ToLower());
                query = query.Where(i => i.Enquiries.Count > 0 && i.Enquiries.Any(i => unitnames.Contains(i.UnitName.ToLower()))).AsQueryable();
            }
            if (request?.Nationality?.Any() ?? false)
            {
                var nationality = request.Nationality.ConvertAll(i => i.ToLower());
                query = query.Where(i => i.Nationality != null && nationality.Contains(i.Nationality.ToLower())).AsQueryable();
            }

            if (request?.ClusterName?.Any() ?? false)
            {
                var ClusterName = request.ClusterName.ConvertAll(i => i.ToLower());
                query = query.Where(i => i.Enquiries.Count > 0 && i.Enquiries.Any(i => ClusterName.Contains(i.ClusterName.ToLower()))).AsQueryable();
            }
            if (request?.LeadType != null && request.LeadType.Any())
            {
                var predicate = PredicateBuilder.New<Lead>(false);

                if (request.LeadType.Contains(LeadType.ShowPrimeLeads))
                {
                    predicate = predicate.Or(i => i.ChildLeadsCount == 0 && i.RootId == null && i.ParentLeadId == null);
                }

                if (request.LeadType.Contains(LeadType.ShowOnlyParentLeads))
                {
                    if (request.ChildLeadsCount != null && request.ChildLeadsCount > 0)
                    {
                        predicate = predicate.Or(i => i.ChildLeadsCount == request.ChildLeadsCount);
                    }
                    else
                    {
                        predicate = predicate.Or(i => i.ChildLeadsCount > 0);
                    }
                }

                if (request.LeadType.Contains(LeadType.ShowOnlyDuplicateLeads))
                {
                    predicate = predicate.Or(i => i.ChildLeadsCount == 0 && (i.RootId != null || i.ParentLeadId != null));
                }

                query = query.Where(predicate);
            }
            if (request?.CountryCode?.Any() ?? false)
            {
                var codes = request.CountryCode
        .Where(c => !string.IsNullOrWhiteSpace(c))
        .Select(c => c.Trim().TrimStart('+'))
        .Distinct()
        .ToList();

                Expression<Func<Lead, bool>> filter = lead => false;

                foreach (var code in codes)
                {
                    var local = code;

                    filter = filter.Or(lead =>
                        (lead.CountryCode != null && lead.CountryCode.Trim().TrimStart('+') == local) ||
                        (lead.ContactNo != null &&
                            (lead.ContactNo.StartsWith("+" + local) || lead.ContactNo.StartsWith(local)))
                    );
                }

                query = query.Where(filter);
            }
            if (request?.AltCountryCode?.Any() ?? false)
            {
                query = query.Where(i => i.AltCountryCode != null && request.AltCountryCode.Contains(i.AltCountryCode));
            }
            if (request?.GenderTypes?.Any() ?? false)
            {
                query = query.Where(i => i.Gender != null && request.GenderTypes.Contains(i.Gender.Value));
            }
            if (request?.MaritalStatuses?.Any() ?? false)
            {
                query = query.Where(i => i.MaritalStatus != null && request.MaritalStatuses.Contains(i.MaritalStatus.Value));
            }
            if (request?.DateOfBirth != null)
            {
                query = query.Where(i => i.DateOfBirth != null && i.DateOfBirth == request.DateOfBirth);
            }

            if (request?.CallDirections != null && request.CallDirections.Any() && !request.CallDirections.Contains(CallDirection.None))
            {
                query = query.Where(i =>
                    i.LeadCallLogs != null && i.LeadCallLogs.Any(j => request.CallDirections.Contains(j.CallDirection)));
            }

            if (request?.CallStatuses != null && request.CallStatuses.Any() && !request.CallStatuses.Contains(CallStatus.None))
            {
                query = query.Where(i =>
                    i.LeadCallLogs != null && i.LeadCallLogs.Any(j => request.CallStatuses.Contains(j.CallStatus)));
            }

            if (request?.UserIds != null && request.UserIds.Any())
            {
                query = query.Where(i =>
                    i.LeadCallLogs != null && i.LeadCallLogs.Any(j => request.UserIds.Contains(j.UserId)));
            }

            return query.AsQueryable();
        }
        public (List<AppointmentType>, List<bool>) GetAppointmentTypes(Application.Lead.Mobile.v2.V2GetAllLeadsRequest request)
        {
            List<AppointmentType> appTypes = new();
            List<bool> appDoneStatuses = new();
            if (request.MeetingOrVisitStatuses == null)
            {
                return (appTypes, appDoneStatuses);
            }
            request.MeetingOrVisitStatuses?.ForEach(appType =>
            {
                switch (appType)
                {
                    case MeetingOrVisitCompletionStatus.IsMeetingDone:
                        appTypes.Add(AppointmentType.Meeting);
                        appDoneStatuses.Add(true);
                        break;
                    case MeetingOrVisitCompletionStatus.IsMeetingNotDone:
                        appTypes.Add(AppointmentType.Meeting);
                        appDoneStatuses.Add(false);
                        break;
                    case MeetingOrVisitCompletionStatus.IsSiteVisitDone:
                        appTypes.Add(AppointmentType.SiteVisit);
                        appDoneStatuses.Add(true);
                        break;
                    case MeetingOrVisitCompletionStatus.IsSiteVisitNotDone:
                        appTypes.Add(AppointmentType.SiteVisit);
                        appDoneStatuses.Add(false);
                        break;
                }
            });
            appTypes = appTypes.Distinct().ToList();
            appDoneStatuses = appDoneStatuses.Distinct().ToList();
            return (appTypes, appDoneStatuses);
        }
        public (List<AppointmentType>, List<bool>) GetAppointmentTypes(Application.Lead.Mobile.v2.V2GetLeadCategoryRequest request)
        {
            List<AppointmentType> appTypes = new();
            List<bool> appDoneStatuses = new();
            if (request.MeetingOrVisitStatuses == null)
            {
                return (appTypes, appDoneStatuses);
            }
            foreach (var appType in request.MeetingOrVisitStatuses)
            {
                switch (appType)
                {
                    case MeetingOrVisitCompletionStatus.IsMeetingDone:
                        appTypes.Add(AppointmentType.Meeting);
                        appDoneStatuses.Add(true);
                        break;
                    case MeetingOrVisitCompletionStatus.IsMeetingNotDone:
                        appTypes.Add(AppointmentType.Meeting);
                        appDoneStatuses.Add(false);
                        break;
                    case MeetingOrVisitCompletionStatus.IsSiteVisitDone:
                        appTypes.Add(AppointmentType.SiteVisit);
                        appDoneStatuses.Add(true);
                        break;
                    case MeetingOrVisitCompletionStatus.IsSiteVisitNotDone:
                        appTypes.Add(AppointmentType.SiteVisit);
                        appDoneStatuses.Add(false);
                        break;
                }
            }
            appTypes = appTypes.Distinct().ToList();
            appDoneStatuses = appDoneStatuses.Distinct().ToList();
            return (appTypes, appDoneStatuses);
        }
        private IQueryable<Lead> BuildQuery(Application.Lead.Mobile.v2.V2GetLeadCategoryRequest request, Guid userId, IList<Guid> subIds, List<Guid>? leadHistoryIds, List<Guid>? scheduledMeetingLeadHistoryIds, List<Guid>? scheduledVisitLeadHistoryIds, List<CustomMasterLeadStatus> customLeadStatuses = null,bool? isAdmin = null)
        {
            var tenantId = _currentUser.GetTenant();
            var scope = _provider.CreateScope();
            var context = scope.ServiceProvider.GetRequiredService<ApplicationDbContext>();
            //request.UserType = request?.UserType ?? UserType.None;
            var reporteeIds = subIds.Where(i => i != userId && i != Guid.Empty);
            var selfWithReporteeIds = subIds.Where(i => i != Guid.Empty);
            var query = context.Leads.Where(i => !i.IsDeleted)
                .Include(i => i.CustomLeadStatus)
                .Include(i => i.Enquiries)
                    //.ThenInclude(i => i.Address)
                    .ThenInclude(i => i.Addresses)
                .Include(i => i.Enquiries)
                    .ThenInclude(i => i.PropertyType)
                .Include(i => i.Appointments)
                    .ThenInclude(i => i.Location)
                .Include(i => i.Projects)
                .Include(i => i.Properties)
                .Include(i => i.CustomFlags)
                .ThenInclude(i => i.Flag)
                .Include(i => i.Agencies)
                .Include(i => i.Enquiries)
                .ThenInclude(i => i.PropertyTypes)
                .AsQueryable();


            if (request.CustomFlags?.Any() ?? false)
            {
                query = query.Where(i => i.CustomFlags != null && i.CustomFlags.Any(j => j.Flag != null && request.CustomFlags.Contains(j.Flag.Name ?? string.Empty)));
            }
            //if (request?.IsDualOwnershipEnabled ?? false)
            //{
            //    if (request.SecondaryUsers?.Any() ?? false)
            //    {
            //        query = query.Where(i => request.SecondaryUsers.Contains(i.SecondaryUserId ?? Guid.Empty));
            //    }
            //}
            if (request.DataConverted != null)
            {
                if (request.DataConverted == true)
                {
                    query = query.Where(i => i.IsConvertedFromData == true);
                }
                else
                {
                    query = query.Where(i => i.IsConvertedFromData != true); 
                }
            }
            if (request.QualifiedByIds?.Any() ?? false)
            {
                query = query.Where(i => i.QualifiedBy.HasValue && request.QualifiedByIds.Contains(i.QualifiedBy.Value));
            }
            if (request.IsPicked != null && request.IsPicked != default)
            {
                query = query.Where(i => i.IsPicked == request.IsPicked.Value);
            }

            if (!string.IsNullOrWhiteSpace(request.ConfidentialNotes))
            {
                request.ConfidentialNotes = request.ConfidentialNotes.ToLower().Trim();
                query = query.Where(i => i.ConfidentialNotes.ToLower().Trim().Contains(request.ConfidentialNotes));
            }

            if (request.CarpetArea != null && request.CarpetAreaUnitId != default)
            {
                query = query.Where(i => i.Enquiries.Any(i => i.CarpetArea == request.CarpetArea && i.CarpetAreaUnitId == request.CarpetAreaUnitId));
            }
            else if (request.CarpetArea != null)
            {
                query = query.Where(i => i.Enquiries.Any(i => i.CarpetArea == request.CarpetArea || i.CarpetAreaInSqMtr == request.CarpetArea));
            }

            if (!string.IsNullOrEmpty(request.ReferralName))
            {
                query = query.Where(i => (i.ReferralName.ToLower().Trim().Contains(request.ReferralName.ToLower().Trim())));
            }
            if (!string.IsNullOrEmpty(request.ReferralContactNo))
            {
                query = query.Where(i => !string.IsNullOrEmpty(i.ReferralContactNo) && request.ReferralContactNo.Contains(i.ReferralContactNo.Substring(i.ReferralContactNo.Length - 10)));
            }
            if (!string.IsNullOrEmpty(request.ReferralEmail))
            {
                query = query.Where(i => (i.ReferralEmail.ToLower().Trim().Contains(request.ReferralEmail.ToLower().Trim())));
            }

            if (leadHistoryIds?.Any() ?? false)
            {
                query = query.Where(i => leadHistoryIds.Contains(i.Id));
            }
            //if (request?.MeetingOrVisitStatuses?.Any() ?? false)
            //{
            //    query = query.Where(i => (request.MeetingOrVisitStatuses.Contains(MeetingOrVisitCompletionStatus.IsMeetingDone) && i.Appointments.Any(i => i.Type == AppointmentType.Meeting && i.IsDone))
            //            || (request.MeetingOrVisitStatuses.Contains(MeetingOrVisitCompletionStatus.IsMeetingNotDone) && i.Appointments.Any(i => i.Type == AppointmentType.Meeting && !i.IsDone))
            //            || (request.MeetingOrVisitStatuses.Contains(MeetingOrVisitCompletionStatus.IsSiteVisitDone) && i.Appointments.Any(i => i.Type == AppointmentType.SiteVisit && i.IsDone))
            //            || (request.MeetingOrVisitStatuses.Contains(MeetingOrVisitCompletionStatus.IsSiteVisitNotDone) && i.Appointments.Any(i => i.Type == AppointmentType.SiteVisit && !i.IsDone)));
            //}

            //request.FromDateForMeetingOrVisit = request.FromDateForMeetingOrVisit != null ? request.FromDateForMeetingOrVisit.Value.ConvertFromDateToUtc() : null;
            //request.ToDateForMeetingOrVisit = request.ToDateForMeetingOrVisit != null ? request.ToDateForMeetingOrVisit.Value.ConvertToDateToUtc() : null;

            //if (request.FromDateForMeetingOrVisit != null && request.ToDateForMeetingOrVisit != null)
            //{
            //    query = query.Where(i => i.Appointments.Any(i => i.LastModifiedOn > request.FromDateForMeetingOrVisit.Value && i.LastModifiedOn < request.ToDateForMeetingOrVisit.Value));
            //}
            //else if (request.FromDateForMeetingOrVisit != null)
            //{
            //    query = query.Where(i => i.Appointments.Any(i => i.LastModifiedOn > request.FromDateForMeetingOrVisit.Value));
            //}
            //else if (request.ToDateForMeetingOrVisit != null)
            //{
            //    query = query.Where(i => i.Appointments.Any(i => i.LastModifiedOn < request.ToDateForMeetingOrVisit.Value));
            //}


            if (request?.MeetingOrVisitStatuses?.Any() ?? false)
            {
                DateTime? fromDateForMeetingOrVisit = request.FromDateForMeetingOrVisit != null ? request.FromDateForMeetingOrVisit.Value.ConvertFromDateToUtc() : null;
                DateTime? toDateForMeetingOrVisit = request.ToDateForMeetingOrVisit != null ? request.ToDateForMeetingOrVisit.Value.ConvertToDateToUtc() : null;
                (List<AppointmentType> appTypes, List<bool> appDoneStatuses) = GetAppointmentTypes(request);
                if (request.AppointmentDoneByUserIds?.Any() ?? false)
                {
                    //query = query.Where(i => i.Appointments.Any(app => request.AppointmentDoneByUserIds.Contains(app.CreatedBy) && app.Type != AppointmentType.None));
                    query = query.Where(i => i.Appointments.Any(app => request.AppointmentDoneByUserIds.Contains(app.CreatedBy) && app.Type != AppointmentType.None && appTypes.Contains(app.Type) && appDoneStatuses.Contains(app.IsDone)));
                }
                if (!(isAdmin ?? false) && (!request.AppointmentDoneByUserIds?.Any() ?? true))
                {
                    query = query.Where(i => i.Appointments.Any(j => j.UserId == i.AssignTo && j.Type != AppointmentType.None));
                }
                if (fromDateForMeetingOrVisit != null && toDateForMeetingOrVisit != null)
                {
                    query = query.Where(i => (request.MeetingOrVisitStatuses.Contains(MeetingOrVisitCompletionStatus.IsMeetingDone)
                                         && i.Appointments.Any(i => i.Type == AppointmentType.Meeting && i.IsDone && i.CreatedOn > fromDateForMeetingOrVisit && i.CreatedOn < toDateForMeetingOrVisit))
                                         ||
                                             (request.MeetingOrVisitStatuses.Contains(MeetingOrVisitCompletionStatus.IsMeetingNotDone)
                                             && i.Appointments.Any(i => i.Type == AppointmentType.Meeting && !i.IsDone && i.CreatedOn > fromDateForMeetingOrVisit && i.CreatedOn < toDateForMeetingOrVisit))
                                         ||
                                             (request.MeetingOrVisitStatuses.Contains(MeetingOrVisitCompletionStatus.IsSiteVisitDone)
                                             && i.Appointments.Any(i => i.Type == AppointmentType.SiteVisit && i.IsDone && i.CreatedOn > fromDateForMeetingOrVisit && i.CreatedOn < toDateForMeetingOrVisit))
                                         ||
                                             (request.MeetingOrVisitStatuses.Contains(MeetingOrVisitCompletionStatus.IsSiteVisitNotDone)
                                             && i.Appointments.Any(i => i.Type == AppointmentType.SiteVisit && !i.IsDone && i.CreatedOn > fromDateForMeetingOrVisit && i.CreatedOn < toDateForMeetingOrVisit)));
                }
                else if (fromDateForMeetingOrVisit != null)
                {
                    query = query.Where(i => (request.MeetingOrVisitStatuses.Contains(MeetingOrVisitCompletionStatus.IsMeetingDone)
                                         && i.Appointments.Any(i => i.Type == AppointmentType.Meeting && i.IsDone && i.CreatedOn > fromDateForMeetingOrVisit))
                                         ||
                                             (request.MeetingOrVisitStatuses.Contains(MeetingOrVisitCompletionStatus.IsMeetingNotDone)
                                             && i.Appointments.Any(i => i.Type == AppointmentType.Meeting && !i.IsDone && i.CreatedOn > fromDateForMeetingOrVisit))
                                         ||
                                             (request.MeetingOrVisitStatuses.Contains(MeetingOrVisitCompletionStatus.IsSiteVisitDone)
                                             && i.Appointments.Any(i => i.Type == AppointmentType.SiteVisit && i.IsDone && i.CreatedOn > fromDateForMeetingOrVisit))
                                         ||
                                             (request.MeetingOrVisitStatuses.Contains(MeetingOrVisitCompletionStatus.IsSiteVisitNotDone)
                                             && i.Appointments.Any(i => i.Type == AppointmentType.SiteVisit && !i.IsDone && i.CreatedOn > fromDateForMeetingOrVisit)));
                }
                else if (toDateForMeetingOrVisit != null)
                {
                    query = query.Where(i => (request.MeetingOrVisitStatuses.Contains(MeetingOrVisitCompletionStatus.IsMeetingDone)
                                         && i.Appointments.Any(i => i.Type == AppointmentType.Meeting && i.IsDone && i.CreatedOn < toDateForMeetingOrVisit.Value))
                                         ||
                                             (request.MeetingOrVisitStatuses.Contains(MeetingOrVisitCompletionStatus.IsMeetingNotDone)
                                             && i.Appointments.Any(i => i.Type == AppointmentType.Meeting && !i.IsDone && i.CreatedOn < toDateForMeetingOrVisit.Value))
                                         ||
                                             (request.MeetingOrVisitStatuses.Contains(MeetingOrVisitCompletionStatus.IsSiteVisitDone)
                                             && i.Appointments.Any(i => i.Type == AppointmentType.SiteVisit && i.IsDone && i.CreatedOn < toDateForMeetingOrVisit.Value))
                                         ||
                                             (request.MeetingOrVisitStatuses.Contains(MeetingOrVisitCompletionStatus.IsSiteVisitNotDone)
                                             && i.Appointments.Any(i => i.Type == AppointmentType.SiteVisit && !i.IsDone && i.CreatedOn < toDateForMeetingOrVisit.Value)));
                }
                else if ((request.AssignToIds?.Any() ?? false) && (request.OwnerSelection == OwnerSelectionType.Both) && (isAdmin ?? false))
                {
                    query = query.Where(i => (request.MeetingOrVisitStatuses.Contains(MeetingOrVisitCompletionStatus.IsMeetingDone)
                                               && i.Appointments.Any(j => j.Type == AppointmentType.Meeting && j.IsDone && (j.UserId != Guid.Empty) && (subIds != null) && (subIds.Contains(i.AssignTo) || request.AssignToIds.Contains(i.SecondaryUserId.Value))))
                                          ||
                                               (request.MeetingOrVisitStatuses.Contains(MeetingOrVisitCompletionStatus.IsMeetingNotDone)
                                               && i.Appointments.Any(j => j.Type == AppointmentType.Meeting && !j.IsDone && (j.UserId != Guid.Empty) && (subIds != null) && (subIds.Contains(i.AssignTo) || request.AssignToIds.Contains(i.SecondaryUserId.Value))))
                                          ||
                                               (request.MeetingOrVisitStatuses.Contains(MeetingOrVisitCompletionStatus.IsSiteVisitDone)
                                               && i.Appointments.Any(j => j.Type == AppointmentType.SiteVisit && j.IsDone && (j.UserId != Guid.Empty) && (subIds != null) && (subIds.Contains(i.AssignTo) || request.AssignToIds.Contains(i.SecondaryUserId.Value))))
                                          ||
                                               (request.MeetingOrVisitStatuses.Contains(MeetingOrVisitCompletionStatus.IsSiteVisitNotDone)
                                               && i.Appointments.Any(j => j.Type == AppointmentType.SiteVisit && !j.IsDone && (j.UserId != Guid.Empty) && (subIds != null) && (subIds.Contains(i.AssignTo) || request.AssignToIds.Contains(i.SecondaryUserId.Value)))));

                }
                else if ((request.OwnerSelection == OwnerSelectionType.SecondaryOwner) && (request.AssignToIds?.Any() ?? false) && (isAdmin ?? false))
                {
                    query = query.Where(i => (request.MeetingOrVisitStatuses.Contains(MeetingOrVisitCompletionStatus.IsMeetingDone)
                                                && i.Appointments.Any(j => j.Type == AppointmentType.Meeting && j.IsDone && (j.UserId != Guid.Empty) && request.AssignToIds.Contains(i.SecondaryUserId.Value)))
                                           ||
                                                (request.MeetingOrVisitStatuses.Contains(MeetingOrVisitCompletionStatus.IsMeetingNotDone)
                                                && i.Appointments.Any(j => j.Type == AppointmentType.Meeting && !j.IsDone && (j.UserId != Guid.Empty) && request.AssignToIds.Contains(i.SecondaryUserId.Value)))
                                           ||
                                                (request.MeetingOrVisitStatuses.Contains(MeetingOrVisitCompletionStatus.IsSiteVisitDone)
                                                && i.Appointments.Any(j => j.Type == AppointmentType.SiteVisit && j.IsDone && (j.UserId != Guid.Empty) && request.AssignToIds.Contains(i.SecondaryUserId.Value)))
                                           ||
                                                (request.MeetingOrVisitStatuses.Contains(MeetingOrVisitCompletionStatus.IsSiteVisitNotDone)
                                                && i.Appointments.Any(j => j.Type == AppointmentType.SiteVisit && !j.IsDone && (j.UserId != Guid.Empty) && request.AssignToIds.Contains(i.SecondaryUserId.Value))));

                }
                else if ((request.AssignToIds?.Any() ?? false) && (isAdmin ?? false))
                {
                    query = query.Where(i => (request.MeetingOrVisitStatuses.Contains(MeetingOrVisitCompletionStatus.IsMeetingDone)
                                                && i.Appointments.Any(j => j.Type == AppointmentType.Meeting && j.IsDone && (j.UserId != Guid.Empty) && (subIds != null) && subIds.Contains(i.AssignTo)))
                                           ||
                                                (request.MeetingOrVisitStatuses.Contains(MeetingOrVisitCompletionStatus.IsMeetingNotDone)
                                                && i.Appointments.Any(j => j.Type == AppointmentType.Meeting && !j.IsDone && (j.UserId != Guid.Empty) && (subIds != null) && subIds.Contains(i.AssignTo)))
                                           ||
                                                (request.MeetingOrVisitStatuses.Contains(MeetingOrVisitCompletionStatus.IsSiteVisitDone)
                                                && i.Appointments.Any(j => j.Type == AppointmentType.SiteVisit && j.IsDone && (j.UserId != Guid.Empty) && (subIds != null) && subIds.Contains(i.AssignTo)))
                                           ||
                                                (request.MeetingOrVisitStatuses.Contains(MeetingOrVisitCompletionStatus.IsSiteVisitNotDone)
                                                && i.Appointments.Any(j => j.Type == AppointmentType.SiteVisit && !j.IsDone && (j.UserId != Guid.Empty) && (subIds != null) && subIds.Contains(i.AssignTo))));

                }

                else
                {
                    query = query.Where(i => (request.MeetingOrVisitStatuses.Contains(MeetingOrVisitCompletionStatus.IsMeetingDone)
                                                && i.Appointments.Any(i => i.Type == AppointmentType.Meeting && i.IsDone))
                                           ||
                                                (request.MeetingOrVisitStatuses.Contains(MeetingOrVisitCompletionStatus.IsMeetingNotDone)
                                                && i.Appointments.Any(i => i.Type == AppointmentType.Meeting && !i.IsDone))
                                           ||
                                                (request.MeetingOrVisitStatuses.Contains(MeetingOrVisitCompletionStatus.IsSiteVisitDone)
                                                && i.Appointments.Any(i => i.Type == AppointmentType.SiteVisit && i.IsDone))
                                           ||
                                                (request.MeetingOrVisitStatuses.Contains(MeetingOrVisitCompletionStatus.IsSiteVisitNotDone)
                                                && i.Appointments.Any(i => i.Type == AppointmentType.SiteVisit && !i.IsDone)));
                }

            }
            if ((request?.IsDualOwnershipEnabled ?? false) && (request?.AssignToIds?.Any() ?? false) && (request.OwnerSelection == OwnerSelectionType.Both))
            {
                switch (request.LeadVisibility)
                {
                    case BaseLeadVisibility.SelfWithReportee:
                        query = query.Where(i => (selfWithReporteeIds.Contains(i.AssignTo) || selfWithReporteeIds.Contains(i.SecondaryUserId ?? Guid.Empty))).Where(i => !i.IsArchived);
                        break;
                    case BaseLeadVisibility.Self:
                        query = query.Where(i => (i.AssignTo == userId || i.SecondaryUserId == userId)).Where(i => !i.IsArchived);
                        break;
                    case BaseLeadVisibility.Reportee:
                        query = query.Where(i => (reporteeIds.Contains(i.AssignTo) || reporteeIds.Contains(i.SecondaryUserId ?? Guid.Empty))).Where(i => !i.IsArchived);
                        break;
                    case BaseLeadVisibility.UnassignLead:
                        query = query.Where(i => i.AssignTo == Guid.Empty).Where(i => !i.IsArchived);
                        break;
                    case BaseLeadVisibility.DeletedLeads:
                        query = query.Where(i => i.IsArchived && selfWithReporteeIds.Contains(i.AssignTo));
                        break;
                    default:
                        break;
                }
            }
            else if ((request?.IsDualOwnershipEnabled ?? false) && (request?.AssignToIds?.Any() ?? false) && (request.OwnerSelection == OwnerSelectionType.SecondaryOwner))
            {
                switch (request.LeadVisibility)
                {
                    case BaseLeadVisibility.SelfWithReportee:
                        query = query.Where(i => selfWithReporteeIds.Contains(i.SecondaryUserId.Value)).Where(i => !i.IsArchived);
                        break;
                    case BaseLeadVisibility.Self:
                        query = query.Where(i => i.SecondaryUserId == userId).Where(i => !i.IsArchived);
                        break;
                    case BaseLeadVisibility.Reportee:
                        query = query.Where(i => reporteeIds.Contains(i.SecondaryUserId.Value)).Where(i => !i.IsArchived);
                        break;
                    case BaseLeadVisibility.UnassignLead:
                        query = query.Where(i => i.AssignTo == Guid.Empty).Where(i => !i.IsArchived);
                        break;
                    case BaseLeadVisibility.DeletedLeads:
                        query = query.Where(i => i.IsArchived && selfWithReporteeIds.Contains(i.SecondaryUserId.Value));
                        break;
                    default:
                        break;
                }
            }
            else if ((request?.IsDualOwnershipEnabled ?? false) && (!request?.AssignToIds?.Any() ?? true))
            {
                switch (request.LeadVisibility)
                {
                    case BaseLeadVisibility.SelfWithReportee:
                        query = query.Where(i => (selfWithReporteeIds.Contains(i.AssignTo) || selfWithReporteeIds.Contains(i.SecondaryUserId ?? Guid.Empty))).Where(i => !i.IsArchived);
                        break;
                    case BaseLeadVisibility.Self:
                        query = query.Where(i => (i.AssignTo == userId || i.SecondaryUserId == userId)).Where(i => !i.IsArchived);
                        break;
                    case BaseLeadVisibility.Reportee:
                        query = query.Where(i => (reporteeIds.Contains(i.AssignTo) || reporteeIds.Contains(i.SecondaryUserId ?? Guid.Empty))).Where(i => !i.IsArchived);
                        break;
                    case BaseLeadVisibility.UnassignLead:
                        query = query.Where(i => i.AssignTo == Guid.Empty).Where(i => !i.IsArchived);
                        break;
                    case BaseLeadVisibility.DeletedLeads:
                        query = query.Where(i => i.IsArchived && selfWithReporteeIds.Contains(i.AssignTo));
                        break;
                    default:
                        break;
                }
            }
            else
            {
                switch (request.LeadVisibility)
                {
                    case BaseLeadVisibility.SelfWithReportee:
                        query = query.Where(i => selfWithReporteeIds.Contains(i.AssignTo)).Where(i => !i.IsArchived);
                        break;
                    case BaseLeadVisibility.Self:
                        query = query.Where(i => i.AssignTo == userId).Where(i => !i.IsArchived);
                        break;
                    case BaseLeadVisibility.Reportee:
                        query = query.Where(i => reporteeIds.Contains(i.AssignTo)).Where(i => !i.IsArchived);
                        break;
                    case BaseLeadVisibility.UnassignLead:
                        query = query.Where(i => i.AssignTo == Guid.Empty).Where(i => !i.IsArchived);
                        break;
                    case BaseLeadVisibility.DeletedLeads:
                        query = query.Where(i => i.IsArchived && selfWithReporteeIds.Contains(i.AssignTo));
                        break;
                    default:
                        break;
                }
            }
            if (request.EnquiredFor != null && request.EnquiredFor.Any())
            {
                //query = query.Where(i => i.Enquiries != null && i.Enquiries.Any(i => request.EnquiredFor.Contains(i.EnquiredFor)));
                query = query.Where(i => i.Enquiries != null && i.Enquiries.Any(i => i.EnquiryTypes != null && i.EnquiryTypes.Any(j => request.EnquiredFor.Contains(j))));
            }
            //if (request.EnquiredFor.HasValue)
            //{
            //    query = query.Where(i => i.Enquiries != null && i.Enquiries.Count > 0 && i.Enquiries.Any(i => i.IsPrimary && i.EnquiredFor == request.EnquiredFor.Value));
            //}
            if (request.DateType.HasValue && ((request.FromDate.HasValue && request.FromDate.Value != default) || (request.ToDate.HasValue && request.ToDate.Value != default)))
            {
                DateTime? fromDate = request.FromDate.HasValue ? request.FromDate.Value.ConvertFromDateToUtc() : DateTime.MinValue;
                DateTime? toDate = request.ToDate.HasValue ? request.ToDate.Value.ConvertToDateToUtc() : DateTime.MaxValue;
                switch (request.DateType)
                {
                    case DateType.ReceivedDate:
                        query = query.Where(i => i.CreatedOn >= fromDate.Value && i.CreatedOn <= toDate.Value);
                        break;
                    case DateType.ScheduledDate:
                        query = query.Where(i => i.ScheduledDate != null && i.ScheduledDate.Value >= fromDate.Value && i.ScheduledDate.Value <= toDate.Value);
                        break;
                    case DateType.ModifiedDate:
                        query = query.Where(i => i.LastModifiedOn != null && i.LastModifiedOn.Value >= fromDate.Value && i.LastModifiedOn.Value <= toDate.Value);
                        break;
                    case DateType.DeletedDate:
                        query = query.Where(i => i.ArchivedOn >= fromDate.Value && i.ArchivedOn < toDate.Value);
                        break;
                    case DateType.PickedDate:
                        query = query.Where(i => i.PickedDate >= fromDate.Value && i.PickedDate <= toDate.Value);
                        break;
                    case DateType.BookedDate:
                        query = query.Where(item => item.BookedDetails.Any(detail => detail.BookedDate.Value >= fromDate.Value && detail.BookedDate.Value <= toDate.Value));
                        break;
                    case DateType.AssignedDate:
                        query = query.Where(i => i.Assignments != null && i.Assignments.Any(i => i.AssignmentDate != null && i.AssignmentDate.Value >= fromDate.Value && i.AssignmentDate.Value <= toDate.Value));
                        break;
                    default:
                        break;
                }
            }
            if (!string.IsNullOrEmpty(request?.DatesJsonFormattedString ?? string.Empty))
            {
                try
                {
                    var data = JsonConvert.DeserializeObject<List<Lrb.Application.Lead.Mobile.v2.Date>>(request?.DatesJsonFormattedString ?? string.Empty);
                    if (data?.Any() ?? false)
                    {
                        request.Dates = data;
                    }
                    else
                    {
                        request.Dates = null;
                    }
                }
                catch (Exception ex)
                {
                    request.Dates = null;
                    Console.WriteLine("Exception details while deserializing the date object " + ex.Serialize());
                }
            }
            if (request.Dates?.Any() ?? false)
            {
                foreach (var date in request.Dates)
                {
                    if (date.MultiDateType.HasValue && ((date.MultiFromDate.HasValue && date.MultiFromDate.Value != default) || (date.MultiToDate.HasValue && date.MultiToDate.Value != default)))
                    {
                        DateTime? multiFromDate = date.MultiFromDate.HasValue ? date.MultiFromDate.Value.ConvertFromDateToUtc() : DateTime.MinValue;
                        DateTime? multiToDate = date.MultiToDate.HasValue ? date.MultiToDate.Value.ConvertToDateToUtc() : DateTime.MaxValue;

                        switch (date.MultiDateType)
                        {
                            case DateType.ReceivedDate:
                                query = query.Where(i => i.CreatedOn >= multiFromDate.Value && i.CreatedOn <= multiToDate.Value);
                                break;
                            case DateType.ScheduledDate:
                                query = query.Where(i => i.ScheduledDate != null && i.ScheduledDate.Value >= multiFromDate.Value && i.ScheduledDate.Value <= multiToDate.Value);
                                break;
                            case DateType.ModifiedDate:
                                query = query.Where(i => i.LastModifiedOn != null && i.LastModifiedOn.Value >= multiFromDate.Value && i.LastModifiedOn.Value <= multiToDate.Value);
                                break;
                            case DateType.DeletedDate:
                                query = query.Where(i => i.ArchivedOn >= multiFromDate.Value && i.ArchivedOn < multiToDate.Value);
                                break;
                            case DateType.PickedDate:
                                query = query.Where(i => i.PickedDate >= multiFromDate.Value && i.PickedDate <= multiToDate.Value);
                                break;
                            case DateType.AssignedDate:
                                query = query.Where(i => i.Assignments != null && i.Assignments.Any(i => i.AssignmentDate != null && i.AssignmentDate.Value >= multiFromDate.Value && i.AssignmentDate.Value <= multiToDate.Value));
                                break;
                            default:
                                break;
                        }
                    }
                }
            }

            if (request.Source?.Any() ?? false)
            {
                query = query.Where(i => i.Enquiries != null && i.Enquiries.Any(i => request.Source.Contains(i.LeadSource)));
            }
            var Statuses = (customLeadStatuses?.Where(i => i.Status is "not_interested" or "dropped" or "booked" or "booking_cancel"))?.ToList() ?? new();
            var StatusesIds = Statuses.Select(i => i.Id).ToList();
            var ScheduledStatus = (customLeadStatuses?.Where(i => i.Status is "site_visit_scheduled" or "callback" or "meeting_scheduled"))?.ToList() ?? new();
            var schuduledStatusIds = ScheduledStatus.Select(i => i.Id).ToList();
            TimeSpan userUtcOffset = request.BaseUTcOffset ?? TimeZoneInfo.Local.GetUtcOffset(DateTime.UtcNow);
            DateTime today = DateTime.UtcNow.Add(userUtcOffset).Date;
            switch (request.FilterType)
            {
                case LeadFilterTypeMobile.New:
                    query = query.Where(i => i.CustomLeadStatus != null && i.CustomLeadStatus.Status == "new");
                    break;
                #region ScheduleDate based categories
                case LeadFilterTypeMobile.ScheduleToday:
                    query = query.Where(i => ((i.ScheduledDate ?? DateTime.MinValue) + userUtcOffset) >= today && ((i.ScheduledDate ?? DateTime.MinValue) + userUtcOffset) < today.AddDays(1))
                    .Where(i => i.CustomLeadStatus == null || schuduledStatusIds.Contains(i.CustomLeadStatus.BaseId ?? Guid.Empty) || schuduledStatusIds.Contains(i.CustomLeadStatus.Id)); ;
                    break;
                case LeadFilterTypeMobile.Overdue:
                    query = query.Where(i => ((i.ScheduledDate ?? DateTime.MinValue) + userUtcOffset) < today)
                    .Where(i => i.CustomLeadStatus != null && (schuduledStatusIds.Contains(i.CustomLeadStatus.BaseId ?? Guid.Empty) || schuduledStatusIds.Contains(i.CustomLeadStatus.Id)));
                    break;
                case LeadFilterTypeMobile.ScheduledTomorrow:
                    query = query.Where(i => ((i.ScheduledDate ?? DateTime.MinValue) + userUtcOffset) >= today.AddDays(1) && ((i.ScheduledDate ?? DateTime.MinValue) + userUtcOffset) < today.AddDays(2))
                    .Where(i => i.CustomLeadStatus != null && (schuduledStatusIds.Contains(i.CustomLeadStatus.BaseId ?? Guid.Empty) || schuduledStatusIds.Contains(i.CustomLeadStatus.Id)));
                    break;
                case LeadFilterTypeMobile.UpcomingSchedules:
                    query = query.Where(i => ((i.ScheduledDate ?? DateTime.MinValue) + userUtcOffset) >= today.AddDays(1))
                    .Where(i => i.CustomLeadStatus == null || schuduledStatusIds.Contains(i.CustomLeadStatus.BaseId ?? Guid.Empty) || schuduledStatusIds.Contains(i.CustomLeadStatus.Id));
                    break;
                #endregion
                case LeadFilterTypeMobile.Pending:
                    query = query.Where(i => i.CustomLeadStatus != null && i.CustomLeadStatus.Status == "pending");
                    break;
                case LeadFilterTypeMobile.NotInterested:
                    query = query.Where(i => i.CustomLeadStatus != null && (i.CustomLeadStatus.MasterLeadStatusBaseId == Guid.Parse("065fa6fd-a15e-4248-b469-e8596980c97a") || i.CustomLeadStatus.Status == "not_interested"));
                    break;
                case LeadFilterTypeMobile.CallBack:
                    query = query.Where(i => i.CustomLeadStatus != null && i.CustomLeadStatus.MasterLeadStatusBaseId == Guid.Parse("5ae346bc-c695-4af4-8c3b-c8648587fbd6"));
                    break;
                case LeadFilterTypeMobile.SiteVisitScheduled:
                    query = query.Where(i => i.CustomLeadStatus != null && (i.CustomLeadStatus.Status == "site_visit_scheduled" || i.CustomLeadStatus.MasterLeadStatusBaseId.ToString() == "59647294-09d6-44a2-a346-9de5ba829e04"));
                    //if (scheduledVisitLeadHistoryIds?.Any() ?? false)
                    //{
                    //    var dNStatuses = new List<string>() { "not_interested", "dropped" };
                    //    var dNStatusesIds = new List<Guid?>() { Guid.Parse("065fa6fd-a15e-4248-b469-e8596980c97a"), Guid.Parse("023fcb89-037e-42f4-bcc2-647bb4e95c99") };
                    //    query = query.Where(i => scheduledVisitLeadHistoryIds.Contains(i.Id) && i.Status != null && !dNStatuses.Contains(i.Status.Status) && !dNStatusesIds.Contains(i.Status.BaseId));
                    //}
                    break;
                case LeadFilterTypeMobile.ScheduledMeeting:
                    query = query.Where(i => i.CustomLeadStatus != null && (i.CustomLeadStatus.Status == "meeting_scheduled" || i.CustomLeadStatus.MasterLeadStatusBaseId.ToString() == "99a7f794-9046-4a9d-b7e2-e0a2196b98dd"));
                    //if (scheduledMeetingLeadHistoryIds?.Any() ?? false)
                    //{
                    //    var dNStatuses = new List<string>() { "not_interested", "dropped" };
                    //    var dNStatusesIds = new List<Guid?>() { Guid.Parse("065fa6fd-a15e-4248-b469-e8596980c97a"), Guid.Parse("023fcb89-037e-42f4-bcc2-647bb4e95c99") };
                    //    query = query.Where(i => scheduledMeetingLeadHistoryIds.Contains(i.Id) && i.Status != null && !dNStatuses.Contains(i.Status.Status) && !dNStatusesIds.Contains(i.Status.BaseId));
                    //}
                    break;
                case LeadFilterTypeMobile.UnassignLeads:
                    query = query.Where(i => i.AssignTo == Guid.Empty);
                    break;
                case LeadFilterTypeMobile.Booked:
                    query = query.Where(i => i.CustomLeadStatus != null && i.CustomLeadStatus.Status == "booked");
                    break;
                case LeadFilterTypeMobile.Dropped:
                    query = query.Where(i => i.CustomLeadStatus != null && (i.CustomLeadStatus.MasterLeadStatusBaseId == Guid.Parse("023fcb89-037e-42f4-bcc2-647bb4e95c99") || i.CustomLeadStatus.Status == "dropped"));
                    break;
                case LeadFilterTypeMobile.Escalated:
                    query = query.Where(i => i.TagInfo != null && i.TagInfo.IsEscalated)
                    .Where(i => i.CustomLeadStatus == null || i.CustomLeadStatus.MasterLeadStatusBaseId != Guid.Parse("023fcb89-037e-42f4-bcc2-647bb4e95c99"))
                    .Where(i => i.CustomLeadStatus == null || i.CustomLeadStatus.Status != "booked")
                    .Where(i => i.CustomLeadStatus == null || i.CustomLeadStatus.MasterLeadStatusBaseId != Guid.Parse("065fa6fd-a15e-4248-b469-e8596980c97a"));
                    break;
                case LeadFilterTypeMobile.HotLeads:
                    query = query.Where(i => i.TagInfo != null && i.TagInfo.IsHotLead)
                    .Where(i => i.CustomLeadStatus == null || i.CustomLeadStatus.MasterLeadStatusBaseId != Guid.Parse("023fcb89-037e-42f4-bcc2-647bb4e95c99"))
                    .Where(i => i.CustomLeadStatus == null || i.CustomLeadStatus.Status != "booked")
                    .Where(i => i.CustomLeadStatus == null || i.CustomLeadStatus.MasterLeadStatusBaseId != Guid.Parse("065fa6fd-a15e-4248-b469-e8596980c97a"));
                    break;
                case LeadFilterTypeMobile.AboutToConvert:
                    query = query.Where(i => i.TagInfo != null && i.TagInfo.IsAboutToConvert)
                    .Where(i => i.CustomLeadStatus == null || i.CustomLeadStatus.MasterLeadStatusBaseId != Guid.Parse("023fcb89-037e-42f4-bcc2-647bb4e95c99"))
                    .Where(i => i.CustomLeadStatus == null || i.CustomLeadStatus.Status != "booked")
                    .Where(i => i.CustomLeadStatus == null || i.CustomLeadStatus.MasterLeadStatusBaseId != Guid.Parse("065fa6fd-a15e-4248-b469-e8596980c97a"));
                    break;
                case LeadFilterTypeMobile.WarmLeads:
                    query = query.Where(i => i.TagInfo != null && i.TagInfo.IsWarmLead)
                    .Where(i => i.CustomLeadStatus == null || i.CustomLeadStatus.MasterLeadStatusBaseId != Guid.Parse("023fcb89-037e-42f4-bcc2-647bb4e95c99"))
                    .Where(i => i.CustomLeadStatus == null || i.CustomLeadStatus.Status != "booked")
                    .Where(i => i.CustomLeadStatus == null || i.CustomLeadStatus.MasterLeadStatusBaseId != Guid.Parse("065fa6fd-a15e-4248-b469-e8596980c97a"));
                    break;
                case LeadFilterTypeMobile.ColdLead:
                    query = query.Where(i => i.TagInfo != null && i.TagInfo.IsColdLead)
                    .Where(i => i.CustomLeadStatus == null || i.CustomLeadStatus.MasterLeadStatusBaseId != Guid.Parse("023fcb89-037e-42f4-bcc2-647bb4e95c99"))
                    .Where(i => i.CustomLeadStatus == null || i.CustomLeadStatus.MasterLeadStatusBaseId != Guid.Parse("065fa6fd-a15e-4248-b469-e8596980c97a"))
                    .Where(i => i.CustomLeadStatus == null || i.CustomLeadStatus.Status != "booked");
                    break;
                case LeadFilterTypeMobile.Highlighted:
                    query = query.Where(i => i.TagInfo != null && i.TagInfo.IsHighlighted)
                    .Where(i => i.CustomLeadStatus == null || i.CustomLeadStatus.MasterLeadStatusBaseId != Guid.Parse("023fcb89-037e-42f4-bcc2-647bb4e95c99"))
                    .Where(i => i.CustomLeadStatus == null || i.CustomLeadStatus.MasterLeadStatusBaseId != Guid.Parse("065fa6fd-a15e-4248-b469-e8596980c97a"))
                    .Where(i => i.CustomLeadStatus == null || i.CustomLeadStatus.Status != "booked");
                    break;
                case LeadFilterTypeMobile.Active:
                    query = query.Where(i => i.CustomLeadStatus != null && !StatusesIds.Contains(i.CustomLeadStatus.BaseId ?? Guid.Empty) && !StatusesIds.Contains(i.CustomLeadStatus.Id));
                    break;
                case LeadFilterTypeMobile.AllWithNID:
                case LeadFilterTypeMobile.All:
                    query = query.Where(i => i.CustomLeadStatus != null);
                    break;
                case LeadFilterTypeMobile.BookingCancel:
                    query = query.Where(i => i.CustomLeadStatus != null && i.CustomLeadStatus.Status == "booking_cancel");
                    break;
                case LeadFilterTypeMobile.Untouched:
                    query = query.Where(i => !i.IsPicked);
                    break;
                case LeadFilterTypeMobile.ExpressionOfInterest:
                    query = query.Where(i => i.CustomLeadStatus != null && i.CustomLeadStatus.Status == "expression_of_interest");
                    break;
                case LeadFilterTypeMobile.MeetingDone:
                    query = query.Where(i => i.Appointments.Any(i => i.Type == AppointmentType.Meeting && i.IsDone));
                    break;
                case LeadFilterTypeMobile.SiteVisitDone:
                    query = query.Where(i => i.Appointments.Any(i => i.Type == AppointmentType.SiteVisit && i.IsDone));
                    break;
                default:
                    break;
            }
            //if ((request?.MinBudgets != null && request?.MaxBudgets != null))
            //{
            //    query = query.Where(i => i.Enquiries != null && i.Enquiries.Any(i => i.UpperBudget >= request.MinBudgets && i.UpperBudget <= request.MaxBudgets));
            //}
            //else if (request?.MinBudgets != null)
            //{
            //    query = query.Where(i => i.Enquiries != null && i.Enquiries.Any(i => i.UpperBudget >= request.MinBudgets));
            //}
            //else if (request?.MaxBudgets != null)
            //{
            //    request.MinBudgets ??= 0;
            //    query = query.Where(i => i.Enquiries != null && i.Enquiries.Any(i => i.UpperBudget >= request.MinBudgets && i.UpperBudget <= request.MaxBudgets));
            //}
            if (request.AssignToIds?.Any() ?? false)
            {
                query = query.Where(i => request.AssignToIds.Contains(i.AssignTo));
            }
            if (request.Properties?.Any() ?? false)
            {
                var propertyNames = request.Properties.Select(i => i.ToLower());
                query = query.Where(i => i.Properties.Count > 0 && i.Properties.Any(i => propertyNames.Contains(i.Title.ToLower())));
            }
            if (request.Projects?.Any() ?? false)
            {
                var projectNames = request.Projects.Select(i => i.ToLower());
                query = query.Where(i => i.Projects.Count > 0 && i.Projects.Any(i => projectNames.Contains(i.Name.ToLower()))).AsQueryable();
            }
            if (request.NoOfBHKs != null && request.NoOfBHKs.Any())
            {
                // query = query.Where(i => i.Enquiries != null && i.Enquiries.Any(i => request.NoOfBHKs.Contains(i.NoOfBHKs)));
                query = query.Where(i => i.Enquiries != null && i.Enquiries.Any(i => i.BHKs != null && i.BHKs.Any(j => request.NoOfBHKs.Contains(j))));
            }
            if (request.Beds?.Any() ?? false)
            {
                query = query.Where(i => i.Enquiries != null && i.Enquiries.Any(i => i.Beds != null && i.Beds.Any(j => request.Beds.Contains(j))));
            }
            if (request.Baths?.Any() ?? false)
            {
                query = query.Where(i => i.Enquiries != null && i.Enquiries.Any(i => i.Baths != null && i.Baths.Any(j => request.Baths.Contains(j))));
            }
            if (request.Floors?.Any() ?? false)
            {
                query = query.Where(i => i.Enquiries != null && i.Enquiries.Any(i => i.Floors != null && i.Floors.Any(j => request.Floors.Contains(j))));
            }
            if (request.OfferTypes?.Any() ?? false)
            {
                query = query.Where(i => i.Enquiries != null && i.Enquiries.Any(i => i.OfferType != null && request.OfferTypes.Contains(i.OfferType.Value)));
            }
            if (request.Purposes?.Any() ?? false)
            {
                query = query.Where(i => i.Enquiries != null && i.Enquiries.Any(i => i.Purpose != null && request.Purposes.Contains(i.Purpose.Value)));
            }
            if (request.Furnished?.Any() ?? false)
            {
                query = query.Where(i => i.Enquiries != null && i.Enquiries.Any(i => i.Furnished != null && request.Furnished.Contains(i.Furnished.Value)));
            }
            if (request.SubSources != null && request.SubSources.Any())
            {
                request.SubSources = request.SubSources.ConvertAll(i => i.ToLower().Trim());
                query = query.Where(i => i.Enquiries != null && i.Enquiries.Any(i => request.SubSources.Contains(i.SubSource.ToLower().Trim())));
            }
            if (request.BHKTypes != null && request.BHKTypes.Any())
            {
                //query = query.Where(i => i.Enquiries != null && i.Enquiries.Any(i => request.BHKTypes.Contains(i.BHKType)));
                query = query.Where(i => i.Enquiries != null && i.Enquiries.Any(i => i.BHKTypes != null && i.BHKTypes.Any(j => request.BHKTypes.Contains(j))));
            }
            if (request.PropertyType != null && request.PropertyType.Any())
            {
                query = query.Where(i => i.Enquiries.Any(e => e.PropertyTypes.Any(i => request.PropertyType.Contains(i.BaseId ?? Guid.Empty))));
            }
            if (request.PropertySubType != null && request.PropertySubType.Any())
            {
                query = query.Where(i => i.Enquiries.Any(e => e.PropertyTypes.Any(i => request.PropertySubType.Contains(i.Id))));
            }
            if (request.StatusIds != null && request.StatusIds.Any())
            {
                query = query.Where(i => i.CustomLeadStatus != null && request.StatusIds.Contains(i.CustomLeadStatus.Id));
            }
            if (request.Budget != null && request.Budget.Any())
            {
                foreach (var budget in request.Budget)
                {
                    switch (budget)
                    {
                        case Budget.UpToTenLakhs:
                            query = query.Where(i => i.Enquiries != null && i.Enquiries.Any(j => j.UpperBudget <= 1000000));
                            break;
                        case Budget.TenToTwentyLakhs:
                            query = query.Where(i => i.Enquiries != null && i.Enquiries.Any(j => j.UpperBudget > 1000000 && j.UpperBudget <= 2000000));
                            break;
                        case Budget.TwentyToThirtyLakhs:
                            query = query.Where(i => i.Enquiries != null && i.Enquiries.Any(j => j.UpperBudget > 2000000 && j.UpperBudget <= 3000000));
                            break;
                        case Budget.ThirtyToFourtyLakhs:
                            query = query.Where(i => i.Enquiries != null && i.Enquiries.Any(j => j.UpperBudget > 3000000 && j.UpperBudget <= 4000000));
                            break;
                        case Budget.FourtyToFiftyLakhs:
                            query = query.Where(i => i.Enquiries != null && i.Enquiries.Any(j => j.UpperBudget > 4000000 && j.UpperBudget <= 5000000));
                            break;
                        case Budget.FiftyToOneCrore:
                            query = query.Where(i => i.Enquiries != null && i.Enquiries.Any(j => j.UpperBudget > 5000000 && j.UpperBudget <= 10000000));
                            break;
                        case Budget.MoreThanOneCrore:
                            query = query.Where(i => i.Enquiries != null && i.Enquiries.Any(j => j.UpperBudget > 10000000));
                            break;
                    }
                }
            }
            if (request.MinBudget != null || request.MaxBudget != null)
            {
                if (request.MinBudget != null && request.MaxBudget != null)
                {
                    query = query.Where(i => i.Enquiries.Any(i => (i.UpperBudget >= request.MinBudget && i.UpperBudget <= request.MaxBudget) || (i.LowerBudget >= request.MinBudget && i.LowerBudget <= request.MaxBudget)));
                }
                else if (request.MinBudget != null && request.MaxBudget == null)
                {
                    query = query.Where(i => i.Enquiries.Any(i => (i.UpperBudget >= request.MinBudget) || (i.LowerBudget >= request.MinBudget)));
                }
                else if (request.MinBudget == null && request.MaxBudget != null)
                {
                    query = query.Where(i => i.Enquiries.Any(i => (i.UpperBudget <= request.MaxBudget) || (i.LowerBudget <= request.MaxBudget)));
                }
            }
            if (request.BudgetFilters != null && request.BudgetFilters.Any())
            {
                foreach (var budget in request.BudgetFilters)
                {
                    query = query.Where(i => i.Enquiries != null && i.Enquiries.Any(i => i.UpperBudget >= budget.MinBudget && i.UpperBudget <= budget.MaxBudget));

                }
            }
            if (request.Locations != null && request.Locations.Any())
            {
                request.Locations = request.Locations.ConvertAll(i => Uri.UnescapeDataString(i).Replace(",", "").ToLower().Trim().Replace(" ", "")).ToList();
                query = query.Where(i => i.Enquiries.Any(e => e.Addresses.Any(j =>
                request.Locations.Contains(
                    (j.SubLocality +
                    j.Locality +
                    j.Community +
                    j.SubCommunity +
                    j.TowerName +
                    j.District +
                    j.City +
                    j.State +
                    j.Country +
                    j.PostalCode).Replace(",", "").ToLower().Trim().Replace(" ", "")))));

            }
            if (request.IntegrationAccountIds != null && request.IntegrationAccountIds.Any())
            {
                query = query.Where(i => request.IntegrationAccountIds.Contains(i.AccountId));
            }
            if (request.AgencyNames != null && request.AgencyNames.Any())
            {
                request.AgencyNames = request.AgencyNames.ConvertAll(i => i.ToLower().Trim());
                query = query.Where(i => i.Agencies.Any(i => i.Name != null && request.AgencyNames.Contains(i.Name.ToLower().Trim())));
            }
            if (request?.LeadIds?.Any() ?? false)
            {
                query = query.Where(i => request.LeadIds.Contains(i.Id));
            }
            if (request?.SerialNumbers?.Any() ?? false)
            {
                query = query.Where(i => request.SerialNumbers.Contains(i.SerialNumber));
            }
            if (request.Designations?.Any() ?? false)
            {
                request.Designations = request.Designations.ConvertAll(i => i.ToLower().Trim());
                query = query.Where(i => request.Designations.Contains(i.Designation.ToLower().Trim()));
            }
            if (!string.IsNullOrEmpty(request?.Designation))
            {
                query = query.Where(i => i.Designation.ToLower().Contains(request.Designation.ToLower().Trim()));
            }
            if (request?.BookedByIds?.Any() ?? false)
            {
                query = query.Where(i => i.BookedDetails.Any(i => request.BookedByIds.Contains(i.BookedBy.Value)));
            }
            if ((request.AssignFromIds?.Any() ?? false) && !(request?.IsWithHistory ?? false))
            {
                query = query.Where(i => i.AssignedFrom != null && request.AssignFromIds.Contains(i.AssignedFrom.Value));
            }
            if ((request?.SecondaryUsers?.Any() ?? false) && (request?.IsDualOwnershipEnabled ?? false) && !(request?.IsWithHistory ?? false))
            {
                query = query.Where(i => i.SecondaryUserId != null && request.SecondaryUsers.Contains(i.SecondaryUserId.Value));
            }

            if ((request?.SecondaryFromIds?.Any() ?? false) && (request?.IsDualOwnershipEnabled ?? false) && !(request?.IsWithHistory ?? false))
            {
                query = query.Where(i => i.SecondaryFromUserId != null && request.SecondaryFromIds.Contains(i.SecondaryFromUserId.Value));
            }
            if (request?.OriginalOwnerIds?.Any() ?? false)
            {
                query = query.Where(i => i.OriginalOwner != null && request.OriginalOwnerIds.Contains(i.OriginalOwner.Value));
            }
            if (request?.IsWithHistory ?? false)
            {
                DateTime? fromDate = null;
                DateTime? toDate = null;

                if (request.DateType.HasValue && request.FromDate.HasValue && request.FromDate.Value != default && request.ToDate.HasValue && request.ToDate.Value != default)
                {
                    fromDate = request.FromDate.Value.ConvertFromDateToUtc();
                    toDate = request.ToDate.Value.ConvertToDateToUtc();
                }
                query = query.Where(i => i.Assignments != null && i.Assignments.Any(j =>
                    (request.HistoryAssignedToIds == null || !request.HistoryAssignedToIds.Any() || (j.AssignTo != null && request.HistoryAssignedToIds.Contains(j.AssignTo ?? Guid.Empty))) &&
                    (request.AssignFromIds == null || !request.AssignFromIds.Any() || (j.AssignedFrom != null && request.AssignFromIds.Contains(j.AssignedFrom ?? Guid.Empty))) &&
                    (request.SecondaryUsers == null || !request.SecondaryUsers.Any() || (j.SecondaryAssignTo != null && request.SecondaryUsers.Contains(j.SecondaryAssignTo ?? Guid.Empty))) &&
                    (request.SecondaryFromIds == null || !request.SecondaryFromIds.Any() || (j.SecondaryAssignFrom != null && request.SecondaryFromIds.Contains(j.SecondaryAssignFrom ?? Guid.Empty))) &&
                    (request.DoneBy == null || !request.DoneBy.Any() || request.DoneBy.Contains(j.LastModifiedBy)) &&
                    (fromDate == null || toDate == null ||
                    (j.AssignmentDate.HasValue && j.AssignmentDate.Value >= fromDate.Value && j.AssignmentDate.Value <= toDate.Value))
                ));
            }
            if (request?.DoneBy?.Any() ?? false)
            {
                DateTime? fromDate = null;
                DateTime? toDate = null;

                if (request.DateType.HasValue && request.FromDate.HasValue && request.FromDate.Value != default && request.ToDate.HasValue && request.ToDate.Value != default)
                {
                    fromDate = request.FromDate.Value.ConvertFromDateToUtc();
                    toDate = request.ToDate.Value.ConvertToDateToUtc();
                }

                query = query.Where(i => i.Assignments != null && i.Assignments.Any(j =>
                    request.DoneBy.Contains(j.LastModifiedBy) &&
                    (fromDate == null || toDate == null || (j.AssignmentDate.HasValue && j.AssignmentDate.Value >= fromDate.Value && j.AssignmentDate.Value <= toDate.Value))
                ));
            }
            if (request.Cities?.Any() ?? false)
            {
                var normalizedCityNames = request.Cities.ConvertAll(i => i.ToLower().Trim().Replace(" ", ""));
                query = query.Where(i => i.Enquiries.Any(e => e.Addresses.Where(i => normalizedCityNames.Contains(i.City.ToLower().Trim().Replace(" ", ""))).Any()));
            }

            if (request.States?.Any() ?? false)
            {
                var normalizedStateNames = request.States.ConvertAll(i => i.ToLower().Trim().Replace(" ", ""));
                query = query.Where(i => i.Enquiries.Any(e => e.Addresses.Where(i => normalizedStateNames.Contains(i.State.ToLower().Trim().Replace(" ", ""))).Any()));
            }
            if (request.Countries?.Any() ?? false)
            {
                var normalizedStateNames = request.Countries.ConvertAll(i => i.ToLower().Trim().Replace(" ", ""));
                query = query.Where(i => i.Enquiries.Any(e => e.Addresses.Where(i => normalizedStateNames.Contains(i.Country.ToLower().Trim().Replace(" ", ""))).Any()));
            }
            if (request.Communities?.Any() ?? false)
            {
                var normalizedStateNames = request.Communities.ConvertAll(i => i.ToLower().Trim().Replace(" ", ""));
                query = query.Where(i => i.Enquiries.Any(e => e.Addresses.Where(i => normalizedStateNames.Contains(i.Community.ToLower().Trim().Replace(" ", ""))).Any()));
            }
            if (request.SubCommunities?.Any() ?? false)
            {
                var normalizedStateNames = request.SubCommunities.ConvertAll(i => i.ToLower().Trim().Replace(" ", ""));
                query = query.Where(i => i.Enquiries.Any(e => e.Addresses.Where(i => normalizedStateNames.Contains(i.SubCommunity.ToLower().Trim().Replace(" ", ""))).Any()));
            }
            if (request.TowerNames?.Any() ?? false)
            {
                var normalizedStateNames = request.TowerNames.ConvertAll(i => i.ToLower().Trim().Replace(" ", ""));
                query = query.Where(i => i.Enquiries.Any(e => e.Addresses.Where(i => normalizedStateNames.Contains(i.TowerName.ToLower().Trim().Replace(" ", ""))).Any()));
            }
            if (request.PostalCodes?.Any() ?? false)
            {
                var normalizedStateNames = request.PostalCodes.ConvertAll(i => i.Replace(" ", ""));
                query = query.Where(i => i.Enquiries.Any(e => e.Addresses.Where(i => normalizedStateNames.Contains(i.PostalCode.ToLower().Trim().Replace(" ", ""))).Any()));
            }
            if (request?.PossesionType != null && request?.PossesionType != PossesionType.None)
            {

                switch (request?.PossesionType)
                {
                    case PossesionType.UnderConstruction:
                        query = query.Where(i => i.PossesionType == PossesionType.UnderConstruction);
                        break;

                    case PossesionType.SixMonth:
                        query = query.Where(i => i.PossesionType == PossesionType.SixMonth);
                        break;

                    case PossesionType.Year:
                        query = query.Where(i => i.PossesionType == PossesionType.Year);
                        break;

                    case PossesionType.TwoYears:
                        query = query.Where(i => i.PossesionType == PossesionType.TwoYears);
                        break;

                    case PossesionType.CustomDate:
                        DateTime? tempToPossesionDate = request?.ToPossesionDate?.ConvertToDateToUtc();
                        DateTime? tempFrompossesionDate = request?.FromPossesionDate?.ConvertFromDateToUtc();
                        query = query.Where(i => i.Enquiries.Any(j => j.PossessionDate != null && j.PossessionDate >= tempFrompossesionDate.Value && j.PossessionDate <= tempToPossesionDate.Value));

                        break;
                }

            }
            if (request?.LandLine != null && request.LandLine.Any())
            {
                query = query.Where(i => i.LandLine != null && request.LandLine.Contains(i.LandLine.Trim()));
            }
            if (request?.CountryCode?.Any() ?? false)
            {
                var codes = request.CountryCode
        .Where(c => !string.IsNullOrWhiteSpace(c))
        .Select(c => c.Trim().TrimStart('+'))
        .Distinct()
        .ToList();

                Expression<Func<Lead, bool>> filter = lead => false;

                foreach (var code in codes)
                {
                    var local = code;

                    filter = filter.Or(lead =>
                        (lead.CountryCode != null && lead.CountryCode.Trim().TrimStart('+') == local) ||
                        (lead.ContactNo != null &&
                            (lead.ContactNo.StartsWith("+" + local) || lead.ContactNo.StartsWith(local)))
                    );
                }

                query = query.Where(filter);
            }
            if (request?.AltCountryCode?.Any() ?? false)
            {
                query = query.Where(i => i.AltCountryCode != null && request.AltCountryCode.Contains(i.AltCountryCode));
            }
            if (request?.GenderTypes?.Any() ?? false)
            {
                query = query.Where(i => i.Gender != null && request.GenderTypes.Contains(i.Gender.Value));
            }
            if (request?.MaritalStatuses?.Any() ?? false)
            {
                query = query.Where(i => i.MaritalStatus != null && request.MaritalStatuses.Contains(i.MaritalStatus.Value));
            }
            if (request?.DateOfBirth != null)
            {
                query = query.Where(i => i.DateOfBirth != null && i.DateOfBirth == request.DateOfBirth);
            }
            return query.AsQueryable();

            //}
        }
        private IQueryable<Lead> BuildQuery(Application.Lead.Mobile.v2.V2SearchLeadRequest request, Guid userId, KeyValuePair<bool, List<Guid>> adminWithSubIds, List<CustomMasterLeadStatus>? customMasterLeadStatus = null)
        {
            var scope = _provider.CreateScope();
            var context = scope.ServiceProvider.GetRequiredService<ApplicationDbContext>();
            var selfWithReporteeIds = adminWithSubIds.Value.Where(i => i != Guid.Empty);
            var query = context.Leads.Where(i => !i.IsDeleted).Where(i => !i.IsArchived)
                  .Include(i => i.CustomLeadStatus)
                  .Include(i => i.Enquiries)
                      //.ThenInclude(i => i.Address)
                      .ThenInclude(i => i.Addresses)
                  .Include(i => i.Enquiries)
                      .ThenInclude(i => i.PropertyType)
                  .Include(i => i.Appointments)
                      .ThenInclude(i => i.Location)
                  .Include(i => i.Projects)
                  .Include(i => i.Properties)
                  .Include(i => i.Enquiries)
                  .ThenInclude(i => i.PropertyTypes)
                  .AsQueryable();
            /*if (!string.IsNullOrWhiteSpace(request.SearchByNameOrNumber))
            {
                request.SearchByNameOrNumber = request.SearchByNameOrNumber.Replace(" ", "").ToLower();
                query = query.Where(i => i.AlternateContactNo.Contains(request.SearchByNameOrNumber) ||
                                         i.ContactNo.Contains(request.SearchByNameOrNumber) ||
                                         i.Name.Replace(" ", "").ToLower().Contains(request.SearchByNameOrNumber) ||
                                         i.Email.ToLower().Contains(request.SearchByNameOrNumber) ||
                                         i.SerialNumber.ToLower().Contains(request.SearchByNameOrNumber.Replace(" ", "")));
                if (!adminWithSubIds.Key)
                {
                    query = query.Where(i => (selfWithReporteeIds.Contains(i.AssignTo) || selfWithReporteeIds.Contains(i.SecondaryUserId ?? Guid.Empty)));
                }
            }*/
            if (!string.IsNullOrWhiteSpace(request.SearchByNameOrNumber) && request.PropertyToSearch?.Any() == true)
            {
                var searchTerm = request.SearchByNameOrNumber.ToLower().Trim().Replace(" ", "");
                var isPurposeValid = TryParseEnum<Purpose>(searchTerm, out var parsedPurpose);
                bool isSourceValid = TryParseEnum<LeadSource>(searchTerm, out var parsedSource);
                var isEnquiryTypeValid = TryParseEnum<EnquiryType>(searchTerm, out var parsedEnquiryType);
                var statusId = customMasterLeadStatus?.Where(i => i.DisplayName.ToLower().Trim().Replace(" ", "").Contains(searchTerm)).Select(i => i.Id).ToList();
                query = query.Where(i =>
                   (request.PropertyToSearch.Contains("LeadName") && i.Name != null && i.Name.ToLower().Trim().Replace(" ", "").Contains(searchTerm)) ||
                   (request.PropertyToSearch.Contains("ContactNo") && i.ContactNo != null && i.ContactNo.Replace(" ", "").Contains(searchTerm)) ||
                   (request.PropertyToSearch.Contains("SerialNumber") && i.SerialNumber != null && i.SerialNumber.Replace(" ", "").Contains(searchTerm)) ||
                   (request.PropertyToSearch.Contains("AlternateContactNo") && i.AlternateContactNo != null && i.AlternateContactNo.Replace(" ", "").Contains(searchTerm)) ||
                   (request.PropertyToSearch.Contains("Email") && i.Email != null && i.Email.ToLower().Contains(searchTerm)) ||
                   (request.PropertyToSearch.Contains("Nationality") && i.Nationality != null && i.Nationality.ToLower().Trim().Contains(searchTerm)) ||
                   (request.PropertyToSearch.Contains("PropertyName") && i.Properties.Any(a => a.Title.ToLower().Trim().Replace(" ", "").Contains(searchTerm))) ||
                   (request.PropertyToSearch.Contains("ProjectName") && i.Projects.Any(a => a.Name.ToLower().Trim().Replace(" ", "").Contains(searchTerm))) ||
                   (request.PropertyToSearch.Contains("SubSource") && i.Enquiries.Any(a => !string.IsNullOrEmpty(a.SubSource) && a.SubSource.ToLower().Trim().Replace(" ", "").Contains(searchTerm))) ||
                   (request.PropertyToSearch.Contains("Status") && (statusId.Contains(i.CustomLeadStatus.BaseId ?? Guid.Empty) || statusId.Contains(i.CustomLeadStatus.Id))) ||
                    (request.PropertyToSearch.Contains("Purpose") && isPurposeValid && i.Enquiries.Any(e => e.Purpose == parsedPurpose)) ||
                     (request.PropertyToSearch.Contains("Source") && isSourceValid && i.Enquiries.Any(e => e.LeadSource == parsedSource)) ||
                     (request.PropertyToSearch.Contains("EnquiredFor") && isEnquiryTypeValid && i.Enquiries.Any(e => e.EnquiryTypes.Contains(parsedEnquiryType))) ||
                   (request.PropertyToSearch.Contains("Location") && i.Enquiries.Any(e => e.Addresses.Any(j =>
                    (j.SubLocality +
                     j.Locality +
                     j.Community +
                     j.SubCommunity +
                     j.TowerName +
                     j.District +
                     j.City +
                     j.State +
                     j.Country +
                     j.PostalCode).Replace(",", "").ToLower().Trim().Replace(" ", "")
                    .Contains(searchTerm)))));
            }
            else if (!string.IsNullOrWhiteSpace(request.SearchByNameOrNumber))
            {
                query = query.Where(i => i.ContactNo.Contains(request.SearchByNameOrNumber.Replace(" ", "")) ||
                         i.Name.ToLower().Contains(request.SearchByNameOrNumber.ToLower()) ||
                         i.SerialNumber.ToLower().Contains(request.SearchByNameOrNumber.ToLower().Replace(" ", "")));
            }
            if (!adminWithSubIds.Key)
            {
                query = query.Where(i => (selfWithReporteeIds.Contains(i.AssignTo) || selfWithReporteeIds.Contains(i.SecondaryUserId ?? Guid.Empty)));
            }
            return query;
        }


        #endregion

        #region v3 methods for mobile
        public async Task<IEnumerable<Lead>> GetAllCategoryLeadsForMobileAsync(V3GetAllLeadsRequest request, Guid userId, IList<Guid> subIds, List<CustomMasterLeadStatus> statuses = null)
        {
            var query = BuildQuery(request, userId, subIds, statuses);
            if (request?.FilterType == LeadFilterTypeMobile.ScheduledMeeting || request?.FilterType == LeadFilterTypeMobile.ScheduleToday ||
                       request?.FilterType == LeadFilterTypeMobile.Overdue || request?.FilterType == LeadFilterTypeMobile.SiteVisitScheduled ||
                       request?.FilterType == LeadFilterTypeMobile.CallBack || request?.FilterType == LeadFilterTypeMobile.ScheduledTomorrow ||
                       request?.FilterType == LeadFilterTypeMobile.UpcomingSchedules)
            {
                query = query.OrderBy(i => (i.ScheduledDate ?? DateTime.MinValue)).ThenBy(i => i.Name);
            }
            else
            {
                query = query.OrderByDescending(i => (i.LastModifiedOn ?? DateTime.MaxValue)).ThenBy(i => i.Name);
            }
            query = query.Skip(request.PageSize * (request.PageNumber - 1))
                .Take(request.PageSize)
                .AsQueryable();
            return await query.ToListAsync();
        }

        public async Task<int> GetAllCategoryLeadsCountForMobileAsync(V3GetAllLeadsRequest request, Guid userId, IList<Guid> subIds, List<CustomMasterLeadStatus> statuses = null)
        {
            var query = BuildQuery(request, userId, subIds, statuses);
            var count = await query.Select(i =>i.Id).CountAsync();
            return count;
        }

        public async Task<int> GetAllCategoryLeadsCountForMobileAsync(V3GetLeadCategoryRequest request, Guid userId, IList<Guid> subIds, List<CustomMasterLeadStatus> statuses = null)
        {
            var query = BuildQuery(request, userId, subIds, statuses);
            var count = await query.Select(i => i.Id).CountAsync();
            return count;
        }
        public async Task<IEnumerable<Lead>> GetLeadsByCategoryForMobileAsync(V3GetLeadCategoryRequest request, Guid userId, IList<Guid> subIds, List<CustomMasterLeadStatus> statuses = null)
        {
            var query = BuildQuery(request, userId, subIds, statuses);
            if (request?.FilterType == LeadFilterTypeMobile.ScheduledMeeting || request?.FilterType == LeadFilterTypeMobile.ScheduleToday ||
                       request?.FilterType == LeadFilterTypeMobile.Overdue || request?.FilterType == LeadFilterTypeMobile.SiteVisitScheduled ||
                       request?.FilterType == LeadFilterTypeMobile.CallBack || request?.FilterType == LeadFilterTypeMobile.ScheduledTomorrow ||
                       request?.FilterType == LeadFilterTypeMobile.UpcomingSchedules)
            {
                query = query.OrderBy(i => (i.ScheduledDate ?? DateTime.MinValue)).ThenBy(i => i.Name);
            }
            else
            {
                query = query.OrderByDescending(i => (i.LastModifiedOn ?? DateTime.MaxValue)).ThenBy(i => i.Name);
            }
            query = query.Skip(request.PageSize * (request.PageNumber - 1))
                .Take(request.PageSize)
                .AsQueryable();
            return await query.ToListAsync();
        }

        private IQueryable<Lead> BuildQuery(V3GetLeadCategoryRequest request, Guid userId, IList<Guid> subIds, List<CustomMasterLeadStatus> customLeadStatuses = null)
        {
            var scope = _provider.CreateScope();
            var context = scope.ServiceProvider.GetRequiredService<ApplicationDbContext>();
            //request.UserType = request?.UserType ?? UserType.None;
            var reporteeIds = subIds.Where(i => i != userId && i != Guid.Empty);
            var selfWithReporteeIds = subIds.Where(i => i != Guid.Empty);
            var query = context.Leads.Where(i => !i.IsDeleted)
                .Include(i => i.CustomLeadStatus)
                .Include(i => i.Enquiries)
                .Include(i => i.Enquiries)
                    .ThenInclude(i => i.PropertyType)
                .Include(i => i.Projects)
                .Include(i => i.Enquiries)
                .ThenInclude(i => i.PropertyTypes)
                .AsQueryable();

            //if (request?.IsDualOwnershipEnabled ?? false)
            //{
            //    if (request.SecondaryUsers?.Any() ?? false)
            //    {
            //        query = query.Where(i => request.SecondaryUsers.Contains(i.SecondaryUserId ?? Guid.Empty));
            //    }
            //}
            if (request.DataConverted != null)
            {
                if (request.DataConverted == true)
                {
                    query = query.Where(i => i.IsConvertedFromData == true);
                }
                else
                {
                    query = query.Where(i => i.IsConvertedFromData != true); // Matches false or null
                }
            }
            if (request.QualifiedByIds?.Any() ?? false)
            {
                query = query.Where(i => i.QualifiedBy.HasValue && request.QualifiedByIds.Contains(i.QualifiedBy.Value));
            }
            if (request?.OriginalOwnerIds?.Any() ?? false)
            {
                query = query.Where(i => i.OriginalOwner != null && request.OriginalOwnerIds.Contains(i.OriginalOwner.Value));
            }
            if ((request?.IsDualOwnershipEnabled ?? false) && (!request?.AssignToIds?.Any() ?? true))
            {
                switch (request.LeadVisibility)
                {
                    case BaseLeadVisibility.SelfWithReportee:
                        query = query.Where(i => (selfWithReporteeIds.Contains(i.AssignTo) || selfWithReporteeIds.Contains(i.SecondaryUserId ?? Guid.Empty))).Where(i => !i.IsArchived);
                        break;
                    case BaseLeadVisibility.Self:
                        query = query.Where(i => (i.AssignTo == userId || i.SecondaryUserId == userId)).Where(i => !i.IsArchived);
                        break;
                    case BaseLeadVisibility.Reportee:
                        query = query.Where(i => (reporteeIds.Contains(i.AssignTo) || reporteeIds.Contains(i.SecondaryUserId ?? Guid.Empty))).Where(i => !i.IsArchived);
                        break;
                    case BaseLeadVisibility.UnassignLead:
                        query = query.Where(i => i.AssignTo == Guid.Empty).Where(i => !i.IsArchived);
                        break;
                    case BaseLeadVisibility.DeletedLeads:
                        query = query.Where(i => i.IsArchived && selfWithReporteeIds.Contains(i.AssignTo));
                        break;
                    default:
                        break;
                }
            }
            else
            {
                switch (request.LeadVisibility)
                {
                    case BaseLeadVisibility.SelfWithReportee:
                        query = query.Where(i => selfWithReporteeIds.Contains(i.AssignTo)).Where(i => !i.IsArchived);
                        break;
                    case BaseLeadVisibility.Self:
                        query = query.Where(i => i.AssignTo == userId).Where(i => !i.IsArchived);
                        break;
                    case BaseLeadVisibility.Reportee:
                        query = query.Where(i => reporteeIds.Contains(i.AssignTo)).Where(i => !i.IsArchived);
                        break;
                    case BaseLeadVisibility.UnassignLead:
                        query = query.Where(i => i.AssignTo == Guid.Empty).Where(i => !i.IsArchived);
                        break;
                    case BaseLeadVisibility.DeletedLeads:
                        query = query.Where(i => i.IsArchived && selfWithReporteeIds.Contains(i.AssignTo));
                        break;
                    default:
                        break;
                }
            }
            if (request.IsPicked != null && request.IsPicked != default)
            {
                query = query.Where(i => i.IsPicked == request.IsPicked.Value);
            }
            if (!string.IsNullOrWhiteSpace(request?.SearchByNameOrNumber))
            {
                request.SearchByNameOrNumber = request.SearchByNameOrNumber.ToLower();
                query = query.Where(i => i.AlternateContactNo.Contains(request.SearchByNameOrNumber.Replace(" ", "")) ||
                                         i.ContactNo.Contains(request.SearchByNameOrNumber.Replace(" ", "")) ||
                                         i.Name.ToLower().Contains(request.SearchByNameOrNumber) ||
                                         i.Email.ToLower().Contains(request.SearchByNameOrNumber) ||
                                         i.SerialNumber.Contains(request.SearchByNameOrNumber.Replace(" ", "")));
            }
            var Statuses = (customLeadStatuses?.Where(i => i.Status is "not_interested" or "dropped" or "booked" or "booking_cancel"))?.ToList() ?? new();
            var StatusesIds = Statuses.Select(i => i.Id).ToList();
            var ScheduledStatus = (customLeadStatuses?.Where(i => i.Status is "site_visit_scheduled" or "callback" or "meeting_scheduled"))?.ToList() ?? new();
            var schuduledStatusIds = ScheduledStatus.Select(i => i.Id).ToList();
            TimeSpan userUtcOffset = request.BaseUTcOffset ?? TimeZoneInfo.Local.GetUtcOffset(DateTime.UtcNow);
            DateTime today = DateTime.UtcNow.Add(userUtcOffset).Date;
            switch (request.FilterType)
            {
                case LeadFilterTypeMobile.New:
                    query = query.Where(i => i.CustomLeadStatus != null && i.CustomLeadStatus.Status == "new");
                    break;
                #region ScheduleDate based categories
                case LeadFilterTypeMobile.ScheduleToday:
                    query = query.Where(i => ((i.ScheduledDate ?? DateTime.MinValue) + userUtcOffset) >= today && ((i.ScheduledDate ?? DateTime.MinValue) + userUtcOffset) < today.AddDays(1))
                    .Where(i => i.CustomLeadStatus == null || schuduledStatusIds.Contains(i.CustomLeadStatus.BaseId ?? Guid.Empty) || schuduledStatusIds.Contains(i.CustomLeadStatus.Id)); ;
                    break;
                case LeadFilterTypeMobile.Overdue:
                    query = query.Where(i => ((i.ScheduledDate ?? DateTime.MinValue) + userUtcOffset) < today)
                    .Where(i => i.CustomLeadStatus != null && (schuduledStatusIds.Contains(i.CustomLeadStatus.BaseId ?? Guid.Empty) || schuduledStatusIds.Contains(i.CustomLeadStatus.Id)));
                    break;
                case LeadFilterTypeMobile.ScheduledTomorrow:
                    query = query.Where(i => ((i.ScheduledDate ?? DateTime.MinValue) + userUtcOffset) >= today.AddDays(1) && ((i.ScheduledDate ?? DateTime.MinValue) + userUtcOffset) < today.AddDays(2))
                    .Where(i => i.CustomLeadStatus != null && (schuduledStatusIds.Contains(i.CustomLeadStatus.BaseId ?? Guid.Empty) || schuduledStatusIds.Contains(i.CustomLeadStatus.Id)));
                    break;
                case LeadFilterTypeMobile.UpcomingSchedules:
                    query = query.Where(i => ((i.ScheduledDate ?? DateTime.MinValue) + userUtcOffset) >= today.AddDays(1))
                    .Where(i => i.CustomLeadStatus == null || schuduledStatusIds.Contains(i.CustomLeadStatus.BaseId ?? Guid.Empty) || schuduledStatusIds.Contains(i.CustomLeadStatus.Id));
                    break;
                #endregion
                case LeadFilterTypeMobile.Pending:
                    query = query.Where(i => i.CustomLeadStatus != null && i.CustomLeadStatus.Status == "pending");
                    break;
                case LeadFilterTypeMobile.NotInterested:
                    query = query.Where(i => i.CustomLeadStatus != null && (i.CustomLeadStatus.MasterLeadStatusBaseId == Guid.Parse("065fa6fd-a15e-4248-b469-e8596980c97a") || i.CustomLeadStatus.Status == "not_interested"));
                    break;
                case LeadFilterTypeMobile.CallBack:
                    query = query.Where(i => i.CustomLeadStatus != null && i.CustomLeadStatus.MasterLeadStatusBaseId == Guid.Parse("5ae346bc-c695-4af4-8c3b-c8648587fbd6"));
                    break;
                case LeadFilterTypeMobile.SiteVisitScheduled:
                    query = query.Where(i => i.CustomLeadStatus != null && (i.CustomLeadStatus.Status == "site_visit_scheduled" || i.CustomLeadStatus.MasterLeadStatusBaseId.ToString() == "59647294-09d6-44a2-a346-9de5ba829e04"));
                    break;
                case LeadFilterTypeMobile.ScheduledMeeting:
                    query = query.Where(i => i.CustomLeadStatus != null && (i.CustomLeadStatus.Status == "meeting_scheduled" || i.CustomLeadStatus.MasterLeadStatusBaseId.ToString() == "99a7f794-9046-4a9d-b7e2-e0a2196b98dd"));

                    break;
                case LeadFilterTypeMobile.UnassignLeads:
                    query = query.Where(i => i.AssignTo == Guid.Empty);
                    break;
                case LeadFilterTypeMobile.Booked:
                    query = query.Where(i => i.CustomLeadStatus != null && i.CustomLeadStatus.Status == "booked");
                    break;
                case LeadFilterTypeMobile.Dropped:
                    query = query.Where(i => i.CustomLeadStatus != null && (i.CustomLeadStatus.MasterLeadStatusBaseId == Guid.Parse("023fcb89-037e-42f4-bcc2-647bb4e95c99") || i.CustomLeadStatus.Status == "dropped"));
                    break;
                case LeadFilterTypeMobile.Escalated:
                    query = query.Where(i => i.TagInfo != null && i.TagInfo.IsEscalated)
                    .Where(i => i.CustomLeadStatus == null || i.CustomLeadStatus.MasterLeadStatusBaseId != Guid.Parse("023fcb89-037e-42f4-bcc2-647bb4e95c99"))
                    .Where(i => i.CustomLeadStatus == null || i.CustomLeadStatus.Status != "booked")
                    .Where(i => i.CustomLeadStatus == null || i.CustomLeadStatus.MasterLeadStatusBaseId != Guid.Parse("065fa6fd-a15e-4248-b469-e8596980c97a"));
                    break;
                case LeadFilterTypeMobile.HotLeads:
                    query = query.Where(i => i.TagInfo != null && i.TagInfo.IsHotLead)
                    .Where(i => i.CustomLeadStatus == null || i.CustomLeadStatus.MasterLeadStatusBaseId != Guid.Parse("023fcb89-037e-42f4-bcc2-647bb4e95c99"))
                    .Where(i => i.CustomLeadStatus == null || i.CustomLeadStatus.Status != "booked")
                    .Where(i => i.CustomLeadStatus == null || i.CustomLeadStatus.MasterLeadStatusBaseId != Guid.Parse("065fa6fd-a15e-4248-b469-e8596980c97a"));
                    break;
                case LeadFilterTypeMobile.AboutToConvert:
                    query = query.Where(i => i.TagInfo != null && i.TagInfo.IsAboutToConvert)
                    .Where(i => i.CustomLeadStatus == null || i.CustomLeadStatus.MasterLeadStatusBaseId != Guid.Parse("023fcb89-037e-42f4-bcc2-647bb4e95c99"))
                    .Where(i => i.CustomLeadStatus == null || i.CustomLeadStatus.Status != "booked")
                    .Where(i => i.CustomLeadStatus == null || i.CustomLeadStatus.MasterLeadStatusBaseId != Guid.Parse("065fa6fd-a15e-4248-b469-e8596980c97a"));
                    break;
                case LeadFilterTypeMobile.WarmLeads:
                    query = query.Where(i => i.TagInfo != null && i.TagInfo.IsWarmLead)
                    .Where(i => i.CustomLeadStatus == null || i.CustomLeadStatus.MasterLeadStatusBaseId != Guid.Parse("023fcb89-037e-42f4-bcc2-647bb4e95c99"))
                    .Where(i => i.CustomLeadStatus == null || i.CustomLeadStatus.Status != "booked")
                    .Where(i => i.CustomLeadStatus == null || i.CustomLeadStatus.MasterLeadStatusBaseId != Guid.Parse("065fa6fd-a15e-4248-b469-e8596980c97a"));
                    break;
                case LeadFilterTypeMobile.ColdLead:
                    query = query.Where(i => i.TagInfo != null && i.TagInfo.IsColdLead)
                    .Where(i => i.CustomLeadStatus == null || i.CustomLeadStatus.MasterLeadStatusBaseId != Guid.Parse("023fcb89-037e-42f4-bcc2-647bb4e95c99"))
                    .Where(i => i.CustomLeadStatus == null || i.CustomLeadStatus.MasterLeadStatusBaseId != Guid.Parse("065fa6fd-a15e-4248-b469-e8596980c97a"))
                    .Where(i => i.CustomLeadStatus == null || i.CustomLeadStatus.Status != "booked");
                    break;
                case LeadFilterTypeMobile.Highlighted:
                    query = query.Where(i => i.TagInfo != null && i.TagInfo.IsHighlighted)
                    .Where(i => i.CustomLeadStatus == null || i.CustomLeadStatus.MasterLeadStatusBaseId != Guid.Parse("023fcb89-037e-42f4-bcc2-647bb4e95c99"))
                    .Where(i => i.CustomLeadStatus == null || i.CustomLeadStatus.MasterLeadStatusBaseId != Guid.Parse("065fa6fd-a15e-4248-b469-e8596980c97a"))
                    .Where(i => i.CustomLeadStatus == null || i.CustomLeadStatus.Status != "booked");
                    break;
                case LeadFilterTypeMobile.Active:
                    query = query.Where(i => i.CustomLeadStatus != null && !StatusesIds.Contains(i.CustomLeadStatus.BaseId ?? Guid.Empty) && !StatusesIds.Contains(i.CustomLeadStatus.Id));
                    break;
                case LeadFilterTypeMobile.AllWithNID:
                case LeadFilterTypeMobile.All:
                    query = query.Where(i => i.CustomLeadStatus != null);
                    break;
                case LeadFilterTypeMobile.BookingCancel:
                    query = query.Where(i => i.CustomLeadStatus != null && i.CustomLeadStatus.Status == "booking_cancel");
                    break;
                case LeadFilterTypeMobile.Untouched:
                    query = query.Where(i => !i.IsPicked);
                    break;
                case LeadFilterTypeMobile.ExpressionOfInterest:
                    query = query.Where(i => i.CustomLeadStatus != null && i.CustomLeadStatus.Status == "expression_of_interest");
                    break;
                case LeadFilterTypeMobile.MeetingDone:
                    query = query.Where(i => i.Appointments.Any(i => i.Type == AppointmentType.Meeting && i.IsDone));
                    break;
                case LeadFilterTypeMobile.SiteVisitDone:
                    query = query.Where(i => i.Appointments.Any(i => i.Type == AppointmentType.SiteVisit && i.IsDone));
                    break;
                default:
                    break;
            }
            if (request.DateType.HasValue && ((request.FromDate.HasValue && request.FromDate.Value != default) || (request.ToDate.HasValue && request.ToDate.Value != default)))
            {
                DateTime? fromDate = request.FromDate.HasValue ? request.FromDate.Value.ConvertFromDateToUtc() : DateTime.MinValue;
                DateTime? toDate = request.ToDate.HasValue ? request.ToDate.Value.ConvertToDateToUtc() : DateTime.MaxValue;
                switch (request.DateType)
                {
                    case DateType.ReceivedDate:
                        query = query.Where(i => i.CreatedOn >= fromDate.Value && i.CreatedOn <= toDate.Value);
                        break;
                    case DateType.ScheduledDate:
                        query = query.Where(i => i.ScheduledDate != null && i.ScheduledDate.Value >= fromDate.Value && i.ScheduledDate.Value <= toDate.Value);
                        break;
                    case DateType.ModifiedDate:
                        query = query.Where(i => i.LastModifiedOn != null && i.LastModifiedOn.Value >= fromDate.Value && i.LastModifiedOn.Value <= toDate.Value);
                        break;
                    case DateType.DeletedDate:
                        query = query.Where(i => i.ArchivedOn >= fromDate.Value && i.ArchivedOn < toDate.Value);
                        break;
                    case DateType.PickedDate:
                        query = query.Where(i => i.PickedDate >= fromDate.Value && i.PickedDate <= toDate.Value);
                        break;
                    case DateType.AssignedDate:
                        query = query.Where(i => i.Assignments != null && i.Assignments.Any(i => i.AssignmentDate != null && i.AssignmentDate.Value >= fromDate.Value && i.AssignmentDate.Value <= toDate.Value));
                        break;
                    default:
                        break;
                }
            }

            if ((request.AssignFromIds?.Any() ?? false) && !(request?.IsWithHistory ?? false))
            {
                query = query.Where(i => i.AssignedFrom != null && request.AssignFromIds.Contains(i.AssignedFrom.Value));
            }
            if ((request?.SecondaryUsers?.Any() ?? false) && (request?.IsDualOwnershipEnabled ?? false) && !(request?.IsWithHistory ?? false))
            {
                query = query.Where(i => i.SecondaryUserId != null && request.SecondaryUsers.Contains(i.SecondaryUserId.Value));
            }

            if ((request?.SecondaryFromIds?.Any() ?? false) && (request?.IsDualOwnershipEnabled ?? false) && !(request?.IsWithHistory ?? false))
            {
                query = query.Where(i => i.SecondaryFromUserId != null && request.SecondaryFromIds.Contains(i.SecondaryFromUserId.Value));
            }
            if (request?.IsWithHistory ?? false)
            {
                DateTime? fromDate = null;
                DateTime? toDate = null;

                if (request.DateType.HasValue && request.FromDate.HasValue && request.FromDate.Value != default && request.ToDate.HasValue && request.ToDate.Value != default)
                {
                    fromDate = request.FromDate.Value.ConvertFromDateToUtc();
                    toDate = request.ToDate.Value.ConvertToDateToUtc();
                }
                query = query.Where(i => i.Assignments != null && i.Assignments.Any(j =>
                    (request.HistoryAssignedToIds == null || !request.HistoryAssignedToIds.Any() || (j.AssignTo != null && request.HistoryAssignedToIds.Contains(j.AssignTo ?? Guid.Empty))) &&
                    (request.AssignFromIds == null || !request.AssignFromIds.Any() || (j.AssignedFrom != null && request.AssignFromIds.Contains(j.AssignedFrom ?? Guid.Empty))) &&
                    (request.SecondaryUsers == null || !request.SecondaryUsers.Any() || (j.SecondaryAssignTo != null && request.SecondaryUsers.Contains(j.SecondaryAssignTo ?? Guid.Empty))) &&
                    (request.SecondaryFromIds == null || !request.SecondaryFromIds.Any() || (j.SecondaryAssignFrom != null && request.SecondaryFromIds.Contains(j.SecondaryAssignFrom ?? Guid.Empty))) &&
                    (request.DoneBy == null || !request.DoneBy.Any() || request.DoneBy.Contains(j.LastModifiedBy)) &&
                    (fromDate == null || toDate == null ||
                    (j.AssignmentDate.HasValue && j.AssignmentDate.Value >= fromDate.Value && j.AssignmentDate.Value <= toDate.Value))
                ));
            }
            if (request?.DoneBy?.Any() ?? false)
            {
                DateTime? fromDate = null;
                DateTime? toDate = null;

                if (request.DateType.HasValue && request.FromDate.HasValue && request.FromDate.Value != default && request.ToDate.HasValue && request.ToDate.Value != default)
                {
                    fromDate = request.FromDate.Value.ConvertFromDateToUtc();
                    toDate = request.ToDate.Value.ConvertToDateToUtc();
                }

                query = query.Where(i => i.Assignments != null && i.Assignments.Any(j =>
                    request.DoneBy.Contains(j.LastModifiedBy) &&
                    (fromDate == null || toDate == null || (j.AssignmentDate.HasValue && j.AssignmentDate.Value >= fromDate.Value && j.AssignmentDate.Value <= toDate.Value))
                ));
            }
            if (request.Cities?.Any() ?? false)
            {
                var normalizedCityNames = request.Cities.ConvertAll(i => i.ToLower().Trim().Replace(" ", ""));
                query = query.Where(i => i.Enquiries.Any(e => e.Addresses.Where(i => normalizedCityNames.Contains(i.City.ToLower().Trim().Replace(" ", ""))).Any()));
            }

            if (request.States?.Any() ?? false)
            {
                var normalizedStateNames = request.States.ConvertAll(i => i.ToLower().Trim().Replace(" ", ""));
                query = query.Where(i => i.Enquiries.Any(e => e.Addresses.Where(i => normalizedStateNames.Contains(i.State.ToLower().Trim().Replace(" ", ""))).Any()));
            }
            if (request.Countries?.Any() ?? false)
            {
                var normalizedStateNames = request.Countries.ConvertAll(i => i.ToLower().Trim().Replace(" ", ""));
                query = query.Where(i => i.Enquiries.Any(e => e.Addresses.Where(i => normalizedStateNames.Contains(i.Country.ToLower().Trim().Replace(" ", ""))).Any()));
            }
            if (request.Communities?.Any() ?? false)
            {
                var normalizedStateNames = request.Communities.ConvertAll(i => i.ToLower().Trim().Replace(" ", ""));
                query = query.Where(i => i.Enquiries.Any(e => e.Addresses.Where(i => normalizedStateNames.Contains(i.Community.ToLower().Trim().Replace(" ", ""))).Any()));
            }
            if (request.SubCommunities?.Any() ?? false)
            {
                var normalizedStateNames = request.SubCommunities.ConvertAll(i => i.ToLower().Trim().Replace(" ", ""));
                query = query.Where(i => i.Enquiries.Any(e => e.Addresses.Where(i => normalizedStateNames.Contains(i.SubCommunity.ToLower().Trim().Replace(" ", ""))).Any()));
            }
            if (request.TowerNames?.Any() ?? false)
            {
                var normalizedStateNames = request.TowerNames.ConvertAll(i => i.ToLower().Trim().Replace(" ", ""));
                query = query.Where(i => i.Enquiries.Any(e => e.Addresses.Where(i => normalizedStateNames.Contains(i.TowerName.ToLower().Trim().Replace(" ", ""))).Any()));
            }
            if (request.PostalCodes?.Any() ?? false)
            {
                var normalizedStateNames = request.PostalCodes.ConvertAll(i => i.Replace(" ", ""));
                query = query.Where(i => i.Enquiries.Any(e => e.Addresses.Where(i => normalizedStateNames.Contains(i.PostalCode.ToLower().Trim().Replace(" ", ""))).Any()));
            }
            if (!string.IsNullOrEmpty(request?.AdditionalPropertiesKey))
            {
                var key = request.AdditionalPropertiesKey;

                if (!string.IsNullOrEmpty(request.AdditionalPropertiesValue))
                {
                    var value = request.AdditionalPropertiesValue;
                    query = query
                        .Where(lead => EF.Functions.JsonContains(
                            lead.AdditionalProperties,
                            JsonConvert.SerializeObject(new Dictionary<string, string> { { key, value } })));
                }
                else
                {
                    query = query
                        .Where(lead => EF.Functions.JsonContains(
                            lead.AdditionalProperties,
                            JsonConvert.SerializeObject(new Dictionary<string, string> { { key, "" } })));
                }
            }
            if (request.ChannelPartnerNames != null && request.ChannelPartnerNames.Any())
            {
                request.ChannelPartnerNames = request.ChannelPartnerNames.ConvertAll(i => i.ToLower().Trim());
                query = query.Where(i => i.ChannelPartners.Any(i => i.FirmName != null && request.ChannelPartnerNames.Contains(i.FirmName.ToLower().Trim())));
            }
            if (request.LastModifiedByIds?.Any() ?? false)
            {
                query = query.Where(i => request.LastModifiedByIds.Contains(i.LastModifiedBy));
            }
            if (request.CreatedByIds?.Any() ?? false)
            {
                query = query.Where(i => request.CreatedByIds.Contains(i.CreatedBy));
            }

            if (request.RestoredByIds?.Any() ?? false)
            {
                query = query.Where(i => request.RestoredByIds.Contains(i.RestoredBy.Value));
            }
            if (request.ArchivedByIds?.Any() ?? false)
            {
                query = query.Where(i => request.ArchivedByIds.Contains(i.ArchivedBy.Value));
            }
            if (request.SourcingManagers?.Any() ?? false)
            {
                query = query.Where(i => request.SourcingManagers.Contains(i.SourcingManager.Value));
            }
            if (request.ClosingManagers?.Any() ?? false)
            {
                query = query.Where(i => request.ClosingManagers.Contains(i.ClosingManager.Value));
            }
            if (request.Profession != null && request.Profession.Any())
            {
                query = query.Where(i => request.Profession.Contains(i.Profession));
            }
            if (!string.IsNullOrEmpty(request?.UploadTypeName))
            {
                query = query.Where(i => i.UploadTypeName != null && i.UploadTypeName.ToLower().Contains(request.UploadTypeName.ToLower().Trim()));
            }
            if (request?.LeadType != null && request.LeadType.Any())
            {
                var predicate = PredicateBuilder.New<Lead>(false);

                if (request.LeadType.Contains(LeadType.ShowPrimeLeads))
                {
                    predicate = predicate.Or(i => i.ChildLeadsCount == 0 && i.RootId == null && i.ParentLeadId == null);
                }

                if (request.LeadType.Contains(LeadType.ShowOnlyParentLeads))
                {
                    if (request.ChildLeadsCount != null && request.ChildLeadsCount > 0)
                    {
                        predicate = predicate.Or(i => i.ChildLeadsCount == request.ChildLeadsCount);
                    }
                    else
                    {
                        predicate = predicate.Or(i => i.ChildLeadsCount > 0);
                    }
                }

                if (request.LeadType.Contains(LeadType.ShowOnlyDuplicateLeads))
                {
                    predicate = predicate.Or(i => i.ChildLeadsCount == 0 && (i.RootId != null || i.ParentLeadId != null));
                }

                query = query.Where(predicate);
            }
            if (request?.IsUntouched != null && request.IsUntouched != default)
            {
                query = query.Where(i => !i.IsPicked == request.IsUntouched.Value);
            }
            if (request.Designations?.Any() ?? false)
            {
                request.Designations = request.Designations.ConvertAll(i => i.ToLower().Trim());
                query = query.Where(i => request.Designations.Contains(i.Designation.ToLower().Trim()));
            }
            if (request?.Nationality?.Any() ?? false)
            {
                var nationality = request.Nationality.ConvertAll(i => i.ToLower());
                query = query.Where(i => i.Nationality != null && nationality.Contains(i.Nationality.ToLower())).AsQueryable();
            }

            if (request?.ClusterName?.Any() ?? false)
            {
                var ClusterName = request.ClusterName.ConvertAll(i => i.ToLower());
                query = query.Where(i => i.Enquiries.Count > 0 && i.Enquiries.Any(i => ClusterName.Contains(i.ClusterName.ToLower()))).AsQueryable();
            }
            if (!string.IsNullOrEmpty(request.ReferralName))
            {
                query = query.Where(i => (i.ReferralName.ToLower().Trim().Contains(request.ReferralName.ToLower().Trim())));
            }
            if (!string.IsNullOrEmpty(request.ReferralContactNo))
            {
                query = query.Where(i => !string.IsNullOrEmpty(i.ReferralContactNo) && request.ReferralContactNo.Contains(i.ReferralContactNo.Substring(i.ReferralContactNo.Length - 10)));
            }
            if (!string.IsNullOrEmpty(request.ReferralEmail))
            {
                query = query.Where(i => (i.ReferralEmail.ToLower().Trim().Contains(request.ReferralEmail.ToLower().Trim())));
            }
            if (request?.PossesionType != null && request?.PossesionType != PossesionType.None)
            {

                switch (request?.PossesionType)
                {
                    case PossesionType.UnderConstruction:
                        query = query.Where(i => i.PossesionType == PossesionType.UnderConstruction);
                        break;

                    case PossesionType.SixMonth:
                        query = query.Where(i => i.PossesionType == PossesionType.SixMonth);
                        break;

                    case PossesionType.Year:
                        query = query.Where(i => i.PossesionType == PossesionType.Year);
                        break;

                    case PossesionType.TwoYears:
                        query = query.Where(i => i.PossesionType == PossesionType.TwoYears);
                        break;

                    case PossesionType.CustomDate:
                        DateTime? tempToPossesionDate = request?.ToPossesionDate?.ConvertToDateToUtc();
                        DateTime? tempFrompossesionDate = request?.FromPossesionDate?.ConvertFromDateToUtc();
                        query = query.Where(i => i.Enquiries.Any(j => j.PossessionDate != null && j.PossessionDate >= tempFrompossesionDate.Value && j.PossessionDate <= tempToPossesionDate.Value));

                        break;
                }
            }
            if (request?.LandLine != null && request.LandLine.Any())
            {
                query = query.Where(i => i.LandLine != null && request.LandLine.Contains(i.LandLine.Trim()));
            }
            if (request?.CountryCode?.Any() ?? false)
            {
                var codes = request.CountryCode
        .Where(c => !string.IsNullOrWhiteSpace(c))
        .Select(c => c.Trim().TrimStart('+'))
        .Distinct()
        .ToList();

                Expression<Func<Lead, bool>> filter = lead => false;

                foreach (var code in codes)
                {
                    var local = code;

                    filter = filter.Or(lead =>
                        (lead.CountryCode != null && lead.CountryCode.Trim().TrimStart('+') == local) ||
                        (lead.ContactNo != null &&
                            (lead.ContactNo.StartsWith("+" + local) || lead.ContactNo.StartsWith(local)))
                    );
                }

                query = query.Where(filter);
            }
            if (request?.AltCountryCode?.Any() ?? false)
            {
                query = query.Where(i => i.AltCountryCode != null && request.AltCountryCode.Contains(i.AltCountryCode));
            }
            if (request?.GenderTypes?.Any() ?? false)
            {
                query = query.Where(i => i.Gender != null && request.GenderTypes.Contains(i.Gender.Value));
            }
            if (request?.MaritalStatuses?.Any() ?? false)
            {
                query = query.Where(i => i.MaritalStatus != null && request.MaritalStatuses.Contains(i.MaritalStatus.Value));
            }
            if (request?.DateOfBirth != null)
            {
                query = query.Where(i => i.DateOfBirth != null && i.DateOfBirth == request.DateOfBirth);
            }
            return query.AsQueryable();
        }
        private IQueryable<Lead> BuildQuery(V3GetAllLeadsRequest request, Guid userId, IList<Guid> subIds, List<CustomMasterLeadStatus> customLeadStatuses = null)
        {
            var scope = _provider.CreateScope();
            var context = scope.ServiceProvider.GetRequiredService<ApplicationDbContext>();
            //request.UserType = request?.UserType ?? UserType.None;
            var reporteeIds = subIds.Where(i => i != userId && i != Guid.Empty);
            var selfWithReporteeIds = subIds.Where(i => i != Guid.Empty);
            var query = context.Leads.Where(i => !i.IsDeleted)
                .Include(i => i.CustomLeadStatus)
                .Include(i => i.Campaigns)
                .Include(i => i.Enquiries)
                .Include(i => i.Enquiries)
                    .ThenInclude(i => i.PropertyType)
                 .Include(i => i.Enquiries)
                   //.ThenInclude(i => i.Address)
                   .ThenInclude(i => i.Addresses)
                .Include(i => i.Appointments)
                .ThenInclude(i => i.Location)
                .Include(i => i.Projects)
                .Include(i => i.Enquiries)
                .ThenInclude(i => i.PropertyTypes)
                .AsQueryable();

            if (request.IsPicked != null && request.IsPicked != default)
            {
                query = query.Where(i => i.IsPicked == request.IsPicked.Value);
            }
            if (request?.IsDualOwnershipEnabled ?? false)
            {
                if (request.SecondaryUsers?.Any() ?? false)
                {
                    query = query.Where(i => request.SecondaryUsers.Contains(i.SecondaryUserId ?? Guid.Empty));
                }
            }
            if (request?.IsUntouched != null && request.IsUntouched != default)
            {
                query = query.Where(i => !i.IsPicked == request.IsUntouched.Value);
            }

            if ((request?.IsDualOwnershipEnabled ?? false) && (!request?.AssignToIds?.Any() ?? true))
            {
                switch (request.LeadVisibility)
                {
                    case BaseLeadVisibility.SelfWithReportee:
                        query = query.Where(i => (selfWithReporteeIds.Contains(i.AssignTo) || selfWithReporteeIds.Contains(i.SecondaryUserId ?? Guid.Empty))).Where(i => !i.IsArchived);
                        break;
                    case BaseLeadVisibility.Self:
                        query = query.Where(i => (i.AssignTo == userId || i.SecondaryUserId == userId)).Where(i => !i.IsArchived);
                        break;
                    case BaseLeadVisibility.Reportee:
                        query = query.Where(i => (reporteeIds.Contains(i.AssignTo) || reporteeIds.Contains(i.SecondaryUserId ?? Guid.Empty))).Where(i => !i.IsArchived);
                        break;
                    case BaseLeadVisibility.UnassignLead:
                        query = query.Where(i => i.AssignTo == Guid.Empty).Where(i => !i.IsArchived);
                        break;
                    case BaseLeadVisibility.DeletedLeads:
                        query = query.Where(i => i.IsArchived && selfWithReporteeIds.Contains(i.AssignTo));
                        break;
                    default:
                        break;
                }
            }
            else
            {
                switch (request.LeadVisibility)
                {
                    case BaseLeadVisibility.SelfWithReportee:
                        query = query.Where(i => selfWithReporteeIds.Contains(i.AssignTo)).Where(i => !i.IsArchived);
                        break;
                    case BaseLeadVisibility.Self:
                        query = query.Where(i => i.AssignTo == userId).Where(i => !i.IsArchived);
                        break;
                    case BaseLeadVisibility.Reportee:
                        query = query.Where(i => reporteeIds.Contains(i.AssignTo)).Where(i => !i.IsArchived);
                        break;
                    case BaseLeadVisibility.UnassignLead:
                        query = query.Where(i => i.AssignTo == Guid.Empty).Where(i => !i.IsArchived);
                        break;
                    case BaseLeadVisibility.DeletedLeads:
                        query = query.Where(i => i.IsArchived && selfWithReporteeIds.Contains(i.AssignTo));
                        break;
                    default:
                        break;
                }
            }
            if (!string.IsNullOrWhiteSpace(request?.SearchByNameOrNumber))
            {
                request.SearchByNameOrNumber = request.SearchByNameOrNumber.ToLower();
                query = query.Where(i => i.AlternateContactNo.Contains(request.SearchByNameOrNumber.Replace(" ", "")) ||
                                         i.ContactNo.Contains(request.SearchByNameOrNumber.Replace(" ", "")) ||
                                         i.Name.ToLower().Contains(request.SearchByNameOrNumber) ||
                                         i.Email.ToLower().Contains(request.SearchByNameOrNumber) ||
                                         i.SerialNumber.Contains(request.SearchByNameOrNumber.Replace(" ", "")));
            }
            if (request.SubStatuses?.Any() ?? false)
            {
                query = query.Where(i => i.CustomLeadStatus != null && request.SubStatuses.Any(j => j == i.CustomLeadStatus.Id));
            }
            var Statuses = (customLeadStatuses?.Where(i => i.Status is "not_interested" or "dropped" or "booked" or "booking_cancel"))?.ToList() ?? new();
            var StatusesIds = Statuses.Select(i => i.Id).ToList();
            var ScheduledStatus = (customLeadStatuses?.Where(i => i.Status is "site_visit_scheduled" or "callback" or "meeting_scheduled"))?.ToList() ?? new();
            var schuduledStatusIds = ScheduledStatus.Select(i => i.Id).ToList();
            TimeSpan userUtcOffset = request.BaseUTcOffset ?? TimeZoneInfo.Local.GetUtcOffset(DateTime.UtcNow);
            DateTime today = DateTime.UtcNow.Add(userUtcOffset).Date;
            switch (request.FilterType)
            {
                case LeadFilterTypeMobile.New:
                    query = query.Where(i => i.CustomLeadStatus != null && i.CustomLeadStatus.Status == "new");
                    break;
                #region ScheduleDate based categories
                case LeadFilterTypeMobile.ScheduleToday:
                    query = query.Where(i => ((i.ScheduledDate ?? DateTime.MinValue) + userUtcOffset)>= today && ((i.ScheduledDate ?? DateTime.MinValue) + userUtcOffset) < today.AddDays(1))
                    .Where(i => i.CustomLeadStatus == null || schuduledStatusIds.Contains(i.CustomLeadStatus.BaseId ?? Guid.Empty) || schuduledStatusIds.Contains(i.CustomLeadStatus.Id)); ;
                    break;
                case LeadFilterTypeMobile.Overdue:
                    query = query.Where(i => ((i.ScheduledDate ?? DateTime.MinValue) + userUtcOffset) < today)
                    .Where(i => i.CustomLeadStatus != null && (schuduledStatusIds.Contains(i.CustomLeadStatus.BaseId ?? Guid.Empty) || schuduledStatusIds.Contains(i.CustomLeadStatus.Id)));
                    break;
                case LeadFilterTypeMobile.ScheduledTomorrow:
                    query = query.Where(i => ((i.ScheduledDate ?? DateTime.MinValue) + userUtcOffset) >= today.AddDays(1) && ((i.ScheduledDate ?? DateTime.MinValue) + userUtcOffset) < today.AddDays(2))
                    .Where(i => i.CustomLeadStatus != null && (schuduledStatusIds.Contains(i.CustomLeadStatus.BaseId ?? Guid.Empty) || schuduledStatusIds.Contains(i.CustomLeadStatus.Id)));
                    break;
                case LeadFilterTypeMobile.UpcomingSchedules:
                    query = query.Where(i => ((i.ScheduledDate ?? DateTime.MinValue) + userUtcOffset) >= today.AddDays(1))
                    .Where(i => i.CustomLeadStatus == null || schuduledStatusIds.Contains(i.CustomLeadStatus.BaseId ?? Guid.Empty) || schuduledStatusIds.Contains(i.CustomLeadStatus.Id));
                    break;
                #endregion
                case LeadFilterTypeMobile.Pending:
                    query = query.Where(i => i.CustomLeadStatus != null && i.CustomLeadStatus.Status == "pending");
                    break;
                case LeadFilterTypeMobile.NotInterested:
                    query = query.Where(i => i.CustomLeadStatus != null && (i.CustomLeadStatus.MasterLeadStatusBaseId == Guid.Parse("065fa6fd-a15e-4248-b469-e8596980c97a") || i.CustomLeadStatus.Status == "not_interested"));
                    break;
                case LeadFilterTypeMobile.CallBack:
                    query = query.Where(i => i.CustomLeadStatus != null && i.CustomLeadStatus.MasterLeadStatusBaseId == Guid.Parse("5ae346bc-c695-4af4-8c3b-c8648587fbd6"));
                    break;
                case LeadFilterTypeMobile.SiteVisitScheduled:
                    query = query.Where(i => i.CustomLeadStatus != null && (i.CustomLeadStatus.Status == "site_visit_scheduled" || i.CustomLeadStatus.MasterLeadStatusBaseId.ToString() == "59647294-09d6-44a2-a346-9de5ba829e04"));
                    break;
                case LeadFilterTypeMobile.ScheduledMeeting:
                    query = query.Where(i => i.CustomLeadStatus != null && (i.CustomLeadStatus.Status == "meeting_scheduled" || i.CustomLeadStatus.MasterLeadStatusBaseId.ToString() == "99a7f794-9046-4a9d-b7e2-e0a2196b98dd"));

                    break;
                case LeadFilterTypeMobile.UnassignLeads:
                    query = query.Where(i => i.AssignTo == Guid.Empty);
                    break;
                case LeadFilterTypeMobile.Booked:
                    query = query.Where(i => i.CustomLeadStatus != null && i.CustomLeadStatus.Status == "booked");
                    break;
                case LeadFilterTypeMobile.Dropped:
                    query = query.Where(i => i.CustomLeadStatus != null && (i.CustomLeadStatus.MasterLeadStatusBaseId == Guid.Parse("023fcb89-037e-42f4-bcc2-647bb4e95c99") || i.CustomLeadStatus.Status == "dropped"));
                    break;
                case LeadFilterTypeMobile.Escalated:
                    query = query.Where(i => i.TagInfo != null && i.TagInfo.IsEscalated)
                    .Where(i => i.CustomLeadStatus == null || i.CustomLeadStatus.MasterLeadStatusBaseId != Guid.Parse("023fcb89-037e-42f4-bcc2-647bb4e95c99"))
                    .Where(i => i.CustomLeadStatus == null || i.CustomLeadStatus.Status != "booked")
                    .Where(i => i.CustomLeadStatus == null || i.CustomLeadStatus.MasterLeadStatusBaseId != Guid.Parse("065fa6fd-a15e-4248-b469-e8596980c97a"));
                    break;
                case LeadFilterTypeMobile.HotLeads:
                    query = query.Where(i => i.TagInfo != null && i.TagInfo.IsHotLead)
                    .Where(i => i.CustomLeadStatus == null || i.CustomLeadStatus.MasterLeadStatusBaseId != Guid.Parse("023fcb89-037e-42f4-bcc2-647bb4e95c99"))
                    .Where(i => i.CustomLeadStatus == null || i.CustomLeadStatus.Status != "booked")
                    .Where(i => i.CustomLeadStatus == null || i.CustomLeadStatus.MasterLeadStatusBaseId != Guid.Parse("065fa6fd-a15e-4248-b469-e8596980c97a"));
                    break;
                case LeadFilterTypeMobile.AboutToConvert:
                    query = query.Where(i => i.TagInfo != null && i.TagInfo.IsAboutToConvert)
                    .Where(i => i.CustomLeadStatus == null || i.CustomLeadStatus.MasterLeadStatusBaseId != Guid.Parse("023fcb89-037e-42f4-bcc2-647bb4e95c99"))
                    .Where(i => i.CustomLeadStatus == null || i.CustomLeadStatus.Status != "booked")
                    .Where(i => i.CustomLeadStatus == null || i.CustomLeadStatus.MasterLeadStatusBaseId != Guid.Parse("065fa6fd-a15e-4248-b469-e8596980c97a"));
                    break;
                case LeadFilterTypeMobile.WarmLeads:
                    query = query.Where(i => i.TagInfo != null && i.TagInfo.IsWarmLead)
                    .Where(i => i.CustomLeadStatus == null || i.CustomLeadStatus.MasterLeadStatusBaseId != Guid.Parse("023fcb89-037e-42f4-bcc2-647bb4e95c99"))
                    .Where(i => i.CustomLeadStatus == null || i.CustomLeadStatus.Status != "booked")
                    .Where(i => i.CustomLeadStatus == null || i.CustomLeadStatus.MasterLeadStatusBaseId != Guid.Parse("065fa6fd-a15e-4248-b469-e8596980c97a"));
                    break;
                case LeadFilterTypeMobile.ColdLead:
                    query = query.Where(i => i.TagInfo != null && i.TagInfo.IsColdLead)
                    .Where(i => i.CustomLeadStatus == null || i.CustomLeadStatus.MasterLeadStatusBaseId != Guid.Parse("023fcb89-037e-42f4-bcc2-647bb4e95c99"))
                    .Where(i => i.CustomLeadStatus == null || i.CustomLeadStatus.MasterLeadStatusBaseId != Guid.Parse("065fa6fd-a15e-4248-b469-e8596980c97a"))
                    .Where(i => i.CustomLeadStatus == null || i.CustomLeadStatus.Status != "booked");
                    break;
                case LeadFilterTypeMobile.Highlighted:
                    query = query.Where(i => i.TagInfo != null && i.TagInfo.IsHighlighted)
                    .Where(i => i.CustomLeadStatus == null || i.CustomLeadStatus.MasterLeadStatusBaseId != Guid.Parse("023fcb89-037e-42f4-bcc2-647bb4e95c99"))
                    .Where(i => i.CustomLeadStatus == null || i.CustomLeadStatus.MasterLeadStatusBaseId != Guid.Parse("065fa6fd-a15e-4248-b469-e8596980c97a"))
                    .Where(i => i.CustomLeadStatus == null || i.CustomLeadStatus.Status != "booked");
                    break;
                case LeadFilterTypeMobile.Active:
                    query = query.Where(i => i.CustomLeadStatus != null && !StatusesIds.Contains(i.CustomLeadStatus.BaseId ?? Guid.Empty) && !StatusesIds.Contains(i.CustomLeadStatus.Id));
                    break;
                case LeadFilterTypeMobile.AllWithNID:
                case LeadFilterTypeMobile.All:
                    query = query.Where(i => i.CustomLeadStatus != null);
                    break;
                case LeadFilterTypeMobile.BookingCancel:
                    query = query.Where(i => i.CustomLeadStatus != null && i.CustomLeadStatus.Status == "booking_cancel");
                    break;
                case LeadFilterTypeMobile.Untouched:
                    query = query.Where(i => !i.IsPicked);
                    break;
                case LeadFilterTypeMobile.ExpressionOfInterest:
                    query = query.Where(i => i.CustomLeadStatus != null && i.CustomLeadStatus.Status == "expression_of_interest");
                    break;
                case LeadFilterTypeMobile.MeetingDone:
                    query = query.Where(i => i.Appointments.Any(i => i.Type == AppointmentType.Meeting && i.IsDone));
                    break;
                case LeadFilterTypeMobile.SiteVisitDone:
                    query = query.Where(i => i.Appointments.Any(i => i.Type == AppointmentType.SiteVisit && i.IsDone));
                    break;
                default:
                    break;
            }
            if (request.DateType.HasValue && ((request.FromDate.HasValue && request.FromDate.Value != default) || (request.ToDate.HasValue && request.ToDate.Value != default)))
            {
                DateTime? fromDate = request.FromDate.HasValue ? request.FromDate.Value.ConvertFromDateToUtc() : DateTime.MinValue;
                DateTime? toDate = request.ToDate.HasValue ? request.ToDate.Value.ConvertToDateToUtc() : DateTime.MaxValue;
                switch (request.DateType)
                {
                    case DateType.ReceivedDate:
                        query = query.Where(i => i.CreatedOn >= fromDate.Value && i.CreatedOn <= toDate.Value);
                        break;
                    case DateType.ScheduledDate:
                        query = query.Where(i => i.ScheduledDate != null && i.ScheduledDate.Value >= fromDate.Value && i.ScheduledDate.Value <= toDate.Value);
                        break;
                    case DateType.ModifiedDate:
                        query = query.Where(i => i.LastModifiedOn != null && i.LastModifiedOn.Value >= fromDate.Value && i.LastModifiedOn.Value <= toDate.Value);
                        break;
                    case DateType.DeletedDate:
                        query = query.Where(i => i.ArchivedOn >= fromDate.Value && i.ArchivedOn < toDate.Value);
                        break;
                    case DateType.PickedDate:
                        query = query.Where(i => i.PickedDate >= fromDate.Value && i.PickedDate <= toDate.Value);
                        break;
                    case DateType.BookedDate:
                        // query = query.Where(i => i.BookedDate >= fromDate.Value && i.BookedDate <= toDate.Value);
                        query = query.Where(item => item.BookedDetails.Any(detail => detail.BookedDate.Value >= fromDate.Value && detail.BookedDate.Value <= toDate.Value));

                        break;
                    default:
                        break;
                }
            }
            if (request?.BookedIds?.Any() ?? false)
            {
                query = query.Where(i => i.BookedDetails.Any(i => request.BookedIds.Contains(i.BookedBy.Value)));
            }
            if (request?.LeadType != null && request.LeadType.Any())
            {
                var predicate = PredicateBuilder.New<Lead>(false);

                if (request.LeadType.Contains(LeadType.ShowPrimeLeads))
                {
                    predicate = predicate.Or(i => i.ChildLeadsCount == 0 && i.RootId == null && i.ParentLeadId == null);
                }

                if (request.LeadType.Contains(LeadType.ShowOnlyParentLeads))
                {
                    if (request.ChildLeadsCount != null && request.ChildLeadsCount > 0)
                    {
                        predicate = predicate.Or(i => i.ChildLeadsCount == request.ChildLeadsCount);
                    }
                    else
                    {
                        predicate = predicate.Or(i => i.ChildLeadsCount > 0);
                    }
                }

                if (request.LeadType.Contains(LeadType.ShowOnlyDuplicateLeads))
                {
                    predicate = predicate.Or(i => i.ChildLeadsCount == 0 && (i.RootId != null || i.ParentLeadId != null));
                }

                query = query.Where(predicate);
            }
            if (request?.OriginalOwnerIds?.Any() ?? false)
            {
                query = query.Where(i => i.OriginalOwner != null && request.OriginalOwnerIds.Contains(i.OriginalOwner.Value));
            }
            if (request?.CountryCode?.Any() ?? false)
            {
                var codes = request.CountryCode
        .Where(c => !string.IsNullOrWhiteSpace(c))
        .Select(c => c.Trim().TrimStart('+'))
        .Distinct()
        .ToList();

                Expression<Func<Lead, bool>> filter = lead => false;

                foreach (var code in codes)
                {
                    var local = code;

                    filter = filter.Or(lead =>
                        (lead.CountryCode != null && lead.CountryCode.Trim().TrimStart('+') == local) ||
                        (lead.ContactNo != null &&
                            (lead.ContactNo.StartsWith("+" + local) || lead.ContactNo.StartsWith(local)))
                    );
                }

                query = query.Where(filter);
            }
            if (request?.AltCountryCode?.Any() ?? false)
            {
                query = query.Where(i => i.AltCountryCode != null && request.AltCountryCode.Contains(i.AltCountryCode));
            }

            if (request?.CallDirections != null && request.CallDirections.Any() && !request.CallDirections.Contains(CallDirection.None))
            {
                query = query.Where(i =>
                    i.LeadCallLogs != null && i.LeadCallLogs.Any(j => request.CallDirections.Contains(j.CallDirection)));
            }

            if (request?.CallStatuses != null && request.CallStatuses.Any() && !request.CallStatuses.Contains(CallStatus.None))
            {
                query = query.Where(i =>
                    i.LeadCallLogs != null && i.LeadCallLogs.Any(j => request.CallStatuses.Contains(j.CallStatus)));
            }

            if (request?.UserIds != null && request.UserIds.Any())
            {
                query = query.Where(i =>
                    i.LeadCallLogs != null && i.LeadCallLogs.Any(j => request.UserIds.Contains(j.UserId)));
            }

            return query.AsQueryable();
        }
        #endregion



        #region CustomFiltersMobile
        public async Task<IEnumerable<Domain.Entities.Lead>> GetAllLeadsByCustomFiltersForMobileAsync(CustomFilter? filter, GetAllLeadsParametersNewFilters request, IEnumerable<Guid> subIds, Guid userId, bool isAdmin, List<Guid> leadHistoryIds = null)
        {
            var query = MobileCustomFilterBuildQuery(filter, request, subIds, userId, leadHistoryIds, isAdmin);

            if (filter.IsForward ?? false)
            {
                query = query.OrderBy(i => (i.ScheduledDate ?? DateTime.MinValue)).ThenBy(i => i.Name);
            }
            else
            {
                query = query.OrderByDescending(i => (i.LastModifiedOn ?? DateTime.MaxValue)).ThenBy(i => i.Name);
            }

            query = query
                .Skip(request.PageSize * (request.PageNumber - 1))
                .Take(request.PageSize)
                .AsQueryable();

            var leads = await query.ToListAsync().ConfigureAwait(false);
            return leads;
        }
        public async Task<int> GetLeadsCountByCustomFiltersForMobileAsync(CustomFilter? filter, GetAllLeadsParametersNewFilters request, IEnumerable<Guid> subIds, Guid userId, bool isAdmin, List<Guid> leadHistoryIds = null, List<CustomMasterLeadStatus>? customMasterLeadStatus = null)
        {
            var query = MobileCustomFilterBuildQuery(filter, request, subIds, userId, leadHistoryIds, isAdmin,customMasterLeadStatus);
            var count = await query.Select(i => i.Id).CountAsync();
            return count;
        }
        private IQueryable<Lead> MobileCustomFilterBuildQuery(CustomFilter? filter, GetAllLeadsParametersNewFilters request, IEnumerable<Guid> subIds, Guid userId, List<Guid> leadHistoryIds, bool isAdmin, List<CustomMasterLeadStatus>? customMasterLeadStatus = null)
        {
            var scope = _provider.CreateScope();
            var context = scope.ServiceProvider.GetRequiredService<ApplicationDbContext>();
            var reporteeIds = subIds.Where(i => i != userId && i != Guid.Empty);
            var selfWithReporteeIds = subIds.Where(i => i != Guid.Empty);
            var query = context.Leads.Where(i => !i.IsDeleted)
                .Include(i => i.CustomLeadStatus)
                .Include(i => i.Campaigns)
                .Include(i => i.Enquiries)
                    .ThenInclude(i => i.PropertyType)
                .Include(i => i.Appointments)
                .Include(i => i.Enquiries)
                .ThenInclude(i => i.PropertyTypes)
                .AsQueryable();

            if (request.CustomFlags?.Any() ?? false)
            {
                query = query.Where(i => i.CustomFlags != null && i.CustomFlags.Any(j => j.Flag != null && request.CustomFlags.Contains(j.Flag.Name ?? string.Empty)));
            }
            //if (request?.IsDualOwnershipEnabled ?? false)
            //{
            //    if (request.SecondaryUsers?.Any() ?? false)
            //    {
            //        query = query.Where(i => request.SecondaryUsers.Contains(i.SecondaryUserId ?? Guid.Empty));
            //    }
            //}
            if (request.IsPicked != null && request.IsPicked != default)
            {
                query = query.Where(i => i.IsPicked == request.IsPicked.Value);
            }
            if (request.DataConverted != null)
            {
                if (request.DataConverted == true)
                {
                    query = query.Where(i => i.IsConvertedFromData == true);
                }
                else
                {
                    query = query.Where(i => i.IsConvertedFromData != true);
                }
            }
            if (request.QualifiedByIds?.Any() ?? false)
            {
                query = query.Where(i => i.QualifiedBy.HasValue && request.QualifiedByIds.Contains(i.QualifiedBy.Value));
            }
            if (request?.OriginalOwnerIds?.Any() ?? false)
            {
                query = query.Where(i => i.OriginalOwner != null && request.OriginalOwnerIds.Contains(i.OriginalOwner.Value));
            }
            if (!string.IsNullOrWhiteSpace(request.ConfidentialNotes))
            {
                request.ConfidentialNotes = request.ConfidentialNotes.ToLower().Trim();
                query = query.Where(i => i.ConfidentialNotes.ToLower().Trim().Contains(request.ConfidentialNotes));
            }

            if (request.CarpetArea != null && request.CarpetAreaUnitId != default)
            {
                query = query.Where(i => i.Enquiries.Any(i => i.CarpetArea == request.CarpetArea && i.CarpetAreaUnitId == request.CarpetAreaUnitId));
            }
            else if (request.CarpetArea != null)
            {
                query = query.Where(i => i.Enquiries.Any(i => i.CarpetArea == request.CarpetArea || i.CarpetAreaInSqMtr == request.CarpetArea));
            }
            if (request.BuiltUpArea != null && request.BuiltUpAreaUnitId != default)
            {
                query = query.Where(i => i.Enquiries.Any(i => i.BuiltUpArea == request.BuiltUpArea && i.BuiltUpAreaUnitId == request.BuiltUpAreaUnitId));
            }
            else if (request.BuiltUpArea != null)
            {
                query = query.Where(i => i.Enquiries.Any(i => i.BuiltUpArea == request.BuiltUpArea || i.BuiltUpAreaInSqMtr == request.BuiltUpArea));
            }
            if (request.SaleableArea != null && request.SaleableAreaUnitId != default)
            {
                query = query.Where(i => i.Enquiries.Any(i => i.SaleableArea == request.SaleableArea && i.SaleableAreaUnitId == request.SaleableAreaUnitId));
            }
            else if (request.SaleableArea != null)
            {
                query = query.Where(i => i.Enquiries.Any(i => i.SaleableArea == request.SaleableArea || i.SaleableAreaInSqMtr == request.SaleableArea));
            }
            if (request.NetArea != null && request.NetAreaUnitId != default)
            {
                query = query.Where(i => i.Enquiries.Any(i => i.NetArea == request.NetArea && i.NetAreaUnitId == request.NetAreaUnitId));
            }
            else if (request.NetArea != null)
            {
                query = query.Where(i => i.Enquiries.Any(i => i.NetArea == request.NetArea || i.NetAreaInSqMtr == request.NetArea));
            }
            if (request.PropertyArea != null && request.PropertyAreaUnitId != default)
            {
                query = query.Where(i => i.Enquiries.Any(i => i.PropertyArea == request.PropertyArea && i.PropertyAreaUnitId == request.PropertyAreaUnitId));
            }
            else if (request.PropertyArea != null)
            {
                query = query.Where(i => i.Enquiries.Any(i => i.PropertyArea == request.PropertyArea || i.PropertyAreaInSqMtr == request.PropertyArea));
            }
            if ((request.MinCarpetArea != null || request.MaxCarpetArea != null) && request.CarpetAreaUnitId != default)
            {
                query = query.Where(i => i.Enquiries.Any(e =>
                    e.CarpetAreaUnitId == request.CarpetAreaUnitId &&
                    (request.MinCarpetArea == null || e.CarpetArea >= request.MinCarpetArea) &&
                    (request.MaxCarpetArea == null || e.CarpetArea <= request.MaxCarpetArea)));
            }
            else if (request.MinCarpetArea != null || request.MaxCarpetArea != null)
            {
                query = query.Where(i => i.Enquiries.Any(e =>
                    (request.MinCarpetArea == null || e.CarpetArea >= request.MinCarpetArea) &&
                    (request.MaxCarpetArea == null || e.CarpetArea <= request.MaxCarpetArea)));
            }
            if ((request.MinBuiltUpArea != null || request.MaxBuiltUpArea != null) && request.BuiltUpAreaUnitId != default && request.BuiltUpAreaUnitId != Guid.Empty)
            {
                query = query.Where(i => i.Enquiries.Any(e =>
                    e.BuiltUpAreaUnitId == request.BuiltUpAreaUnitId &&
                    (request.MinBuiltUpArea == null || e.BuiltUpArea >= request.MinBuiltUpArea) &&
                    (request.MaxBuiltUpArea == null || e.BuiltUpArea <= request.MaxBuiltUpArea)));
            }
            else if (request.MinBuiltUpArea != null || request.MaxBuiltUpArea != null)
            {
                query = query.Where(i => i.Enquiries.Any(e =>
                    (request.MinBuiltUpArea == null || e.BuiltUpArea >= request.MinBuiltUpArea) &&
                    (request.MaxBuiltUpArea == null || e.BuiltUpArea <= request.MaxBuiltUpArea)));
            }
            if ((request.MinSaleableArea != null || request.MaxSaleableArea != null) && request.SaleableAreaUnitId != default && request.SaleableAreaUnitId != Guid.Empty)
            {
                query = query.Where(i => i.Enquiries.Any(e =>
                    e.SaleableAreaUnitId == request.SaleableAreaUnitId &&
                    (request.MinSaleableArea == null || e.SaleableArea >= request.MinSaleableArea) &&
                    (request.MaxSaleableArea == null || e.SaleableArea <= request.MaxSaleableArea)));
            }
            else if (request.MinSaleableArea != null || request.MaxSaleableArea != null)
            {
                query = query.Where(i => i.Enquiries.Any(e =>
                    (request.MinSaleableArea == null || e.SaleableArea >= request.MinSaleableArea) &&
                    (request.MaxSaleableArea == null || e.SaleableArea <= request.MaxSaleableArea)));
            }
            if ((request.MinPropertyArea != null || request.MaxPropertyArea != null) && request.PropertyAreaUnitId != default && request.PropertyAreaUnitId != Guid.Empty)
            {
                query = query.Where(i => i.Enquiries.Any(e =>
                    e.PropertyAreaUnitId == request.PropertyAreaUnitId &&
                    (request.MinPropertyArea == null || e.PropertyArea >= request.MinPropertyArea) &&
                    (request.MaxPropertyArea == null || e.PropertyArea <= request.MaxPropertyArea)));
            }
            else if (request.MinPropertyArea != null || request.MaxPropertyArea != null)
            {
                query = query.Where(i => i.Enquiries.Any(e =>
                    (request.MinPropertyArea == null || e.PropertyArea >= request.MinPropertyArea) &&
                    (request.MaxPropertyArea == null || e.PropertyArea <= request.MaxPropertyArea)));
            }

            if ((request.MinNetArea != null || request.MaxNetArea != null) && request.NetAreaUnitId != default && request.NetAreaUnitId != Guid.Empty)
            {
                query = query.Where(i => i.Enquiries.Any(e =>
                    e.NetAreaUnitId == request.NetAreaUnitId &&
                    (request.MinNetArea == null || e.NetArea >= request.MinNetArea) &&
                    (request.MaxNetArea == null || e.NetArea <= request.MaxNetArea)));
            }
            else if (request.MinNetArea != null || request.MaxNetArea != null)
            {
                query = query.Where(i => i.Enquiries.Any(e =>
                    (request.MinNetArea == null || e.NetArea >= request.MinNetArea) &&
                    (request.MaxNetArea == null || e.NetArea <= request.MaxNetArea)));
            }
            if (request.FromMinBudget != null || request.ToMinBudget != null || request.FromMaxBudget != null || request.ToMaxBudget != null)
            {
                if (request.FromMinBudget != null && request.ToMinBudget != null)
                {
                    query = query.Where(i => i.Enquiries.Any(e =>
                        (e.LowerBudget >= request.FromMinBudget && e.LowerBudget <= request.ToMinBudget) ||
                        (e.LowerBudget >= request.FromMinBudget && e.LowerBudget <= request.ToMinBudget)
                    ));
                }
                else if (request.FromMinBudget != null && request.ToMinBudget == null)
                {
                    query = query.Where(i => i.Enquiries.Any(e =>
                        e.LowerBudget >= request.FromMinBudget ||
                        e.LowerBudget >= request.FromMinBudget
                    ));
                }
                else if (request.FromMinBudget == null && request.ToMinBudget != null)
                {
                    query = query.Where(i => i.Enquiries.Any(e =>
                        e.LowerBudget <= request.ToMinBudget ||
                        e.LowerBudget <= request.ToMinBudget
                    ));
                }

                if (request.FromMaxBudget != null && request.ToMaxBudget != null)
                {
                    query = query.Where(i => i.Enquiries.Any(e =>
                        (e.UpperBudget >= request.FromMaxBudget && e.UpperBudget <= request.ToMaxBudget) ||
                        (e.UpperBudget >= request.FromMaxBudget && e.UpperBudget <= request.ToMaxBudget)
                    ));
                }
                else if (request.FromMaxBudget != null && request.ToMaxBudget == null)
                {
                    query = query.Where(i => i.Enquiries.Any(e =>
                        e.UpperBudget >= request.FromMaxBudget ||
                        e.UpperBudget >= request.FromMaxBudget
                    ));
                }
                else if (request.FromMaxBudget == null && request.ToMaxBudget != null)
                {
                    query = query.Where(i => i.Enquiries.Any(e =>
                        e.UpperBudget <= request.ToMaxBudget ||
                        e.UpperBudget <= request.ToMaxBudget
                    ));
                }
            }

            if (!string.IsNullOrEmpty(request.UnitName))
            {
                query = query.Where(i => i.Enquiries.Any(e => e.UnitName != null && e.UnitName.ToLower().Trim() == request.UnitName.ToLower().Trim()));
            }
            if (request?.UnitNames?.Any() ?? false)
            {
                var unitnames = request.UnitNames.ConvertAll(i => i.ToLower());
                query = query.Where(i => i.Enquiries.Count > 0 && i.Enquiries.Any(i => unitnames.Contains(i.UnitName.ToLower()))).AsQueryable();
            }
            if (request?.Nationality?.Any() ?? false)
            {
                var nationality = request.Nationality.ConvertAll(i => i.ToLower());
                query = query.Where(i => i.Nationality != null && nationality.Contains(i.Nationality.ToLower())).AsQueryable();
            }

            if (request?.ClusterName?.Any() ?? false)
            {
                var ClusterName = request.ClusterName.ConvertAll(i => i.ToLower());
                query = query.Where(i => i.Enquiries.Count > 0 && i.Enquiries.Any(i => ClusterName.Contains(i.ClusterName.ToLower()))).AsQueryable();
            }
            if (!string.IsNullOrEmpty(request.ReferralName))
            {
                query = query.Where(i => (i.ReferralName.ToLower().Trim().Contains(request.ReferralName.ToLower().Trim())));
            }
            if (!string.IsNullOrEmpty(request.ReferralContactNo))
            {
                query = query.Where(i => !string.IsNullOrEmpty(i.ReferralContactNo) && request.ReferralContactNo.Contains(i.ReferralContactNo.Substring(i.ReferralContactNo.Length - 10)));
            }
            if (!string.IsNullOrEmpty(request.ReferralEmail))
            {
                query = query.Where(i => (i.ReferralEmail.ToLower().Trim().Contains(request.ReferralEmail.ToLower().Trim())));
            }
            if (leadHistoryIds?.Any() ?? false)
            {
                query = query.Where(i => leadHistoryIds.Contains(i.Id));
            }

            if (request?.MeetingOrVisitStatuses?.Any() ?? false)
            {
                DateTime? fromDateForMeetingOrVisit = request.FromDateForMeetingOrVisit != null ? request.FromDateForMeetingOrVisit.Value.ConvertFromDateToUtc() : null;
                DateTime? toDateForMeetingOrVisit = request.ToDateForMeetingOrVisit != null ? request.ToDateForMeetingOrVisit.Value.ConvertToDateToUtc() : null;
                (List<AppointmentType> appTypes, List<bool> appDoneStatuses) = GetAppointmentTypes(request);
                if (request.AppointmentDoneByUserIds?.Any() ?? false)
                {
                    query = query.Where(i => i.Appointments.Any(app => request.AppointmentDoneByUserIds.Contains(app.CreatedBy) && app.Type != AppointmentType.None && appTypes.Contains(app.Type) && appDoneStatuses.Contains(app.IsDone)));
                }
                else if (!isAdmin && (!request.AppointmentDoneByUserIds?.Any() ?? true))
                {
                    query = query.Where(i => i.Appointments.Any(j => j.UserId == i.AssignTo && j.Type != AppointmentType.None));
                }
                if (fromDateForMeetingOrVisit != null && toDateForMeetingOrVisit != null)
                {
                    query = query.Where(i => (request.MeetingOrVisitStatuses.Contains(MeetingOrVisitCompletionStatus.IsMeetingDone)
                                         && i.Appointments.Any(i => i.Type == AppointmentType.Meeting && i.IsDone && (i.UserId != Guid.Empty) && (subIds != null) && subIds.Contains(i.UserId) && i.CreatedOn > fromDateForMeetingOrVisit && i.CreatedOn < toDateForMeetingOrVisit))
                                         ||
                                             (request.MeetingOrVisitStatuses.Contains(MeetingOrVisitCompletionStatus.IsMeetingNotDone)
                                             && i.Appointments.Any(i => i.Type == AppointmentType.Meeting && !i.IsDone && (i.UserId != Guid.Empty) && (subIds != null) && subIds.Contains(i.UserId) && i.CreatedOn > fromDateForMeetingOrVisit && i.CreatedOn < toDateForMeetingOrVisit))
                                         ||
                                             (request.MeetingOrVisitStatuses.Contains(MeetingOrVisitCompletionStatus.IsSiteVisitDone)
                                             && i.Appointments.Any(i => i.Type == AppointmentType.SiteVisit && i.IsDone && (i.UserId != Guid.Empty) && (subIds != null) && subIds.Contains(i.UserId) && i.CreatedOn > fromDateForMeetingOrVisit && i.CreatedOn < toDateForMeetingOrVisit))
                                         ||
                                             (request.MeetingOrVisitStatuses.Contains(MeetingOrVisitCompletionStatus.IsSiteVisitNotDone)
                                             && i.Appointments.Any(i => i.Type == AppointmentType.SiteVisit && !i.IsDone && (i.UserId != Guid.Empty) && (subIds != null) && subIds.Contains(i.UserId) && i.CreatedOn > fromDateForMeetingOrVisit && i.CreatedOn < toDateForMeetingOrVisit)));
                }
                else if (fromDateForMeetingOrVisit != null)
                {
                    query = query.Where(i => (request.MeetingOrVisitStatuses.Contains(MeetingOrVisitCompletionStatus.IsMeetingDone)
                                         && i.Appointments.Any(i => i.Type == AppointmentType.Meeting && i.IsDone && (i.UserId != Guid.Empty) && (subIds != null) && subIds.Contains(i.UserId) && i.CreatedOn > fromDateForMeetingOrVisit))
                                         ||
                                             (request.MeetingOrVisitStatuses.Contains(MeetingOrVisitCompletionStatus.IsMeetingNotDone)
                                             && i.Appointments.Any(i => i.Type == AppointmentType.Meeting && !i.IsDone && (i.UserId != Guid.Empty) && (subIds != null) && subIds.Contains(i.UserId) && i.CreatedOn > fromDateForMeetingOrVisit))
                                         ||
                                             (request.MeetingOrVisitStatuses.Contains(MeetingOrVisitCompletionStatus.IsSiteVisitDone)
                                             && i.Appointments.Any(i => i.Type == AppointmentType.SiteVisit && i.IsDone && (i.UserId != Guid.Empty) && (subIds != null) && subIds.Contains(i.UserId) && i.CreatedOn > fromDateForMeetingOrVisit))
                                         ||
                                             (request.MeetingOrVisitStatuses.Contains(MeetingOrVisitCompletionStatus.IsSiteVisitNotDone)
                                             && i.Appointments.Any(i => i.Type == AppointmentType.SiteVisit && !i.IsDone && (i.UserId != Guid.Empty) && (subIds != null) && subIds.Contains(i.UserId) && i.CreatedOn > fromDateForMeetingOrVisit)));
                }
                else if (toDateForMeetingOrVisit != null)
                {
                    query = query.Where(i => (request.MeetingOrVisitStatuses.Contains(MeetingOrVisitCompletionStatus.IsMeetingDone)
                                         && i.Appointments.Any(i => i.Type == AppointmentType.Meeting && i.IsDone && (i.UserId != Guid.Empty) && (subIds != null) && subIds.Contains(i.UserId) && i.CreatedOn < toDateForMeetingOrVisit.Value))
                                         ||
                                             (request.MeetingOrVisitStatuses.Contains(MeetingOrVisitCompletionStatus.IsMeetingNotDone)
                                             && i.Appointments.Any(i => i.Type == AppointmentType.Meeting && !i.IsDone && (i.UserId != Guid.Empty) && (subIds != null) && subIds.Contains(i.UserId) && i.CreatedOn < toDateForMeetingOrVisit.Value))
                                         ||
                                             (request.MeetingOrVisitStatuses.Contains(MeetingOrVisitCompletionStatus.IsSiteVisitDone)
                                             && i.Appointments.Any(i => i.Type == AppointmentType.SiteVisit && i.IsDone && (i.UserId != Guid.Empty) && (subIds != null) && subIds.Contains(i.UserId) && i.CreatedOn < toDateForMeetingOrVisit.Value))
                                         ||
                                             (request.MeetingOrVisitStatuses.Contains(MeetingOrVisitCompletionStatus.IsSiteVisitNotDone)
                                             && i.Appointments.Any(i => i.Type == AppointmentType.SiteVisit && !i.IsDone && (i.UserId != Guid.Empty) && (subIds != null) && subIds.Contains(i.UserId) && i.CreatedOn < toDateForMeetingOrVisit.Value)));
                }
                else if ((request.AssignToIds?.Any() ?? false) && (request.OwnerSelection == OwnerSelectionType.Both) )
                {
                    query = query.Where(i => (request.MeetingOrVisitStatuses.Contains(MeetingOrVisitCompletionStatus.IsMeetingDone)
                                                && i.Appointments.Any(j => j.Type == AppointmentType.Meeting && j.IsDone && (j.UserId != Guid.Empty) && (subIds != null) && (subIds.Contains(i.AssignTo) || request.AssignToIds.Contains(i.SecondaryUserId.Value))))
                                           ||
                                                (request.MeetingOrVisitStatuses.Contains(MeetingOrVisitCompletionStatus.IsMeetingNotDone)
                                                && i.Appointments.Any(j => j.Type == AppointmentType.Meeting && !j.IsDone && (j.UserId != Guid.Empty) && (subIds != null) && (subIds.Contains(i.AssignTo) || request.AssignToIds.Contains(i.SecondaryUserId.Value))))
                                           ||
                                                (request.MeetingOrVisitStatuses.Contains(MeetingOrVisitCompletionStatus.IsSiteVisitDone)
                                                && i.Appointments.Any(j => j.Type == AppointmentType.SiteVisit && j.IsDone && (j.UserId != Guid.Empty) && (subIds != null) && (subIds.Contains(i.AssignTo) || request.AssignToIds.Contains(i.SecondaryUserId.Value))))
                                           ||
                                                (request.MeetingOrVisitStatuses.Contains(MeetingOrVisitCompletionStatus.IsSiteVisitNotDone)
                                                && i.Appointments.Any(j => j.Type == AppointmentType.SiteVisit && !j.IsDone && (j.UserId != Guid.Empty) && (subIds != null) && (subIds.Contains(i.AssignTo) || request.AssignToIds.Contains(i.SecondaryUserId.Value)))));

                }
                else if ((request.OwnerSelection == OwnerSelectionType.SecondaryOwner) && (request.AssignToIds?.Any() ?? false))
                {
                    query = query.Where(i => (request.MeetingOrVisitStatuses.Contains(MeetingOrVisitCompletionStatus.IsMeetingDone)
                                                && i.Appointments.Any(j => j.Type == AppointmentType.Meeting && j.IsDone && (j.UserId != Guid.Empty) && request.AssignToIds.Contains(i.SecondaryUserId.Value)))
                                           ||
                                                (request.MeetingOrVisitStatuses.Contains(MeetingOrVisitCompletionStatus.IsMeetingNotDone)
                                                && i.Appointments.Any(j => j.Type == AppointmentType.Meeting && !j.IsDone && (j.UserId != Guid.Empty) && request.AssignToIds.Contains(i.SecondaryUserId.Value)))
                                           ||
                                                (request.MeetingOrVisitStatuses.Contains(MeetingOrVisitCompletionStatus.IsSiteVisitDone)
                                                && i.Appointments.Any(j => j.Type == AppointmentType.SiteVisit && j.IsDone && (j.UserId != Guid.Empty) && request.AssignToIds.Contains(i.SecondaryUserId.Value)))
                                           ||
                                                (request.MeetingOrVisitStatuses.Contains(MeetingOrVisitCompletionStatus.IsSiteVisitNotDone)
                                                && i.Appointments.Any(j => j.Type == AppointmentType.SiteVisit && !j.IsDone && (j.UserId != Guid.Empty) && request.AssignToIds.Contains(i.SecondaryUserId.Value))));

                }
                else if ((request.AssignToIds?.Any() ?? false) )
                {
                    query = query.Where(i => (request.MeetingOrVisitStatuses.Contains(MeetingOrVisitCompletionStatus.IsMeetingDone)
                                                && i.Appointments.Any(j => j.Type == AppointmentType.Meeting && j.IsDone && (j.UserId != Guid.Empty) && (subIds != null) && subIds.Contains(i.AssignTo)))
                                           ||
                                                (request.MeetingOrVisitStatuses.Contains(MeetingOrVisitCompletionStatus.IsMeetingNotDone)
                                                && i.Appointments.Any(j => j.Type == AppointmentType.Meeting && !j.IsDone && (j.UserId != Guid.Empty) && (subIds != null) && subIds.Contains(i.AssignTo)))
                                           ||
                                                (request.MeetingOrVisitStatuses.Contains(MeetingOrVisitCompletionStatus.IsSiteVisitDone)
                                                && i.Appointments.Any(j => j.Type == AppointmentType.SiteVisit && j.IsDone && (j.UserId != Guid.Empty) && (subIds != null) && subIds.Contains(i.AssignTo)))
                                           ||
                                                (request.MeetingOrVisitStatuses.Contains(MeetingOrVisitCompletionStatus.IsSiteVisitNotDone)
                                                && i.Appointments.Any(j => j.Type == AppointmentType.SiteVisit && !j.IsDone && (j.UserId != Guid.Empty) && (subIds != null) && subIds.Contains(i.AssignTo))));

                }

                else
                {
                    query = query.Where(i => (request.MeetingOrVisitStatuses.Contains(MeetingOrVisitCompletionStatus.IsMeetingDone)
                                                && i.Appointments.Any(i => i.Type == AppointmentType.Meeting && i.IsDone && (i.UserId != Guid.Empty) && (subIds != null) && subIds.Contains(i.UserId)))
                                           ||
                                                (request.MeetingOrVisitStatuses.Contains(MeetingOrVisitCompletionStatus.IsMeetingNotDone)
                                                && i.Appointments.Any(i => i.Type == AppointmentType.Meeting && !i.IsDone && (i.UserId != Guid.Empty) && (subIds != null) && subIds.Contains(i.UserId)))
                                           ||
                                                (request.MeetingOrVisitStatuses.Contains(MeetingOrVisitCompletionStatus.IsSiteVisitDone)
                                                && i.Appointments.Any(i => i.Type == AppointmentType.SiteVisit && i.IsDone && (i.UserId != Guid.Empty) && (subIds != null) && subIds.Contains(i.UserId)))
                                           ||
                                                (request.MeetingOrVisitStatuses.Contains(MeetingOrVisitCompletionStatus.IsSiteVisitNotDone)
                                                && i.Appointments.Any(i => i.Type == AppointmentType.SiteVisit && !i.IsDone && (i.UserId != Guid.Empty) && (subIds != null) && subIds.Contains(i.UserId))));
                }
            }
            if ((request?.IsDualOwnershipEnabled ?? false) && (request?.AssignToIds?.Any() ?? false) && (request.OwnerSelection == OwnerSelectionType.Both))
            {
                switch (request.LeadVisibility)
                {
                    case BaseLeadVisibility.SelfWithReportee:
                        query = query.Where(i => (selfWithReporteeIds.Contains(i.AssignTo) || selfWithReporteeIds.Contains(i.SecondaryUserId ?? Guid.Empty))).Where(i => !i.IsArchived);
                        break;
                    case BaseLeadVisibility.Self:
                        query = query.Where(i => (i.AssignTo == userId || i.SecondaryUserId == userId)).Where(i => !i.IsArchived);
                        break;
                    case BaseLeadVisibility.Reportee:
                        query = query.Where(i => (reporteeIds.Contains(i.AssignTo) || reporteeIds.Contains(i.SecondaryUserId ?? Guid.Empty))).Where(i => !i.IsArchived);
                        break;
                    case BaseLeadVisibility.UnassignLead:
                        query = query.Where(i => i.AssignTo == Guid.Empty).Where(i => !i.IsArchived);
                        break;
                    case BaseLeadVisibility.DeletedLeads:
                        query = query.Where(i => i.IsArchived && selfWithReporteeIds.Contains(i.AssignTo));
                        break;
                    default:
                        break;
                }
            }
            else if ((request?.IsDualOwnershipEnabled ?? false) && (request?.AssignToIds?.Any() ?? false) && (request.OwnerSelection == OwnerSelectionType.SecondaryOwner))
            {
                switch (request.LeadVisibility)
                {
                    case BaseLeadVisibility.SelfWithReportee:
                        query = query.Where(i => selfWithReporteeIds.Contains(i.SecondaryUserId.Value)).Where(i => !i.IsArchived);
                        break;
                    case BaseLeadVisibility.Self:
                        query = query.Where(i => i.SecondaryUserId == userId).Where(i => !i.IsArchived);
                        break;
                    case BaseLeadVisibility.Reportee:
                        query = query.Where(i => reporteeIds.Contains(i.SecondaryUserId.Value)).Where(i => !i.IsArchived);
                        break;
                    case BaseLeadVisibility.UnassignLead:
                        query = query.Where(i => i.AssignTo == Guid.Empty).Where(i => !i.IsArchived);
                        break;
                    case BaseLeadVisibility.DeletedLeads:
                        query = query.Where(i => i.IsArchived && selfWithReporteeIds.Contains(i.SecondaryUserId.Value));
                        break;
                    default:
                        break;
                }
            }
            else if ((request?.IsDualOwnershipEnabled ?? false) && (!request?.AssignToIds?.Any() ?? true))
            {
                switch (request.LeadVisibility)
                {
                    case BaseLeadVisibility.SelfWithReportee:
                        query = query.Where(i => (selfWithReporteeIds.Contains(i.AssignTo) || selfWithReporteeIds.Contains(i.SecondaryUserId ?? Guid.Empty))).Where(i => !i.IsArchived);
                        break;
                    case BaseLeadVisibility.Self:
                        query = query.Where(i => (i.AssignTo == userId || i.SecondaryUserId == userId)).Where(i => !i.IsArchived);
                        break;
                    case BaseLeadVisibility.Reportee:
                        query = query.Where(i => (reporteeIds.Contains(i.AssignTo) || reporteeIds.Contains(i.SecondaryUserId ?? Guid.Empty))).Where(i => !i.IsArchived);
                        break;
                    case BaseLeadVisibility.UnassignLead:
                        query = query.Where(i => i.AssignTo == Guid.Empty).Where(i => !i.IsArchived);
                        break;
                    case BaseLeadVisibility.DeletedLeads:
                        query = query.Where(i => i.IsArchived && selfWithReporteeIds.Contains(i.AssignTo));
                        break;
                    default:
                        break;
                }
            }
            else
            {
                switch (request.LeadVisibility)
                {
                    case BaseLeadVisibility.SelfWithReportee:
                        query = query.Where(i => selfWithReporteeIds.Contains(i.AssignTo)).Where(i => !i.IsArchived);
                        break;
                    case BaseLeadVisibility.Self:
                        query = query.Where(i => i.AssignTo == userId).Where(i => !i.IsArchived);
                        break;
                    case BaseLeadVisibility.Reportee:
                        query = query.Where(i => reporteeIds.Contains(i.AssignTo)).Where(i => !i.IsArchived);
                        break;
                    case BaseLeadVisibility.UnassignLead:
                        query = query.Where(i => i.AssignTo == Guid.Empty).Where(i => !i.IsArchived);
                        break;
                    case BaseLeadVisibility.DeletedLeads:
                        query = query.Where(i => i.IsArchived && selfWithReporteeIds.Contains(i.AssignTo));
                        break;
                    default:
                        break;
                }
            }
            if (request.EnquiredFor != null && request.EnquiredFor.Any())
            {
                query = query.Where(i => i.Enquiries != null && i.Enquiries.Any(i => i.EnquiryTypes != null && i.EnquiryTypes.Any(j => request.EnquiredFor.Contains(j))));
            }

            if (request.DateType.HasValue && ((request.FromDate.HasValue && request.FromDate.Value != default) || (request.ToDate.HasValue && request.ToDate.Value != default)))
            {
                DateTime? fromDate = request.FromDate.HasValue ? request.FromDate.Value.ConvertFromDateToUtc() : DateTime.MinValue;
                DateTime? toDate = request.ToDate.HasValue ? request.ToDate.Value.ConvertToDateToUtc() : DateTime.MaxValue;
                switch (request.DateType)
                {
                    case DateType.ReceivedDate:
                        query = query.Where(i => i.CreatedOn >= fromDate.Value && i.CreatedOn <= toDate.Value);
                        break;
                    case DateType.ScheduledDate:
                        query = query.Where(i => i.ScheduledDate != null && i.ScheduledDate.Value >= fromDate.Value && i.ScheduledDate.Value <= toDate.Value);
                        break;
                    case DateType.ModifiedDate:
                        query = query.Where(i => i.LastModifiedOn != null && i.LastModifiedOn.Value >= fromDate.Value && i.LastModifiedOn.Value <= toDate.Value);
                        break;
                    case DateType.DeletedDate:
                        query = query.Where(i => i.ArchivedOn >= fromDate.Value && i.ArchivedOn < toDate.Value);
                        break;
                    case DateType.PickedDate:
                        query = query.Where(i => i.PickedDate >= fromDate.Value && i.PickedDate <= toDate.Value);
                        break;
                    case DateType.BookedDate:
                        query = query.Where(item => item.BookedDetails.Any(detail => detail.BookedDate.Value >= fromDate.Value && detail.BookedDate.Value <= toDate.Value));
                        break;
                    case DateType.AssignedDate:
                        query = query.Where(i => i.Assignments != null && i.Assignments.Any(i => i.AssignmentDate != null && i.AssignmentDate.Value >= fromDate.Value && i.AssignmentDate.Value <= toDate.Value));
                        break;
                    default:
                        break;
                }
            }
            /*  if (!string.IsNullOrWhiteSpace(request.SearchByNameOrNumber))
              {
                  query = query.Where(i => i.ContactNo.Contains(request.SearchByNameOrNumber.Replace(" ", "")) ||
                                           i.Name.ToLower().Contains(request.SearchByNameOrNumber.ToLower()) ||
                                           i.Email.ToLower().Contains(request.SearchByNameOrNumber.ToLower()) ||
                                           i.AlternateContactNo.Contains(request.SearchByNameOrNumber.Replace(" ", "")) ||
                                           i.SerialNumber.Contains(request.SearchByNameOrNumber.Replace(" ", "")));
              }*/
            if (!string.IsNullOrWhiteSpace(request.SearchByNameOrNumber) && request.PropertyToSearch?.Any() == true)
            {
                var searchTerm = request.SearchByNameOrNumber.ToLower().Trim().Replace(" ", "");
                var isPurposeValid = TryParseEnum<Purpose>(searchTerm, out var parsedPurpose);
                bool isSourceValid = TryParseEnum<LeadSource>(searchTerm, out var parsedSource);
                var isEnquiryTypeValid = TryParseEnum<EnquiryType>(searchTerm, out var parsedEnquiryType);
                var statusId = customMasterLeadStatus?.Where(i => i.DisplayName.ToLower().Trim().Replace(" ", "").Contains(searchTerm)).Select(i => i.Id).ToList();
                query = query.Where(i =>
                   (request.PropertyToSearch.Contains("LeadName") && i.Name != null && i.Name.ToLower().Trim().Replace(" ", "").Contains(searchTerm)) ||
                   (request.PropertyToSearch.Contains("ContactNo") && i.ContactNo != null && i.ContactNo.Replace(" ", "").Contains(searchTerm)) ||
                   (request.PropertyToSearch.Contains("SerialNumber") && i.SerialNumber != null && i.SerialNumber.Replace(" ", "").Contains(searchTerm)) ||
                   (request.PropertyToSearch.Contains("AlternateContactNo") && i.AlternateContactNo != null && i.AlternateContactNo.Replace(" ", "").Contains(searchTerm)) ||
                   (request.PropertyToSearch.Contains("Email") && i.Email != null && i.Email.ToLower().Contains(searchTerm)) ||
                   (request.PropertyToSearch.Contains("Nationality") && i.Nationality != null && i.Nationality.ToLower().Trim().Contains(searchTerm)) ||
                   (request.PropertyToSearch.Contains("PropertyName") && i.Properties.Any(a => a.Title.ToLower().Trim().Replace(" ", "").Contains(searchTerm))) ||
                   (request.PropertyToSearch.Contains("ProjectName") && i.Projects.Any(a => a.Name.ToLower().Trim().Replace(" ", "").Contains(searchTerm))) ||
                   (request.PropertyToSearch.Contains("SubSource") && i.Enquiries.Any(a => !string.IsNullOrEmpty(a.SubSource) && a.SubSource.ToLower().Trim().Replace(" ", "").Contains(searchTerm))) ||
                   (request.PropertyToSearch.Contains("Status") && (statusId.Contains(i.CustomLeadStatus.BaseId ?? Guid.Empty) || statusId.Contains(i.CustomLeadStatus.Id))) ||
                    (request.PropertyToSearch.Contains("Purpose") && isPurposeValid && i.Enquiries.Any(e => e.Purpose == parsedPurpose)) ||
                     (request.PropertyToSearch.Contains("Source") && isSourceValid && i.Enquiries.Any(e => e.LeadSource == parsedSource)) ||
                     (request.PropertyToSearch.Contains("EnquiredFor") && isEnquiryTypeValid && i.Enquiries.Any(e => e.EnquiryTypes.Contains(parsedEnquiryType))) ||
                   (request.PropertyToSearch.Contains("Location") && i.Enquiries.Any(e => e.Addresses.Any(j =>
                    (j.SubLocality +
                     j.Locality +
                     j.Community +
                     j.SubCommunity +
                     j.TowerName +
                     j.District +
                     j.City +
                     j.State +
                     j.Country +
                     j.PostalCode).Replace(",", "").ToLower().Trim().Replace(" ", "")
                    .Contains(searchTerm)))));
            }
            else if (!string.IsNullOrWhiteSpace(request.SearchByNameOrNumber))
            {
                query = query.Where(i => i.ContactNo.Contains(request.SearchByNameOrNumber.Replace(" ", "")) ||
                         i.Name.ToLower().Contains(request.SearchByNameOrNumber.ToLower()) ||
                         i.SerialNumber.ToLower().Contains(request.SearchByNameOrNumber.ToLower().Replace(" ", "")));
            }
            if (request.Source?.Any() ?? false)
            {
                query = query.Where(i => i.Enquiries != null && i.Enquiries.Any(i => request.Source.Contains(i.LeadSource)));
            }

            if (request.Properties?.Any() ?? false)
            {
                var propertyNames = request.Properties.Select(i => i.ToLower());
                query = query.Where(i => i.Properties.Count > 0 && i.Properties.Any(i => propertyNames.Contains(i.Title.ToLower())));
            }
            if (request.Projects?.Any() ?? false)
            {
                var projectNames = request.Projects.Select(i => i.ToLower());
                query = query.Where(i => i.Projects.Count > 0 && i.Projects.Any(i => projectNames.Contains(i.Name.ToLower()))).AsQueryable();
            }
            if (request.NoOfBHKs != null && request.NoOfBHKs.Any())
            {
                query = query.Where(i => i.Enquiries != null && i.Enquiries.Any(i => i.BHKs != null && i.BHKs.Any(j => request.NoOfBHKs.Contains(j))));
            }
            if (request.Beds?.Any() ?? false)
            {
                query = query.Where(i => i.Enquiries != null && i.Enquiries.Any(i => i.Beds != null && i.Beds.Any(j => request.Beds.Contains(j))));
            }
            if (request.Baths?.Any() ?? false)
            {
                query = query.Where(i => i.Enquiries != null && i.Enquiries.Any(i => i.Baths != null && i.Baths.Any(j => request.Baths.Contains(j))));
            }
            if (request.Floors?.Any() ?? false)
            {
                query = query.Where(i => i.Enquiries != null && i.Enquiries.Any(i => i.Floors != null && i.Floors.Any(j => request.Floors.Contains(j))));
            }
            if (request.OfferTypes?.Any() ?? false)
            {
                query = query.Where(i => i.Enquiries != null && i.Enquiries.Any(i => i.OfferType != null && request.OfferTypes.Contains(i.OfferType.Value)));
            }
            if (request.Purposes?.Any() ?? false)
            {
                query = query.Where(i => i.Enquiries != null && i.Enquiries.Any(i => i.Purpose != null && request.Purposes.Contains(i.Purpose.Value)));
            }
            if (request.Furnished?.Any() ?? false)
            {
                query = query.Where(i => i.Enquiries != null && i.Enquiries.Any(i => i.Furnished != null && request.Furnished.Contains(i.Furnished.Value)));
            }
            if (request.SubSources != null && request.SubSources.Any())
            {
                request.SubSources = request.SubSources.ConvertAll(i => i.ToLower().Trim());
                query = query.Where(i => i.Enquiries != null && i.Enquiries.Any(i => request.SubSources.Contains(i.SubSource.ToLower().Trim())));
            }
            if (request.BHKTypes != null && request.BHKTypes.Any())
            {
                query = query.Where(i => i.Enquiries != null && i.Enquiries.Any(i => i.BHKTypes != null && i.BHKTypes.Any(j => request.BHKTypes.Contains(j))));
            }
            if (request.PropertyType != null && request.PropertyType.Any())
            {
                query = query.Where(i => i.Enquiries.Any(e => e.PropertyTypes.Any(i => request.PropertyType.Contains(i.BaseId ?? Guid.Empty))));
            }
            if (request.PropertySubType != null && request.PropertySubType.Any())
            {
                query = query.Where(i => i.Enquiries.Any(e => e.PropertyTypes.Any(i => request.PropertySubType.Contains(i.Id))));
            }
            #region Custom Filters
            if (filter != null)
            {
                if (!filter.Statuses?.Any() ?? true)
                {
                    query = query.Where(i => i.CustomLeadStatus != null);
                }
                else if (filter.IsForward != null)
                {
                    TimeSpan userUtcOffset = request.BaseUTcOffset ?? TimeZoneInfo.Local.GetUtcOffset(DateTime.UtcNow);
                    DateTime userLocalNow = DateTime.UtcNow.Add(userUtcOffset);
                    DateTime today = userLocalNow.Date;
                    if (filter.IsForward ?? false)
                    {
                        if (filter.FromNoOfDays == 0 && filter.ToNoOfDays == 0)
                        {
                            query = query.Where(i => ((i.ScheduledDate ?? DateTime.MinValue) + userUtcOffset) >= today && (i.ScheduledDate ?? DateTime.MinValue) < today.AddDays(1));
                        }
                        else if (filter.FromNoOfDays >= 0 && filter.ToNoOfDays > 0)
                        {
                            query = query.Where(i => ((i.ScheduledDate ?? DateTime.MinValue) + userUtcOffset) >= today.AddDays(filter.FromNoOfDays ?? 0) && ((i.ScheduledDate ?? DateTime.MinValue) + userUtcOffset) < today.AddDays(filter.ToNoOfDays ?? 0));
                        }
                        else if (filter.FromNoOfDays >= 0)
                        {
                            query = query.Where(i => ((i.ScheduledDate ?? DateTime.MinValue) + userUtcOffset) >= today.AddDays(filter.FromNoOfDays ?? 0));
                        }
                        else if (filter.ToNoOfDays >= 0)
                        {
                            query = query.Where(i => ((i.ScheduledDate ?? DateTime.MinValue) + userUtcOffset) >= today && ((i.ScheduledDate ?? DateTime.MinValue) + userUtcOffset) < today.AddDays(filter.ToNoOfDays ?? 0));
                        }
                        else
                        {
                            query = query.Where(i => ((i.ScheduledDate ?? DateTime.MinValue) + userUtcOffset) >= today);
                        }
                        var statusId = filter.Statuses?.Select(i => i.Id)?.ToList() ?? new List<Guid>();
                        if (statusId?.Any() ?? false)
                        {
                            query = query.Where(i => i.CustomLeadStatus != null && statusId.Contains(i.CustomLeadStatus.Id));
                        }
                    }
                    else
                    {
                        if (filter.FromNoOfDays >= 0 && filter.ToNoOfDays > 0)
                        {
                            query = query.Where(i => ((i.ScheduledDate ?? DateTime.MinValue) + userUtcOffset) <= today.AddDays((-filter.FromNoOfDays) ?? 0) && ((i.ScheduledDate ?? DateTime.MinValue) + userUtcOffset) > today.AddDays((-filter.ToNoOfDays) ?? 0));
                        }
                        else if (filter.FromNoOfDays >= 0)
                        {
                            query = query.Where(i => ((i.ScheduledDate ?? DateTime.MinValue) + userUtcOffset) <= today.AddDays((-filter.FromNoOfDays) ?? 0));
                        }
                        else if (filter.ToNoOfDays >= 0)
                        {
                            query = query.Where(i => ((i.ScheduledDate ?? DateTime.MinValue) + userUtcOffset) <= today && ((i.ScheduledDate ?? DateTime.MinValue) + userUtcOffset) > today.AddDays((-filter.ToNoOfDays) ?? 0));
                        }
                        else
                        {
                            query = query.Where(i => ((i.ScheduledDate ?? DateTime.MinValue) + userUtcOffset) < today);
                        }
                        var statusId = filter.Statuses?.Select(i => i.Id)?.ToList() ?? new List<Guid>();
                        if (statusId?.Any() ?? false)
                        {
                            query = query.Where(i => i.CustomLeadStatus != null && statusId.Contains(i.CustomLeadStatus.Id));
                        }
                    }
                }
                else if (filter.Statuses?.Any() ?? false)
                {
                    var statusId = filter.Statuses.Select(i => i.Id).ToList();
                    query = query.Where(i => i.CustomLeadStatus != null && statusId.Contains(i.CustomLeadStatus.Id));
                }
                else if (filter.Flags?.Any() ?? false)
                {
                    var flagNames = filter.Flags.Select(i => i.Name).ToList();
                    query = query.Where(i => i.CustomFlags != null && i.CustomFlags.Any(j => j.Flag != null && flagNames.Contains(j.Flag.Name ?? string.Empty)));
                }
            }
            #endregion
            var statusIds = new List<Guid>();
            statusIds.AddRange(request?.StatusIds ?? new List<Guid>());
            statusIds.AddRange(request?.SubStatuses ?? new List<Guid>());
            if (statusIds.Any())
            {
                query = query.Where(i => i.CustomLeadStatus != null && (statusIds.Contains(i.CustomLeadStatus.BaseId ?? Guid.Empty) ||
                statusIds.Contains(i.CustomLeadStatus.Id)));
            }
            if (request.Budget != null && request.Budget.Any())
            {
                foreach (var budget in request.Budget)
                {
                    switch (budget)
                    {
                        case Budget.UpToTenLakhs:
                            query = query.Where(i => i.Enquiries != null && i.Enquiries.Any(j => j.UpperBudget <= 1000000));
                            break;
                        case Budget.TenToTwentyLakhs:
                            query = query.Where(i => i.Enquiries != null && i.Enquiries.Any(j => j.UpperBudget > 1000000 && j.UpperBudget <= 2000000));
                            break;
                        case Budget.TwentyToThirtyLakhs:
                            query = query.Where(i => i.Enquiries != null && i.Enquiries.Any(j => j.UpperBudget > 2000000 && j.UpperBudget <= 3000000));
                            break;
                        case Budget.ThirtyToFourtyLakhs:
                            query = query.Where(i => i.Enquiries != null && i.Enquiries.Any(j => j.UpperBudget > 3000000 && j.UpperBudget <= 4000000));
                            break;
                        case Budget.FourtyToFiftyLakhs:
                            query = query.Where(i => i.Enquiries != null && i.Enquiries.Any(j => j.UpperBudget > 4000000 && j.UpperBudget <= 5000000));
                            break;
                        case Budget.FiftyToOneCrore:
                            query = query.Where(i => i.Enquiries != null && i.Enquiries.Any(j => j.UpperBudget > 5000000 && j.UpperBudget <= 10000000));
                            break;
                        case Budget.MoreThanOneCrore:
                            query = query.Where(i => i.Enquiries != null && i.Enquiries.Any(j => j.UpperBudget > 10000000));
                            break;
                    }
                }
            }
            if (request.Currency != null)
            {
                query = query.Where(i => i.Enquiries.Any(e => e.Currency == request.Currency));
            }
            if (request.MinBudget != null || request.MaxBudget != null)
            {
                if (request.MinBudget != null && request.MaxBudget != null)
                {
                    query = query.Where(i => i.Enquiries.Any(i => (i.UpperBudget >= request.MinBudget && i.UpperBudget <= request.MaxBudget) || (i.LowerBudget >= request.MinBudget && i.LowerBudget <= request.MaxBudget)));
                }
                else if (request.MinBudget != null && request.MaxBudget == null)
                {
                    query = query.Where(i => i.Enquiries.Any(i => (i.UpperBudget >= request.MinBudget) || (i.LowerBudget >= request.MinBudget)));
                }
                else if (request.MinBudget == null && request.MaxBudget != null)
                {
                    query = query.Where(i => i.Enquiries.Any(i => (i.UpperBudget <= request.MaxBudget) || (i.LowerBudget <= request.MaxBudget)));
                }
            }
            if (request.BudgetFilters != null && request.BudgetFilters.Any())
            {
                foreach (var budget in request.BudgetFilters)
                {
                    query = query.Where(i => i.Enquiries != null && i.Enquiries.Any(i => i.UpperBudget >= budget.MinBudget && i.UpperBudget <= budget.MaxBudget));
                }
            }
            if (request.Locations != null && request.Locations.Any())
            {
                request.Locations = request.Locations.ConvertAll(i => Uri.UnescapeDataString(i).Replace(",", "").ToLower().Trim().Replace(" ", "")).ToList();
                query = query.Where(i => i.Enquiries.Any(e => e.Addresses.Any(j =>
                request.Locations.Contains(
                    (j.SubLocality +
                    j.Locality +
                    j.Community +
                    j.SubCommunity +
                    j.TowerName +
                    j.District +
                    j.City +
                    j.State +
                    j.Country +
                    j.PostalCode).Replace(",", "").ToLower().Trim().Replace(" ", "")))));

            }
            if (request.IntegrationAccountIds != null && request.IntegrationAccountIds.Any())
            {
                query = query.Where(i => request.IntegrationAccountIds.Contains(i.AccountId));
            }
            if (request.AgencyNames != null && request.AgencyNames.Any())
            {
                request.AgencyNames = request.AgencyNames.ConvertAll(i => i.ToLower().Trim());
                query = query.Where(i => i.Agencies.Any(i => i.Name != null && request.AgencyNames.Contains(i.Name.ToLower().Trim())));
            }
            if (request.CampaignNames != null && request.CampaignNames.Any())
            {
                request.CampaignNames = request.CampaignNames.ConvertAll(i => i.ToLower().Trim());
                query = query.Where(i => i.Campaigns.Any(i => i.Name != null && request.CampaignNames.Contains(i.Name.ToLower().Trim())));
            }
            if (request?.LeadIds?.Any() ?? false)
            {
                query = query.Where(i => request.LeadIds.Contains(i.Id));
            }
            if (request?.SerialNumbers?.Any() ?? false)
            {
                query = query.Where(i => request.SerialNumbers.Contains(i.SerialNumber));
            }

            if (request?.BookedByIds?.Any() ?? false)
            {
                query = query.Where(i => i.BookedDetails.Any(i => request.BookedByIds.Contains(i.BookedBy.Value)));
            }
            if (request?.CallStatus != null)
            {
                query = query.Where(i => i.LeadCallLogs != null && i.LeadCallLogs.Any(j => j.CallStatus == request.CallStatus));
            }
            if (request?.CallDirection != null)
            {
                query = query.Where(i => i.LeadCallLogs != null && i.LeadCallLogs.Any(j => j.CallDirection == request.CallDirection));
            }
            if ((request.AssignFromIds?.Any() ?? false) && !(request?.IsWithHistory ?? false))
            {
                query = query.Where(i => i.AssignedFrom != null && request.AssignFromIds.Contains(i.AssignedFrom.Value));
            }
            if ((request?.SecondaryUsers?.Any() ?? false) && (request?.IsDualOwnershipEnabled ?? false) && !(request?.IsWithHistory ?? false))
            {
                query = query.Where(i => i.SecondaryUserId != null && request.SecondaryUsers.Contains(i.SecondaryUserId.Value));
            }
            if (request?.OriginalOwnerIds?.Any() ?? false)
            {
                query = query.Where(i => i.OriginalOwner != null && request.OriginalOwnerIds.Contains(i.OriginalOwner.Value));
            }
            if ((request?.SecondaryFromIds?.Any() ?? false) && (request?.IsDualOwnershipEnabled ?? false) && !(request?.IsWithHistory ?? false))
            {
                query = query.Where(i => i.SecondaryFromUserId != null && request.SecondaryFromIds.Contains(i.SecondaryFromUserId.Value));
            }
            if (request?.IsWithHistory ?? false)
            {
                DateTime? fromDate = null;
                DateTime? toDate = null;

                if (request.DateType.HasValue && request.FromDate.HasValue && request.FromDate.Value != default && request.ToDate.HasValue && request.ToDate.Value != default)
                {
                    fromDate = request.FromDate.Value.ConvertFromDateToUtc();
                    toDate = request.ToDate.Value.ConvertToDateToUtc();
                }
                query = query.Where(i => i.Assignments != null && i.Assignments.Any(j =>
                    (request.HistoryAssignedToIds == null || !request.HistoryAssignedToIds.Any() || (j.AssignTo != null && request.HistoryAssignedToIds.Contains(j.AssignTo ?? Guid.Empty))) &&
                    (request.AssignFromIds == null || !request.AssignFromIds.Any() || (j.AssignedFrom != null && request.AssignFromIds.Contains(j.AssignedFrom ?? Guid.Empty))) &&
                    (request.SecondaryUsers == null || !request.SecondaryUsers.Any() || (j.SecondaryAssignTo != null && request.SecondaryUsers.Contains(j.SecondaryAssignTo ?? Guid.Empty))) &&
                    (request.SecondaryFromIds == null || !request.SecondaryFromIds.Any() || (j.SecondaryAssignFrom != null && request.SecondaryFromIds.Contains(j.SecondaryAssignFrom ?? Guid.Empty))) &&
                    (request.DoneBy == null || !request.DoneBy.Any() || request.DoneBy.Contains(j.LastModifiedBy)) &&
                    (fromDate == null || toDate == null ||
                    (j.AssignmentDate.HasValue && j.AssignmentDate.Value >= fromDate.Value && j.AssignmentDate.Value <= toDate.Value))
                ));
            }
            if (request?.DoneBy?.Any() ?? false)
            {
                DateTime? fromDate = null;
                DateTime? toDate = null;

                if (request.DateType.HasValue && request.FromDate.HasValue && request.FromDate.Value != default && request.ToDate.HasValue && request.ToDate.Value != default)
                {
                    fromDate = request.FromDate.Value.ConvertFromDateToUtc();
                    toDate = request.ToDate.Value.ConvertToDateToUtc();
                }

                query = query.Where(i => i.Assignments != null && i.Assignments.Any(j =>
                    request.DoneBy.Contains(j.LastModifiedBy) &&
                    (fromDate == null || toDate == null || (j.AssignmentDate.HasValue && j.AssignmentDate.Value >= fromDate.Value && j.AssignmentDate.Value <= toDate.Value))
                ));
            }
            if (request.Cities?.Any() ?? false)
            {
                var normalizedCityNames = request.Cities.ConvertAll(i => i.ToLower().Trim().Replace(" ", ""));
                query = query.Where(i => i.Enquiries.Any(e => e.Addresses.Where(i => normalizedCityNames.Contains(i.City.ToLower().Trim().Replace(" ", ""))).Any()));
            }

            if (request.States?.Any() ?? false)
            {
                var normalizedStateNames = request.States.ConvertAll(i => i.ToLower().Trim().Replace(" ", ""));
                query = query.Where(i => i.Enquiries.Any(e => e.Addresses.Where(i => normalizedStateNames.Contains(i.State.ToLower().Trim().Replace(" ", ""))).Any()));
            }
            if (request.Countries?.Any() ?? false)
            {
                var normalizedStateNames = request.Countries.ConvertAll(i => i.ToLower().Trim().Replace(" ", ""));
                query = query.Where(i => i.Enquiries.Any(e => e.Addresses.Where(i => normalizedStateNames.Contains(i.Country.ToLower().Trim().Replace(" ", ""))).Any()));
            }
            if (request.Communities?.Any() ?? false)
            {
                var normalizedStateNames = request.Communities.ConvertAll(i => i.ToLower().Trim().Replace(" ", ""));
                query = query.Where(i => i.Enquiries.Any(e => e.Addresses.Where(i => normalizedStateNames.Contains(i.Community.ToLower().Trim().Replace(" ", ""))).Any()));
            }
            if (request.SubCommunities?.Any() ?? false)
            {
                var normalizedStateNames = request.SubCommunities.ConvertAll(i => i.ToLower().Trim().Replace(" ", ""));
                query = query.Where(i => i.Enquiries.Any(e => e.Addresses.Where(i => normalizedStateNames.Contains(i.SubCommunity.ToLower().Trim().Replace(" ", ""))).Any()));
            }
            if (request.TowerNames?.Any() ?? false)
            {
                var normalizedStateNames = request.TowerNames.ConvertAll(i => i.ToLower().Trim().Replace(" ", ""));
                query = query.Where(i => i.Enquiries.Any(e => e.Addresses.Where(i => normalizedStateNames.Contains(i.TowerName.ToLower().Trim().Replace(" ", ""))).Any()));
            }
            if (request.PostalCodes?.Any() ?? false)
            {
                var normalizedStateNames = request.PostalCodes.ConvertAll(i => i.Replace(" ", ""));
                query = query.Where(i => i.Enquiries.Any(e => e.Addresses.Where(i => normalizedStateNames.Contains(i.PostalCode.ToLower().Trim().Replace(" ", ""))).Any()));
            }
            if (!string.IsNullOrEmpty(request?.AdditionalPropertiesKey))
            {
                var key = request.AdditionalPropertiesKey;

                if (!string.IsNullOrEmpty(request.AdditionalPropertiesValue))
                {
                    var value = request.AdditionalPropertiesValue;
                    query = query
                        .Where(lead => EF.Functions.JsonContains(
                            lead.AdditionalProperties,
                            JsonConvert.SerializeObject(new Dictionary<string, string> { { key, value } })));
                }
                else
                {
                    query = query
                        .Where(lead => EF.Functions.JsonContains(
                            lead.AdditionalProperties,
                            JsonConvert.SerializeObject(new Dictionary<string, string> { { key, "" } })));
                }
            }
            if (request.ChannelPartnerNames != null && request.ChannelPartnerNames.Any())
            {
                request.ChannelPartnerNames = request.ChannelPartnerNames.ConvertAll(i => i.ToLower().Trim());
                query = query.Where(i => i.ChannelPartners.Any(i => i.FirmName != null && request.ChannelPartnerNames.Contains(i.FirmName.ToLower().Trim())));
            }
            if (request.LastModifiedByIds?.Any() ?? false)
            {
                query = query.Where(i => request.LastModifiedByIds.Contains(i.LastModifiedBy));
            }
            if (request.CreatedByIds?.Any() ?? false)
            {
                query = query.Where(i => request.CreatedByIds.Contains(i.CreatedBy));
            }

            if (request.RestoredByIds?.Any() ?? false)
            {
                query = query.Where(i => request.RestoredByIds.Contains(i.RestoredBy.Value));
            }
            if (request.ArchivedByIds?.Any() ?? false)
            {
                query = query.Where(i => request.ArchivedByIds.Contains(i.ArchivedBy.Value));
            }
            if (request.SourcingManagers?.Any() ?? false)
            {
                query = query.Where(i => request.SourcingManagers.Contains(i.SourcingManager.Value));
            }
            if (request.ClosingManagers?.Any() ?? false)
            {
                query = query.Where(i => request.ClosingManagers.Contains(i.ClosingManager.Value));
            }
            if (request.Profession != null && request.Profession.Any())
            {
                query = query.Where(i => request.Profession.Contains(i.Profession));
            }
            if (!string.IsNullOrEmpty(request?.UploadTypeName))
            {
                query = query.Where(i => i.UploadTypeName != null && i.UploadTypeName.ToLower().Contains(request.UploadTypeName.ToLower().Trim()));
            }
            if (request?.LeadType != null && request.LeadType.Any())
            {
                var predicate = PredicateBuilder.New<Lead>(false);

                if (request.LeadType.Contains(LeadType.ShowPrimeLeads))
                {
                    predicate = predicate.Or(i => i.ChildLeadsCount == 0 && i.RootId == null && i.ParentLeadId == null);
                }

                if (request.LeadType.Contains(LeadType.ShowOnlyParentLeads))
                {
                    if (request.ChildLeadsCount != null && request.ChildLeadsCount > 0)
                    {
                        predicate = predicate.Or(i => i.ChildLeadsCount == request.ChildLeadsCount);
                    }
                    else
                    {
                        predicate = predicate.Or(i => i.ChildLeadsCount > 0);
                    }
                }

                if (request.LeadType.Contains(LeadType.ShowOnlyDuplicateLeads))
                {
                    predicate = predicate.Or(i => i.ChildLeadsCount == 0 && (i.RootId != null || i.ParentLeadId != null));
                }

                query = query.Where(predicate);
            }
            if (request?.IsUntouched != null && request.IsUntouched != default)
            {
                query = query.Where(i => !i.IsPicked == request.IsUntouched.Value);
            }
            if (request?.PossesionType != null && request?.PossesionType != PossesionType.None)
            {

                switch (request?.PossesionType)
                {
                    case PossesionType.UnderConstruction:
                        query = query.Where(i => i.PossesionType == PossesionType.UnderConstruction);
                        break;

                    case PossesionType.SixMonth:
                        query = query.Where(i => i.PossesionType == PossesionType.SixMonth);
                        break;

                    case PossesionType.Year:
                        query = query.Where(i => i.PossesionType == PossesionType.Year);
                        break;

                    case PossesionType.TwoYears:
                        query = query.Where(i => i.PossesionType == PossesionType.TwoYears);
                        break;

                    case PossesionType.CustomDate:
                        DateTime? tempToPossesionDate = request?.ToPossesionDate?.ConvertToDateToUtc();
                        DateTime? tempFrompossesionDate = request?.FromPossesionDate?.ConvertFromDateToUtc();
                        query = query.Where(i => i.Enquiries.Any(j => j.PossessionDate != null && j.PossessionDate >= tempFrompossesionDate.Value && j.PossessionDate <= tempToPossesionDate.Value));

                        break;
                }
            }
            if (request?.LandLine != null && request.LandLine.Any())
            {
                query = query.Where(i => i.LandLine != null && request.LandLine.Contains(i.LandLine.Trim()));
            }
            if (request?.CountryCode?.Any() ?? false)
            {
                var codes = request.CountryCode
        .Where(c => !string.IsNullOrWhiteSpace(c))
        .Select(c => c.Trim().TrimStart('+'))
        .Distinct()
        .ToList();

                Expression<Func<Lead, bool>> leadFilter = lead => false;

                foreach (var code in codes)
                {
                    var local = code;

                    leadFilter = leadFilter.Or(lead =>
                        (lead.CountryCode != null && lead.CountryCode.Trim().TrimStart('+') == local) ||
                        (lead.ContactNo != null &&
                            (lead.ContactNo.StartsWith("+" + local) || lead.ContactNo.StartsWith(local)))
                    );
                }

                query = query.Where(leadFilter);
            }
            if (request?.AltCountryCode?.Any() ?? false)
            {
                query = query.Where(i => i.AltCountryCode != null && request.AltCountryCode.Contains(i.AltCountryCode));
            }
            if (request?.GenderTypes?.Any() ?? false)
            {
                query = query.Where(i => i.Gender != null && request.GenderTypes.Contains(i.Gender.Value));
            }
            if (request?.MaritalStatuses?.Any() ?? false)
            {
                query = query.Where(i => i.MaritalStatus != null && request.MaritalStatuses.Contains(i.MaritalStatus.Value));
            }
            if (request?.DateOfBirth != null)
            {
                query = query.Where(i => i.DateOfBirth != null && i.DateOfBirth == request.DateOfBirth);
            }

            if (request?.CallDirections != null && request.CallDirections.Any() && !request.CallDirections.Contains(CallDirection.None))
            {
                query = query.Where(i =>
                    i.LeadCallLogs != null && i.LeadCallLogs.Any(j => request.CallDirections.Contains(j.CallDirection)));
            }

            if (request?.CallStatuses != null && request.CallStatuses.Any() && !request.CallStatuses.Contains(CallStatus.None))
            {
                query = query.Where(i =>
                    i.LeadCallLogs != null && i.LeadCallLogs.Any(j => request.CallStatuses.Contains(j.CallStatus)));
            }

            if (request?.UserIds != null && request.UserIds.Any())
            {
                query = query.Where(i =>
                    i.LeadCallLogs != null && i.LeadCallLogs.Any(j => request.UserIds.Contains(j.UserId)));
            }

            return query.AsQueryable();
        }
        public (List<AppointmentType>, List<bool>) GetAppointmentTypes(GetAllLeadsParametersNewFilters request)
        {
            List<AppointmentType> appTypes = new();
            List<bool> appDoneStatuses = new();
            if (request.MeetingOrVisitStatuses == null)
            {
                return (appTypes, appDoneStatuses);
            }
            foreach (var appType in request.MeetingOrVisitStatuses)
            {
                switch (appType)
                {
                    case MeetingOrVisitCompletionStatus.IsMeetingDone:
                        appTypes.Add(AppointmentType.Meeting);
                        appDoneStatuses.Add(true);
                        break;
                    case MeetingOrVisitCompletionStatus.IsMeetingNotDone:
                        appTypes.Add(AppointmentType.Meeting);
                        appDoneStatuses.Add(false);
                        break;
                    case MeetingOrVisitCompletionStatus.IsSiteVisitDone:
                        appTypes.Add(AppointmentType.SiteVisit);
                        appDoneStatuses.Add(true);
                        break;
                    case MeetingOrVisitCompletionStatus.IsSiteVisitNotDone:
                        appTypes.Add(AppointmentType.SiteVisit);
                        appDoneStatuses.Add(false);
                        break;
                }
            }
            appTypes = appTypes.Distinct().ToList();
            appDoneStatuses = appDoneStatuses.Distinct().ToList();
            return (appTypes, appDoneStatuses);
        }


        #endregion
    }
}
