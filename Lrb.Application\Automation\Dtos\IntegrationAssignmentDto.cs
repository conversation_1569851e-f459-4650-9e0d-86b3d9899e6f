﻿using Lrb.Application.TempProject.Dtos;
using Lrb.Application.ZonewiseLocation.Web.Dtos;

namespace Lrb.Application.Automation.Dtos
{
    public class IntegrationAssignmentDto : IDto
    {
        public TempProjectWithUserAssignmentDto? Project { get; set; }
        public LocationWithUserAssignmentDto? Location { get; set; }
        public string? CountryCode { get; set; }
        public PropertyAssignmentDto? Property { get; set; }
        public ChannelPartnerAssignmentDto? ChannelPartner { get; set; }
        public CampaignAssignmentDto? Campaign { get; set; }
        public AgencyAssignmentDto? Agency { get; set; }

    }
}
