﻿using Lrb.Application.Integration.Web;
using Lrb.Application.Integration.Web.Helpers;
using Lrb.Application.Lead.Web.Dtos;

namespace Lrb.Application.Lead.Web.Requests
{
    public class GetAssignedUserDetailsByLeadContactRequest : IRequest<Response<AssignedUserDetailsDto>>
    {
        public string ContactNo { get; set; }
        public string? CountryCode { get; set; }
        public string ApiKey { get; set; }
        public string TenantId { get; set; }
        public GetAssignedUserDetailsByLeadContactRequest(string contactNo,string? countryCode,string tenantId, string apikey)
        {
            ContactNo = contactNo;
            CountryCode = countryCode;
            TenantId = tenantId;
            ApiKey = apikey;
        }
        public class GetAssignedUserDetailsByLeadContactRequestHandller : IRequestHandler<GetAssignedUserDetailsByLeadContactRequest, Response<AssignedUserDetailsDto>>
        {
            private readonly IRepositoryWithEvents<Domain.Entities.IntegrationAccountInfo> _integrationAccRepo;
            private IDapperRepository _dapperRepository;
            public GetAssignedUserDetailsByLeadContactRequestHandller(IDapperRepository dapperRepository, IRepositoryWithEvents<Domain.Entities.IntegrationAccountInfo> integrationAccRepo)
            {
                _integrationAccRepo = integrationAccRepo;
                _dapperRepository = dapperRepository;
            }
            public async Task<Response<AssignedUserDetailsDto>> Handle(GetAssignedUserDetailsByLeadContactRequest request, CancellationToken cancellationToken)
            {
                try
                {
                    var accountId = AccountIdHelper.GetAccountId(request.ApiKey ?? string.Empty);
                    var integrationAccountInfo = await _integrationAccRepo.FirstOrDefaultAsync(new GetIntegrationAccInfoWithAgencySpec(accountId), cancellationToken);
                    if (integrationAccountInfo != null)
                    {
                        var leadContactNo = ListingSitesHelper.ConcatenatePhoneNumber(request.CountryCode, request.ContactNo);
                        var userDetails = await _dapperRepository.GetAssignToDetailsByContactNo(request.TenantId, leadContactNo);
                        return new(userDetails);
                    }
                    return new("Invalid api-key");
                }
                catch (Exception ex)
                {
                    return new();
                }
            }
        }
    }
}