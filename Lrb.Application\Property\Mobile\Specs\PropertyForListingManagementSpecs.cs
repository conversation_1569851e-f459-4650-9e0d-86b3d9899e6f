﻿using Lrb.Application.Property.Web;
using Lrb.Application.Utils;
using System;
using System.Text.RegularExpressions;

namespace Lrb.Application.Property.Mobile.Specs
{
    public class PropertyForListingManagementSpecs : EntitiesByPaginationFilterSpec<Domain.Entities.Property>
    {
        public PropertyForListingManagementSpecs(
            GetAllPropertyForListingManagementRequest filter, 
            Guid? masterPropertyAttributeId, 
            Guid? masterPropertyAmenityId, 
            Guid? masterPropertyTypeId, 
            List<Guid>? propertyDimensionIds, 
            NumericAttributesDto numericAttributesDto, 
            List<Guid>? userIds, 
            bool showAllProperties, List<CustomPropertyAttributeDto>? attributes = null
            ) : base(filter) 
        {
            Query
            .Include(i => i.Address)
            .Include(i => i.MonetaryInfo)
            .Include(i => i.PropertyType)
            .Include(i => i.OwnerDetails)
            .Include(i => i.Dimension)
            .Include(i => i.Galleries.Where(j => !j.IsDeleted & j.IsCoverImage).Take(1))
            .Include(i => i.Project)
            .Include(i => i.PropertyAssignments)
            .Include(i => i.TenantContactInfo)
            .Include(i => i.ListingSources)
            .Include(i => i.ListingSourceAddresses)
            .ThenInclude(i => i.ListingSource)
            .Include(i => i.PropertyOwnerDetails)
            .OrderBy(i => i.Status)
            .ThenByDescending(i => i.LastModifiedOn)
            .Where(i => !i.IsDeleted);

            switch (filter.PropertyVisiblity)
            {
                case PropertyVisiblity.All:
                    Query.Where(i => !i.IsArchived);
                    break;
                case PropertyVisiblity.Draft:
                    Query.Where(i => !i.IsArchived && i.ListingStatus == ListingStatus.Draft);
                    break;
                case PropertyVisiblity.Approved:
                    Query.Where(i => !i.IsArchived && i.ListingStatus == ListingStatus.Approved);
                    break;
                case PropertyVisiblity.Refused:
                    Query.Where(i => !i.IsArchived && i.ListingStatus == ListingStatus.Refused);
                    break;
                case PropertyVisiblity.Sold:
                    Query.Where(i => !i.IsArchived && i.Status == PropertyStatus.Sold);
                    break;
                case PropertyVisiblity.Archived:
                    Query.Where(i => i.IsArchived);
                    break;
            }

            if (!string.IsNullOrEmpty(filter.SerialNo))
            {
                Query.Where(i => i.SerialNo.Contains(filter.SerialNo));
            }

            switch (filter.FirstLevelFilter)
            {
                case FirstLevelFilter.All:
                    break;
                case FirstLevelFilter.Ready:
                    Query.Where(i => i.OfferingType == OfferingType.Ready);
                    switch (filter.SecondLevelFilter)
                    {
                        case SecondLevelFilter.All:
                            break;
                        case SecondLevelFilter.Residential:
                            Query.Where(i => (i.PropertyType.BaseId == Guid.Parse("310c9209-8390-4744-8e75-984ddff24a3c") || i.PropertyType.Id == Guid.Parse("310c9209-8390-4744-8e75-984ddff24a3cs")));
                            break;
                        case SecondLevelFilter.Commercial:
                            Query.Where(i => (i.PropertyType.BaseId == Guid.Parse("c3a1cd50-0ec4-4541-8291-c898c96cd573") || i.PropertyType.Id == Guid.Parse("c3a1cd50-0ec4-4541-8291-c898c96cd573")));
                            break;
                        case SecondLevelFilter.Agricultural:
                            Query.Where(i => (i.PropertyType.BaseId == Guid.Parse("965332f3-0575-4402-b6c1-85caa7cc92c8") || i.PropertyType.Id == Guid.Parse("965332f3-0575-4402-b6c1-85caa7cc92c8")));
                            break;
                    }
                    break;
                case FirstLevelFilter.OffPlan:
                    Query.Where(i => i.OfferingType == OfferingType.OffPlan);
                    switch (filter.SecondLevelFilter)
                    {
                        case SecondLevelFilter.All:
                            break;
                        case SecondLevelFilter.Residential:
                            Query.Where(i => (i.PropertyType.BaseId == Guid.Parse("310c9209-8390-4744-8e75-984ddff24a3c") || i.PropertyType.Id == Guid.Parse("310c9209-8390-4744-8e75-984ddff24a3cs")));
                            break;
                        case SecondLevelFilter.Commercial:
                            Query.Where(i => (i.PropertyType.BaseId == Guid.Parse("c3a1cd50-0ec4-4541-8291-c898c96cd573") || i.PropertyType.Id == Guid.Parse("c3a1cd50-0ec4-4541-8291-c898c96cd573")));
                            break;
                        case SecondLevelFilter.Agricultural:
                            Query.Where(i => (i.PropertyType.BaseId == Guid.Parse("965332f3-0575-4402-b6c1-85caa7cc92c8") || i.PropertyType.Id == Guid.Parse("965332f3-0575-4402-b6c1-85caa7cc92c8")));
                            break;
                    }
                    break;
                case FirstLevelFilter.Secondary:
                    Query.Where(i => i.OfferingType == OfferingType.Secondary);
                    switch (filter.SecondLevelFilter)
                    {
                        case SecondLevelFilter.All:
                            break;
                        case SecondLevelFilter.Residential:
                            Query.Where(i => (i.PropertyType.BaseId == Guid.Parse("310c9209-8390-4744-8e75-984ddff24a3c") || i.PropertyType.Id == Guid.Parse("310c9209-8390-4744-8e75-984ddff24a3cs")));
                            break;
                        case SecondLevelFilter.Commercial:
                            Query.Where(i => (i.PropertyType.BaseId == Guid.Parse("c3a1cd50-0ec4-4541-8291-c898c96cd573") || i.PropertyType.Id == Guid.Parse("c3a1cd50-0ec4-4541-8291-c898c96cd573")));
                            break;
                        case SecondLevelFilter.Agricultural:
                            Query.Where(i => (i.PropertyType.BaseId == Guid.Parse("965332f3-0575-4402-b6c1-85caa7cc92c8") || i.PropertyType.Id == Guid.Parse("965332f3-0575-4402-b6c1-85caa7cc92c8")));
                            break;
                    }
                    break;
            }

            if (filter.ListingSourceIds?.Any() ?? false)
            {
                Query.Where(i => i.ListingSources.Any(j => filter.ListingSourceIds.Contains(j.Id)));
            }

            if ((userIds?.Any() ?? false) && !showAllProperties)
            {
                Query.Where(i => i.PropertyAssignments.Any(i => userIds.Contains((Guid)i.AssignedTo) && i.IsCurrentlyAssigned));
            }
            if (filter.EnquiredFor != null)
            {
                Query.Where(i => i.EnquiredFor == filter.EnquiredFor);
            }
            if (filter.PropertyStatus != null)
            {
                Query.Where(i => i.Status == filter.PropertyStatus);
            }
            if (filter.Locations?.Any() ?? false)
            {

                filter.Locations = filter.Locations.Select(i => i.ToLower()).ToList();
                Query.Where(i => i.Address != null &&
                   (
                       (i.Address.SubLocality != null && i.Address.Locality != null &&
                        filter.Locations.Contains(i.Address.SubLocality.ToLower() + ", " + i.Address.Locality.ToLower())) ||
                       (i.Address.SubLocality == null && i.Address.Locality != null &&
                        filter.Locations.Contains(", " + i.Address.Locality.ToLower())) ||
                       (i.Address.SubLocality != null && i.Address.Locality == null &&
                        filter.Locations.Contains(i.Address.SubLocality.ToLower() + ", "))
                   )
               );

            }
            if (filter.Cities?.Any() ?? false)
            {
                Query.Where(i => i.Address != null && i.Address.City != null && filter.Cities.Select(i => i.ToLower()).Contains(i.Address.City.ToLower()));
            }
            if (filter.Countries?.Any() ?? false)
            {
                Query.Where(i => i.Address != null && i.Address.Country != null && filter.Countries.Select(i => i.ToLower()).Contains(i.Address.Country.ToLower()));
            }
            if (filter.Communities?.Any() ?? false)
            {
                Query.Where(i => i.Address != null && i.Address.Community != null && filter.Communities.Select(i => i.ToLower()).Contains(i.Address.Community.ToLower()));
            }

            if (filter.SubCommunities?.Any() ?? false)
            {
                Query.Where(i => i.Address != null && i.Address.SubCommunity != null && filter.SubCommunities.Select(i => i.ToLower()).Contains(i.Address.SubCommunity.ToLower()));
            }

            if(filter.ListingLevel != null)
            {
                Query.Where(i => i.ListingLevel == filter.ListingLevel);
            }

            if (filter.CompletionStatus != null)
            {
                Query.Where(i => i.CompletionStatus == filter.CompletionStatus);
            }

            if (filter.States?.Any() ?? false)
            {
                Query.Where(i => i.Address != null && i.Address.State != null && filter.States.Select(i => i.ToLower()).Contains(i.Address.State.ToLower()));
            }
            if (filter?.BHKTypes?.Any() ?? false)
            {
                Query.Where(i => filter.BHKTypes.Contains(i.BHKType));
            }
            if (filter?.FurnishStatuses?.Any() ?? false)
            {
                Query.Where(i => filter.FurnishStatuses.Contains(i.FurnishStatus));
            }

            if (filter.Facing != null)
            {
                Query.Where(i => i.Facing == filter.Facing);

            }
            if (!string.IsNullOrEmpty(filter.PropertyTitle))
            {
                string propertyTitle = filter.PropertyTitle.ToLower().Replace(" ", "");
                Query.Where(i => i.Title.Replace(" ", "").ToLower() == propertyTitle);
            }
            if (!string.IsNullOrEmpty(filter.OwnerName))
            {
                string ownerName = filter.OwnerName.ToLower().Replace(" ", "");
                Query.Where(i => i.OwnerDetails.Name.Replace(" ", "").ToLower() == ownerName);
            }
            if (filter?.OwnerNames?.Any() ?? false)
            {
                filter.OwnerNames = filter.OwnerNames.ConvertAll(i => i.ToLower().Trim().Replace(" ", ""));
                Query.Where(e => e.PropertyOwnerDetails.Any(i => filter.OwnerNames.Contains(i.Name.ToLower().Trim().Replace(" ", "") ?? string.Empty)));
            }
            if (filter?.Projects?.Any() ?? false)
            {
                var projectNames = filter.Projects.ConvertAll(i => i.ToLower());
                Query.Where(i => i.Project != null && projectNames.Contains(i.Project.Name.ToLower()));
            }
            if (filter.NoOfBHK != default)
            {
                Query.Where(i => i.NoOfBHKs == filter.NoOfBHK);
            }
            if (filter?.BHKs?.Any() ?? false)
            {
                Query.Where(i => filter.BHKs.Contains(i.NoOfBHKs));
            }
            if (filter.Ratings != default)
            {
                Query.Where(i => i.Rating == filter.Ratings);
            }
            if (filter.FloorNumber != default)
            {
                Query.Where(i => i.Attributes != null && i.Attributes.Any(i => i.MasterPropertyAttributeId == Guid.Parse("c146a273-e342-4d66-aeee-6b2e6106d225") && i.Value == filter.FloorNumber.ToString()));

            }
            if (filter.BasePropertyTypeId != default)
            {
                Query.Where(i => i.PropertyType != null && i.PropertyType.BaseId == filter.BasePropertyTypeId);
            }
            if (filter.PropertyTypes?.Any() ?? false)
            {
                Query.Where(i => i.PropertyType != null && i.PropertyType.BaseId != null && filter.PropertyTypes.Contains(i.PropertyType.BaseId ?? default));
            }
            if (filter.PropertySubTypes?.Any() ?? false)
            {
                Query.Where(i => i.PropertyType != null && filter.PropertySubTypes.Contains(i.PropertyType.Id));
            }
            if (filter.SubPropertyTypeIds?.Any() ?? false)
            {
                Query.Where(i => i.PropertyType != null && filter.SubPropertyTypeIds.Contains(i.PropertyType.Id));
            }
            if (filter.MinBudget != null && filter.MaxBudget != null)
            {
                Query.Where(i => i.MonetaryInfo != null && i.MonetaryInfo.ExpectedPrice >= filter.MinBudget && i.MonetaryInfo.ExpectedPrice <= filter.MaxBudget);
            }
            else if (filter.MinBudget != null && filter.MaxBudget == null)
            {
                Query.Where(i => i.MonetaryInfo != null && i.MonetaryInfo.ExpectedPrice >= filter.MinBudget);
            }
            else if (filter.MinBudget == null && filter.MaxBudget != null)
            {
                Query.Where(i => i.MonetaryInfo != null && i.MonetaryInfo.ExpectedPrice <= filter.MaxBudget);
            }

            if (filter.DateType.HasValue && (filter.FromDate != default || filter.ToDate != default))
            {
                DateTime? fromDate = filter.FromDate.HasValue ? filter.FromDate.Value.ConvertFromDateToUtc() : null;
                DateTime? toDate = filter.ToDate.HasValue ? filter.ToDate.Value.ConvertToDateToUtc() : null;

                switch (filter.DateType)
                {
                    case PropertyDateType.CreatedDate:
                        if (fromDate == null && toDate != null)
                        {
                            Query.Where(i => i.CreatedOn <= toDate);
                        }
                        else if (fromDate != null && toDate == null)
                        {
                            Query.Where(i => i.CreatedOn >= fromDate);
                        }
                        else if (fromDate != null && toDate != null)
                        {
                            Query.Where(i => i.CreatedOn >= fromDate && i.CreatedOn <= toDate);
                        }
                        break;
                    case PropertyDateType.ModifiedDate:
                        if (fromDate == null && toDate != null)
                        {
                            Query.Where(i => i.LastModifiedOn != null && i.LastModifiedOn.Value <= toDate);
                        }
                        else if (fromDate != null && toDate == null)
                        {
                            Query.Where(i => i.LastModifiedOn != null && i.LastModifiedOn.Value >= fromDate);
                        }
                        else if (fromDate != null && toDate != null)
                        {
                            Query.Where(i => i.LastModifiedOn != null && i.LastModifiedOn.Value >= fromDate && i.LastModifiedOn.Value <= toDate);
                        }
                        break;
                    case PropertyDateType.PossessionDate:
                        if (fromDate == null && toDate != null)
                        {
                            Query.Where(i => i.PossessionDate != null && i.PossessionDate.Value <= toDate);
                        }
                        else if (fromDate != null && toDate == null)
                        {
                            Query.Where(i => i.PossessionDate != null && i.PossessionDate.Value >= fromDate);
                        }
                        else if (filter.FromDate != null && filter.ToDate != null)
                        {
                            Query.Where(i => i.PossessionDate != null && i.PossessionDate.Value >= fromDate && i.PossessionDate.Value < toDate);
                        }
                        break;
                    case PropertyDateType.None:
                        if (filter.FromDate == null && filter.ToDate != null)
                        {
                            Query.Where(i => i.CreatedOn <= filter.ToDate.Value ||
                                             (i.LastModifiedOn.HasValue && i.LastModifiedOn.Value <= filter.ToDate.Value) ||
                                             (i.PossessionDate.HasValue && i.PossessionDate.Value <= filter.ToDate.Value));
                        }
                        else if (filter.FromDate != null && filter.ToDate == null)
                        {
                            Query.Where(i => i.CreatedOn >= filter.FromDate.Value ||
                                             (i.LastModifiedOn.HasValue && i.LastModifiedOn.Value >= filter.FromDate.Value) ||
                                             (i.PossessionDate.HasValue && i.PossessionDate.Value >= filter.FromDate.Value));
                        }
                        else if (filter.FromDate != null && filter.ToDate != null)
                        {
                            Query.Where(i => (i.CreatedOn >= filter.FromDate.Value && i.CreatedOn <= filter.ToDate.Value) ||
                                             (i.LastModifiedOn.HasValue && i.LastModifiedOn.Value >= filter.FromDate.Value && i.LastModifiedOn.Value <= filter.ToDate.Value) ||
                                             (i.PossessionDate.HasValue && i.PossessionDate.Value >= filter.FromDate.Value && i.PossessionDate.Value <= filter.ToDate.Value));
                        }
                        break;
                    default:
                        break;
                }
            }

            if (filter.FromPossessionDate != default || filter.ToPossessionDate != default)
            {
                DateTime? fromDate = filter.FromPossessionDate.HasValue ? filter.FromPossessionDate.Value.ConvertFromDateToUtc() : null;
                DateTime? toDate = filter.ToPossessionDate.HasValue ? filter.ToPossessionDate.Value.ConvertToDateToUtc() : null;
                if (fromDate == null && toDate != null)
                {
                    Query.Where(i => i.PossessionDate != null && i.PossessionDate.Value <= toDate);
                }
                else if (fromDate != null && toDate == null)
                {
                    Query.Where(i => i.PossessionDate != null && i.PossessionDate.Value >= fromDate);
                }
                else if (fromDate != null && toDate != null)
                {
                    Query.Where(i => i.PossessionDate != null && i.PossessionDate.Value >= fromDate && i.PossessionDate.Value < toDate);

                }
            }

            if (filter.MinPrice != null && filter.MaxPrice != null)
            {
                Query.Where(i => i.MonetaryInfo != null && i.MonetaryInfo.ExpectedPrice >= filter.MinPrice && i.MonetaryInfo.ExpectedPrice <= filter.MaxPrice);
            }
            else if (filter.MinPrice != null && filter.MaxPrice == null)
            {
                Query.Where(i => i.MonetaryInfo != null && i.MonetaryInfo.ExpectedPrice >= filter.MinPrice);
            }
            else if (filter.MinPrice == null && filter.MaxPrice != null)
            {
                Query.Where(i => i.MonetaryInfo != null && i.MonetaryInfo.ExpectedPrice <= filter.MaxPrice);
            }
            if (filter.Amenities != null && filter.Amenities.Any())
            {
                Query.Where(i => i.Amenities.Any(a => filter.Amenities.Contains(a.MasterPropertyAmenityId)));
            }
            if (filter.PropertySize != null && filter.PropertySize != default)
            {
                if (filter.PropertySize.Area != default && filter.PropertySize.ConversionFactor != default)
                {
                    Query.Where(i => propertyDimensionIds.Any() && propertyDimensionIds.Contains(i.Dimension.Id));
                }
                else if (filter.PropertySize.Area != default && filter.PropertySize.ConversionFactor == default)
                {
                    Query.Where(i => i.Dimension.Area == filter.PropertySize.Area || i.Dimension.AreaInSqMtr == filter.PropertySize.Area);
                }
            }

            if (!string.IsNullOrWhiteSpace(filter.PropertySearch))
            {
                //var minBudget = (Int64.TryParse(Regex.Matches(filter.PropertySearch, @"\d+")?.FirstOrDefault()?.Value ?? string.Empty, out long min) ? (long?)min : null);
                //if (minBudget != null) { minBudget = Convert.ToInt64(minBudget * 0.8); }
                //var maxBudget = (Int64.TryParse(Regex.Matches(filter.PropertySearch, @"\d+")?.FirstOrDefault()?.Value ?? string.Empty, out long max) ? (long?)max : null);
                //if (maxBudget != null) { maxBudget = Convert.ToInt64(maxBudget * 1.2); }
                //var noOfBHK = double.TryParse(Regex.Matches(filter.PropertySearch, @"\d+")?.FirstOrDefault()?.Value ?? string.Empty, out double nB) ? (double?)nB : null;
                //var saleType = (Enum.TryParse<SaleType>((Enum.GetNames<SaleType>()).Where(i => filter.PropertySearch.Contains(i.ToLower())).ToList().FirstOrDefault(), true, out SaleType sT)) ? (SaleType?)sT : null;
                //var enquiryType = (Enum.TryParse<EnquiryType>((Enum.GetNames<EnquiryType>()).Where(i => filter.PropertySearch.Contains(i.ToLower())).ToList().FirstOrDefault(), true, out EnquiryType eT)) ? (EnquiryType?)eT : null;
                //var furnishStatus = (Enum.TryParse<FurnishStatus>((Enum.GetNames<FurnishStatus>()).Where(i => filter.PropertySearch.Contains(i.ToLower())).ToList().FirstOrDefault(), true, out FurnishStatus fS)) ? (FurnishStatus?)fS : null;
                //var propertyStatus = (Enum.TryParse<PropertyStatus>((Enum.GetNames<PropertyStatus>()).Where(i => filter.PropertySearch.Contains(i.ToLower())).ToList().FirstOrDefault(), true, out PropertyStatus pS)) ? (PropertyStatus?)pS : null;
                //var bHKType = Enum.TryParse<BHKType>((Enum.GetNames<BHKType>()).Where(i => filter.PropertySearch.Contains(i.ToLower())).ToList().FirstOrDefault(), true, out BHKType bK) ? (BHKType?)bK : null;
                //var facing = Enum.TryParse<Facing>((Enum.GetNames<Facing>()).Where(i => filter.PropertySearch.Contains(i.ToLower())).ToList().FirstOrDefault(), true, out Facing fc) ? (Facing?)fc : null;

                Query.Where(
                i => (i.Title + " " +
                //i.Rating + " " +
                //i.Address.SubLocality + " " +
                //i.Address.Locality + " " +
                //i.Address.District + " " +
                //i.Address.City + " " +
                //i.Address.State + " " +
                //i.Address.Country + " " +
                //i.Address.PostalCode + " " +
                //i.PropertyType.Type + " " +
                //i.PropertyType.DisplayName + " " +
                i.OwnerDetails.Name + " " +
                i.OwnerDetails.Phone + " " +
                i.OwnerDetails.Email
                //i.AboutProperty + " " +
                //i.SerialNo + " "
                ).ToLower().Contains(filter.PropertySearch.ToLower()));
                //(i.SaleType == saleType && saleType != null) ||
                //(i.EnquiredFor == enquiryType && enquiryType != null) ||
                //(i.FurnishStatus == furnishStatus && furnishStatus != null) ||
                //(i.Status == propertyStatus && propertyStatus != null) ||
                //(i.BHKType == bHKType && bHKType != null) ||
                //(i.Facing == facing && facing != null) ||
                //(i.Attributes.Any(i => i.MasterPropertyAttributeId == masterPropertyAttributeId) && masterPropertyAttributeId != null) ||
                //(i.Amenities.Any(i => i.MasterPropertyAmenityId == masterPropertyAmenityId) && masterPropertyAmenityId != null) ||
                ////((i.MonetaryInfo.ExpectedPrice >= minBudget && i.MonetaryInfo.ExpectedPrice <= maxBudget) && minBudget != null && maxBudget != null) ||
                //((i.PropertyType.BaseId == masterPropertyTypeId || i.PropertyType.Id == masterPropertyTypeId) && masterPropertyTypeId != null));
            }
            if (filter.NoOfFloors?.Any() ?? false && numericAttributesDto?.NoOfFloors?.NoOfAttributes != null && attributes != null)
            {
                var attribute = attributes?.FirstOrDefault(i => i.AttributeDisplayName == "Total Floors");
                if (attribute != null)
                {
                    if (numericAttributesDto.NoOfFloors.IsMaxValueIncluded)
                    {
                        Query.Where(i => i.Attributes.Any(i => i.MasterPropertyAttributeId == attribute.Id &&
                     !numericAttributesDto.NoOfFloors.NoOfAttributes.Contains(i.Value) && !string.IsNullOrEmpty(i.Value) && i.Value != "0" && i.Value != "Ground Floor"));
                    }
                    else
                    {
                        Query.Where(i => i.Attributes.Any(i => i.MasterPropertyAttributeId == attribute.Id &&
                           numericAttributesDto.NoOfFloors.NoOfAttributes.Contains(i.Value) && !string.IsNullOrEmpty(i.Value) && i.Value != "0"));
                    }
                }
            }
            if (filter.NoOfBedrooms?.Any() ?? false && numericAttributesDto?.NoOfBedrooms?.NoOfAttributes != null && attributes != null)
            {
                var attribute = attributes?.FirstOrDefault(i => i.AttributeDisplayName == "No. of Bed Rooms");
                if (attribute != null)
                {
                    if (numericAttributesDto.NoOfBedrooms.IsMaxValueIncluded)
                    {
                        Query.Where(i => i.Attributes.Any(i => i.MasterPropertyAttributeId == attribute.Id &&
                     !numericAttributesDto.NoOfBedrooms.NoOfAttributes.Contains(i.Value) && !string.IsNullOrEmpty(i.Value) && i.Value != "0"));
                    }
                    else
                    {
                        Query.Where(i => i.Attributes.Any(i => i.MasterPropertyAttributeId == attribute.Id &&
                           numericAttributesDto.NoOfBedrooms.NoOfAttributes.Contains(i.Value) && !string.IsNullOrEmpty(i.Value) && i.Value != "0"));
                    }
                }
            }
            if (filter.NoOfKitchens?.Any() ?? false && numericAttributesDto?.NoOfKitchens?.NoOfAttributes != null && attributes != null)
            {
                var attribute = attributes?.FirstOrDefault(i => i.AttributeDisplayName == "No. of Kitchens");
                if (attribute != null)
                {
                    if (numericAttributesDto.NoOfKitchens.IsMaxValueIncluded)
                    {
                        Query.Where(i => i.Attributes.Any(i => i.MasterPropertyAttributeId == attribute.Id &&
                     !numericAttributesDto.NoOfKitchens.NoOfAttributes.Contains(i.Value) && !string.IsNullOrEmpty(i.Value) && i.Value != "0"));
                    }
                    else
                    {
                        Query.Where(i => i.Attributes.Any(i => i.MasterPropertyAttributeId == attribute.Id &&
                            numericAttributesDto.NoOfKitchens.NoOfAttributes.Contains(i.Value) && !string.IsNullOrEmpty(i.Value) && i.Value != "0"));
                    }
                }
            }

            if (filter.NoOfUtilites?.Any() ?? false && numericAttributesDto?.NoOfUtilites?.NoOfAttributes != null && attributes != null)
            {
                var attribute = attributes?.FirstOrDefault(i => i.AttributeDisplayName == "No. of Utilities");
                if (attribute != null)
                {
                    if (numericAttributesDto.NoOfUtilites.IsMaxValueIncluded)
                    {
                        Query.Where(i => i.Attributes.Any(i => i.MasterPropertyAttributeId == attribute.Id &&
                     !numericAttributesDto.NoOfUtilites.NoOfAttributes.Contains(i.Value) && !string.IsNullOrEmpty(i.Value) && i.Value != "0"));
                    }
                    else
                    {
                        Query.Where(i => i.Attributes.Any(i => i.MasterPropertyAttributeId == attribute.Id &&
                           numericAttributesDto.NoOfUtilites.NoOfAttributes.Contains(i.Value) && !string.IsNullOrEmpty(i.Value) && i.Value != "0"));
                    }
                }
            }
            if (filter.NoOfLivingrooms?.Any() ?? false && numericAttributesDto?.NoOfLivingrooms?.NoOfAttributes != null && attributes != null)
            {
                var attribute = attributes?.FirstOrDefault(i => i.AttributeDisplayName == "No. of Drawing or Living Rooms");
                if (attribute != null)
                {
                    if (numericAttributesDto.NoOfLivingrooms.IsMaxValueIncluded)
                    {
                        Query.Where(i => i.Attributes.Any(i => i.MasterPropertyAttributeId == attribute.Id &&
                     !numericAttributesDto.NoOfLivingrooms.NoOfAttributes.Contains(i.Value) && !string.IsNullOrEmpty(i.Value) && i.Value != "0"));
                    }
                    else
                    {
                        Query.Where(i => i.Attributes.Any(i => i.MasterPropertyAttributeId == attribute.Id &&
                           numericAttributesDto.NoOfLivingrooms.NoOfAttributes.Contains(i.Value) && !string.IsNullOrEmpty(i.Value) && i.Value != "0"));
                    }
                }

            }
            if (filter.NoOfBalconies?.Any() ?? false && numericAttributesDto?.NoOfBalconies?.NoOfAttributes != null && attributes != null)
            {
                var attribute = attributes?.FirstOrDefault(i => i.AttributeDisplayName == "No. of Balconies");
                if (attribute != null)
                {
                    if (numericAttributesDto.NoOfBalconies.IsMaxValueIncluded)
                    {
                        Query.Where(i => i.Attributes.Any(i => i.MasterPropertyAttributeId == attribute.Id &&
                     !numericAttributesDto.NoOfBalconies.NoOfAttributes.Contains(i.Value) && !string.IsNullOrEmpty(i.Value) && i.Value != "0"));
                    }
                    else
                    {
                        Query.Where(i => i.Attributes.Any(i => i.MasterPropertyAttributeId == attribute.Id &&
                           numericAttributesDto.NoOfBalconies.NoOfAttributes.Contains(i.Value) && !string.IsNullOrEmpty(i.Value) && i.Value != "0"));
                    }
                }
            }
            if (filter.NoOfBathrooms?.Any() ?? false && numericAttributesDto?.NoOfBathrooms?.NoOfAttributes != null && attributes != null)
            {
                var attribute = attributes?.FirstOrDefault(i => i.AttributeDisplayName == "No. of Bath Rooms");
                if (attribute != null)
                {
                    if (numericAttributesDto.NoOfBathrooms.IsMaxValueIncluded)
                    {
                        Query.Where(i => i.Attributes.Any(i => i.MasterPropertyAttributeId == attribute.Id &&
                     !numericAttributesDto.NoOfBathrooms.NoOfAttributes.Contains(i.Value) && !string.IsNullOrEmpty(i.Value) && i.Value != "0" && i.Value != "Ground Floor"));
                    }
                    else
                    {
                        Query.Where(i => i.Attributes.Any(i => i.MasterPropertyAttributeId == attribute.Id &&
                           numericAttributesDto.NoOfBathrooms.NoOfAttributes.Contains(i.Value) && !string.IsNullOrEmpty(i.Value) && i.Value != "0"));
                    }
                }
            }
            if (filter.Parking?.Any() ?? false && numericAttributesDto?.Parking?.NoOfAttributes != null && attributes != null)
            {
                var attribute = attributes?.FirstOrDefault(i => i.AttributeDisplayName == "Parking");
                if (attribute != null)
                {
                    if (numericAttributesDto.Parking.IsMaxValueIncluded)
                    {
                        Query.Where(i => i.Attributes.Any(i => i.MasterPropertyAttributeId == attribute.Id &&
                     !numericAttributesDto.Parking.NoOfAttributes.Contains(i.Value) && !string.IsNullOrEmpty(i.Value) && i.Value != "0"));
                    }
                    else
                    {
                        Query.Where(i => i.Attributes.Any(i => i.MasterPropertyAttributeId == attribute.Id &&
                           numericAttributesDto.Parking.NoOfAttributes.Contains(i.Value) && !string.IsNullOrEmpty(i.Value) && i.Value != "0"));
                    }
                }
            }
            if (filter.Currency != null)
            {
                Query.Where(i => i.MonetaryInfo != null && i.MonetaryInfo.Currency == filter.Currency);
            }

            #region Removed
            if (filter.PropertySize != null && filter.PropertySize != default)
            {
                if (filter.PropertySize.CarpetArea != default && filter.PropertySize.ConversionFactor != default)
                {
                    Query.Where(i => propertyDimensionIds.Any() && propertyDimensionIds.Contains(i.Dimension.Id));
                }
                else if (filter.PropertySize.CarpetArea == 999)
                {
                    Query.Where(i => i.Dimension.CarpetArea <= filter.PropertySize.CarpetArea || i.Dimension.AreaInSqMtr <= filter.PropertySize.CarpetArea);

                }
                else if (filter.PropertySize.CarpetArea != default && filter.PropertySize.ConversionFactor == default)
                {
                    Query.Where(i => i.Dimension.CarpetArea >= filter.PropertySize.CarpetArea || i.Dimension.AreaInSqMtr >= filter.PropertySize.CarpetArea);
                }
            }
            if (filter.PropertySize != null && filter.PropertySize != default)
            {
                if (filter.PropertySize.NetArea != default && filter.PropertySize.ConversionFactor != default)
                {
                    Query.Where(i => propertyDimensionIds.Any() && propertyDimensionIds.Contains(i.Dimension.Id));
                }
                else if (filter.PropertySize.NetArea == 999)
                {
                    Query.Where(i => i.Dimension.NetArea <= filter.PropertySize.NetArea || i.Dimension.AreaInSqMtr <= filter.PropertySize.NetArea);

                }
                else if (filter.PropertySize.NetArea != default && filter.PropertySize.ConversionFactor == default)
                {
                    Query.Where(i => i.Dimension.NetArea >= filter.PropertySize.NetArea || i.Dimension.AreaInSqMtr >= filter.PropertySize.NetArea);
                }
            }
            if (filter.PropertySize != null && filter.PropertySize != default)
            {
                if (filter.PropertySize.BuildUpArea != default && filter.PropertySize.ConversionFactor != default)
                {
                    Query.Where(i => propertyDimensionIds.Any() && propertyDimensionIds.Contains(i.Dimension.Id));
                }

                else if (filter.PropertySize.BuildUpArea != default && filter.PropertySize.ConversionFactor == default)
                {
                    Query.Where(i => i.Dimension.BuildUpArea == filter.PropertySize.BuildUpArea || i.Dimension.AreaInSqMtr == filter.PropertySize.BuildUpArea);
                }
            }
            if (filter.PropertySize != null && filter.PropertySize != default)
            {

                if (filter.PropertySize.SaleableArea != default && filter.PropertySize.ConversionFactor != default)
                {
                    Query.Where(i => propertyDimensionIds.Any() && propertyDimensionIds.Contains(i.Dimension.Id));
                }

                else if (filter.PropertySize.SaleableArea != default && filter.PropertySize.ConversionFactor == default)
                {
                    Query.Where(i => i.Dimension.SaleableArea == filter.PropertySize.SaleableArea || i.Dimension.AreaInSqMtr == filter.PropertySize.SaleableArea);
                }
            }
            #endregion

            #region Property Area
            if (filter.MinPropertySize != null && filter.MaxPropertySize != null)
            {
                Query.Where(i => i.Dimension.Area >= filter.MinPropertySize && i.Dimension.Area <= filter.MaxPropertySize);
                if (filter?.PropertySizeUnit != null)
                {
                    Query.Where(i => i.Dimension.AreaUnitId == filter.PropertySizeUnit);
                }
            }
            else if (filter.MaxPropertySize != null)
            {
                Query.Where(i => i.Dimension.Area <= filter.MaxPropertySize);
                if (filter?.PropertySizeUnit != null)
                {
                    Query.Where(i => i.Dimension.AreaUnitId == filter.PropertySizeUnit);
                }
            }
            else if (filter.MinPropertySize != null)
            {
                Query.Where(i => i.Dimension.Area >= filter.MinPropertySize);
                if (filter?.PropertySizeUnit != null)
                {
                    Query.Where(i => i.Dimension.AreaUnitId == filter.PropertySizeUnit);
                }
            }
            #endregion

            #region Carpet Area
            if (filter.MinCarpetArea != null && filter.MaxCarpetArea != null)
            {
                Query.Where(i => i.Dimension.CarpetArea >= filter.MinCarpetArea && i.Dimension.CarpetArea <= filter.MaxCarpetArea);
                if (filter?.CarpetAreaUnit != null)
                {
                    Query.Where(i => i.Dimension.CarpetAreaId == filter.CarpetAreaUnit);
                }
            }
            else if (filter.MaxCarpetArea != null)
            {
                Query.Where(i => i.Dimension.CarpetArea <= filter.MaxCarpetArea);
                if (filter?.CarpetAreaUnit != null)
                {
                    Query.Where(i => i.Dimension.CarpetAreaId == filter.CarpetAreaUnit);
                }
            }
            else if (filter.MinCarpetArea != null)
            {
                Query.Where(i => i.Dimension.CarpetArea >= filter.MinCarpetArea);
                if (filter?.CarpetAreaUnit != null)
                {
                    Query.Where(i => i.Dimension.CarpetAreaId == filter.CarpetAreaUnit);
                }
            }
            #endregion

            #region BuildUp Area
            if (filter.MinBuitUpArea != null && filter.MaxBuitUpArea != null)
            {
                Query.Where(i => i.Dimension.BuildUpArea >= filter.MinBuitUpArea && i.Dimension.BuildUpArea <= filter.MaxBuitUpArea);
                if (filter?.BuitUpAreaUnit != null)
                {
                    Query.Where(i => i.Dimension.BuildUpAreaId == filter.BuitUpAreaUnit);
                }
            }
            else if (filter.MaxBuitUpArea != null)
            {
                Query.Where(i => i.Dimension.BuildUpArea <= filter.MaxPropertySize);
                if (filter?.BuitUpAreaUnit != null)
                {
                    Query.Where(i => i.Dimension.BuildUpAreaId == filter.BuitUpAreaUnit);
                }
            }
            else if (filter.MinBuitUpArea != null)
            {
                Query.Where(i => i.Dimension.BuildUpArea >= filter.MinBuitUpArea);
                if (filter?.BuitUpAreaUnit != null)
                {
                    Query.Where(i => i.Dimension.BuildUpAreaId == filter.BuitUpAreaUnit);
                }
            }
            #endregion

            #region Saleable Area
            if (filter.MinSaleableArea != null && filter.MaxSaleableArea != null)
            {
                Query.Where(i => i.Dimension.SaleableArea >= filter.MinSaleableArea && i.Dimension.SaleableArea <= filter.MaxSaleableArea);

                if (filter?.SaleableAreaUnit != null)
                {
                    Query.Where(i => i.Dimension.SaleableAreaId == filter.SaleableAreaUnit);
                }
            }
            else if (filter.MaxSaleableArea != null)
            {
                Query.Where(i => i.Dimension.SaleableArea <= filter.MaxSaleableArea);

                if (filter?.SaleableAreaUnit != null)
                {
                    Query.Where(i => i.Dimension.SaleableAreaId == filter.SaleableAreaUnit);
                }
            }
            else if (filter.MinSaleableArea != null)
            {
                Query.Where(i => i.Dimension.SaleableArea >= filter.MinSaleableArea);

                if (filter?.SaleableAreaUnit != null)
                {
                    Query.Where(i => i.Dimension.SaleableAreaId == filter.SaleableAreaUnit);
                }
            }
            if (filter.MinNetArea != null && filter.MaxNetArea != null)
            {
                Query.Where(i => i.Dimension.NetArea >= filter.MinNetArea && i.Dimension.NetArea <= filter.MaxNetArea);

                if (filter?.NetAreaUnit != null)
                {
                    Query.Where(i => i.Dimension.NetAreaUnitId == filter.NetAreaUnit);
                }
            }
            else if (filter.MaxNetArea != null)
            {
                Query.Where(i => i.Dimension.NetArea <= filter.MaxNetArea);

                if (filter?.NetAreaUnit != null)
                {
                    Query.Where(i => i.Dimension.NetAreaUnitId == filter.NetAreaUnit);
                }
            }
            else if (filter.MinNetArea != null)
            {
                Query.Where(i => i.Dimension.NetArea >= filter.MinNetArea);

                if (filter?.NetAreaUnit != null)
                {
                    Query.Where(i => i.Dimension.NetAreaUnitId == filter.NetAreaUnit);
                }
            }
            if (filter.ListingOnBehalf?.Any() ?? false)
            {
                Query.Where(i => i.ListingOnBehalf != null && i.ListingOnBehalf.Any(j => filter.ListingOnBehalf.Contains(j)));
            }
            #endregion
            if (filter?.PossesionType != null && filter?.PossesionType != PossesionType.None)
            {

                switch (filter?.PossesionType)
                {
                    case PossesionType.UnderConstruction:
                        Query.Where(i => i.PossesionType == PossesionType.UnderConstruction);
                        break;

                    case PossesionType.SixMonth:
                        Query.Where(i => i.PossesionType == PossesionType.SixMonth);
                        break;

                    case PossesionType.Year:
                        Query.Where(i => i.PossesionType == PossesionType.Year);
                        break;

                    case PossesionType.TwoYears:
                        Query.Where(i => i.PossesionType == PossesionType.TwoYears);
                        break;

                    case PossesionType.CustomDate:
                        DateTime? tempToPossesionDate = filter?.ToPossesionDate?.ConvertToDateToUtc();
                        DateTime? tempFrompossesionDate = filter?.FromPossesionDate?.ConvertFromDateToUtc();
                        Query.Where(i => i.PossessionDate != null && i.PossessionDate >= tempFrompossesionDate.Value && i.PossessionDate <= tempToPossesionDate.Value);

                        break;
                }

            }
        }
    }

    #region Count
    public class PropertyCountForListingManagementSpecs : Specification<Domain.Entities.Property>
    {
        public PropertyCountForListingManagementSpecs(
            GetAllPropertyForListingManagementRequest filter,
            Guid? masterPropertyAttributeId,
            Guid? masterPropertyAmenityId,
            Guid? masterPropertyTypeId,
            List<Guid>? propertyDimensionIds,
            NumericAttributesDto numericAttributesDto,
            List<Guid>? userIds,
            bool showAllProperties, List<CustomPropertyAttributeDto>? attributes = null
            )
        {
            Query
            .Where(i => !i.IsDeleted);

            switch (filter.PropertyVisiblity)
            {
                case PropertyVisiblity.All:
                    Query.Where(i => !i.IsArchived);
                    break;
                case PropertyVisiblity.Draft:
                    Query.Where(i => !i.IsArchived && i.ListingStatus == ListingStatus.Draft);
                    break;
                case PropertyVisiblity.Approved:
                    Query.Where(i => !i.IsArchived && i.ListingStatus == ListingStatus.Approved);
                    break;
                case PropertyVisiblity.Refused:
                    Query.Where(i => !i.IsArchived && i.ListingStatus == ListingStatus.Approved);
                    break;
                case PropertyVisiblity.Sold:
                    Query.Where(i => !i.IsArchived && i.Status == PropertyStatus.Sold);
                    break;
                case PropertyVisiblity.Archived:
                    Query.Where(i => i.IsArchived);
                    break;
            }

            switch (filter.FirstLevelFilter)
            {
                case FirstLevelFilter.All:
                    break;
                case FirstLevelFilter.Ready:
                    Query.Where(i => i.OfferingType == OfferingType.Ready);
                    switch (filter.SecondLevelFilter)
                    {
                        case SecondLevelFilter.All:
                            break;
                        case SecondLevelFilter.Residential:
                            Query.Where(i => (i.PropertyType.BaseId == Guid.Parse("310c9209-8390-4744-8e75-984ddff24a3c") || i.PropertyType.Id == Guid.Parse("310c9209-8390-4744-8e75-984ddff24a3cs")));
                            break;
                        case SecondLevelFilter.Commercial:
                            Query.Where(i => (i.PropertyType.BaseId == Guid.Parse("c3a1cd50-0ec4-4541-8291-c898c96cd573") || i.PropertyType.Id == Guid.Parse("c3a1cd50-0ec4-4541-8291-c898c96cd573")));
                            break;
                        case SecondLevelFilter.Agricultural:
                            Query.Where(i => (i.PropertyType.BaseId == Guid.Parse("965332f3-0575-4402-b6c1-85caa7cc92c8") || i.PropertyType.Id == Guid.Parse("965332f3-0575-4402-b6c1-85caa7cc92c8")));
                            break;
                    }
                    break;
                case FirstLevelFilter.OffPlan:
                    Query.Where(i => i.OfferingType == OfferingType.OffPlan);
                    switch (filter.SecondLevelFilter)
                    {
                        case SecondLevelFilter.All:
                            break;
                        case SecondLevelFilter.Residential:
                            Query.Where(i => (i.PropertyType.BaseId == Guid.Parse("310c9209-8390-4744-8e75-984ddff24a3c") || i.PropertyType.Id == Guid.Parse("310c9209-8390-4744-8e75-984ddff24a3cs")));
                            break;
                        case SecondLevelFilter.Commercial:
                            Query.Where(i => (i.PropertyType.BaseId == Guid.Parse("c3a1cd50-0ec4-4541-8291-c898c96cd573") || i.PropertyType.Id == Guid.Parse("c3a1cd50-0ec4-4541-8291-c898c96cd573")));
                            break;
                        case SecondLevelFilter.Agricultural:
                            Query.Where(i => (i.PropertyType.BaseId == Guid.Parse("965332f3-0575-4402-b6c1-85caa7cc92c8") || i.PropertyType.Id == Guid.Parse("965332f3-0575-4402-b6c1-85caa7cc92c8")));
                            break;
                    }
                    break;
                case FirstLevelFilter.Secondary:
                    Query.Where(i => i.OfferingType == OfferingType.Secondary);
                    switch (filter.SecondLevelFilter)
                    {
                        case SecondLevelFilter.All:
                            break;
                        case SecondLevelFilter.Residential:
                            Query.Where(i => (i.PropertyType.BaseId == Guid.Parse("310c9209-8390-4744-8e75-984ddff24a3c") || i.PropertyType.Id == Guid.Parse("310c9209-8390-4744-8e75-984ddff24a3cs")));
                            break;
                        case SecondLevelFilter.Commercial:
                            Query.Where(i => (i.PropertyType.BaseId == Guid.Parse("c3a1cd50-0ec4-4541-8291-c898c96cd573") || i.PropertyType.Id == Guid.Parse("c3a1cd50-0ec4-4541-8291-c898c96cd573")));
                            break;
                        case SecondLevelFilter.Agricultural:
                            Query.Where(i => (i.PropertyType.BaseId == Guid.Parse("965332f3-0575-4402-b6c1-85caa7cc92c8") || i.PropertyType.Id == Guid.Parse("965332f3-0575-4402-b6c1-85caa7cc92c8")));
                            break;
                    }
                    break;
            }

            if (filter.ListingSourceIds?.Any() ?? false)
            {
                Query.Where(i => i.ListingSources.Any(j => filter.ListingSourceIds.Contains(j.Id)));
            }

            if ((userIds?.Any() ?? false) && !showAllProperties)
            {
                Query.Where(i => i.PropertyAssignments.Any(i => userIds.Contains((Guid)i.AssignedTo) && i.IsCurrentlyAssigned));
            }
            if (filter.EnquiredFor != null)
            {
                Query.Where(i => i.EnquiredFor == filter.EnquiredFor);
            }
            if (filter.PropertyStatus != null)
            {
                Query.Where(i => i.Status == filter.PropertyStatus);
            }
            if (filter.Locations?.Any() ?? false)
            {

                filter.Locations = filter.Locations.Select(i => i.ToLower()).ToList();
                Query.Where(i => i.Address != null &&
                   (
                       (i.Address.SubLocality != null && i.Address.Locality != null &&
                        filter.Locations.Contains(i.Address.SubLocality.ToLower() + ", " + i.Address.Locality.ToLower())) ||
                       (i.Address.SubLocality == null && i.Address.Locality != null &&
                        filter.Locations.Contains(", " + i.Address.Locality.ToLower())) ||
                       (i.Address.SubLocality != null && i.Address.Locality == null &&
                        filter.Locations.Contains(i.Address.SubLocality.ToLower() + ", "))
                   )
               );

            }
            if (filter.CompletionStatus != null)
            {
                Query.Where(i => i.CompletionStatus == filter.CompletionStatus);
            }
            if (filter.ListingLevel != null)
            {
                Query.Where(i => i.ListingLevel == filter.ListingLevel);
            }
            if (filter.Cities?.Any() ?? false)
            {
                Query.Where(i => i.Address != null && i.Address.City != null && filter.Cities.Select(i => i.ToLower()).Contains(i.Address.City.ToLower()));
            }
            if (filter.States?.Any() ?? false)
            {
                Query.Where(i => i.Address != null && i.Address.State != null && filter.States.Select(i => i.ToLower()).Contains(i.Address.State.ToLower()));
            }
            if (filter?.BHKTypes?.Any() ?? false)
            {
                Query.Where(i => filter.BHKTypes.Contains(i.BHKType));
            }
            if (filter?.FurnishStatuses?.Any() ?? false)
            {
                Query.Where(i => filter.FurnishStatuses.Contains(i.FurnishStatus));
            }

            if (filter.Facing != null)
            {
                Query.Where(i => i.Facing == filter.Facing);

            }
            if (!string.IsNullOrEmpty(filter.PropertyTitle))
            {
                string propertyTitle = filter.PropertyTitle.ToLower().Replace(" ", "");
                Query.Where(i => i.Title.Replace(" ", "").ToLower() == propertyTitle);
            }
            if (!string.IsNullOrEmpty(filter.OwnerName))
            {
                string ownerName = filter.OwnerName.ToLower().Replace(" ", "");
                Query.Where(i => i.OwnerDetails.Name.Replace(" ", "").ToLower() == ownerName);
            }
            if (filter?.OwnerNames?.Any() ?? false)
            {
                filter.OwnerNames = filter.OwnerNames.ConvertAll(i => i.ToLower().Trim().Replace(" ", ""));
                Query.Where(e => e.PropertyOwnerDetails.Any(i => filter.OwnerNames.Contains(i.Name.ToLower().Trim().Replace(" ", "") ?? string.Empty)));
            }
            if (filter?.Projects?.Any() ?? false)
            {
                var projectNames = filter.Projects.ConvertAll(i => i.ToLower());
                Query.Where(i => i.Project != null && projectNames.Contains(i.Project.Name.ToLower()));
            }
            if (filter.NoOfBHK != default)
            {
                Query.Where(i => i.NoOfBHKs == filter.NoOfBHK);
            }
            if (filter?.BHKs?.Any() ?? false)
            {
                Query.Where(i => filter.BHKs.Contains(i.NoOfBHKs));
            }
            if (filter.Ratings != default)
            {
                Query.Where(i => i.Rating == filter.Ratings);
            }
            if (filter.FloorNumber != default)
            {
                Query.Where(i => i.Attributes != null && i.Attributes.Any(i => i.MasterPropertyAttributeId == Guid.Parse("c146a273-e342-4d66-aeee-6b2e6106d225") && i.Value == filter.FloorNumber.ToString()));

            }
            if (filter.BasePropertyTypeId != default)
            {
                Query.Where(i => i.PropertyType != null && i.PropertyType.BaseId == filter.BasePropertyTypeId);
            }
            if (filter.PropertyTypes?.Any() ?? false)
            {
                Query.Where(i => i.PropertyType != null && i.PropertyType.BaseId != null && filter.PropertyTypes.Contains(i.PropertyType.BaseId ?? default));
            }
            if (filter.PropertySubTypes?.Any() ?? false)
            {
                Query.Where(i => i.PropertyType != null && filter.PropertySubTypes.Contains(i.PropertyType.Id));
            }
            if (filter.SubPropertyTypeIds?.Any() ?? false)
            {
                Query.Where(i => i.PropertyType != null && filter.SubPropertyTypeIds.Contains(i.PropertyType.Id));
            }
            if (filter.MinBudget != null && filter.MaxBudget != null)
            {
                Query.Where(i => i.MonetaryInfo != null && i.MonetaryInfo.ExpectedPrice >= filter.MinBudget && i.MonetaryInfo.ExpectedPrice <= filter.MaxBudget);
            }
            else if (filter.MinBudget != null && filter.MaxBudget == null)
            {
                Query.Where(i => i.MonetaryInfo != null && i.MonetaryInfo.ExpectedPrice >= filter.MinBudget);
            }
            else if (filter.MinBudget == null && filter.MaxBudget != null)
            {
                Query.Where(i => i.MonetaryInfo != null && i.MonetaryInfo.ExpectedPrice <= filter.MaxBudget);
            }

            if (filter.FromPossessionDate != default || filter.ToPossessionDate != default)
            {

                if (filter.FromPossessionDate != null && filter.ToPossessionDate != null)
                {
                    Query.Where(i => i.PossessionDate != null && i.PossessionDate.Value >= filter.FromPossessionDate.Value && i.PossessionDate.Value < filter.ToPossessionDate.Value);
                }
                else if (filter.FromPossessionDate != null && filter.ToPossessionDate == null)
                {
                    Query.Where(i => i.PossessionDate != null && i.PossessionDate.Value >= filter.FromPossessionDate.Value);
                }
                else if (filter.FromPossessionDate == null && filter.ToPossessionDate != null)
                {
                    Query.Where(i => i.PossessionDate != null && i.PossessionDate.Value <= filter.ToPossessionDate.Value);
                }
            }

            if (filter.MinPrice != null && filter.MaxPrice != null)
            {
                Query.Where(i => i.MonetaryInfo != null && i.MonetaryInfo.ExpectedPrice >= filter.MinPrice && i.MonetaryInfo.ExpectedPrice <= filter.MaxPrice);
            }
            else if (filter.MinPrice != null && filter.MaxPrice == null)
            {
                Query.Where(i => i.MonetaryInfo != null && i.MonetaryInfo.ExpectedPrice >= filter.MinPrice);
            }
            else if (filter.MinPrice == null && filter.MaxPrice != null)
            {
                Query.Where(i => i.MonetaryInfo != null && i.MonetaryInfo.ExpectedPrice <= filter.MaxPrice);
            }
            if (filter.Amenities != null && filter.Amenities.Any())
            {
                Query.Where(i => i.Amenities.Any(a => filter.Amenities.Contains(a.MasterPropertyAmenityId)));
            }
            if (filter.PropertySize != null && filter.PropertySize != default)
            {
                if (filter.PropertySize.Area != default && filter.PropertySize.ConversionFactor != default)
                {
                    Query.Where(i => propertyDimensionIds.Any() && propertyDimensionIds.Contains(i.Dimension.Id));
                }
                else if (filter.PropertySize.Area != default && filter.PropertySize.ConversionFactor == default)
                {
                    Query.Where(i => i.Dimension.Area == filter.PropertySize.Area || i.Dimension.AreaInSqMtr == filter.PropertySize.Area);
                }
            }
            if (filter.DateType.HasValue && (filter.FromDate != default || filter.ToDate != default))
            {
                filter.FromDate = filter.FromDate.HasValue ? filter.FromDate.Value.ConvertFromDateToUtc() : null;
                filter.ToDate = filter.ToDate.HasValue ? filter.ToDate.Value.ConvertToDateToUtc() : null;

                switch (filter.DateType)
                {
                    case PropertyDateType.CreatedDate:
                        if (filter.FromDate == null && filter.ToDate != null)
                        {
                            Query.Where(i => i.CreatedOn <= filter.ToDate.Value);
                        }
                        else if (filter.FromDate != null && filter.ToDate == null)
                        {
                            Query.Where(i => i.CreatedOn >= filter.FromDate.Value);
                        }
                        else if (filter.FromDate != null && filter.ToDate != null)
                        {
                            Query.Where(i => i.CreatedOn >= filter.FromDate.Value && i.CreatedOn <= filter.ToDate.Value);
                        }
                        break;
                    case PropertyDateType.ModifiedDate:
                        if (filter.FromDate == null && filter.ToDate != null)
                        {
                            Query.Where(i => i.LastModifiedOn != null && i.LastModifiedOn.Value <= filter.ToDate.Value);
                        }
                        else if (filter.FromDate != null && filter.ToDate == null)
                        {
                            Query.Where(i => i.LastModifiedOn != null && i.LastModifiedOn.Value >= filter.FromDate.Value);
                        }
                        else if (filter.FromDate != null && filter.ToDate != null)
                        {
                            Query.Where(i => i.LastModifiedOn != null && i.LastModifiedOn.Value >= filter.FromDate.Value && i.LastModifiedOn.Value <= filter.ToDate.Value);
                        }
                        break;
                    case PropertyDateType.PossessionDate:
                        if (filter.FromDate == null && filter.ToDate != null)
                        {
                            Query.Where(i => i.PossessionDate != null && i.PossessionDate.Value <= filter.ToDate.Value);
                        }
                        else if (filter.FromDate != null && filter.ToDate == null)
                        {
                            Query.Where(i => i.PossessionDate != null && i.PossessionDate.Value >= filter.FromDate.Value);
                        }
                        else if (filter.FromDate != null && filter.ToDate != null)
                        {
                            Query.Where(i => i.PossessionDate != null && i.PossessionDate.Value >= filter.FromDate.Value && i.PossessionDate.Value < filter.ToDate.Value.AddDays(1));
                        }
                        break;
                    case PropertyDateType.None:
                        if (filter.FromDate == null && filter.ToDate != null)
                        {
                            Query.Where(i => i.CreatedOn <= filter.ToDate.Value ||
                                             (i.LastModifiedOn.HasValue && i.LastModifiedOn.Value <= filter.ToDate.Value) ||
                                             (i.PossessionDate.HasValue && i.PossessionDate.Value <= filter.ToDate.Value));
                        }
                        else if (filter.FromDate != null && filter.ToDate == null)
                        {
                            Query.Where(i => i.CreatedOn >= filter.FromDate.Value ||
                                             (i.LastModifiedOn.HasValue && i.LastModifiedOn.Value >= filter.FromDate.Value) ||
                                             (i.PossessionDate.HasValue && i.PossessionDate.Value >= filter.FromDate.Value));
                        }
                        else if (filter.FromDate != null && filter.ToDate != null)
                        {
                            Query.Where(i => (i.CreatedOn >= filter.FromDate.Value && i.CreatedOn <= filter.ToDate.Value) ||
                                             (i.LastModifiedOn.HasValue && i.LastModifiedOn.Value >= filter.FromDate.Value && i.LastModifiedOn.Value <= filter.ToDate.Value) ||
                                             (i.PossessionDate.HasValue && i.PossessionDate.Value >= filter.FromDate.Value && i.PossessionDate.Value <= filter.ToDate.Value));
                        }
                        break;
                    default:
                        break;
                }
            }


            if (!string.IsNullOrWhiteSpace(filter.PropertySearch))
            {
                //var minBudget = (Int64.TryParse(Regex.Matches(filter.PropertySearch, @"\d+")?.FirstOrDefault()?.Value ?? string.Empty, out long min) ? (long?)min : null);
                //if (minBudget != null) { minBudget = Convert.ToInt64(minBudget * 0.8); }
                //var maxBudget = (Int64.TryParse(Regex.Matches(filter.PropertySearch, @"\d+")?.FirstOrDefault()?.Value ?? string.Empty, out long max) ? (long?)max : null);
                //if (maxBudget != null) { maxBudget = Convert.ToInt64(maxBudget * 1.2); }
                //var noOfBHK = double.TryParse(Regex.Matches(filter.PropertySearch, @"\d+")?.FirstOrDefault()?.Value ?? string.Empty, out double nB) ? (double?)nB : null;
                //var saleType = (Enum.TryParse<SaleType>((Enum.GetNames<SaleType>()).Where(i => filter.PropertySearch.Contains(i.ToLower())).ToList().FirstOrDefault(), true, out SaleType sT)) ? (SaleType?)sT : null;
                //var enquiryType = (Enum.TryParse<EnquiryType>((Enum.GetNames<EnquiryType>()).Where(i => filter.PropertySearch.Contains(i.ToLower())).ToList().FirstOrDefault(), true, out EnquiryType eT)) ? (EnquiryType?)eT : null;
                //var furnishStatus = (Enum.TryParse<FurnishStatus>((Enum.GetNames<FurnishStatus>()).Where(i => filter.PropertySearch.Contains(i.ToLower())).ToList().FirstOrDefault(), true, out FurnishStatus fS)) ? (FurnishStatus?)fS : null;
                //var propertyStatus = (Enum.TryParse<PropertyStatus>((Enum.GetNames<PropertyStatus>()).Where(i => filter.PropertySearch.Contains(i.ToLower())).ToList().FirstOrDefault(), true, out PropertyStatus pS)) ? (PropertyStatus?)pS : null;
                //var bHKType = Enum.TryParse<BHKType>((Enum.GetNames<BHKType>()).Where(i => filter.PropertySearch.Contains(i.ToLower())).ToList().FirstOrDefault(), true, out BHKType bK) ? (BHKType?)bK : null;
                //var facing = Enum.TryParse<Facing>((Enum.GetNames<Facing>()).Where(i => filter.PropertySearch.Contains(i.ToLower())).ToList().FirstOrDefault(), true, out Facing fc) ? (Facing?)fc : null;

                Query.Where(
                i => (i.Title + " " +
                //i.Rating + " " +
                //i.Address.SubLocality + " " +
                //i.Address.Locality + " " +
                //i.Address.District + " " +
                //i.Address.City + " " +
                //i.Address.State + " " +
                //i.Address.Country + " " +
                //i.Address.PostalCode + " " +
                //i.PropertyType.Type + " " +
                //i.PropertyType.DisplayName + " " +
                i.OwnerDetails.Name + " " +
                i.OwnerDetails.Phone + " " +
                i.OwnerDetails.Email
                //i.AboutProperty + " " +
                //i.SerialNo + " "
                ).ToLower().Contains(filter.PropertySearch.ToLower()));
                //(i.SaleType == saleType && saleType != null) ||
                //(i.EnquiredFor == enquiryType && enquiryType != null) ||
                //(i.FurnishStatus == furnishStatus && furnishStatus != null) ||
                //(i.Status == propertyStatus && propertyStatus != null) ||
                //(i.BHKType == bHKType && bHKType != null) ||
                //(i.Facing == facing && facing != null) ||
                //(i.Attributes.Any(i => i.MasterPropertyAttributeId == masterPropertyAttributeId) && masterPropertyAttributeId != null) ||
                //(i.Amenities.Any(i => i.MasterPropertyAmenityId == masterPropertyAmenityId) && masterPropertyAmenityId != null) ||
                ////((i.MonetaryInfo.ExpectedPrice >= minBudget && i.MonetaryInfo.ExpectedPrice <= maxBudget) && minBudget != null && maxBudget != null) ||
                //((i.PropertyType.BaseId == masterPropertyTypeId || i.PropertyType.Id == masterPropertyTypeId) && masterPropertyTypeId != null));
            }
            if (filter.NoOfFloors?.Any() ?? false && numericAttributesDto?.NoOfFloors?.NoOfAttributes != null && attributes != null)
            {
                var attribute = attributes?.FirstOrDefault(i => i.AttributeDisplayName == "Total Floors");
                if (attribute != null)
                {
                    if (numericAttributesDto.NoOfFloors.IsMaxValueIncluded)
                    {
                        Query.Where(i => i.Attributes.Any(i => i.MasterPropertyAttributeId == attribute.Id &&
                     !numericAttributesDto.NoOfFloors.NoOfAttributes.Contains(i.Value) && !string.IsNullOrEmpty(i.Value) && i.Value != "0" && i.Value != "Ground Floor"));
                    }
                    else
                    {
                        Query.Where(i => i.Attributes.Any(i => i.MasterPropertyAttributeId == attribute.Id &&
                           numericAttributesDto.NoOfFloors.NoOfAttributes.Contains(i.Value) && !string.IsNullOrEmpty(i.Value) && i.Value != "0"));
                    }
                }
            }
            if (filter.NoOfBedrooms?.Any() ?? false && numericAttributesDto?.NoOfBedrooms?.NoOfAttributes != null && attributes != null)
            {
                var attribute = attributes?.FirstOrDefault(i => i.AttributeDisplayName == "No. of Bed Rooms");
                if (attribute != null)
                {
                    if (numericAttributesDto.NoOfBedrooms.IsMaxValueIncluded)
                    {
                        Query.Where(i => i.Attributes.Any(i => i.MasterPropertyAttributeId == attribute.Id &&
                     !numericAttributesDto.NoOfBedrooms.NoOfAttributes.Contains(i.Value) && !string.IsNullOrEmpty(i.Value) && i.Value != "0"));
                    }
                    else
                    {
                        Query.Where(i => i.Attributes.Any(i => i.MasterPropertyAttributeId == attribute.Id &&
                           numericAttributesDto.NoOfBedrooms.NoOfAttributes.Contains(i.Value) && !string.IsNullOrEmpty(i.Value) && i.Value != "0"));
                    }
                }
            }
            if (filter.NoOfKitchens?.Any() ?? false && numericAttributesDto?.NoOfKitchens?.NoOfAttributes != null && attributes != null)
            {
                var attribute = attributes?.FirstOrDefault(i => i.AttributeDisplayName == "No. of Kitchens");
                if (attribute != null)
                {
                    if (numericAttributesDto.NoOfKitchens.IsMaxValueIncluded)
                    {
                        Query.Where(i => i.Attributes.Any(i => i.MasterPropertyAttributeId == attribute.Id &&
                     !numericAttributesDto.NoOfKitchens.NoOfAttributes.Contains(i.Value) && !string.IsNullOrEmpty(i.Value) && i.Value != "0"));
                    }
                    else
                    {
                        Query.Where(i => i.Attributes.Any(i => i.MasterPropertyAttributeId == attribute.Id &&
                            numericAttributesDto.NoOfKitchens.NoOfAttributes.Contains(i.Value) && !string.IsNullOrEmpty(i.Value) && i.Value != "0"));
                    }
                }
            }

            if (filter.NoOfUtilites?.Any() ?? false && numericAttributesDto?.NoOfUtilites?.NoOfAttributes != null && attributes != null)
            {
                var attribute = attributes?.FirstOrDefault(i => i.AttributeDisplayName == "No. of Utilities");
                if (attribute != null)
                {
                    if (numericAttributesDto.NoOfUtilites.IsMaxValueIncluded)
                    {
                        Query.Where(i => i.Attributes.Any(i => i.MasterPropertyAttributeId == attribute.Id &&
                     !numericAttributesDto.NoOfUtilites.NoOfAttributes.Contains(i.Value) && !string.IsNullOrEmpty(i.Value) && i.Value != "0"));
                    }
                    else
                    {
                        Query.Where(i => i.Attributes.Any(i => i.MasterPropertyAttributeId == attribute.Id &&
                           numericAttributesDto.NoOfUtilites.NoOfAttributes.Contains(i.Value) && !string.IsNullOrEmpty(i.Value) && i.Value != "0"));
                    }
                }
            }
            if (filter.NoOfLivingrooms?.Any() ?? false && numericAttributesDto?.NoOfLivingrooms?.NoOfAttributes != null && attributes != null)
            {
                var attribute = attributes?.FirstOrDefault(i => i.AttributeDisplayName == "No. of Drawing or Living Rooms");
                if (attribute != null)
                {
                    if (numericAttributesDto.NoOfLivingrooms.IsMaxValueIncluded)
                    {
                        Query.Where(i => i.Attributes.Any(i => i.MasterPropertyAttributeId == attribute.Id &&
                     !numericAttributesDto.NoOfLivingrooms.NoOfAttributes.Contains(i.Value) && !string.IsNullOrEmpty(i.Value) && i.Value != "0"));
                    }
                    else
                    {
                        Query.Where(i => i.Attributes.Any(i => i.MasterPropertyAttributeId == attribute.Id &&
                           numericAttributesDto.NoOfLivingrooms.NoOfAttributes.Contains(i.Value) && !string.IsNullOrEmpty(i.Value) && i.Value != "0"));
                    }
                }

            }
            if (filter.NoOfBalconies?.Any() ?? false && numericAttributesDto?.NoOfBalconies?.NoOfAttributes != null && attributes != null)
            {
                var attribute = attributes?.FirstOrDefault(i => i.AttributeDisplayName == "No. of Balconies");
                if (attribute != null)
                {
                    if (numericAttributesDto.NoOfBalconies.IsMaxValueIncluded)
                    {
                        Query.Where(i => i.Attributes.Any(i => i.MasterPropertyAttributeId == attribute.Id &&
                     !numericAttributesDto.NoOfBalconies.NoOfAttributes.Contains(i.Value) && !string.IsNullOrEmpty(i.Value) && i.Value != "0"));
                    }
                    else
                    {
                        Query.Where(i => i.Attributes.Any(i => i.MasterPropertyAttributeId == attribute.Id &&
                           numericAttributesDto.NoOfBalconies.NoOfAttributes.Contains(i.Value) && !string.IsNullOrEmpty(i.Value) && i.Value != "0"));
                    }
                }
            }
            if (filter.NoOfBathrooms?.Any() ?? false && numericAttributesDto?.NoOfBathrooms?.NoOfAttributes != null && attributes != null)
            {
                var attribute = attributes?.FirstOrDefault(i => i.AttributeDisplayName == "No. of Bath Rooms");
                if (attribute != null)
                {
                    if (numericAttributesDto.NoOfBathrooms.IsMaxValueIncluded)
                    {
                        Query.Where(i => i.Attributes.Any(i => i.MasterPropertyAttributeId == attribute.Id &&
                     !numericAttributesDto.NoOfBathrooms.NoOfAttributes.Contains(i.Value) && !string.IsNullOrEmpty(i.Value) && i.Value != "0" && i.Value != "Ground Floor"));
                    }
                    else
                    {
                        Query.Where(i => i.Attributes.Any(i => i.MasterPropertyAttributeId == attribute.Id &&
                           numericAttributesDto.NoOfBathrooms.NoOfAttributes.Contains(i.Value) && !string.IsNullOrEmpty(i.Value) && i.Value != "0"));
                    }
                }
            }
            if (filter.Parking?.Any() ?? false && numericAttributesDto?.Parking?.NoOfAttributes != null && attributes != null)
            {
                var attribute = attributes?.FirstOrDefault(i => i.AttributeDisplayName == "Parking");
                if (attribute != null)
                {
                    if (numericAttributesDto.Parking.IsMaxValueIncluded)
                    {
                        Query.Where(i => i.Attributes.Any(i => i.MasterPropertyAttributeId == attribute.Id &&
                     !numericAttributesDto.Parking.NoOfAttributes.Contains(i.Value) && !string.IsNullOrEmpty(i.Value) && i.Value != "0"));
                    }
                    else
                    {
                        Query.Where(i => i.Attributes.Any(i => i.MasterPropertyAttributeId == attribute.Id &&
                           numericAttributesDto.Parking.NoOfAttributes.Contains(i.Value) && !string.IsNullOrEmpty(i.Value) && i.Value != "0"));
                    }
                }
            }
            if (filter.PropertySize != null && filter.PropertySize != default)
            {
                if (filter.PropertySize.CarpetArea != default && filter.PropertySize.ConversionFactor != default)
                {
                    Query.Where(i => propertyDimensionIds.Any() && propertyDimensionIds.Contains(i.Dimension.Id));
                }
                else if (filter.PropertySize.CarpetArea == 999)
                {
                    Query.Where(i => i.Dimension.CarpetArea <= filter.PropertySize.CarpetArea || i.Dimension.AreaInSqMtr <= filter.PropertySize.CarpetArea);

                }
                else if (filter.PropertySize.CarpetArea != default && filter.PropertySize.ConversionFactor == default)
                {
                    Query.Where(i => i.Dimension.CarpetArea >= filter.PropertySize.CarpetArea || i.Dimension.AreaInSqMtr >= filter.PropertySize.CarpetArea);
                }
            }
            if (filter.PropertySize != null && filter.PropertySize != default)
            {
                if (filter.PropertySize.BuildUpArea != default && filter.PropertySize.ConversionFactor != default)
                {
                    Query.Where(i => propertyDimensionIds.Any() && propertyDimensionIds.Contains(i.Dimension.Id));
                }

                else if (filter.PropertySize.BuildUpArea != default && filter.PropertySize.ConversionFactor == default)
                {
                    Query.Where(i => i.Dimension.BuildUpArea == filter.PropertySize.BuildUpArea || i.Dimension.AreaInSqMtr == filter.PropertySize.BuildUpArea);
                }
            }
            if (filter.PropertySize != null && filter.PropertySize != default)
            {

                if (filter.PropertySize.SaleableArea != default && filter.PropertySize.ConversionFactor != default)
                {
                    Query.Where(i => propertyDimensionIds.Any() && propertyDimensionIds.Contains(i.Dimension.Id));
                }

                else if (filter.PropertySize.SaleableArea != default && filter.PropertySize.ConversionFactor == default)
                {
                    Query.Where(i => i.Dimension.SaleableArea == filter.PropertySize.SaleableArea || i.Dimension.AreaInSqMtr == filter.PropertySize.SaleableArea);
                }
            }

            if (filter.PropertySize != null && filter.PropertySize != default)
            {

                if (filter.PropertySize.NetArea != default && filter.PropertySize.ConversionFactor != default)
                {
                    Query.Where(i => propertyDimensionIds.Any() && propertyDimensionIds.Contains(i.Dimension.Id));
                }

                else if (filter.PropertySize.NetArea != default && filter.PropertySize.ConversionFactor == default)
                {
                    Query.Where(i => i.Dimension.NetArea == filter.PropertySize.NetArea || i.Dimension.AreaInSqMtr == filter.PropertySize.NetArea);
                }
            }
            if (filter.Currency != null)
            {
                Query.Where(i => i.MonetaryInfo != null && i.MonetaryInfo.Currency == filter.Currency);
            }
            if (filter.MinSaleableArea != null && filter.MaxSaleableArea != null)
            {
                Query.Where(i => i.Dimension.SaleableArea >= filter.MinSaleableArea && i.Dimension.SaleableArea <= filter.MaxSaleableArea);

                if (filter?.SaleableAreaUnit != null)
                {
                    Query.Where(i => i.Dimension.SaleableAreaId == filter.SaleableAreaUnit);
                }
            }
            else if (filter.MaxSaleableArea != null)
            {
                Query.Where(i => i.Dimension.SaleableArea <= filter.MaxSaleableArea);

                if (filter?.SaleableAreaUnit != null)
                {
                    Query.Where(i => i.Dimension.SaleableAreaId == filter.SaleableAreaUnit);
                }
            }
            else if (filter.MinSaleableArea != null)
            {
                Query.Where(i => i.Dimension.SaleableArea >= filter.MinSaleableArea);

                if (filter?.SaleableAreaUnit != null)
                {
                    Query.Where(i => i.Dimension.SaleableAreaId == filter.SaleableAreaUnit);
                }
            }
            if (filter.MinPropertySize != null && filter.MaxPropertySize != null)
            {
                Query.Where(i => i.Dimension.Area >= filter.MinPropertySize && i.Dimension.Area <= filter.MaxPropertySize);
                if (filter?.PropertySizeUnit != null)
                {
                    Query.Where(i => i.Dimension.AreaUnitId == filter.PropertySizeUnit);
                }
            }
            else if (filter.MaxPropertySize != null)
            {
                Query.Where(i => i.Dimension.Area <= filter.MaxPropertySize);
                if (filter?.PropertySizeUnit != null)
                {
                    Query.Where(i => i.Dimension.AreaUnitId == filter.PropertySizeUnit);
                }
            }
            if (filter.MinBuitUpArea != null && filter.MaxBuitUpArea != null)
            {
                Query.Where(i => i.Dimension.BuildUpArea >= filter.MinBuitUpArea && i.Dimension.BuildUpArea <= filter.MaxBuitUpArea);
                if (filter?.BuitUpAreaUnit != null)
                {
                    Query.Where(i => i.Dimension.BuildUpAreaId == filter.BuitUpAreaUnit);
                }
            }
            else if (filter.MaxBuitUpArea != null)
            {
                Query.Where(i => i.Dimension.BuildUpArea <= filter.MaxPropertySize);
                if (filter?.BuitUpAreaUnit != null)
                {
                    Query.Where(i => i.Dimension.BuildUpAreaId == filter.BuitUpAreaUnit);
                }
            }
            else if (filter.MinBuitUpArea != null)
            {
                Query.Where(i => i.Dimension.BuildUpArea >= filter.MinBuitUpArea);
                if (filter?.BuitUpAreaUnit != null)
                {
                    Query.Where(i => i.Dimension.BuildUpAreaId == filter.BuitUpAreaUnit);
                }
            }
            if (filter.MinCarpetArea != null && filter.MaxCarpetArea != null)
            {
                Query.Where(i => i.Dimension.CarpetArea >= filter.MinCarpetArea && i.Dimension.CarpetArea <= filter.MaxCarpetArea);
                if (filter?.CarpetAreaUnit != null)
                {
                    Query.Where(i => i.Dimension.CarpetAreaId == filter.CarpetAreaUnit);
                }
            }
            else if (filter.MaxCarpetArea != null)
            {
                Query.Where(i => i.Dimension.CarpetArea <= filter.MaxCarpetArea);
                if (filter?.CarpetAreaUnit != null)
                {
                    Query.Where(i => i.Dimension.CarpetAreaId == filter.CarpetAreaUnit);
                }
            }
            else if (filter.MinCarpetArea != null)
            {
                Query.Where(i => i.Dimension.CarpetArea >= filter.MinCarpetArea);
                if (filter?.CarpetAreaUnit != null)
                {
                    Query.Where(i => i.Dimension.CarpetAreaId == filter.CarpetAreaUnit);
                }
            }
            if (filter?.MinNetArea != null && filter?.MaxNetArea != null)
            {
                Query.Where(i => i.Dimension.NetArea >= filter.MinNetArea && i.Dimension.NetArea <= filter.MaxNetArea);

                if (filter?.NetAreaUnit != null)
                {
                    Query.Where(i => i.Dimension.NetAreaUnitId == filter.NetAreaUnit);
                }
            }
            else if (filter?.MaxNetArea != null)
            {
                Query.Where(i => i.Dimension.NetArea <= filter.MaxNetArea);

                if (filter?.NetAreaUnit != null)
                {
                    Query.Where(i => i.Dimension.NetAreaUnitId == filter.NetAreaUnit);
                }
            }
            else if (filter?.MinNetArea != null)
            {
                Query.Where(i => i.Dimension.NetArea >= filter.MinNetArea);

                if (filter?.NetAreaUnit != null)
                {
                    Query.Where(i => i.Dimension.NetAreaUnitId == filter.NetAreaUnit);
                }
            }
            if (filter.Communities?.Any() ?? false)
            {
                Query.Where(i => i.Address != null && i.Address.Community != null && filter.Communities.Select(i => i.ToLower()).Contains(i.Address.Community.ToLower()));
            }

            if (filter.SubCommunities?.Any() ?? false)
            {
                Query.Where(i => i.Address != null && i.Address.SubCommunity != null && filter.SubCommunities.Select(i => i.ToLower()).Contains(i.Address.SubCommunity.ToLower()));
            }
            if (filter.ListingOnBehalf?.Any() ?? false)
            {
                Query.Where(i => i.ListingOnBehalf != null && i.ListingOnBehalf.Any(j => filter.ListingOnBehalf.Contains(j)));
            }
            if (filter?.PossesionType != null && filter?.PossesionType != PossesionType.None)
            {
                switch (filter?.PossesionType)
                {
                    case PossesionType.UnderConstruction:
                        Query.Where(i => i.PossesionType == PossesionType.UnderConstruction);
                        break;

                    case PossesionType.SixMonth:
                        Query.Where(i => i.PossesionType == PossesionType.SixMonth);
                        break;

                    case PossesionType.Year:
                        Query.Where(i => i.PossesionType == PossesionType.Year);
                        break;

                    case PossesionType.TwoYears:
                        Query.Where(i => i.PossesionType == PossesionType.TwoYears);
                        break;

                    case PossesionType.CustomDate:
                        DateTime? tempToPossesionDate = filter?.ToPossesionDate?.ConvertToDateToUtc();
                        DateTime? tempFrompossesionDate = filter?.FromPossesionDate?.ConvertFromDateToUtc();
                        Query.Where(i => i.PossessionDate != null && i.PossessionDate >= tempFrompossesionDate.Value && i.PossessionDate <= tempToPossesionDate.Value);

                        break;
                }

            }
        }
    }
    #endregion
}
