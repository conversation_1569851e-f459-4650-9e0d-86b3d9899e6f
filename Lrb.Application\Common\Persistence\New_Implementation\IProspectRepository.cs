﻿using Lrb.Application.DataManagement.Mobile.Requests;
using Lrb.Application.DataManagement.Web.Request;
using Lrb.Domain.Entities.MasterData;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Lrb.Application.Common.Persistence.New_Implementation
{
    public interface IProspectRepository : IEFRepository<Prospect>
    {
        Task<List<Lrb.Domain.Entities.Prospect>> ListAsync();
        Task<IEnumerable<Prospect>> GetAllProspectForWeb(Lrb.Application.DataManagement.Web.Request.GetAllProspectRequest request, Guid userId, IEnumerable<Guid> subIds, List<CustomProspectStatus>? prospectStatuses = null);
        Task<int> GetAllProspectCountForWeb(Lrb.Application.DataManagement.Web.Request.GetAllProspectRequest request, Guid userId, IEnumerable<Guid> subIds, List<CustomProspectStatus>? prospectStatuses = null);

        Task<IEnumerable<Prospect>> GetAllProspectForMobile(Lrb.Application.DataManagement.Mobile.Requests.GetAllProspectRequest request, Guid userId, IEnumerable<Guid> subIds, List<CustomProspectStatus>? prospectStatuses = null);
        Task<int> GetAllProspectCountForMobile(Lrb.Application.DataManagement.Mobile.Requests.GetAllProspectRequest request, Guid userId, IEnumerable<Guid> subIds, List<CustomProspectStatus>? prospectStatuses = null);
        Task<IEnumerable<Prospect>> GetAllSearchProspectForMobile(Lrb.Application.DataManagement.Mobile.Requests.GetAllProspectsBySearchRequest request, IEnumerable<Guid> subIds, List<CustomProspectStatus>? prospectStatuses = null);
        Task<int> GetAllSearchProspectCountForMobile(Lrb.Application.DataManagement.Mobile.Requests.GetAllProspectsBySearchRequest request, IEnumerable<Guid> subIds, List<CustomProspectStatus>? prospectStatuses = null);
        #region CustomFiltersWeb
        Task<List<Lead.Web.CustomFiltersDto>> GetProspectsCountByCustomFiltersForWebAsync(List<CustomFilter>? filters, GetAllProspectParameter request, IEnumerable<Guid> subIds, Guid userId, bool isAdmin, bool shouldGetOnlyBaseFilters = false, List<CustomProspectStatus>? prospectStatuses = null);
        Task<IEnumerable<Prospect>> GetAllProspectsByCustomFiltersForWebAsync(CustomFilter filter, GetAllProspectParameter request, IEnumerable<Guid> subIds, Guid userId, bool isAdmin, List<CustomProspectStatus>? prospectStatuses = null);
        Task<int> GetProspectsCountByCustomFiltersForWebAsync(CustomFilter filter, GetAllProspectParameter request, IEnumerable<Guid> subIds, Guid userId, bool isAdmin, List<CustomProspectStatus>? prospectStatuses = null);
        #endregion
        #region CustomFiltersMobile
        Task<IEnumerable<Prospect>> GetAllProspectsByCustomFiltersForMobileAsync(CustomFilter filter, GetAllProspectFilterParameter request, IEnumerable<Guid> subIds, Guid userId, bool isAdmin, List<CustomProspectStatus>? prospectStatuses = null);
        Task<int> GetProspectsCountByCustomFiltersForMobileAsync(CustomFilter filter, GetAllProspectFilterParameter request, IEnumerable<Guid> subIds, Guid userId, bool isAdmin, List<CustomProspectStatus>? prospectStatuses = null);
        #endregion

        Task<IEnumerable<Prospect>> GetAnonymousProspectForWeb(Lrb.Application.DataManagement.Web.Request.GetAllProspectsAnonymousRequest request ,List<CustomProspectStatus>? prospectStatuses = null);
        Task<int> GetAnonymousProspectCountForWeb(Lrb.Application.DataManagement.Web.Request.GetAllProspectsAnonymousRequest request, List<CustomProspectStatus>? prospectStatuses = null);
        Task<IEnumerable<Prospect>> GetProspectByContactNoAsync(List<string> contactNos);
        Task<IEnumerable<Prospect>> GetAllProspectsExportByCustomFiltersForWebAsync(CustomFilter filter, GetAllProspectParameter request, IEnumerable<Guid> subIds, Guid userId, bool isAdmin, List<CustomProspectStatus>? prospectStatuses = null);
        Task<IQueryable<Prospect>> BuildQueryForProspectsCount(Lrb.Application.DataManagement.Web.Request.GetAllProspectRequest request, Guid userId, IEnumerable<Guid> subIds, List<CustomProspectStatus>? prospectStatuses = null);
        Task<int> GetProspectsCountForWebAsync(Lrb.Application.DataManagement.Web.Request.GetAllProspectRequest request, Guid userId, IEnumerable<Guid> subIds, IQueryable<Prospect> query, List<CustomProspectStatus>? prospectStatuses = null);
    }
}
