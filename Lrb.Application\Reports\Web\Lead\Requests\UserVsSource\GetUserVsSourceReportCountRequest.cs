﻿using Lrb.Application.Reports.Web.Lead.Dtos.UserVsSubSource;
using Lrb.Application.Utils;
using Lrb.Shared.Extensions;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Lrb.Application.Reports.Web.UserVsSource.Requests
{
    public class GetUserVsSourceReportCountRequest : IRequest<int>
    {
     public DateTime? FromDate { get; set; }
    public DateTime? ToDate { get; set; }
    public DateType? DateType { get; set; }
    public bool IsWithTeam { get; set; }
    public List<Guid>? UserIds { get; set; }
    public string? SearchText { get; set; }
    public List<LeadSource>? Sources { get; set; }
    public List<string>? SubSources { get; set; }
    public List<string>? Projects { get; set; }
    public UserStatus? UserStatus { get; set; }
    public ReportPermission? ReportPermission { get; set; }
    public List<string>? Localites { get; set; }
    public List<string>? States { get; set; }
    public List<string>? Cities { get; set; }
    public List<string>? Countries { get; set; }

    }
    public class GetSourceReportCountByUserRequestHandler : IRequestHandler<GetUserVsSourceReportCountRequest, int>
{
    private readonly IDapperRepository _dapperRepository;
    private readonly ICurrentUser _currentUser;

    public GetSourceReportCountByUserRequestHandler(IDapperRepository dapperRepository, ICurrentUser currentUser)
    {
        _dapperRepository = dapperRepository;
        _currentUser = currentUser;
    }

    public async Task<int> Handle(GetUserVsSourceReportCountRequest request, CancellationToken cancellationToken)
    {
        var tenantId = _currentUser.GetTenant();
        var userId = _currentUser.GetUserId();
        List<Guid> teamUserIds = new();
        List<Guid> permittedUserIds = new();
        var isAdmin = await _dapperRepository.IsAdminAsync(userId, tenantId ?? string.Empty);
        if (isAdmin)
        {
            permittedUserIds = (await _dapperRepository.GetAllUserIdsAsync(tenantId ?? string.Empty)).ToList();
        }
        else if (request.ReportPermission != null)
        {
            switch (request.ReportPermission)
            {
                case ReportPermission.All:
                    permittedUserIds = (await _dapperRepository.GetAllUserIdsAsync(tenantId ?? string.Empty)).ToList();
                    break;
                case ReportPermission.Reportees:
                    permittedUserIds = (await _dapperRepository.GetSubordinateIdsWithOnlyReporteesAsync(new List<Guid>() { userId }, tenantId ?? string.Empty)).ToList();
                    break;
            }
        }
        if (request?.UserIds?.Any() ?? false)
        {
            if (request?.IsWithTeam ?? false)
            {
                teamUserIds = (await _dapperRepository.GetSubordinateIdsWithOnlyReporteesAsync(request.UserIds, tenantId ?? string.Empty)).ToList();
            }
            else
            {
                teamUserIds = request?.UserIds ?? new List<Guid>();
            }
        }
        else
        {
            if (!isAdmin)
            {
                teamUserIds = permittedUserIds;
            }
        }
        if (teamUserIds.Any())
        {
            teamUserIds = teamUserIds.Where(userId => permittedUserIds.Contains(userId)).ToList();
        }
        else
        {
            teamUserIds = permittedUserIds;
        }
        request.FromDate = request.FromDate.HasValue ? request.FromDate.Value.ConvertFromDateToUtc() : null;
        request.ToDate = request.ToDate.HasValue ? request.ToDate.Value.ConvertToDateToUtc() : null;
            var totalCount = (await _dapperRepository.QueryStoredProcedureCountFromReadReplicaAsync("LeadratBlack", "GetSourceReportByUserCount", new
            {
                fromdate = request.FromDate,
                todate = request.ToDate,
                datetype = request.DateType,
                tenantid = tenantId,
                userids = teamUserIds,
                searchtext = string.IsNullOrEmpty(request.SearchText) ? null : request.SearchText.Replace(" ", "").ToLower(),
                sources = request?.Sources?.ConvertAll(i => (int)i),
                projects = request?.Projects?.ConvertAll<string>(i => i.Replace(" ", "").ToLower()),
                userstatus = (request?.UserStatus ?? 0),
                subsources = request?.SubSources?.ConvertAll<string>(i => i.Replace(" ", "").ToLower()),
                localites = request?.Localites?.ConvertAll<string>(i => i.LrbNormalize()),
                cities = request?.Cities?.ConvertAll<string>(i => i.LrbNormalize()),
                states = request?.States?.ConvertAll<string>(i => i.LrbNormalize()),
                countries = request?.Countries?.ConvertAll<string>(i => i.Replace(" ", "").ToLower())
            }));

            return totalCount;
    }
}

    
    }

