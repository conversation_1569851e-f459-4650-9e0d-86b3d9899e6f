﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;

namespace Lrb.Application.Lead.Web.Dtos
{
    public class LeadNotesHistoryDto
    {
        public Guid? Id { get; set; }
        public Guid? LeadId { get; set; }
        public int CurrentVersion { get; set; }
        public string? Notes { get; set; }
        public string? LastModifiedBy { get; set; }
        public string? ModifiedDate { get; set; }
        public string? ConfidentialNotes { get; set; }
        [NotMapped]
        public IDictionary<int, string>? NotesDict =>
        !string.IsNullOrWhiteSpace(Notes)
            ? JsonSerializer.Deserialize<IDictionary<int, string>>(Notes)
            : null;

        [NotMapped]
        public IDictionary<int, string>? LastModifiedByDict =>
            !string.IsNullOrWhiteSpace(LastModifiedBy)
                ? JsonSerializer.Deserialize<IDictionary<int, string>>(LastModifiedBy)
                : null;

        [NotMapped]
        public IDictionary<int, DateTime>? ModifiedDateDict =>
            !string.IsNullOrWhiteSpace(ModifiedDate)
                ? JsonSerializer.Deserialize<IDictionary<int, DateTime>>(ModifiedDate)
                : null;

        [NotMapped]
        public IDictionary<int, string>? ConfidentialNotesDict =>
            !string.IsNullOrWhiteSpace(ConfidentialNotes)
                ? JsonSerializer.Deserialize<IDictionary<int, string>>(ConfidentialNotes)
                : null;
    }
}
