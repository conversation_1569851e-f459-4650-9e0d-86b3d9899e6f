﻿using Lrb.Application.Integration.Web.Requests.GoogleAds;
using Lrb.Domain.Entities.Integration;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;

namespace Lrb.Application.Dashboard.Web.Requests
{
    public class GetGoogleAdsMarketingFinanceDetailsRequest : GetAllDashboardParameterFilter, IRequest<PagedResponse<GoogleUserPerformanceDto, string>>
    {
        public Guid AccountId { get; set; }
        public string? Searchtext { get; set; }
        public bool? IsWithTeam { get; set; }
        public int PageNumber { get; set; } = 1;
        public int PageSize { get; set; } = int.MaxValue;
        public List<string>? Projects { get; set; }
    }
    public class GetGoogleAdsMarketingFinanceDetailsRequestHandler : IRequestHandler<GetGoogleAdsMarketingFinanceDetailsRequest, PagedResponse<GoogleUserPerformanceDto, string>>
    {
        private readonly IDapperRepository _dapperRepository;
        private readonly ICurrentUser _currentUser;
        private readonly IRepositoryWithEvents<FacebookLeadGenForm> _facebookLeadGenFormRepo;
        private readonly IRepositoryWithEvents<Lrb.Domain.Entities.Lead> _leadRepository;
        private readonly IRepositoryWithEvents<GoogleAdsAuthResponse> _googleadsAuthResponseRepo;
        private readonly IMediator _mediator;
        public GetGoogleAdsMarketingFinanceDetailsRequestHandler(IDapperRepository dapperRepository,
            ICurrentUser currentUser, IRepositoryWithEvents<FacebookLeadGenForm> facebookLeadGenFormRepo,
            IRepositoryWithEvents<Domain.Entities.Lead> leadRepository,
            IRepositoryWithEvents<GoogleAdsAuthResponse> googleadsAuthResponseRepo, IMediator mediator)
        {
            _dapperRepository = dapperRepository;
            _currentUser = currentUser;
            _facebookLeadGenFormRepo = facebookLeadGenFormRepo;
            _leadRepository = leadRepository;
            _googleadsAuthResponseRepo = googleadsAuthResponseRepo;
            _mediator = mediator;
        }
        public async Task<PagedResponse<GoogleUserPerformanceDto, string>> Handle(GetGoogleAdsMarketingFinanceDetailsRequest request, CancellationToken cancellationToken)
        {
            Guid currentUserId = _currentUser.GetUserId();
            var tenantId = _currentUser.GetTenant();
            List<Guid>? filterIds = new();
            List<Guid>? teamUserIds = new();
            if (request.UserIds?.Any() ?? false)
            {
                filterIds.AddRange(request.UserIds);
                if (request.IsWithTeam ?? false)
                {
                    teamUserIds = (await _dapperRepository.GetSubordinateIdsWithOnlyReporteesAsync(request.UserIds, tenantId ?? string.Empty))?.ToList() ?? new();
                    filterIds.AddRange(teamUserIds);
                }
            }
            else
            {
                switch (request.LeadVisibility)
                {
                    case LeadVisibility.Me:
                        filterIds = new() { currentUserId };
                        break;
                    case LeadVisibility.MyTeam:
                        filterIds = (await _dapperRepository.GetSubordinateIdsWithOnlyReporteesAsync(new List<Guid>() { currentUserId }, tenantId))?.ToList() ?? new();
                        break;
                    case LeadVisibility.Organization:
                        filterIds = (await _dapperRepository.GetSubordinateIdsForDashboardAsync(new List<Guid>() { currentUserId }, tenantId))?.ToList() ?? new();
                        break;
                }
            }

            List<GoogleUserPerformanceDto> userDtos = new List<GoogleUserPerformanceDto>();
            request.PageNumber = request.PageNumber == 0 ? 1 : request.PageNumber;
            var users = (await _dapperRepository.QueryStoredProcedureFromReadReplicaAsync<GoogleUserPerformanceDto>(
                "LeadratBlack", "GoogleAds_Finances_Report", new
                {
                    p_tenantid = tenantId,
                    p_pagesize = request.PageSize,
                    p_pagenumber = request.PageNumber,
                    p_searchtext = request.Searchtext,
                    p_userids = filterIds,
                    p_projects = request?.Projects?.ConvertAll<string>(i => i.Replace(" ", "").ToLower()),
                })).ToList();

            // Fetch Facebook leads for users
            var userIds = users.Where(x => x.UserId.HasValue).Select(x => x.UserId.Value).ToArray();
            var googleAdsLeads = (await _dapperRepository.QueryStoredProcedureFromReadReplicaAsync<GoogleAdLeadDto>(
                "LeadratBlack", "GetGoogleAdsLeadsByUser", new
                {
                    p_tenantid = tenantId,
                    p_userids = filterIds,
                    p_projects = request?.Projects?.ConvertAll<string>(i => i.Replace(" ", "").ToLower()),
                })).ToList();
            var campaignIds = googleAdsLeads.Select(x => x.CampaignId.ToString()).Distinct().ToList();
            var campaignAdsJson = JsonSerializer.Serialize(
                googleAdsLeads.Select(x => new { x.CampaignId}).Distinct());
            var metrics = (await _mediator.Send(new GetGoogleAdsCampaignAdMetricsRequest
            {
                FromDate = request.FromDate,
                ToDate = request.ToDate,
                CampaignIds = campaignIds,
                AccountId = request.AccountId,
                TimeZoneId = request.TimeZoneId,
                BaseUTcOffset = request.BaseUTcOffset
            }, cancellationToken)).Data.ToDictionary(x => (x.CampaignId));
            foreach (var user in users)
            {
                var userLeads = googleAdsLeads.Where(l => l.UserId == user.UserId).ToList();
                var leadsByAd = userLeads.GroupBy(l => new { l.CampaignId }).Select(g => new {CampaignId = g.Key.CampaignId,LeadCount = g.Count()}).ToList();
                user.CPLPerUser = leadsByAd.Sum(ad =>
                {
                    if (metrics.TryGetValue(ad.CampaignId, out var metric))
                    {
                        var cpl = metric?.CostPerLead ?? 0;
                        return cpl * ad.LeadCount;
                    }
                    return 0;
                });
                decimal totalSoldPrice = userLeads.Sum(l => (decimal)l.SoldPrice);
                var roi = totalSoldPrice > 0 && user.CPLPerUser > 0 ? ((totalSoldPrice - user.CPLPerUser) / user.CPLPerUser) * 100 : 0;
                user.ROI = Math.Round(roi ?? 0, 2);
                user.TotalRevenue = totalSoldPrice;
            }

            return new(users, users.Count());
        }
    }
}
