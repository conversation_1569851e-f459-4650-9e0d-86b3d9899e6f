﻿namespace Lrb.Application.Integration.Web
{
    public class WebsiteIntegrationDto
    {
        public string? Name { get; set; }
        public string? State { get; set; }
        public string? City { get; set; }
        public string? Location { get; set; }
        public string? Budget { get; set; }
        public string? Notes { get; set; }
        public string? Email { get; set; }
        public string? CountryCode { get; set; }
        public string? Mobile { get; set; }
        public string? Project { get; set; }
        public string? Property { get; set; }
        public string? LeadExpectedBudget { get; set; }
        public string? PropertyType { get; set; }
        public string? SubmittedDate { get; set; }
        public string? SubmittedTime { get; set; }
        public LeadSource LeadSource { get; set; }
        public string? Source { get; set; }
        public string? Subsource { get; set; }
        public string? AgencyName { get; set; }
        public IDictionary<string, string>? AdditionalProperties { get; set; }
        public string? LeadScheduledDate { get; set; }
        public string? LeadScheduleTime { get; set; }
        public string? LeadStatus { get; set; }
        public string? LeadBookedDate { get; set; }
        public string? LeadBookedTime { get; set; }
        public string? Currency {get;set;}
        // public Guid? TenantId { get; set; }
        public string? PrimaryUser { get; set; }
        public string? SecondaryUser { get; set; }
        public string? Designation { get; set; }
        public string? AlternateContactNo { get; set; }
        public string? CampaignName { get; set; }
        public string? ChannelPartnerName { get; set; }
        public string? ChannelPartnerMobile { get; set; }
        public string? ChannelPartnerEmail { get; set; }
    }
}
