﻿using Lrb.Application.DataCallLogs.Mobile.Dtos;
using Lrb.Application.DataManagement.Mobile.Dtos;
using Lrb.Application.DataManagement.Mobile.Specs;
using Lrb.Application.Identity.Users;
using Lrb.Application.Lead.Mobile.Specs.v1;
using Lrb.Application.Utils;
using Lrb.Domain.Entities.MasterData;
using Newtonsoft.Json;
using System.Globalization;

namespace Lrb.Application.DataManagement.Mobile.Requests
{
    public class GetProspectHistoriesRequest : IRequest<Response<List<ProspectHistoryDto>>>
    {
        public Guid ProspectId { get; set; }

        public bool? CanAccessAllProspects { get; set; }
        public GetProspectHistoriesRequest(Guid prospectId, bool? canAccessAllProspects)
        {
            ProspectId = prospectId;
            CanAccessAllProspects = canAccessAllProspects;
        }

    }

    public class GetProspectHistoriesRequestHandler : IRequestHandler<GetProspectHistoriesRequest, Response<List<ProspectHistoryDto>>>
    {
        public readonly IRepositoryWithEvents<Prospect> _prospectRepo;
        private readonly IRepositoryWithEvents<ProspectHistory> _prospectHistoryrepo;
        private readonly IRepositoryWithEvents<MasterAreaUnit> _masterAreaUnitRepo;
        private readonly IRepositoryWithEvents<ProspectCommunication> _prospectCommunicationRepo;
        private readonly IDapperRepository _dapperRepository;
        private readonly ICurrentUser _currentUser;
        private readonly IRepositoryWithEvents<ProspectCallLog> _prospectCallLogsRepo;
        private readonly IUserService _userService;
        private readonly IReadRepository<Domain.Entities.IVRCommonCallLog> _ivrCommonCallLogRepo;

        public GetProspectHistoriesRequestHandler(
            IRepositoryWithEvents<Prospect> prospectRepo,
            IRepositoryWithEvents<ProspectHistory> prospectHistoryRepo,
            IRepositoryWithEvents<MasterAreaUnit> masterAreaUnitRepo,
            IRepositoryWithEvents<ProspectCommunication> prospectCommunicationRepo,
            IDapperRepository dapperRepository,
            ICurrentUser currentUser,
            IRepositoryWithEvents<ProspectCallLog> prospectCallLogsRepo,
            IUserService userService,
            IReadRepository<Domain.Entities.IVRCommonCallLog> ivrCommonCallLogRepo)
        {
            _prospectRepo = prospectRepo;
            _prospectHistoryrepo = prospectHistoryRepo;
            _masterAreaUnitRepo = masterAreaUnitRepo;
            _prospectCommunicationRepo = prospectCommunicationRepo;
            _dapperRepository = dapperRepository;
            _currentUser = currentUser;
            _prospectCallLogsRepo = prospectCallLogsRepo;
            _userService = userService;
            _ivrCommonCallLogRepo = ivrCommonCallLogRepo;
        }
        public async Task<Response<List<ProspectHistoryDto>>> Handle(GetProspectHistoriesRequest request, CancellationToken cancellationToken)
        {
            var prospect = (await _prospectRepo.ListAsync(new GetProspectByIdSpecs(request.ProspectId), cancellationToken)).FirstOrDefault();
            var currentUserId = _currentUser.GetUserId();
            var tenantId = _currentUser.GetTenant();
            List<Guid> subIds = new();
            if (prospect != null)
            {
                List<ProspectHistory> histories = null;
                var isAdmin = await _dapperRepository.IsAdminAsync((_currentUser?.GetUserId() ?? Guid.Empty), _currentUser?.GetTenant() ?? string.Empty);
                if (isAdmin)
                {
                    histories = await _prospectHistoryrepo.ListAsync(new GetProspectHistoryByProspectIdSpecs(request.ProspectId));
                }
                else
                {
                    subIds = (await _dapperRepository.GetSubordinateIdsAsync(currentUserId, tenantId ?? string.Empty, request?.CanAccessAllProspects, isAdmin))?.ToList() ?? new();
                    subIds.AddRange(new List<Guid>() { currentUserId, prospect.AssignTo });
                    subIds = subIds?.Where(i => i != Guid.Empty)?.Distinct()?.ToList() ?? new List<Guid>();
                    if (prospect.AssignmentType == LeadAssignmentType.WithoutHistory || prospect.AssignmentType == LeadAssignmentType.WithoutHistoryWithNewStatus)
                    {
                        histories = await _prospectHistoryrepo.ListAsync(new GetProspectHistoryByProspectIdSpecs(request.ProspectId, subIds));
                    }
                    if (histories == null || histories.Count == 0) 
                    { histories = await _prospectHistoryrepo.ListAsync(new GetProspectHistoryByProspectIdSpecs(request.ProspectId)); }
                }
                List<ProspectCommunicationDto> communicationDto = new();
                //var communication = await _prospectCommunicationRepo.ListAsync(new GetProspectCommunicationByIdSpecs(request.ProspectId, subIds), cancellationToken);
                var communication = await _dapperRepository.GetProspectsCommunicationsForHistory(tenantId ?? string.Empty, request.ProspectId, subIds);
                if (communication?.Any() == true)
                {
                    List<Guid> userIds = communication.Select(i => i.LastModifiedBy).ToList();
                    var users = _userService.GetListAsync(cancellationToken).Result.Where(i => userIds.Contains(i.Id)).ToList();
                    communicationDto = communication.Adapt<List<ProspectCommunicationDto>>();
                    communicationDto.ForEach(i =>
                    {
                        var user = users.FirstOrDefault(j => j.Id == i.LastModifiedBy);
                        i.LastModifiedByUser = user?.FirstName + " " + user?.LastName;
                    });
                }
                var callLogs = await _prospectCallLogsRepo.ListAsync(new GetProspectCallLogsByProspectIdSpecs(request.ProspectId), cancellationToken);
                List<IVRCommonCallLog> ivrCommonCallLogs = new();
                if (isAdmin)
                {
                    ivrCommonCallLogs = await _ivrCommonCallLogRepo.ListAsync(new IVRCommonCallLogByProspectIdSpec(prospect.Id));
                }
                else
                {
                    ivrCommonCallLogs = await _ivrCommonCallLogRepo.ListAsync(new IVRCommonCallLogByProspectIdSpec(prospect.Id, prospect.AssignTo));
                    callLogs = callLogs?.Where(i => i.UserId == prospect.AssignTo).ToList();
                }
                var dataCallLogsDto = callLogs.Adapt<List<ProspectCallLogDto>>();
                if (ivrCommonCallLogs?.Any() ?? false)
                {
                    dataCallLogsDto.AddRange(ivrCommonCallLogs.Adapt<List<ProspectCallLogDto>>());
                }
                var userDtos = await _userService.GetListOfUsersByIdsAsync((dataCallLogsDto?.Select(i => i.UserId.ToString() ?? string.Empty).ToList() ?? new List<string>())?
                                                                            .Concat(communicationDto?.Select(i => i.LastModifiedBy.ToString())?.ToList() ?? new List<string>())?
                                                                            .ToList() ?? new List<string>(), cancellationToken);

                if (!histories?.Any() ?? true)
                {
                    return new(null, null);
                }

                List<ProspectHistory>? dataHistories = new();
                foreach (var history in histories)
                {
                    if (history.FieldType != typeof(Guid).Name)
                    {
                        if (history.FieldName != "Last Modified On" && history.FieldName != "Last Modified User")
                        {
                            if (history.NewValue != null || history.OldValue != null)
                            {
                                if (history.FieldName == "Created On" || history.FieldName == "Schedule Date" || history.FieldName == "Qualified Date")
                                {
                                    history.NewValue = GetUtcFormattedTime(history.NewValue ?? string.Empty);
                                    if (!string.IsNullOrEmpty(history.OldValue))
                                    {
                                        history.OldValue = GetUtcFormattedTime(history.OldValue ?? string.Empty);
                                    }
                                }
                                if (history.FieldName == "Possesion Date")
                                {
                                    history.NewValue = GetUtcFormattedTime(history.NewValue ?? string.Empty);
                                    if (!string.IsNullOrEmpty(history.OldValue))
                                    {
                                        history.OldValue = GetUtcFormattedTime(history.OldValue ?? string.Empty);
                                    }
                                }
                                else if (DateTime.TryParseExact(history.NewValue, "MM/dd/yyyy HH:mm:ss", CultureInfo.InvariantCulture, DateTimeStyles.None, out var dateTime))
                                {
                                    history.NewValue = GetUtcFormattedTime(history.NewValue ?? string.Empty);
                                    if (!string.IsNullOrEmpty(history.OldValue))
                                    {
                                        history.OldValue = GetUtcFormattedTime(history.OldValue ?? string.Empty);
                                    }
                                }
                                dataHistories.Add(history);
                            }
                        }
                    }
                }

                if (communicationDto != null)
                {
                    var historyItemsWithAppointmentDetails = AttachProspectCommunicationDetails(communicationDto);
                    dataHistories.AddRange(historyItemsWithAppointmentDetails);
                    dataHistories = dataHistories.OrderByDescending(i => i.ModifiedOn).ToList();
                }

                if (dataCallLogsDto?.Any() ?? false)
                {
                    var historyItemsWithCallDetails = AttachProspectCallLogsDetails(dataCallLogsDto, userDtos);
                    dataHistories.AddRange(historyItemsWithCallDetails);
                    dataHistories = dataHistories.OrderByDescending(i => i.ModifiedOn).ToList();
                }

                var datas = dataHistories.OrderByDescending(i => i.ModifiedOn);
                var dataHistoriesDto = dataHistories.OrderByDescending(i => i.ModifiedOn).Adapt<List<ProspectHistoryDto>>();
                //Dictionary<DateTime, List<ProspectHistory>> prospectHistories = new();
                //dataHistories.GroupBy(i => i.GroupKey).ToList();
                //dataHistories.ForEach(i => i.ModifiedOn = i?.ModifiedOn.Value.ToLocalTime("India Standard Time"));
                //var groups = dataHistories.GroupBy(i => i.ModifiedOn).ToList();
                //foreach (var group in groups)
                //{
                //    prospectHistories.Add(group.Key ?? DateTime.Now, group.ToList());
                //}
                //var dto = prospectHistories.Adapt<Dictionary<DateTime, List<ProspectHistoryDto>>>();

                return new(dataHistoriesDto ?? new());
            }
            else
            {
                throw new Exception("data not found by this id");
            }
        }


        private static List<ProspectHistory> AttachProspectCallLogsDetails(List<ProspectCallLogDto> callLogs, List<UserDetailsDto> userDtos)
        {
            List<ProspectHistory> leadHistoryDtos = new();
            foreach (var log in callLogs)
            {
                var user = userDtos.FirstOrDefault(i => i.Id == log.UserId);
                var leadHistoryDto = new ProspectHistory()
                {
                    FieldName = "DataCallLog",
                    //NewValue = $"{log.CallDirection} Call -> {log.CallStatus} -> {log.CallDuration} sec.",
                    NewValue = $"{(string.IsNullOrWhiteSpace(log.UpdatedCallDirection) ? log.CallDirection : log.UpdatedCallDirection)} Call -> {(string.IsNullOrWhiteSpace(log.UpdatedCallStatus) ? log.CallStatus : log.UpdatedCallStatus)} -> {FormatDuration(log.CallDuration.ToString())} sec. , CallRecordingUrl -> {(string.IsNullOrWhiteSpace(log.CallRecordingUrl) ? string.Empty : log.CallRecordingUrl)}",
                    OldValue = null,
                    ModifiedBy = user?.FirstName + " " + user?.LastName,
                    ModifiedOn = log.CreatedOn,
                };
                leadHistoryDtos.Add(leadHistoryDto);
            }
            return leadHistoryDtos;
        }


        private static List<ProspectHistory> AttachProspectCommunicationDetails(List<ProspectCommunicationDto>? prospectCommunicationDetails)
        {
            List<ProspectHistory> leadHistoryDtos = new();
            prospectCommunicationDetails = prospectCommunicationDetails?.Where(i => i.ContactType != ContactType.Call).ToList() ?? new();
            foreach (var communicationDetail in prospectCommunicationDetails)
            {
                var newValue = JsonConvert.SerializeObject(communicationDetail.Adapt<ViewProspectCommunicationForHistoryDto>());
                var leadHistoryDto = new ProspectHistory()
                {
                    FieldName = "Data Communication",
                    NewValue = communicationDetail.Message,
                    OldValue = null,
                    ModifiedBy = communicationDetail.LastModifiedByUser,
                    ModifiedOn = communicationDetail.CreatedOn,
                };
                leadHistoryDtos.Add(leadHistoryDto);
            }
            return leadHistoryDtos;
        }


        public static string? GetLocalDateOfHistory(string date)
        {
            DateTime? parseDate = null;
            if (!string.IsNullOrEmpty(date))
            {
                string[] dateFormats = { "dd-MM-yy HH:mm:ss", "dd-MM-yyyy HH:mm:ss", "dd/MM/yy HH:mm:ss", "dd/MM/yyyy HH:mm:ss", "yyyy-MM-dd HH:mm:ss", "MM/dd/yyyy HH:mm:ss", "dd-MMM-yyyy HH:mm:ss", "dddd, MMMM dd, yyyy HH:mm:ss" };
                if (DateTime.TryParseExact(date, dateFormats, null, System.Globalization.DateTimeStyles.None, out DateTime dateTime))
                {
                    parseDate = dateTime;
                }

                if (parseDate != null)
                {
                    return (DateTimeExtensions.ToIndianStandardTime(parseDate ?? default)).ToString();
                }
                else
                {
                    return null;
                }
            }
            else
            {
                return null;
            }

        }

        public static string? RemoveTimeFromDate(string date)
        {

            DateTime? parseDate = null;
            if (!string.IsNullOrEmpty(date))
            {
                string[] dateFormats = { "dd-MM-yy HH:mm:ss", "dd-MM-yyyy HH:mm:ss", "dd/MM/yy HH:mm:ss", "dd/MM/yyyy HH:mm:ss", "yyyy-MM-dd HH:mm:ss", "MM/dd/yyyy HH:mm:ss", "dd-MMM-yyyy HH:mm:ss", "dddd, MMMM dd, yyyy HH:mm:ss" };
                if (DateTime.TryParseExact(date, dateFormats, null, System.Globalization.DateTimeStyles.None, out DateTime dateTime))
                {
                    parseDate = dateTime;
                }
                if (parseDate != null)
                {
                    return parseDate.Value.ToString("dd/MM/yyyy");
                }
                else
                {
                    return null;
                }
            }
            else
            {
                return null;
            }
        }
        public static string? GetUtcFormattedTime(string dateString)
        {
            try
            {
                if (!string.IsNullOrEmpty(dateString))
                {
                    var isDateTime = DateTime.TryParseExact(dateString, "MM/dd/yyyy HH:mm:ss", CultureInfo.InvariantCulture, DateTimeStyles.None, out DateTime localDateTime);
                    if (isDateTime)
                    {
                        DateTime utcDateTime = localDateTime.ToUniversalTime();
                        return utcDateTime.ToString("yyyy-MM-ddTHH:mm:ss.fffffffZ");
                    }
                }
                return string.Empty;

            }
            catch (Exception ex)
            {
                return string.Empty;
            }

        }
        private static string FormatDuration(string durationStr)
        {
            if (double.TryParse(durationStr, out double duration))
            {
                int durationInSeconds = (int)Math.Floor(duration); // or Math.Round(duration)
                int minutes = durationInSeconds / 60;
                int seconds = durationInSeconds % 60;

                if (minutes > 0 && seconds > 0)
                    return $"{minutes} min {seconds} sec";
                else if (minutes > 0)
                    return $"{minutes} min";
                else
                    return $"{seconds} sec";
            }

            return durationStr; // fallback if parsing fails
        }
    }
}
