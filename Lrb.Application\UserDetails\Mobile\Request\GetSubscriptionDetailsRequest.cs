﻿namespace Lrb.Application.UserDetails.Mobile.Request
{
    public class GetSubscriptionDetailsRequest : IRequest<Response<SubscriptionDetails>>
    {
        public string? TenantId {  get; set; }
        public Guid? UserId { get; set; }
        public GetSubscriptionDetailsRequest(Guid? userId, string? tenantId)
        {
            UserId = userId;
            TenantId = tenantId;
        }
    }
    public class GetSubscriptionDetailsRequestHandle : IRequestHandler<GetSubscriptionDetailsRequest, Response<SubscriptionDetails>>
    {
        private readonly IDapperRepository _dapperRepository;
        public GetSubscriptionDetailsRequestHandle(IDapperRepository dapperRepository)
        {
            _dapperRepository = dapperRepository;
        }

        public async Task<Response<SubscriptionDetails>> Handle(GetSubscriptionDetailsRequest request, CancellationToken cancellationToken)
        {

            var result = (await _dapperRepository.QueryStoredProcedureFromMainDbAsync<SubscriptionDetails>(
                "LeadratBlack",
                "GetSubscriptionAndAdminStatus",
                new
                {
                    uid = request.UserId,
                    tenant = request.TenantId
                })).FirstOrDefault();
            return new(result ?? new());
        }
    }
}
