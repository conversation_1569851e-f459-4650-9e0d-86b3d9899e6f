﻿using Amazon.Runtime.Internal.Transform;
using DocumentFormat.OpenXml.Spreadsheet;
using Lrb.Application.Reports.Web.Dtos.DateWiseSource;
using Lrb.Application.Utils;
using Lrb.Domain.Entities;
using Lrb.Shared.Extensions;
using Microsoft.Azure.Cosmos.Linq;
using System.Linq;

namespace Lrb.Application.Reports.Web.Requests.DatewiseSourceCount
{
    public class GetLeadDateBySourceCountRequest : IRequest<PagedResponse<Dictionary<string, object>, string>>

    {
        public DateType? DateType { get; set; }
        public bool? IsWithTeam { get; set; }
        public List<LeadSource>? LeadSources { get; set; }
        public ReportPermission? ReportPermission { get; set; }
        public List<string>? Projects { get; set; }
        public List<string>? SubSources { get; set; }
        public DateTime? FromDate { get; set; }
        public DateTime? ToDate { get; set; }
        public List<Guid>? UserIds { get; set; }
        public List<string>? Localites { get; set; }
        public List<string>? States { get; set; }
        public List<string>? Cities { get; set; }
        public DateTime? ToDateForLeadReceived { get; set; }
        public DateTime? FromDateForLeadReceived { get; set; }
        public LeadGeneratingFrom GeneratingFrom { get; set; }
        public string? SearchText { get; set; }
        public int PageNumber { get; set; } 
        public int PageSize { get; set; } = int.MaxValue;
        public string? TimeZoneId { get; set; }
        public TimeSpan BaseUTcOffset { get; set; }
        public List<string>? Countries { get; set; }

    }


    public class GetLeadDateBySourceCountRequestHandler : IRequestHandler<GetLeadDateBySourceCountRequest, PagedResponse<Dictionary<string, object>, string>>
    {
        private readonly IDapperRepository _dapperRepository;
        private readonly ICurrentUser _currentUser;

        public GetLeadDateBySourceCountRequestHandler(IDapperRepository dapperRepository, ICurrentUser currentUser)
        {
            _dapperRepository = dapperRepository;
            _currentUser = currentUser;
        }

        public async Task<PagedResponse<Dictionary<string, object>, string>> Handle(GetLeadDateBySourceCountRequest request, CancellationToken cancellationToken)
        {
            var tenantId = _currentUser.GetTenant();
            var userId = _currentUser.GetUserId();
            List<Guid> teamUserIds = new();
            List<Guid> permittedUserIds = new();
            var isAdmin = await _dapperRepository.IsAdminAsync(userId, tenantId ?? string.Empty);
            if (isAdmin)
            {
                permittedUserIds = (await _dapperRepository.GetAllUserIdsAsync(tenantId ?? string.Empty)).ToList();
            }
            else if (request.ReportPermission != null)
            {
                switch (request.ReportPermission)
                {
                    case ReportPermission.All:
                        permittedUserIds = (await _dapperRepository.GetAllUserIdsAsync(tenantId ?? string.Empty)).ToList();
                        break;
                    case ReportPermission.Reportees:
                        permittedUserIds = (await _dapperRepository.GetSubordinateIdsWithOnlyReporteesAsync(new List<Guid>() { userId }, tenantId ?? string.Empty)).ToList();
                        break;
                }
            }
            if (request?.UserIds?.Any() ?? false)
            {
                if (request?.IsWithTeam ?? false)
                {
                    teamUserIds = (await _dapperRepository.GetSubordinateIdsWithOnlyReporteesAsync(request.UserIds, tenantId ?? string.Empty)).ToList();
                }
                else
                {
                    teamUserIds = request?.UserIds ?? new List<Guid>();
                }
            }
            else
            {
                if (!isAdmin)
                {
                    teamUserIds = permittedUserIds;
                }
            }
            if (teamUserIds.Any())
            {
                teamUserIds = teamUserIds.Where(userId => permittedUserIds.Contains(userId)).ToList();
            }
            else
            {
                teamUserIds = permittedUserIds;
            }



            request.FromDate = request.FromDate.HasValue ? request.FromDate.Value.ConvertFromDateToUtc() : null;
            request.ToDate = request.ToDate.HasValue ? request.ToDate.Value.ConvertFromDateToUtc() : null;
            request.FromDateForLeadReceived = request.FromDateForLeadReceived.HasValue ? request.FromDateForLeadReceived.Value.ConvertFromDateToUtc() : null;
            request.ToDateForLeadReceived = request.ToDateForLeadReceived.HasValue ? request.ToDateForLeadReceived.Value.ConvertToDateToUtc() : null;
            IEnumerable<LeadReceivedBySourceDto> leadReportByYear = (await _dapperRepository.QueryStoredProcedureFromReadReplicaAsync<LeadReceivedBySourceDto>("LeadratBlack", "GetOut", new

            {
                lead_source = request?.LeadSources?.ConvertAll(i => (int)i),
                user_id = teamUserIds,
                date_type = request?.DateType,
                from_date = request?.FromDate,
                to_date = request?.ToDate,
                from_date_for_lead_recived = request?.FromDateForLeadReceived,
                to_date_for_lead_recived = request.ToDateForLeadReceived,
                tenant_id = tenantId,
                lead_generating_from = (request?.GeneratingFrom ?? 0),
                projects = request?.Projects,
                subsources = request?.SubSources?.ConvertAll<string>(i => i.Replace(" ", "")),
                searchtext = request?.SearchText,
                pagesize = request?.PageSize,
                pagenumber = request?.PageNumber,
                localites = request?.Localites?.ConvertAll<string>(i => i.LrbNormalize()),
                cities = request?.Cities?.ConvertAll<string>(i => i.LrbNormalize()),
                states = request?.States?.ConvertAll<string>(i => i.LrbNormalize()),
                countries = request?.Countries?.ConvertAll<string>(i => i.Replace(" ", "").ToLower())
            }));

            Dictionary<string, int> topSources = GroupLeadsBySource(leadReportByYear);


            var groupedLeadsByDay = GroupLeadsByDay(leadReportByYear, topSources, request.TimeZoneId, request.BaseUTcOffset);

            int offset = (request.PageSize * (request.PageNumber - 1));

            var pagedGroupedLeadsByDate = groupedLeadsByDay
               .OrderByDescending(dictionary => dictionary["CreatedOn"])
               .Skip(offset)
               .Take(request.PageSize)
               .ToList();

            var convertedItems = pagedGroupedLeadsByDate.Select(dayGroup =>
            {
                var convertedGroup = new Dictionary<string, int>(); 

                foreach (var kvp in dayGroup)
                {
                    if (kvp.Key == "CreatedOn")
                        continue;

                    convertedGroup.Add(kvp.Key, (int)kvp.Value); 
                }

                var resultDictionary = new Dictionary<string, object>();
                resultDictionary.Add("CreatedOn", (string)dayGroup["CreatedOn"]); 

                foreach (var kvp in convertedGroup)
                {
                    resultDictionary.Add(kvp.Key, kvp.Value); 
                }

                return resultDictionary;
            });

            return new PagedResponse<Dictionary<string, object>, string>
            {
                Items = convertedItems,
                Succeeded = true,
                TotalCount = 0
            };


        }
        static List<Dictionary<string, object>> GroupLeadsByDay(IEnumerable<LeadReceivedBySourceDto> leadReportByYear, Dictionary<string, int> topSources,string? timeZoneId,TimeSpan baseUTcOffset)
        {
            var groupedLeadsByDay = leadReportByYear
                .GroupBy(lead => lead.CreatedOn.ToParticularTimeZone(timeZoneId,baseUTcOffset).Date.ConvertDateTime(timeZoneId,baseUTcOffset).ToString("yyyy-MM-ddTHH:mm:ss.fffffffZ"))
                .Select(dayGroup =>
                {
                    var dayDictionary = new Dictionary<string, object>();
                    dayDictionary.Add("CreatedOn", dayGroup.Key);
                    foreach (var source in topSources)
                    {
                        dayDictionary.Add(source.Key, dayGroup.Sum(lead => lead.Source == (LeadSource)Enum.Parse(typeof(LeadSource), source.Key) ? lead.LeadCount : 0));
                    }
                    return dayDictionary;
                })
                .ToList();
            return groupedLeadsByDay;
        }
        static Dictionary<string, int> GroupLeadsBySource(IEnumerable<LeadReceivedBySourceDto> leadReport)
        {
            return leadReport
                .Select(lead => new { Source = lead.Source.ToString(), LeadCount = lead.LeadCount })
                .GroupBy(item => item.Source)
                .ToDictionary(group => group.Key, group => group.Sum(item => item.LeadCount))
                .OrderByDescending(item => item.Value)
                .Take(Enum.GetValues(typeof(LeadSource)).Length)
                .ToDictionary(item => item.Key, item => item.Value);
        }
    }
}
