﻿using Lrb.Application.Common.Persistence.New_Implementation;
using Lrb.Application.Identity.Users;
using Lrb.Application.Property.Web.Specs;
using Lrb.Domain.Entities.ErrorModule;
using Lrb.Domain.Entities.MasterData;
using Newtonsoft.Json;

namespace Lrb.Application.Property.Web.Requests
{
    public class V2GetAllPropertyCountRequest : GetAllPropertyParameters, IRequest<Response<PropertyCountDto>>
    {
    }

    public class V2GetAllPropertyCountRequestHandler : IRequestHandler<V2GetAllPropertyCountRequest, Response<PropertyCountDto>>
    {
        private readonly IPropertyRepository _efPropertyRepository;
        private readonly IUserService _userService;
        private readonly IDapperRepository _dapperRepository;
        private readonly ICurrentUser _currentUser;
        private readonly ILeadRepositoryAsync _leadRepositoryAsync;
        private readonly IRepositoryWithEvents<MasterPropertyType> _masterPropertyTypeRepo;
        private readonly IReadRepository<MasterAreaUnit> _masterAreaUnitRepo;
        private readonly IReadRepository<PropertyDimension> _propertyDimensionRepo;
        public V2GetAllPropertyCountRequestHandler(

            IPropertyRepository efPropertyRepository,
            IUserService userService,
            IDapperRepository dapperRepository,
            ICurrentUser currentUser,
            ILeadRepositoryAsync leadRepositoryAsync,
            IRepositoryWithEvents<MasterPropertyType> masterPropertyTypeRepo,
           IReadRepository<MasterAreaUnit> masterAreaUnitRepo,
           IReadRepository<PropertyDimension> propertyDimensionRepo)

        {
            _efPropertyRepository = efPropertyRepository;
            _dapperRepository = dapperRepository;
            _currentUser = currentUser;
            _userService = userService;
            _leadRepositoryAsync = leadRepositoryAsync;
            _masterPropertyTypeRepo = masterPropertyTypeRepo;
            _masterAreaUnitRepo = masterAreaUnitRepo;
            _propertyDimensionRepo = propertyDimensionRepo;

        }

        public async Task<Response<PropertyCountDto>> Handle(V2GetAllPropertyCountRequest request, CancellationToken cancellationToken)
        {
            if (request != null && request.Permission == ViewAssignmentsPermission.None)
            {
                return new(null);
            }
            List<Guid>? userIds = new();
            List<Guid>? filterIds = new();
            List<Guid>? teamUserIds = new();
            List<Guid>? propertyDimensionIds = new();
            var tenantId = _currentUser.GetTenant();
            var currentUserId = _currentUser.GetUserId();
            bool showAllProperties = false;


            try
            {
                switch (request?.Permission)
                {
                    case ViewAssignmentsPermission.View:
                        if (request.UserIds?.Any() ?? false)
                        {
                            filterIds.AddRange(request.UserIds);
                            if (request.IsWithTeam ?? false)
                            {
                                teamUserIds = (await _dapperRepository.GetSubordinateIdsWithOnlyReporteesAsync(request.UserIds, tenantId ?? string.Empty))?.ToList() ?? new();
                                filterIds.AddRange(teamUserIds);
                            }
                            userIds.AddRange(filterIds);
                        }
                        else
                        {
                            userIds = (await _dapperRepository.GetAllUserIdsAsync(tenantId ?? string.Empty)).ToList();
                            showAllProperties = true;
                        }
                        break;
                    case ViewAssignmentsPermission.ViewAssigned:
                        userIds.Add(currentUserId);
                        break;
                    default:
                        if (request?.UserIds?.Any() ?? false)
                        {
                            filterIds.AddRange(request.UserIds);
                            if (request.IsWithTeam ?? false)
                            {
                                teamUserIds = (await _dapperRepository.GetSubordinateIdsWithOnlyReporteesAsync(request.UserIds, tenantId ?? string.Empty))?.ToList() ?? new();
                                filterIds.AddRange(teamUserIds);
                            }
                            userIds.AddRange(filterIds);
                        }
                        else
                        {
                            userIds = (await _dapperRepository.GetAllUserIdsAsync(tenantId ?? string.Empty)).ToList();
                            showAllProperties = true;
                        }
                        break;
                }
            }
            catch (Exception ex)
            {
                var error = new LrbError()
                {
                    ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                    ErrorSource = ex?.Source,
                    StackTrace = ex?.StackTrace,
                    InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                    ErrorModule = "GetAllPropertyRequestHandler -> Handle()"
                };
                await _leadRepositoryAsync.AddErrorAsync(error);
            }

            (List<Guid>, bool) result = new();
            NumericAttributesDto numericAttributeDto = new();
            List<int> noOfAttributes = Enumerable.Range(1, 5).ToList();


            try
            {

                var tasks = new Task[]
                {
               // Task.Run(async () => propertyDimensionIds = await GetPropertyDimensionIdsAsync(request  ?? new())),
                Task.Run(async () => numericAttributeDto = await InitializeNumericAttributes(noOfAttributes, request))
                };
                await Task.WhenAll(tasks);
            }
            catch (Exception ex)
            {
                var error = new LrbError()
                {
                    ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                    ErrorSource = ex?.Source,
                    StackTrace = ex?.StackTrace,
                    InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                    ErrorModule = "GetAllPropertyRequestHandler -> Handle()"
                };
                await _leadRepositoryAsync.AddErrorAsync(error);
            }


            var propertyCountDto = new PropertyCountDto();
            // var propertyNames = typeof(PropertyCountDto).GetProperties().Select(p => p.Name);

            var residentialPropertyType = (await _masterPropertyTypeRepo.FirstOrDefaultAsync(new GetMasterPropertyTypeByTypeSpec("residential")));
            var agriculturalPropertyType = (await _masterPropertyTypeRepo.FirstOrDefaultAsync(new GetMasterPropertyTypeByTypeSpec("agricultural")));
            var commercialPropertyType = (await _masterPropertyTypeRepo.FirstOrDefaultAsync(new GetMasterPropertyTypeByTypeSpec("commercial")));
            PropertyTypeBaseId propertyTypeIds = new();

            propertyTypeIds.ResidentialBaseId = residentialPropertyType.Id;
            propertyTypeIds.AgricultureBaseId = agriculturalPropertyType.Id;
            propertyTypeIds.CommercialBaseId = commercialPropertyType.Id;
            List<Guid> propertyIds = new();
            if (request.MinLeadCount != null || request.MaxLeadCount != null ||
                request.MinProspectCount != null || request.MaxProspectCount != null)
            {
                var property = (await _dapperRepository.QueryStoredProcedureFromReadReplicaAsync<PropertyIdsDto>("LeadratBlack", "Lead&Prospects_PropertiesAssociatedCountFilter",
                    new
                    {
                        tenantid = tenantId,
                        minprospectcount = request.MinProspectCount,
                        maxprospectcount = request.MaxProspectCount,
                        minleadcount = request.MinLeadCount,
                        maxleadcount = request.MaxLeadCount
                    })).FirstOrDefault()?.PropertyIds ?? new List<Guid>();
                propertyIds = property.ToList();
            }

            propertyCountDto = await AddPropertyCountAsync(propertyCountDto, propertyTypeIds, request, propertyDimensionIds, numericAttributeDto, userIds, showAllProperties, tenantId, propertyIds);
            return new Response<PropertyCountDto>(propertyCountDto);
        }
        public async Task<PropertyCountDto> AddPropertyCountAsync(PropertyCountDto propertyCount, PropertyTypeBaseId? propertyTypeIds, V2GetAllPropertyCountRequest filter, List<Guid>? propertyDimensionIds, NumericAttributesDto numericAttributesDto, List<Guid>? userIds, bool showAllProperties, string? tenantId = null, List<Guid>? propertyIds = null)
        {
            List<CustomPropertyAttributeDto> attributes = new();
            if (filter?.NoOfFloors != null || filter?.NoOfKitchens != null || filter?.NoOfUtilites != null || filter?.NoOfBedrooms != null || filter?.NoOfLivingrooms != null || filter?.NoOfBalconies != null || filter?.NoOfBathrooms != null || filter?.Parking != null)
            {
                attributes = await _dapperRepository.GetAttributeDetails(tenantId ?? string.Empty);
            }
            PropertyCountDto propertyCountDto = await _efPropertyRepository.GetPropertyTopLevelCountAsync(propertyTypeIds, filter, propertyDimensionIds, numericAttributesDto, userIds, showAllProperties, tenantId, propertyIds, attributes);
            propertyCount.ResidentialPropertiesCount = propertyCountDto.ResidentialPropertiesCount;
            propertyCount.CommercialPropertiesCount = propertyCountDto.CommercialPropertiesCount;
            propertyCount.AgriculturalPropertiesCount = propertyCountDto.AgriculturalPropertiesCount;
            propertyCount.AllPropertiesCount = propertyCountDto.AllPropertiesCount;
            return propertyCountDto;

        }
        private async Task<List<Guid>> GetPropertyDimensionIdsAsync(V2GetAllPropertyCountRequest request)
        {
            if (request == null || request.PropertySize == null) return new List<Guid>();

            var propertyDimensionIds = new List<Guid>();
            var propertySize = request.PropertySize;

            try
            {
                var areaConversionFactor = await GetConversionFactorAsync(propertySize?.AreaUnitId ?? Guid.Empty);
                var carpetAreaConversionFactor = await GetConversionFactorAsync(propertySize?.CarpetAreaId ?? Guid.Empty);
                var buildUpAreaConversionFactor = await GetConversionFactorAsync(propertySize?.BuildUpAreaId ?? Guid.Empty);
                var saleableAreaConversionFactor = await GetConversionFactorAsync(propertySize?.SaleableAreaId ?? Guid.Empty);
                var netAreaConversionFactor = await GetConversionFactorAsync(propertySize?.NetAreaUnitId ?? Guid.Empty);

                if (propertySize?.Area != default && areaConversionFactor != default)
                {
                    propertySize.ConversionFactor = areaConversionFactor;
                    var area = propertySize?.Area * propertySize.ConversionFactor;
                    propertyDimensionIds.AddRange(await FilterPropertyDimensionsAsync(area ?? default, "Area"));
                }

                if (propertySize?.CarpetArea != default && carpetAreaConversionFactor != default)
                {
                    propertySize.ConversionFactor = carpetAreaConversionFactor;
                    var carpetArea = propertySize?.CarpetArea * propertySize.ConversionFactor;
                    propertyDimensionIds.AddRange(await FilterPropertyDimensionsAsync(carpetArea ?? default, "CarpetArea"));
                }

                if (propertySize?.BuildUpArea != default && buildUpAreaConversionFactor != default)
                {
                    propertySize.ConversionFactor = buildUpAreaConversionFactor;
                    var buildUpArea = propertySize?.BuildUpArea * propertySize.ConversionFactor;
                    propertyDimensionIds.AddRange(await FilterPropertyDimensionsAsync(buildUpArea ?? default, "BuildUpArea"));
                }

                if (propertySize?.SaleableArea != default && saleableAreaConversionFactor != default)
                {
                    propertySize.ConversionFactor = saleableAreaConversionFactor;
                    var saleableArea = propertySize?.SaleableArea * propertySize.ConversionFactor;
                    propertyDimensionIds.AddRange(await FilterPropertyDimensionsAsync(saleableArea ?? default, "SaleableArea"));
                }
                if (propertySize?.NetArea != default && netAreaConversionFactor != default)
                {
                    propertySize.ConversionFactor = netAreaConversionFactor;
                    var netarea = propertySize?.NetArea * propertySize.ConversionFactor;
                    propertyDimensionIds.AddRange(await FilterPropertyDimensionsAsync(netarea ?? default, "SaleableArea"));
                }

            }
            catch (Exception ex)
            {
                var error = new LrbError()
                {
                    ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                    ErrorSource = ex?.Source,
                    StackTrace = ex?.StackTrace,
                    InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                    ErrorModule = "GetAllPropertyRequestHandler -> Handle()"
                };
                await _leadRepositoryAsync.AddErrorAsync(error);
            }
            return propertyDimensionIds;
        }


        private async Task<float> GetConversionFactorAsync(Guid areaUnitId)
        {
            var masterAreaUnit = await _masterAreaUnitRepo.GetByIdAsync(areaUnitId);
            return masterAreaUnit?.ConversionFactor ?? default;
        }

        private async Task<List<Guid>> FilterPropertyDimensionsAsync(double areaValue, string areaType)
        {
            var propertyDimensions = await _propertyDimensionRepo.ListAsync();
            List<Guid>? propertyDimensionIds = new();
            if (areaType == "Area")
            {
                propertyDimensionIds = propertyDimensions
                   .Where(i => i.Area * PropertySearchHelper.GetConversionFactor(i.AreaUnitId, _masterAreaUnitRepo).Result == areaValue)
                      .Select(i => i.Id)
                      .ToList();
            }
            if (areaType == "CarpetArea")
            {
                propertyDimensionIds = propertyDimensions
                   .Where(i => i.CarpetArea * PropertySearchHelper.GetConversionFactor(i.CarpetAreaId ?? Guid.Empty, _masterAreaUnitRepo).Result == areaValue)
                      .Select(i => i.Id)
                      .ToList();
            }
            if (areaType == "BuildUpArea")
            {
                propertyDimensionIds = propertyDimensions
                   .Where(i => i.BuildUpArea * PropertySearchHelper.GetConversionFactor(i.BuildUpAreaId ?? Guid.Empty, _masterAreaUnitRepo).Result == areaValue)
                      .Select(i => i.Id)
                      .ToList();
            }
            if (areaType == "SaleableArea")
            {
                propertyDimensionIds = propertyDimensions
                   .Where(i => i.SaleableArea * PropertySearchHelper.GetConversionFactor(i.SaleableAreaId ?? Guid.Empty, _masterAreaUnitRepo).Result == areaValue)
                      .Select(i => i.Id)
                      .ToList();
            }
            if (areaType == "NetArea")
            {
                propertyDimensionIds = propertyDimensions
                   .Where(i => i.NetArea * PropertySearchHelper.GetConversionFactor(i.NetAreaUnitId ?? Guid.Empty, _masterAreaUnitRepo).Result == areaValue)
                      .Select(i => i.Id)
                      .ToList();
            }

            return propertyDimensionIds;
        }


        private async Task<NumericAttributesDto> InitializeNumericAttributes(List<int> noOfAttributes, V2GetAllPropertyCountRequest request)
        {
            return new NumericAttributesDto
            {
                NoOfFloor = FilterNumericAttributes(request.NoOfFloor, noOfAttributes),
                NoOfBathrooms = FilterNumericAttributes(request.NoOfBathrooms, noOfAttributes),
                NoOfBedrooms = FilterNumericAttributes(request.NoOfBedrooms, noOfAttributes),
                NoOfKitchens = FilterNumericAttributes(request.NoOfKitchens, noOfAttributes),
                NoOfUtilites = FilterNumericAttributes(request.NoOfUtilites, noOfAttributes),
                NoOfLivingrooms = FilterNumericAttributes(request.NoOfLivingrooms, noOfAttributes),
                NoOfBalconies = FilterNumericAttributes(request.NoOfBalconies, noOfAttributes),
                NoOfFloors = FilterNumericAttributesV1(request.NoOfFloors, noOfAttributes),
                Parking = FilterNumericAttributes(request.Parking, noOfAttributes),


            };
        }

        private NoOfAttributeFilterDto FilterNumericAttributesV1(List<string>? requestValues, List<int> noOfAttributes)
        {
            NoOfAttributeFilterDto noOfAttributesDto = new NoOfAttributeFilterDto();
            var noOfFloorFilterList = new List<int>();
            var selectedAttributes = new List<string>();
            if (requestValues != null)
            {
                if (requestValues.Contains("Ground Floor"))
                {
                    selectedAttributes.Add("Ground Floor");
                }
                if (requestValues.Contains("5"))
                {
                    noOfFloorFilterList = noOfAttributes.Where(i => !requestValues.Contains(i.ToString())).ToList();
                    selectedAttributes.AddRange(noOfFloorFilterList.Select(i => i.ToString()));
                    noOfAttributesDto.IsMaxValueIncluded = true;
                }
                else
                {
                    noOfFloorFilterList = noOfAttributes.Where(i => requestValues.Contains(i.ToString())).ToList();
                    selectedAttributes.AddRange(noOfFloorFilterList.Select(i => i.ToString()));
                    noOfAttributesDto.IsMaxValueIncluded = false;
                }
                noOfAttributesDto.NoOfAttributes = selectedAttributes;
            }

            return noOfAttributesDto;
        }

        private NoOfAttributeFilterDto FilterNumericAttributes(List<int>? requestValues, List<int> noOfAttributes)
        {
            NoOfAttributeFilterDto noOfAttributesDto = new NoOfAttributeFilterDto();
            var noOfFloorFilterList = new List<int>();

            if (requestValues != null)
            {
                if (requestValues.Contains(5))
                {
                    noOfFloorFilterList = noOfAttributes.Where(i => !requestValues.Contains(i)).ToList();
                    noOfAttributesDto.NoOfAttributes = noOfFloorFilterList.Select(i => i.ToString()).ToList();
                    noOfAttributesDto.IsMaxValueIncluded = true;
                }
                else
                {
                    noOfFloorFilterList = noOfAttributes.Where(i => requestValues.Contains(i)).ToList();
                    noOfAttributesDto.NoOfAttributes = noOfFloorFilterList.Select(i => i.ToString()).ToList();
                    noOfAttributesDto.IsMaxValueIncluded = false;
                }
            }

            return noOfAttributesDto;
        }
    }
}



