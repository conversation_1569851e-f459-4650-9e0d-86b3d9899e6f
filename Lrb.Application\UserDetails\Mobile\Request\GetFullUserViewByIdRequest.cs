﻿using Lrb.Application.OrgProfile.Mobile;

namespace Lrb.Application.UserDetails.Mobile.Request
{
    public class GetFullUserViewByIdRequest : IRequest<Response<UserDetailsDto>>
    {
        public Guid UserId { get; set; }
        public string TenantId { get; set; }
        public GetFullUserViewByIdRequest(Guid userId, string tenantId)
        {
            UserId = userId;
            TenantId = tenantId;
        }
    }
    public class GetFullUserViewByIdRequestHandler : IRequestHandler<GetFullUserViewByIdRequest, Response<UserDetailsDto>>
    {
        private readonly IRepositoryWithEvents<FullUserView> _userViewRepo;
        private readonly IRepositoryWithEvents<Domain.Entities.Profile> _profileRepo;
        private readonly IDapperRepository _dapperRepository;
        public GetFullUserViewByIdRequestHandler(IRepositoryWithEvents<FullUserView> userViewRepo,
            IRepositoryWithEvents<Profile> profileRepo, IDapperRepository dapperRepository)
        {
            _userViewRepo = userViewRepo;
            _profileRepo = profileRepo;
            _dapperRepository = dapperRepository;
        }
        public async Task<Response<UserDetailsDto>> Handle(GetFullUserViewByIdRequest request, CancellationToken cancellationToken)
        {
            try
            {
                var result = (await _dapperRepository.QueryStoredProcedureFromMainDbAsync<SubscriptionDetails>(
                "LeadratBlack",
                "GetSubscriptionAndAdminStatus",
                new
                {
                    uid = request.UserId,
                    tenant = request.TenantId
                })).FirstOrDefault();
                var userData = await _userViewRepo.GetByIdAsync(request.UserId) ?? throw new NotFoundException("No User found by the provided Id.");
                var profile = await _profileRepo.FirstOrDefaultAsync(new GetOnlyProfileSpec());
                var leadCount = 0;
                var userDto = userData.Adapt<UserDetailsDto>();
                userDto.OrganizationName = profile?.DisplayName ?? "";
                userDto.LeadCount = leadCount;
                userDto.SubscriptionDetails = result;
                return new(userDto);
            }
            catch (Exception ex)
            {
                return new();
            }
        }
    }
}