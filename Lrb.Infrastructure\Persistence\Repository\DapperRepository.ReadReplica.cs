﻿using Dapper;
using Npgsql;
using System.Data;

/// <summary>
/// Repository class for executing stored procedures on a read replica database using Dapper.
/// </summary>
partial class DapperRepository
{
    /// <summary>
    /// Executes a stored procedure on the read replica and returns the result as an enumerable of type T.
    /// </summary>
    /// <typeparam name="T">The type of the result.</typeparam>
    /// <param name="schemaName">The schema name of the stored procedure.</param>
    /// <param name="spName">The name of the stored procedure.</param>
    /// <param name="param">The parameters to pass to the stored procedure.</param>
    /// <param name="commandTimeout">The command timeout (in seconds).</param>
    /// <returns>An enumerable of type T containing the result of the stored procedure.</returns>
    public async Task<IEnumerable<T>> QueryStoredProcedureFromReadReplicaAsync<T>(string schemaName, string spName, object param, int? commandTimeout = 60)
    {
        await using var conn = new NpgsqlConnection(GetReadReplicaConnectionString());
        try
        {
            var formattedSPName = $"\"{schemaName}\"" + "." + $"\"{spName}\"";
            var res = await conn.QueryAsync<T>(formattedSPName, param, commandType: CommandType.StoredProcedure, commandTimeout: commandTimeout);
            return res;
        }
        catch (Exception ex)
        {
            Console.WriteLine("Exception details while calling the QueryStoredProcedureFromReadReplicaAsync " + ex?.Message ?? ex?.InnerException?.Message ?? string.Empty);
            throw;
        }
    }

    /// <summary>
    /// Executes a stored procedure on the read replica and returns the result as an integer.
    /// </summary>
    /// <param name="schemaName">The schema name of the stored procedure.</param>
    /// <param name="spName">The name of the stored procedure.</param>
    /// <param name="param">The parameters to pass to the stored procedure.</param>
    /// <param name="commandTimeout">The command timeout (in seconds).</param>
    /// <returns>An integer containing the result of the stored procedure.</returns>
    public async Task<int> QueryStoredProcedureCountFromReadReplicaAsync(string schemaName, string spName, object param, int? commandTimeout = 60)
    {
        await using var conn = new NpgsqlConnection(GetReadReplicaConnectionString());
        try
        {
            var formattedSPName = $"\"{schemaName}\"" + "." + $"\"{spName}\"";
            var res = await conn.ExecuteScalarAsync<int>(formattedSPName, param, commandType: CommandType.StoredProcedure, commandTimeout: commandTimeout);
            return res;
        }
        catch (Exception ex)
        {
            Console.WriteLine("Exception details while calling the QueryStoredProcedureCountFromReadReplicaAsync " + ex?.Message ?? ex?.InnerException?.Message ?? string.Empty);
            throw;
        }
    }
    /// <summary>
    /// Executes a stored procedure on the main db conn and returns the result as an enumerable of type T.
    /// </summary>
    /// <typeparam name="T">The type of the result.</typeparam>
    /// <param name="schemaName">The schema name of the stored procedure.</param>
    /// <param name="spName">The name of the stored procedure.</param>
    /// <param name="param">The parameters to pass to the stored procedure.</param>
    /// <param name="commandTimeout">The command timeout (in seconds).</param>
    /// <returns>An enumerable of type T containing the result of the stored procedure.</returns>
    public async Task<IEnumerable<T>> QueryStoredProcedureFromMainDbAsync<T>(string schemaName, string spName, object param, int? commandTimeout = 60)
    {
        await using var conn = new NpgsqlConnection(_settings.ReadReplicaConnectionString);
        try
        {
            var formattedSPName = $"\"{schemaName}\"" + "." + $"\"{spName}\"";
            var res = await conn.QueryAsync<T>(formattedSPName, param, commandType: CommandType.StoredProcedure, commandTimeout: commandTimeout);
            return res;
        }
        catch (Exception ex)
        {
            Console.WriteLine("Exception details while calling the QueryStoredProcedureFromReadReplicaAsync " + ex?.Message ?? ex?.InnerException?.Message ?? string.Empty);
            throw;
        }
    }
}
