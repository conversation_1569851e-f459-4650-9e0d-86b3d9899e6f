﻿using Lrb.Application.Common.BlobStorage;
using Lrb.Application.Common.Persistence.New_Implementation;
using Lrb.Application.GlobalSettings.Web;
using Lrb.Application.Identity.Users;
using Lrb.Application.Property.Web.Specs;
using Lrb.Domain.Entities.AmenitiesAttributes;
using Lrb.Domain.Entities.ErrorModule;
using Lrb.Domain.Entities.MasterData;
using Newtonsoft.Json;

namespace Lrb.Application.Property.Web.Requests
{

    public class GetAllPropertyAnonymousRequest : PaginationFilter, IRequest<PagedResponse<PullViewPropertyDto, PropertyCountDto>>
    {
        public PropertyDimensionDto? PropertySize { get; set; }
        public List<string>? Locations { get; set; }
        public List<string>? Cities { get; set; }
        public List<string>? States { get; set; }
        public EnquiryType? EnquiredFor { get; set; }
        public double? NoOfBHK { get; set; }
        public List<double>? BHKs { get; set; }
        public string? Ratings { get; set; }
        public PropertyStatus? PropertyStatus { get; set; }
        public List<Guid>? PropertyTypes { get; set; }
        public List<Guid>? PropertySubTypes { get; set; }
        public Guid? BasePropertyTypeId { get; set; }
        public string? PropertySearch { get; set; }
        public PropertyDateType? DateType { get; set; }
        public DateTime? FromDate { get; set; }
        public DateTime? ToDate { get; set; }
        public long? MinPrice { get; set; }
        public long? MaxPrice { get; set; }
        public List<Guid>? Amenities { get; set; }
        public List<BHKType>? BHKTypes { get; set; }
        public DateTime? FromPossessionDate { get; set; }
        public DateTime? ToPossessionDate { get; set; }
        public List<string>? Projects { get; set; }
        public List<FurnishStatus>? FurnishStatuses { get; set; }
        public List<SaleType>? SaleTypes { get; set; }
        public string? OwnerName { get; set; }
        public string? PropertyTitle { get; set; }
        public Facing? Facing { get; set; }
        public List<int>? NoOfBathrooms { get; set; }
        public List<int>? NoOfLivingrooms { get; set; }
        public List<int>? NoOfBedrooms { get; set; }
        public List<int>? NoOfUtilites { get; set; }
        public List<int>? NoOfKitchens { get; set; }
        public List<int>? NoOfBalconies { get; set; }
        public List<int>? NoOfFloor { get; set; }
        public int? FloorNumber { get; set; }
        public long? MaxBudget { get; set; }
        public long? MinBudget { get; set; }
        public List<string>? OwnerNames { get; set; }
        public bool? IsWithTeam { get; set; }
        public List<Guid>? UserIds { get; set; }
        public ViewAssignmentsPermission? Permission { get; set; }
        public string? Currency { get; set; }
        public List<Guid>? ListingOnBehalf { get; set; }

        public PossesionType? PossesionType { get; set; }
        public DateTime? FromPossesionDate { get; set; }
        public DateTime? ToPossesionDate { get; set; }
        public List<string>? NoOfFloors { get; set; }
        public List<int>? Parking { get; set; }
        public List<string>? Countries { get; set; }


    }
    public class GetAllPropertyAnonymousRequestHandler : IRequestHandler<GetAllPropertyAnonymousRequest, PagedResponse<PullViewPropertyDto, PropertyCountDto>>
    {
        private readonly IUserService _userService;
        private readonly IReadRepository<Domain.Entities.Property> _propertyRepo;
        private readonly IReadRepository<CustomMasterAttribute> _masterPropertyAttributeRepo;
        private readonly IReadRepository<MasterAreaUnit> _masterAreaUnitRepo;
        private readonly IReadRepository<CustomMasterAmenity> _masterPropertyAmenityRepo;
        private readonly IReadRepository<MasterPropertyType> _masterPropertyTypeRepo;
        private readonly IReadRepository<PropertyDimension> _propertyDimensionRepo;
        private readonly ILeadRepositoryAsync _leadRepositoryAsync;
        private readonly IDapperRepository _dapperRepository;
        private readonly ICurrentUser _currentUser;
        private readonly IRepositoryWithEvents<Domain.Entities.GlobalSettings> _globalSettingsRepository;
        protected readonly IPropertyRepository _efPropertyRepository;
        private readonly IBlobStorageService _blobStorage;


        public GetAllPropertyAnonymousRequestHandler(
            IUserService userService,
            IReadRepository<Domain.Entities.Property> propertyRepo,
            IReadRepository<CustomMasterAttribute> masterPropertyAttributeRepo,
            IReadRepository<MasterAreaUnit> masterAreaUnitRepo,
            IReadRepository<CustomMasterAmenity> masterPropertyAmenityRepo,
            IReadRepository<MasterPropertyType> masterPropertyTypeRepo,
            IReadRepository<PropertyDimension> propertyDimensionRepo,
            ILeadRepositoryAsync leadRepositoryAsync,
            IDapperRepository dapperRepository,
            ICurrentUser currentUser,
            IRepositoryWithEvents<Domain.Entities.GlobalSettings> globalSettingsRepository,
            IPropertyRepository efPropertyRepository,
            IBlobStorageService blobStorage

            )
        {
            _userService = userService;
            _propertyRepo = propertyRepo;
            _masterPropertyAttributeRepo = masterPropertyAttributeRepo;
            _masterAreaUnitRepo = masterAreaUnitRepo;
            _masterPropertyAmenityRepo = masterPropertyAmenityRepo;
            _masterPropertyTypeRepo = masterPropertyTypeRepo;
            _propertyDimensionRepo = propertyDimensionRepo;
            _leadRepositoryAsync = leadRepositoryAsync;
            _dapperRepository = dapperRepository;
            _currentUser = currentUser;
            _globalSettingsRepository = globalSettingsRepository;
            _efPropertyRepository = efPropertyRepository;
            _blobStorage = blobStorage;
        }

        public async Task<PagedResponse<PullViewPropertyDto, PropertyCountDto>> Handle(GetAllPropertyAnonymousRequest request, CancellationToken cancellationToken)
        {
            Domain.Entities.GlobalSettings? globalSettings = await _globalSettingsRepository.FirstOrDefaultAsync(new GetGlobalSettingsSpec(), cancellationToken);
            if (globalSettings?.CanAccessAnonymousApis != true)
            {
                throw new UnauthorizedAccessException("Access to properties is restricted.");
            }
            if (request != null && request.Permission == ViewAssignmentsPermission.None)
            {
                return new PagedResponse<PullViewPropertyDto, PropertyCountDto>(null, 0, null);
            }
            CustomMasterAttribute? masterPropertyAttribute = null;
            CustomMasterAmenity? masterPropertyAmenity = null;
            MasterPropertyType? masterPropertyType = null;
            if (!string.IsNullOrWhiteSpace(request.PropertySearch))
            {
                masterPropertyAttribute = (await _masterPropertyAttributeRepo.ListAsync(new CustomMasterPropertyAttributeSpec(request?.PropertySearch?.Trim().ToLower() ?? string.Empty), cancellationToken)).FirstOrDefault();
                masterPropertyAmenity = (await _masterPropertyAmenityRepo.ListAsync(new GetCustomAmenitySpec(request?.PropertySearch?.Trim().ToLower() ?? string.Empty), cancellationToken)).FirstOrDefault();
                masterPropertyType = (await _masterPropertyTypeRepo.ListAsync(new GetMasterPropertyTypeSpec(request?.PropertySearch?.Trim().ToLower() ?? string.Empty), cancellationToken)).FirstOrDefault();
            }
            //var masterPropertyAttributes = (await _masterPropertyAttributeRepo.ListAsync(new GetAllMasterPropertyAttributeSpec(), cancellationToken));
            List<Guid>? propertyDimensionIds = new();
            //if (request != null && request.PropertySize != null && request.PropertySize.AreaUnitId != default)
            //{
            //    var masterAreaUnit = await _masterAreaUnitRepo.GetByIdAsync(request.PropertySize.AreaUnitId);
            //    request.PropertySize.ConversionFactor = masterAreaUnit?.ConversionFactor ?? default;
            //    var area = request.PropertySize.Area * request.PropertySize.ConversionFactor;
            //    propertyDimensionIds = (await _propertyDimensionRepo.ListAsync()).Where(i => (i.Area * PropertySearchHelper.GetConversionFactor(i.AreaUnitId, _masterAreaUnitRepo).Result) == area).Select(i => i.Id).ToList();
            //}
            //if (request != null && request.PropertySize != null && request.PropertySize.CarpetAreaId != default)
            //{
            //    var masterAreaUnit = await _masterAreaUnitRepo.GetByIdAsync(request.PropertySize.CarpetAreaId);
            //    request.PropertySize.ConversionFactor = masterAreaUnit?.ConversionFactor ?? default;
            //    var Carpetarea = request.PropertySize.CarpetArea * request.PropertySize.ConversionFactor;
            //    propertyDimensionIds = (await _propertyDimensionRepo.ListAsync()).Where(i => (i.CarpetArea * PropertySearchHelper.GetConversionFactor(i.CarpetAreaId ?? Guid.Empty, _masterAreaUnitRepo).Result) == Carpetarea).Select(i => i.Id).ToList();
            //}

            //if (request != null && request.PropertySize != null && request.PropertySize.BuildUpAreaId != default)
            //{
            //    var masterAreaUnit = await _masterAreaUnitRepo.GetByIdAsync(request.PropertySize.BuildUpAreaId);
            //    request.PropertySize.ConversionFactor = masterAreaUnit?.ConversionFactor ?? default;
            //    var builduparea = request.PropertySize.BuildUpArea * request.PropertySize.ConversionFactor;
            //    propertyDimensionIds = (await _propertyDimensionRepo.ListAsync()).Where(i => (i.BuildUpArea * PropertySearchHelper.GetConversionFactor(i.BuildUpAreaId ?? Guid.Empty, _masterAreaUnitRepo).Result) == builduparea).Select(i => i.Id).ToList();
            //}
            //if (request != null && request.PropertySize != null && request.PropertySize.SaleableAreaId != default)
            //{
            //    var masterAreaUnit = await _masterAreaUnitRepo.GetByIdAsync(request.PropertySize.SaleableAreaId);
            //    request.PropertySize.ConversionFactor = masterAreaUnit?.ConversionFactor ?? default;
            //    var saleblearea = request.PropertySize.SaleableArea * request.PropertySize.ConversionFactor;
            //    propertyDimensionIds = (await _propertyDimensionRepo.ListAsync()).Where(i => (i.SaleableArea * PropertySearchHelper.GetConversionFactor(i.SaleableAreaId ?? Guid.Empty, _masterAreaUnitRepo).Result) == saleblearea).Select(i => i.Id).ToList();
            //}
            //if (request != null && request.PropertySize != null && request.PropertySize.NetAreaUnitId != default)
            //{
            //    var masterAreaUnit = await _masterAreaUnitRepo.GetByIdAsync(request.PropertySize.NetAreaUnitId);
            //    request.PropertySize.ConversionFactor = masterAreaUnit?.ConversionFactor ?? default;
            //    var netAreaId = request.PropertySize.NetArea * request.PropertySize.ConversionFactor;
            //    propertyDimensionIds = (await _propertyDimensionRepo.ListAsync()).Where(i => (i.NetArea * PropertySearchHelper.GetConversionFactor(i.NetAreaUnitId ?? Guid.Empty, _masterAreaUnitRepo).Result) == netAreaId).Select(i => i.Id).ToList();
            //}
            List<int> noOfAttributes = Enumerable.Range(1, 5).ToList();
            NumericAttributesDto numericAttributeDto = InitializationOfNumericAttributes(noOfAttributes, request);
            var tenantId = _currentUser.GetTenant();
            var currentUserId = _currentUser.GetUserId();
            List<Guid>? userIds = new();
            List<Guid>? filterIds = new();
            List<Guid>? teamUserIds = new();
            bool showAllProperties = false;
            //var isAdmin = await _dapperRepository.IsAdminAsync(currentUserId, tenantId ?? string.Empty);

            try
            {
                switch (request.Permission)
                {
                    case ViewAssignmentsPermission.View:
                        if (request.UserIds?.Any() ?? false)
                        {
                            filterIds.AddRange(request.UserIds);
                            if (request.IsWithTeam ?? false)
                            {
                                teamUserIds = (await _dapperRepository.GetSubordinateIdsWithOnlyReporteesAsync(request.UserIds, tenantId ?? string.Empty))?.ToList() ?? new();
                                filterIds.AddRange(teamUserIds);
                            }
                            userIds.AddRange(filterIds);
                        }
                        else
                        {
                            userIds = (await _dapperRepository.GetAllUserIdsAsync(tenantId ?? string.Empty)).ToList();
                            showAllProperties = true;
                        }
                        break;
                    case ViewAssignmentsPermission.ViewAssigned:
                        userIds.Add(currentUserId);
                        break;
                    default:
                        if (request.UserIds?.Any() ?? false)
                        {
                            filterIds.AddRange(request.UserIds);
                            if (request.IsWithTeam ?? false)
                            {
                                teamUserIds = (await _dapperRepository.GetSubordinateIdsWithOnlyReporteesAsync(request.UserIds, tenantId ?? string.Empty))?.ToList() ?? new();
                                filterIds.AddRange(teamUserIds);
                            }
                            userIds.AddRange(filterIds);
                        }
                        else
                        {
                            userIds = (await _dapperRepository.GetAllUserIdsAsync(tenantId ?? string.Empty)).ToList();
                            showAllProperties = true;
                        }
                        break;
                }
            }
            catch (Exception ex)
            {
                var error = new LrbError()
                {
                    ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                    ErrorSource = ex?.Source,
                    StackTrace = ex?.StackTrace,
                    InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                    ErrorModule = "GetAllPropertyAnonymousRequestHandler -> Handle()"
                };
                await _leadRepositoryAsync.AddErrorAsync(error);
            }
            var residentialPropertyType = (await _masterPropertyTypeRepo.FirstOrDefaultAsync(new GetMasterPropertyTypeByTypeSpec("residential")));
            var agriculturalPropertyType = (await _masterPropertyTypeRepo.FirstOrDefaultAsync(new GetMasterPropertyTypeByTypeSpec("agricultural")));
            var commercialPropertyType = (await _masterPropertyTypeRepo.FirstOrDefaultAsync(new GetMasterPropertyTypeByTypeSpec("commercial")));
            PropertyTypeBaseId propertyTypeIds = new();
            propertyTypeIds.ResidentialBaseId = residentialPropertyType.Id;
            propertyTypeIds.AgricultureBaseId = agriculturalPropertyType.Id;
            propertyTypeIds.CommercialBaseId = commercialPropertyType.Id;
            (IEnumerable<Lrb.Domain.Entities.Property> allProperties, int totalCount, int residentialCount, int agricultureCount, int commercialCount) propertiesWithCount = new();
            List<CustomPropertyAttributeDto> attributes = new();
            if (request?.NoOfFloors != null ||request?.NoOfKitchens != null || request?.NoOfUtilites != null || request?.NoOfBedrooms != null || request?.NoOfLivingrooms != null || request?.NoOfBalconies != null || request?.NoOfBathrooms != null || request?.Parking != null)
            {
                attributes = await _dapperRepository.GetAttributeDetails(tenantId ?? string.Empty);
            }
            propertiesWithCount = await _efPropertyRepository.GetAllAnonymousPropertiesForWebAsync(request, masterPropertyAttribute?.Id ?? null, masterPropertyAmenity?.Id ?? null, masterPropertyType?.Id ?? null, propertyDimensionIds, numericAttributeDto, userIds, showAllProperties, propertyTypeIds, tenantId,attributes);
            if (propertiesWithCount.allProperties != null)
            {
                propertiesWithCount.allProperties
                    .Where(property => property?.Galleries != null)
                    .SelectMany(property => property.Galleries)
                    .Where(gallery => !string.IsNullOrEmpty(gallery.ImageFilePath) &&
                                      !gallery.ImageFilePath.StartsWith(_blobStorage.AWSS3BucketUrl, StringComparison.OrdinalIgnoreCase))
                    .ToList()
                    .ForEach(gallery => gallery.ImageFilePath = $"{_blobStorage.AWSS3BucketUrl}{gallery.ImageFilePath}");
            }
            PropertyCountDto propertyCountDto = new();
            propertyCountDto.AgriculturalPropertiesCount = propertiesWithCount.agricultureCount;
            propertyCountDto.ResidentialPropertiesCount = propertiesWithCount.residentialCount;
            propertyCountDto.CommercialPropertiesCount = propertiesWithCount.commercialCount;
            propertyCountDto.AllPropertiesCount = propertiesWithCount.totalCount;
            int totalCount = request.BasePropertyTypeId == commercialPropertyType.Id ? propertiesWithCount.commercialCount :
                              request.BasePropertyTypeId == agriculturalPropertyType.Id ? propertiesWithCount.agricultureCount :
                              request.BasePropertyTypeId == residentialPropertyType.Id ? propertiesWithCount.residentialCount :
                              propertiesWithCount.totalCount;

            List<ViewPropertyDto>? propertyDtos = new List<ViewPropertyDto>();
            try
            {
                if (propertiesWithCount.allProperties?.Any() ?? false)
                {
                    propertyDtos = propertiesWithCount.allProperties.Adapt<List<ViewPropertyDto>>();
                }
            }
            catch (Exception ex)
            {
                var error = new LrbError()
                {
                    ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                    ErrorSource = ex?.Source,
                    StackTrace = ex?.StackTrace,
                    InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                    ErrorModule = "GetAllPropertyAnonymousRequestHandler -> Handle()"
                };
                await _leadRepositoryAsync.AddErrorAsync(error);
            }
            List<PullViewPropertyDto> result = propertyDtos.Adapt<List<PullViewPropertyDto>>();
            foreach (var property in propertyDtos.Where(p => p.Amenities != null))
            {
                var amenities = await _dapperRepository.GetAllAmenitiesForPropertyAsync(_currentUser.GetTenant() ?? string.Empty, property.Amenities?.ToList()
                );

                var matchingProperty = result.FirstOrDefault(res => res.Id == property.Id);
                if (matchingProperty != null)
                {
                    matchingProperty.Amenities = amenities;
                }
            }
            return new PagedResponse<PullViewPropertyDto, PropertyCountDto>(result, totalCount, propertyCountDto);
        }
        public NoOfAttributeFilterDto FilterNumericAttributes(List<int>? requestValues, List<int> noOfAttributes)
        {
            NoOfAttributeFilterDto noOfAttributesDto = new NoOfAttributeFilterDto();
            var noOfFloorFilterList = new List<int>();

            if (requestValues != null)
            {
                if (requestValues.Contains(5))
                {
                    noOfFloorFilterList = noOfAttributes.Where(i => !requestValues.Contains(i)).ToList();
                    noOfAttributesDto.NoOfAttributes = noOfFloorFilterList.Select(i => i.ToString()).ToList();
                    noOfAttributesDto.IsMaxValueIncluded = true;
                }
                else
                {
                    noOfFloorFilterList = noOfAttributes.Where(i => requestValues.Contains(i)).ToList();
                    noOfAttributesDto.NoOfAttributes = noOfFloorFilterList.Select(i => i.ToString()).ToList();
                    noOfAttributesDto.IsMaxValueIncluded = false;
                }
            }

            return noOfAttributesDto;
        }

        public NumericAttributesDto InitializationOfNumericAttributes(List<int> noOfAttributes, GetAllPropertyAnonymousRequest request)
        {
            return new NumericAttributesDto
            {
                NoOfFloor = FilterNumericAttributes(request.NoOfFloor, noOfAttributes),
                NoOfBathrooms = FilterNumericAttributes(request.NoOfBathrooms, noOfAttributes),
                NoOfBedrooms = FilterNumericAttributes(request.NoOfBedrooms, noOfAttributes),
                NoOfKitchens = FilterNumericAttributes(request.NoOfKitchens, noOfAttributes),
                NoOfUtilites = FilterNumericAttributes(request.NoOfUtilites, noOfAttributes),
                NoOfLivingrooms = FilterNumericAttributes(request.NoOfLivingrooms, noOfAttributes),
                NoOfBalconies = FilterNumericAttributes(request.NoOfBalconies, noOfAttributes),
                Parking = FilterNumericAttributes(request.Parking, noOfAttributes),

            };
        }

        private async Task<PropertyCountDto> GetPropertyCounts(GetAllPropertyAnonymousRequest request,
        MasterPropertyAttribute? masterPropertyAttribute,
        MasterPropertyAmenity? masterPropertyAmenity,
        MasterPropertyType? masterPropertyType,
        List<Guid>? propertyDimensionIds,
        List<Guid>? userIds,
        bool showAllProperties
        )

        {
            List<int> noOfAttributes = Enumerable.Range(1, 5).ToList();
            NumericAttributesDto numericAttributeDto = InitializationOfNumericAttributes(noOfAttributes, request);
            PropertyCountDto propertyCountDto = new PropertyCountDto();
            var basePropertyTypeId = request.BasePropertyTypeId;
            var residentialPropertyType = (await _masterPropertyTypeRepo.ListAsync(new GetMasterPropertyTypeByTypeSpec("residential"))).FirstOrDefault();
            var agriculturalPropertyType = (await _masterPropertyTypeRepo.ListAsync(new GetMasterPropertyTypeByTypeSpec("agricultural"))).FirstOrDefault();
            var commercialPropertyType = (await _masterPropertyTypeRepo.ListAsync(new GetMasterPropertyTypeByTypeSpec("commercial"))).FirstOrDefault();
            var tenantId = _currentUser.GetTenant();
            List<CustomPropertyAttributeDto> attributes = new();
            if (request?.NoOfFloors != null || request?.NoOfKitchens != null || request?.NoOfUtilites != null || request?.NoOfBedrooms != null || request?.NoOfLivingrooms != null || request?.NoOfBalconies != null || request?.NoOfBathrooms != null || request?.Parking != null)
            {
                attributes = await _dapperRepository.GetAttributeDetails(tenantId ?? string.Empty);
            }
            if (residentialPropertyType != null)
            {
                request.BasePropertyTypeId = residentialPropertyType.Id;
                propertyCountDto.ResidentialPropertiesCount = await _propertyRepo.CountAsync(new PropertyCountByCustomFilterSpec(request, masterPropertyAttribute?.Id ?? null, masterPropertyAmenity?.Id ?? null, masterPropertyType?.Id ?? null, propertyDimensionIds, numericAttributeDto, userIds, showAllProperties,attributes));
            }
            if (agriculturalPropertyType != null)
            {
                request.BasePropertyTypeId = agriculturalPropertyType.Id;
                propertyCountDto.AgriculturalPropertiesCount = await _propertyRepo.CountAsync(new PropertyCountByCustomFilterSpec(request, masterPropertyAttribute?.Id ?? null, masterPropertyAmenity?.Id ?? null, masterPropertyType?.Id ?? null, propertyDimensionIds, numericAttributeDto, userIds, showAllProperties,attributes));
            }
            if (commercialPropertyType != null)
            {
                request.BasePropertyTypeId = commercialPropertyType.Id;
                propertyCountDto.CommercialPropertiesCount = await _propertyRepo.CountAsync(new PropertyCountByCustomFilterSpec(request, masterPropertyAttribute?.Id ?? null, masterPropertyAmenity?.Id ?? null, masterPropertyType?.Id ?? null, propertyDimensionIds, numericAttributeDto, userIds, showAllProperties,attributes));
            }
            request.BasePropertyTypeId = default;
            propertyCountDto.AllPropertiesCount = await _propertyRepo.CountAsync(new PropertyCountByCustomFilterSpec(request, masterPropertyAttribute?.Id ?? null, masterPropertyAmenity?.Id ?? null, masterPropertyType?.Id ?? null, propertyDimensionIds, numericAttributeDto, userIds, showAllProperties,attributes));
            request.BasePropertyTypeId = basePropertyTypeId;
            return propertyCountDto;
        }

    }
    public static class PropertySearchHelper
    {
        public async static Task<float> GetConversionFactor(Guid areaUnitId, IReadRepository<MasterAreaUnit>? masterAreaUnitRepo)
        {
            if (masterAreaUnitRepo != null)
            {
                var masterAreaUnit = await masterAreaUnitRepo.GetByIdAsync(areaUnitId);
                if (masterAreaUnit != null)
                {
                    return masterAreaUnit.ConversionFactor;
                }
                return default;
            }
            return default;
        }


    }



}

