﻿using Lrb.Domain.Constants;
using System.ComponentModel;

namespace Lrb.Domain.Enums
{
    public enum LeadSource
    {

        Any = -1,
        [Description(EnumDescription.LeadSource.Direct)]
        Direct = 0,
        [Description(EnumDescription.LeadSource.IVR)]
        IVR,
        [Description(EnumDescription.LeadSource.Facebook)]
        Facebook,
        [Description(EnumDescription.LeadSource.LinkedIn)]
        LinkedIn,
        [Description(EnumDescription.LeadSource.GoogleAds)]
        GoogleAds,
        [Description(EnumDescription.LeadSource.MagicBricks)]
        MagicBricks,
        [Description(EnumDescription.LeadSource.NinetyNineAcres)]
        NinetyNineAcres,
        [Description(EnumDescription.LeadSource.Housing)]
        Housing,
        [Description(EnumDescription.LeadSource.GharOffice)]
        GharOffice,
        [Description(EnumDescription.LeadSource.Referral)]
        Referral,
        [Description(EnumDescription.LeadSource.WalkIn)]
        WalkIn,
        [Description(EnumDescription.LeadSource.Website)]
        Website,
        [Description(EnumDescription.LeadSource.Gmail)]
        Gmail,
        [Description(EnumDescription.LeadSource.PropertyMicrosite)]
        PropertyMicrosite,
        [Description(EnumDescription.LeadSource.PortfolioMicrosite)]
        PortfolioMicrosite,
        [Description(EnumDescription.LeadSource.Phonebook)]
        Phonebook,
        [Description(EnumDescription.LeadSource.CallLogs)]
        CallLogs,
        [Description(EnumDescription.LeadSource.LeadPool)]
        LeadPool,
        [Description(EnumDescription.LeadSource.SquareYards)]
        SquareYards,
        [Description(EnumDescription.LeadSource.QuikrHomes)]
        QuikrHomes,
        [Description(EnumDescription.LeadSource.JustLead)]
        JustLead,
        [Description(EnumDescription.LeadSource.WhatsApp)]
        WhatsApp,
        [Description(EnumDescription.LeadSource.YouTube)]
        YouTube,
        [Description(EnumDescription.LeadSource.QRCode)]
        QRCode,
        [Description(EnumDescription.LeadSource.Instagram)]
        Instagram,
        [Description(EnumDescription.LeadSource.OLX)]
        OLX,
        [Description(EnumDescription.LeadSource.EstateDekho)]
        EstateDekho,
        [Description(EnumDescription.LeadSource.GoogleSheet)]
        GoogleSheet,
        [Description(EnumDescription.LeadSource.ChannelPartner)]
        ChannelPartner,
        [Description(EnumDescription.LeadSource.RealEstateIndia)]
        RealEstateIndia,
        [Description(EnumDescription.LeadSource.CommonFloor)]
        CommonFloor,
        [Description(EnumDescription.LeadSource.Data)]
        Data,
        [Description(EnumDescription.LeadSource.RoofandFloor)]
        RoofandFloor,
        [Description(EnumDescription.LeadSource.MicrosoftAds)]
        MicrosoftAds,
        [Description(EnumDescription.LeadSource.PropertyWala)]
        PropertyWala,
        [Description(EnumDescription.LeadSource.ProjectMicrosite)]
        ProjectMicrosite,
        [Description(EnumDescription.LeadSource.MyGate)]
        MyGate,
        [Description(EnumDescription.LeadSource.Flipkart)]
        Flipkart,
        [Description(EnumDescription.LeadSource.PropertyFinder)]
        PropertyFinder,
        [Description(EnumDescription.LeadSource.Bayut)]
        Bayut,
        [Description(EnumDescription.LeadSource.Dubizzle)]
        Dubizzle,
        [Description(EnumDescription.LeadSource.Webhook)]
        Webhook,
        [Description(EnumDescription.LeadSource.TikTok)]
        TikTok,
        [Description(EnumDescription.LeadSource.Snapchat)]
        Snapchat,
        [Description(EnumDescription.LeadSource.GoogleAdsCampaign)]
        GoogleAdsCampaign
    }
}
