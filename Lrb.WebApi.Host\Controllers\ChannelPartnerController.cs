﻿using Lrb.Application.ChannelPartner.Web.Request;
using Lrb.Domain.Entities;
using MediatR;
using Newtonsoft.Json;

namespace Lrb.WebApi.Host.Controllers
{
    [Authorize]
    public class ChannelPartnerController : VersionedApiController
    {
        private readonly IMediator _mediator;
        private readonly Serilog.ILogger _logger;
        public ChannelPartnerController(IMediator mediator, Serilog.ILogger logger)
        {
            _mediator = mediator;
            _logger = logger;
        }
        [HttpGet("dapper")]
        [TenantIdHeader]
        [OpenApiOperation("get all channel partner", "")]
        public async Task<Response<List<string>>> GetAllChannelPartner([FromQuery] GetAllChannelPartnersDapperRequest request)
        {
            return await _mediator.Send(request);
        }
        [HttpGet]
        [TenantIdHeader]
        [OpenApiOperation("get all channel partner", "")]
        public async Task<Response<List<string>>> GetAllChannelPartner([FromQuery] GetAllChannelPartnersRequest request)
        {
            return await _mediator.Send(request);
        }
        [HttpPost]
        [TenantIdHeader]
        [OpenApiOperation("create channel partner", "")]
        public async Task<Response<Guid>> CreateChannelPartner(CreateChannelPartnerRequest request)
        {
            return await _mediator.Send(request);
        }
        [HttpPost("excel")]
        [TenantIdHeader]
        [OpenApiOperation("Upload excel File")]
        public async Task<ActionResult<Response<Application.ChannelPartner.Web.Dtos.FileColumnDto>>> UploadExcelFileAsync(IFormFile file)
        {
            try
            {
                return await _mediator.Send(new Application.ChannelPartner.Web.Request.GetExcelColumnsUsingEpPlusRequest(file));
            }
            catch (Exception e)
            {
                _logger.Error("ChannelPartnerController -> UploadExcelFileAsync, Error: " + JsonConvert.SerializeObject(e));
                throw;
            }

        }
        [HttpPost("bulk")]
        [TenantIdHeader]
        [OpenApiOperation("Create new channel partner by excel.", "")]
        public Task<Response<BulkChannelPartnerUploadTracker>> CreateBulkAsync(RunAWSBatchForBulkChannelPartnerUploadRequest request)
        {
            _logger.Information("ChannelPartnerController -> createLeadRequest, File: " + JsonConvert.SerializeObject(request));
            return _mediator.Send(request);
        }
    }
}
