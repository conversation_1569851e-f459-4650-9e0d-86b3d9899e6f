﻿using Lrb.Application.Common.IVR;
using Lrb.Application.Common.Persistence;
using Lrb.Application.Integration.Web.Requests.Bayut;
using Lrb.Application.Source.Web;
using Microsoft.AspNetCore.Mvc;
using Lrb.Domain.Enums;
using Lrb.Application.Integration.Web.Dtos;
using Lrb.Application.Integration.Web.Requests;
using Mapster;
using Lrb.Application.LeadGenRequests;
using Newtonsoft.Json;


namespace Lrb.WebApi.Host.Controllers.V2
{
    [Authorize]
    [Route("api/v2/[controller]")]
    [ApiVersionNeutral]
    public class IntegrationController : VersionedApiController
    {
        private readonly Serilog.ILogger _logger;
        private readonly IReadRepository<Domain.Entities.Source> _sourceRepo;
        public IntegrationController(Serilog.ILogger logger, IReadRepository<Domain.Entities.Source> sourceRepo)
        {
            _logger = logger;
            _sourceRepo = sourceRepo;
        }
        [AllowAnonymous]
        [HttpGet("bayut/WA/{tenant}/{base64}")]
        [HttpPost("bayut/WA/{tenant}/{base64}")]
        [OpenApiOperation("Get end point for bayut whatsapp integration", "")]
        public async Task<Response<bool>> PostWebhookBayutWA(string tenant, string base64)
        {
            if (!await IsSourceEnabled(LeadSource.Bayut))
                return new(false, "Bayut integration is currently disabled");
            BayutNewWAPushIntegrationRequest lead = new(this.HttpContext.Request, tenant, base64);
            _logger.Information("IntegrationController -> POST(Bayut) -> called, Dto: " + tenant + base64);
            return await Mediator.Send(lead);
        }

        [AllowAnonymous]
        [HttpGet("bayut/IVR/{tenant}/{base64}")]
        [HttpPost("bayut/IVR/{tenant}/{base64}")]
        [OpenApiOperation("Get end point for bayut whatsapp integration", "")]
        public async Task<Response<bool>> PostWebhookBayutIVR(string tenant, string base64)
        {
            if (!await IsSourceEnabled(LeadSource.Bayut))
                return new(false, "Bayut integration is currently disabled");
            BayutIVRPushIntegrationRequest lead = new(this.HttpContext.Request, tenant, base64);
            _logger.Information("IntegrationController -> POST(Bayut) -> called, Dto: " + tenant + base64);
            return await Mediator.Send(lead);
        }
        private async Task<bool> IsSourceEnabled(LeadSource leadSource, CancellationToken cancellationToken = default)
        {
            try
            {
                var source = await _sourceRepo.FirstOrDefaultAsync(new GetSourceByValueSpecs((int)leadSource), cancellationToken);

                if (source == null)
                {
                    _logger.Warning($"Source {leadSource} not found");
                    return false;
                }
                if (source.IsEnabled == false) return false;
                return true;
            }
            catch (Exception ex)
            {
                _logger.Error(ex, $"Error checking source status for {leadSource}");
                return false;
            }
        }
    }
}
