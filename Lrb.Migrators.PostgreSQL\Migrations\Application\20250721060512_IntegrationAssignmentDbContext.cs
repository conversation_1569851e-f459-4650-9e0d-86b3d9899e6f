﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Lrb.Migrators.PostgreSQL.Migrations.Application
{
    public partial class IntegrationAssignmentDbContext : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<Guid>(
                name: "AgencyId",
                schema: "LeadratBlack",
                table: "IntegrationAssignments",
                type: "uuid",
                nullable: true);

            migrationBuilder.AddColumn<Guid>(
                name: "CampaignId",
                schema: "LeadratBlack",
                table: "IntegrationAssignments",
                type: "uuid",
                nullable: true);

            migrationBuilder.AddColumn<Guid>(
                name: "ChannelPartnerId",
                schema: "LeadratBlack",
                table: "IntegrationAssignments",
                type: "uuid",
                nullable: true);

            migrationBuilder.AddColumn<Guid>(
                name: "PropertyId",
                schema: "LeadratBlack",
                table: "IntegrationAssignments",
                type: "uuid",
                nullable: true);

            migrationBuilder.CreateIndex(
                name: "IX_IntegrationAssignments_AgencyId",
                schema: "LeadratBlack",
                table: "IntegrationAssignments",
                column: "AgencyId");

            migrationBuilder.CreateIndex(
                name: "IX_IntegrationAssignments_CampaignId",
                schema: "LeadratBlack",
                table: "IntegrationAssignments",
                column: "CampaignId");

            migrationBuilder.CreateIndex(
                name: "IX_IntegrationAssignments_ChannelPartnerId",
                schema: "LeadratBlack",
                table: "IntegrationAssignments",
                column: "ChannelPartnerId");

            migrationBuilder.CreateIndex(
                name: "IX_IntegrationAssignments_PropertyId",
                schema: "LeadratBlack",
                table: "IntegrationAssignments",
                column: "PropertyId");

            migrationBuilder.AddForeignKey(
                name: "FK_IntegrationAssignments_Agencies_AgencyId",
                schema: "LeadratBlack",
                table: "IntegrationAssignments",
                column: "AgencyId",
                principalSchema: "LeadratBlack",
                principalTable: "Agencies",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_IntegrationAssignments_Campaigns_CampaignId",
                schema: "LeadratBlack",
                table: "IntegrationAssignments",
                column: "CampaignId",
                principalSchema: "LeadratBlack",
                principalTable: "Campaigns",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_IntegrationAssignments_ChannelPartners_ChannelPartnerId",
                schema: "LeadratBlack",
                table: "IntegrationAssignments",
                column: "ChannelPartnerId",
                principalSchema: "LeadratBlack",
                principalTable: "ChannelPartners",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_IntegrationAssignments_Properties_PropertyId",
                schema: "LeadratBlack",
                table: "IntegrationAssignments",
                column: "PropertyId",
                principalSchema: "LeadratBlack",
                principalTable: "Properties",
                principalColumn: "Id");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_IntegrationAssignments_Agencies_AgencyId",
                schema: "LeadratBlack",
                table: "IntegrationAssignments");

            migrationBuilder.DropForeignKey(
                name: "FK_IntegrationAssignments_Campaigns_CampaignId",
                schema: "LeadratBlack",
                table: "IntegrationAssignments");

            migrationBuilder.DropForeignKey(
                name: "FK_IntegrationAssignments_ChannelPartners_ChannelPartnerId",
                schema: "LeadratBlack",
                table: "IntegrationAssignments");

            migrationBuilder.DropForeignKey(
                name: "FK_IntegrationAssignments_Properties_PropertyId",
                schema: "LeadratBlack",
                table: "IntegrationAssignments");

            migrationBuilder.DropIndex(
                name: "IX_IntegrationAssignments_AgencyId",
                schema: "LeadratBlack",
                table: "IntegrationAssignments");

            migrationBuilder.DropIndex(
                name: "IX_IntegrationAssignments_CampaignId",
                schema: "LeadratBlack",
                table: "IntegrationAssignments");

            migrationBuilder.DropIndex(
                name: "IX_IntegrationAssignments_ChannelPartnerId",
                schema: "LeadratBlack",
                table: "IntegrationAssignments");

            migrationBuilder.DropIndex(
                name: "IX_IntegrationAssignments_PropertyId",
                schema: "LeadratBlack",
                table: "IntegrationAssignments");

            migrationBuilder.DropColumn(
                name: "AgencyId",
                schema: "LeadratBlack",
                table: "IntegrationAssignments");

            migrationBuilder.DropColumn(
                name: "CampaignId",
                schema: "LeadratBlack",
                table: "IntegrationAssignments");

            migrationBuilder.DropColumn(
                name: "ChannelPartnerId",
                schema: "LeadratBlack",
                table: "IntegrationAssignments");

            migrationBuilder.DropColumn(
                name: "PropertyId",
                schema: "LeadratBlack",
                table: "IntegrationAssignments");
        }
    }
}
