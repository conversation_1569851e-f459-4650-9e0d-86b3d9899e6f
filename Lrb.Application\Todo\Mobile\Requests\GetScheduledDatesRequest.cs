﻿using Lrb.Application.Common.Persistence;
using Lrb.Application.Utils;

namespace Lrb.Application.Todo.Mobile.Requests
{
    public class GetScheduledDatesRequest : IRequest<Response<List<DateTime>>>
    {
        public DateTime? FromDate { get; set; }
        public DateTime? ToDate { get; set; }
        public string? TimeZoneId { get; set; }
        public TimeSpan? BaseUtcOffset { get; set; }
    }

    public class GetScheduledDatesRequestHandler : IRequestHandler<GetScheduledDatesRequest, Response<List<DateTime>>>
    {
        private readonly ICurrentUser _currentUser;
        private readonly IDapperRepository _dapperRepository;

        public GetScheduledDatesRequestHandler(
            ICurrentUser currentUser,
            IDapperRepository dapperRepository)
        {
            _currentUser = currentUser;
            _dapperRepository = dapperRepository;
        }

        public async Task<Response<List<DateTime>>> Handle(GetScheduledDatesRequest request, CancellationToken cancellationToken)
        {
            try
            {
                var timeZoneId = !string.IsNullOrWhiteSpace(request.TimeZoneId) ? request.TimeZoneId : "Asia/Kolkata";
                var baseUtcOffset = request.BaseUtcOffset ?? TimeSpan.FromHours(5.5);

                var fromDateUtc = request.FromDate.Value.Date.ToUtcFromTimeZone(timeZoneId, baseUtcOffset);
                var toDateUtc = request.ToDate.Value.Date.AddDays(1).ToUtcFromTimeZone(timeZoneId, baseUtcOffset);

                var tenantId = _currentUser.GetTenant() ?? string.Empty;
                var userid = _currentUser.GetUserId();

                var result = await _dapperRepository.QueryStoredProcedureFromReadReplicaAsync<DateTime>("LeadratBlack", "GetScheduledDatesByDateRange",
                    new
                    {
                        p_user_id = userid,
                        p_tenant_id = tenantId,
                        p_from_date = fromDateUtc,
                        p_to_date = toDateUtc
                    }
                );

                var uniqueDatesByDay = result.Select(d => d.ToParticularTimeZone(timeZoneId, baseUtcOffset).Date).Distinct().OrderBy(d => d).ToList();
                return new Response<List<DateTime>>(uniqueDatesByDay)
                {
                    Succeeded = true
                };
            }
            catch (Exception ex)
            {
                return new Response<List<DateTime>>(new List<DateTime>())
                {
                    Succeeded = false,
                };
            }
        }
    }
}
