﻿using Lrb.Application.DataManagement.Web;
using Lrb.Application.DataManagement.Web.Dtos;
using Lrb.Application.DataManagement.Web.Dtos.CustomData;
using Lrb.Application.DataManagement.Web.Mapping;
using Lrb.Application.DataManagement.Web.Request.CommonHandler;
using Lrb.Application.DataManagement.Web.Specs;
using Lrb.Application.Identity.Users;
using Lrb.Domain.Entities;
using Lrb.Shared.Utils;
using Microsoft.Extensions.DependencyInjection;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Lrb.Application.Lead.Web.Requests.UpdationRequests
{
    public class UpdateProspectBulkAgencyListRequest : IRequest<Response<int>>
    {
        public List<string>? AgencyNames { get; set; }
        public List<Guid> Ids { get; set; } = default!;
        public bool? ShouldRemoveExistingAgency { get; set; }
        public Guid? CurrentUserId { get; set; }
        public string? TenantId { get; set; }


    }
    public class UpdateProspectBulkAgencyListRequestHandler : DataCommonRequestHandler, IRequestHandler<UpdateProspectBulkAgencyListRequest, Response<int>>
    {
        
        protected readonly ICurrentUser _currentUser;
        private readonly IRepositoryWithEvents<CustomProspectStatus> _prospectStatusRepo;
        private readonly IRepositoryWithEvents<MasterProspectSource> _prospectSourceRepo;
        private readonly IUserService _userService;
        private readonly IDapperRepository _dapperRepository;



        public UpdateProspectBulkAgencyListRequestHandler(
            IServiceProvider serviceProvider,
            ICurrentUser currentUser,
            IRepositoryWithEvents<CustomProspectStatus> prospectStatusRepo,
            IRepositoryWithEvents<MasterProspectSource> prospectSourceRepo,
            IUserService userService,
            IDapperRepository dapperRepository
           ) : base(serviceProvider)
        {
            _currentUser = currentUser;
            _userService = userService;
            _prospectStatusRepo = prospectStatusRepo;
            _prospectSourceRepo = prospectSourceRepo;
            _dapperRepository = dapperRepository;
        }

        public async Task<Response<int>> Handle(UpdateProspectBulkAgencyListRequest request, CancellationToken cancellationToken)
        {
            try
            {
                int totalUpdatedCount = 0;
                var existingProspects = await _prospectRepo.ListAsync(new GetProspectByIdsSpecs(request.Ids), cancellationToken);
                var currentUserId = request.CurrentUserId ?? _currentUser.GetUserId();
                var updatedProspects = new List<Prospect>();
                var prospectHistorys = new List<ProspectHistory>();
                List<Domain.Entities.Agency> agency = null;
                MasterProspectSource? source = null;
                if (request.AgencyNames != null)
                {
                     agency = await _agencyRepo.ListAsync(new GetAllAgencySpec(request?.AgencyNames?.ConvertAll(i => i.ToLower()) ?? new()), cancellationToken);

                    if (agency == null)
                    {
                        throw new NotFoundException("No Agency found by this Id");
                    }
                }

                if (existingProspects?.Any() ?? false)
                {
                    var statuses = await _prospectStatusRepo.ListAsync();
                    var propertyTypes = await _propertyTypeRepo.ListAsync();
                    var sources = await _prospectSourceRepo.ListAsync();
                    var userIds = new List<string?>();
                    existingProspects.ForEach(i => {
                        userIds.AddRange(new[] { i.AssignedFrom?.ToString(), i.AssignTo.ToString(), i.LastModifiedBy.ToString(), i.SourcingManager?.ToString(), i.ClosingManager?.ToString() });
                    });
                    userIds.Add(currentUserId.ToString());
                    userIds = userIds.Where(i => i != null && i != string.Empty)?.DistinctBy(i => i).ToList();
                    var users = await _userService.GetListOfUsersByIdsAsync(userIds, cancellationToken);
                    foreach (var existingProspect in existingProspects)
                    {
                        var oldProspect = existingProspect.Adapt<ViewProspectDto>();
                        var prospect = request.Adapt(existingProspect);
                        if ((prospect.Agencies?.Any() ?? false) && request?.ShouldRemoveExistingAgency == false)
                        {
                            List<Lrb.Domain.Entities.Agency> newAgency = new();
                            newAgency.AddRange(prospect.Agencies);
                            newAgency.AddRange(agency);
                            newAgency = newAgency.DistinctBy(p => p.Id).ToList();
                            prospect.Agencies = newAgency;
                        }
                        else
                        {
                            prospect.Agencies = agency;
                        }


                        prospect.LastModifiedBy = currentUserId;
                        prospect.LastModifiedOn = DateTime.UtcNow;
                        totalUpdatedCount++;
                        updatedProspects.Add(prospect);

                        var prospectVM = prospect.Adapt<ViewProspectDto>();
                        prospectVM = await ProspectHistoryHelper.SetUserViewForProspect(prospectVM, _userService, cancellationToken, currentUserId: currentUserId, userDetails: users);
                        oldProspect = await ProspectHistoryHelper.SetUserViewForProspect(oldProspect, _userService, cancellationToken, currentUserId: currentUserId, userDetails: users);
                        var histories = await ProspectHistoryHelper.UpdateProspectHistoryForVM(prospectVM, oldProspect, currentUserId, 1, statuses, propertyTypes, sources, _userService, cancellationToken);
                        prospectHistorys.AddRange(histories);
                    }
                    if (updatedProspects.Any())
                    {
                        var prospectAgencies = updatedProspects.Where(prospect => prospect.Agencies != null && prospect.Agencies.Any()).SelectMany(prospect => prospect.Agencies.Select(i => new AgencyProspectDto
                        {
                            ProspectsId = prospect.Id,
                            AgenciesId = i.Id
                        })).ToList();
                        if (prospectAgencies.Any())
                        {
                            if (request?.ShouldRemoveExistingAgency == true)
                            {
                                var prospectIdsToClear = updatedProspects.Select(l => l.Id).Distinct().ToList();
                                if (prospectIdsToClear.Any())
                                {
                                    var prospectIdList = string.Join(", ", prospectIdsToClear.Select(id => $"'{id}'"));
                                    var deleteQuery = $"DELETE FROM \"{DataBaseDetails.LRBSchema}\".\"AgencyProspect\" WHERE \"ProspectsId\" IN ({prospectIdList});";
                                    await _dapperRepository.ExecuteQueryAsync(deleteQuery);
                                }
                            }
                            List<string> columnNames = new List<string> { "AgenciesId", "ProspectsId" };
                            var agencyInsertQuery = QueryGenerator.GenerateInsertQueryV1(null, DataBaseDetails.LRBSchema, "AgencyProspect", columnNames, prospectAgencies);
                            await _dapperRepository.ExecuteQueryAsync(agencyInsertQuery);
                        }
                        var addHistories = prospectHistorys.Adapt<List<ProspectHistoryDapperDto>>();
                        var columns = QueryGenerator.GetMappedProperties<ProspectHistoryDapperDto>();
                        var insertQuery = QueryGenerator.GenerateInsertQuery(request?.TenantId, DataBaseDetails.LRBSchema, "ProspectHistories", columns, addHistories);
                        await _dapperRepository.ExecuteQueryAsync(insertQuery);
                    }
                }

                return new Response<int>(totalUpdatedCount);
            }
            catch (Exception ex)
            {
                throw new ApplicationException("Error updating prospect Agencies", ex);
            }
        }
    }
}

      
