﻿using Lrb.Domain.Entities.Integration;
using Newtonsoft.Json;
using System.Text;
using System.Timers;

namespace Lrb.Application.Integration.Web
{
    public static class IntegrationTemplateBuilder
    {
        static string? env = Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT");
        public static IDictionary<string, string> CreateIntegrationTemplate(string tenant, IntegrationAccountInfo dataParams, IVRType callType = IVRType.Inbound, IVRServiceProvider ivrServiceProvider = IVRServiceProvider.None, string? serviceProvider = null, Dictionary<string, string>? payloadForExcel = null, bool? isPayloadInForm = false, IVRPayloadMapping? ivrPayloadMapping = null, WebhookPayloadMapping? webhookPayloadMapping = null, bool? canSendApiKey = null)
        {
            string workingEndpointOpenUrlValue = string.Empty;
            switch (env)
            {
                case "dev":
                    workingEndpointOpenUrlValue = IntegrationTemplateValues.DevWorkingEndpointOpenUrlValue;
                    break;
                case "qa":
                    workingEndpointOpenUrlValue = IntegrationTemplateValues.QAWorkingEndpointOpenUrlValue;
                    break;
                case "prd":
                    workingEndpointOpenUrlValue = IntegrationTemplateValues.PrdWorkingEndpointOpenUrlValue;
                    break;
                default:
                    break;
            }
            if (dataParams.LeadSource == LeadSource.WhatsApp)
            {
                workingEndpointOpenUrlValue = GetWAWorkingEndPoint();
            }
            IDictionary<string, string> excelIntegrationContent = new Dictionary<string, string>();
            if (dataParams.LeadSource == LeadSource.Any)
            {
                excelIntegrationContent.Add(IntegrationTemplateKeys.EnumValues, FormatLeadSources());
            }
            excelIntegrationContent.Add(IntegrationTemplateKeys.HeaderKey, IntegrationTemplateValues.HeaderValue);
            excelIntegrationContent.Add(IntegrationTemplateKeys.Method, IntegrationTemplateValues.PushIntegrationTypeValue);
            excelIntegrationContent.Add(IntegrationTemplateKeys.CRMNameKey, IntegrationTemplateValues.CRMNameValue);
            excelIntegrationContent.Add(IntegrationTemplateKeys.APIKey, dataParams.ApiKey);
            string? workingEndPointResource = string.Empty;
            if (dataParams.LeadSource == LeadSource.IVR)
            {
                workingEndPointResource = GetWorkingEndPointUrlKey(ivrServiceProvider, callType, tenant, serviceProvider, isPayloadInForm, ivrPayloadMapping, canSendApiKey, dataParams.Id);
                if (!string.IsNullOrWhiteSpace(ivrPayloadMapping?.MethodType))
                {
                    excelIntegrationContent[IntegrationTemplateKeys.Method] = ivrPayloadMapping.MethodType;
                    excelIntegrationContent.Add(IntegrationTemplateKeys.WorkingEndpointOpenUrlKey,
                        string.Format(workingEndpointOpenUrlValue, 1, dataParams.LeadSource.ToString() + workingEndPointResource));
                }
            }
            else if (dataParams.LeadSource == LeadSource.WhatsApp)
            {
                workingEndPointResource = "whatsapp";
                excelIntegrationContent.Add(IntegrationTemplateKeys.WorkingEndpointOpenUrlKey,
                    string.Format(workingEndpointOpenUrlValue, 1, workingEndPointResource));
            }
            else if (dataParams.LeadSource == LeadSource.Webhook)
            {
                workingEndPointResource = $"Webhook/{tenant}/{dataParams.ApiKey}";
                excelIntegrationContent.Add(IntegrationTemplateKeys.WorkingEndpointOpenUrlKey,
                string.Format(workingEndpointOpenUrlValue, 1, workingEndPointResource));
            }
            else if(dataParams.LeadSource == LeadSource.Dubizzle)
            {
                workingEndPointResource = $"dubizzle/{tenant}/{dataParams.ApiKey}";
                excelIntegrationContent.Add(IntegrationTemplateKeys.WorkingEndpointOpenUrlKey,
                    string.Format(workingEndpointOpenUrlValue, 1, workingEndPointResource));
            }
            else if(dataParams.LeadSource == LeadSource.PropertyFinder)
            {
                workingEndPointResource = $"propertyfinder/{tenant}/{dataParams.ApiKey}";
                excelIntegrationContent.Add(IntegrationTemplateKeys.WorkingEndpointOpenUrlKey,
                    string.Format(workingEndpointOpenUrlValue, 1, workingEndPointResource));
            }
            else if (dataParams.LeadSource == LeadSource.Bayut)
            {
                workingEndPointResource = $"bayut/{tenant}/{dataParams.ApiKey}";
                excelIntegrationContent.Add(IntegrationTemplateKeys.WorkingEndpointOpenUrlKey,
                    string.Format(workingEndpointOpenUrlValue, 1, workingEndPointResource));
            }
            else
            {
                excelIntegrationContent.Add(IntegrationTemplateKeys.WorkingEndpointOpenUrlKey,
                    string.Format(workingEndpointOpenUrlValue, 1, dataParams.LeadSource.ToString() + workingEndPointResource));
            }
            if (dataParams.LeadSource != LeadSource.Webhook)
            {
                excelIntegrationContent.Add(IntegrationTemplateKeys.PayloadKey, GetRequestBodyJsonFromFile(dataParams.LeadSource, ivrServiceProvider, payloadForExcel));
            }
            excelIntegrationContent.Add(IntegrationTemplateKeys.SuccessReponseMessageSuccessKey, IntegrationTemplateValues.SuccessReponseMessageSuccessValue);
            excelIntegrationContent.Add(IntegrationTemplateKeys.FailReponseMessageSuccessKey, IntegrationTemplateValues.FailReponseMessageSuccessValue);
            excelIntegrationContent.Add(IntegrationTemplateKeys.AuthenticationTypeKey, IntegrationTemplateValues.AuthenticationTypeValue);
            excelIntegrationContent.Add(IntegrationTemplateKeys.IntegrationMannualKey, IntegrationTemplateValues.IntegrationMannualValue);
            var result = GenerateCURL(excelIntegrationContent);
            try
            {
                if (dataParams.LeadSource == LeadSource.PropertyWala)
                {
                    excelIntegrationContent["Working Endpoint URL"] += !string.IsNullOrWhiteSpace(tenant) ? $"/{tenant}" : string.Empty;
                }
            }
            catch (Exception ex) { }

            if (dataParams.LeadSource == LeadSource.IVR)
            {
                result = GenerateCURLForIVR(excelIntegrationContent, dataParams.LeadSource, isPayloadInForm, tenant, serviceProvider, ivrPayloadMapping, canSendApiKey, dataParams.Id, payloadForExcel);
            }
            else if (dataParams.LeadSource == LeadSource.RealEstateIndia || dataParams.LeadSource == LeadSource.PropertyWala || (isPayloadInForm ?? false))
            {
                result = GenerateCURLForForm(excelIntegrationContent, dataParams.LeadSource, isPayloadInForm);
            }
            else if(dataParams.LeadSource == LeadSource.PropertyFinder || dataParams.LeadSource == LeadSource.Bayut || dataParams.LeadSource == LeadSource.Dubizzle)
            {
                result = GenerateCURLForDubai(excelIntegrationContent, canIncludeApiKey: true);
            }

            excelIntegrationContent.Add(IntegrationTemplateKeys.CURL, result);
            return excelIntegrationContent.Reverse().ToDictionary(i => i.Key, i => i.Value);
        }
        public static string GetWAWorkingEndPoint()
        {
            switch (env)
            {
                case "dev":
                    return IntegrationTemplateValues.DevWorkingEndpointOpenUrlValue;
                case "qa":
                    return IntegrationTemplateValues.QAWorkingEndpointOpenUrlValue;
                case "prd":
                    return IntegrationTemplateValues.PrdWorkingEndpointOpenUrlValue;
                default:
                    return string.Empty;
            }
        }
        public static IDictionary<string, string> CreateCronberryIntegrationTemplate(string tenant, IntegrationAccountInfo dataParams, IVRType callType = IVRType.Inbound, IVRServiceProvider ivrServiceProvider = IVRServiceProvider.None)
        {
            string workingEndpointOpenUrlValue = string.Empty;
            switch (env)
            {
                case "dev":
                    workingEndpointOpenUrlValue = IntegrationTemplateValues.DevWorkingEndpointOpenUrlValue;
                    break;
                case "qa":
                    workingEndpointOpenUrlValue = IntegrationTemplateValues.QAWorkingEndpointOpenUrlValue;
                    break;
                case "prd":
                    workingEndpointOpenUrlValue = IntegrationTemplateValues.PrdWorkingEndpointOpenUrlValue;
                    break;
                default:
                    break;
            }
            IDictionary<string, string> excelIntegrationContent = new Dictionary<string, string>();
            excelIntegrationContent.Add(IntegrationTemplateKeys.HeaderKey, IntegrationTemplateValues.HeaderValue);
            excelIntegrationContent.Add(IntegrationTemplateKeys.Method, IntegrationTemplateValues.GetIntegrationTypeValue);
            excelIntegrationContent.Add(IntegrationTemplateKeys.CRMNameKey, IntegrationTemplateValues.CRMNameValue);
            excelIntegrationContent.Add(IntegrationTemplateKeys.WorkingEndpointOpenUrlKey,
            string.Format(workingEndpointOpenUrlValue, 1, dataParams.LeadSource.ToString() + GetWorkingEndPointUrlKey(ivrServiceProvider, callType)
            + $"/{tenant}/{dataParams.Id.GenerateApiKey()}"));
            excelIntegrationContent.Add(IntegrationTemplateKeys.PayloadKey, GetRequestBodyJsonFromFile(dataParams.LeadSource, ivrServiceProvider));
            excelIntegrationContent.Add(IntegrationTemplateKeys.SuccessReponseMessageSuccessKey, IntegrationTemplateValues.SuccessReponseMessageSuccessValue);
            excelIntegrationContent.Add(IntegrationTemplateKeys.FailReponseMessageSuccessKey, IntegrationTemplateValues.FailReponseMessageSuccessValue);
            excelIntegrationContent.Add(IntegrationTemplateKeys.AuthenticationTypeKey, IntegrationTemplateValues.AuthenticationTypeValue);
            excelIntegrationContent.Add(IntegrationTemplateKeys.IntegrationMannualKey, IntegrationTemplateValues.IntegrationMannualValue);
            var result = GenerateCURLForCronberry(excelIntegrationContent, dataParams.Id, tenant);
            excelIntegrationContent.Add(IntegrationTemplateKeys.CURL, result);
            return excelIntegrationContent.Reverse().ToDictionary(i => i.Key, i => i.Value);
        }
        public static IDictionary<string, string> CreateExotelIntegrationTemplate(string tenant, IntegrationAccountInfo dataParams, IVRType callType = IVRType.Inbound, IVRServiceProvider ivrServiceProvider = IVRServiceProvider.None)
        {
            string workingEndpointOpenUrlValue = string.Empty;
            switch (env)
            {
                case "dev":
                    workingEndpointOpenUrlValue = IntegrationTemplateValues.DevWorkingEndpointOpenUrlValue;
                    break;
                case "qa":
                    workingEndpointOpenUrlValue = IntegrationTemplateValues.QAWorkingEndpointOpenUrlValue;
                    break;
                case "prd":
                    workingEndpointOpenUrlValue = IntegrationTemplateValues.PrdWorkingEndpointOpenUrlValue;
                    break;
                default:
                    break;
            }
            IDictionary<string, string> excelIntegrationContent = new Dictionary<string, string>();
            excelIntegrationContent.Add(IntegrationTemplateKeys.HeaderKey, IntegrationTemplateValues.HeaderValue);
            excelIntegrationContent.Add(IntegrationTemplateKeys.Method, IntegrationTemplateValues.GetIntegrationTypeValue);
            excelIntegrationContent.Add(IntegrationTemplateKeys.CRMNameKey, IntegrationTemplateValues.CRMNameValue);
            excelIntegrationContent.Add(IntegrationTemplateKeys.WorkingEndpointOpenUrlKey,
            string.Format(workingEndpointOpenUrlValue, 1, dataParams.LeadSource.ToString() + GetWorkingEndPointUrlKey(ivrServiceProvider, callType)
            + $"/{tenant}/{dataParams.Id.GenerateApiKey()}"));
            excelIntegrationContent.Add(IntegrationTemplateKeys.PayloadKey, GetRequestBodyJsonFromFile(dataParams.LeadSource, ivrServiceProvider));
            excelIntegrationContent.Add(IntegrationTemplateKeys.SuccessReponseMessageSuccessKey, IntegrationTemplateValues.SuccessReponseMessageSuccessValue);
            excelIntegrationContent.Add(IntegrationTemplateKeys.FailReponseMessageSuccessKey, IntegrationTemplateValues.FailReponseMessageSuccessValue);
            excelIntegrationContent.Add(IntegrationTemplateKeys.AuthenticationTypeKey, IntegrationTemplateValues.AuthenticationTypeValue);
            excelIntegrationContent.Add(IntegrationTemplateKeys.IntegrationMannualKey, IntegrationTemplateValues.IntegrationMannualValue);
            var result = GenerateCURLForExotel(excelIntegrationContent, dataParams.Id, tenant);
            excelIntegrationContent.Add(IntegrationTemplateKeys.CURL, result);
            return excelIntegrationContent.Reverse().ToDictionary(i => i.Key, i => i.Value);
        }
        private static string GetWorkingEndPointUrlKey(IVRServiceProvider ivrServiceProvider, IVRType callType, string? tenant = null, string? serviceProvider = null, bool? isPayloadInForm = null, IVRPayloadMapping? ivrPayloadMapping = null, bool? canSendApiKey = null, Guid? accountId = null)
        {
            string url = string.Empty;
            switch (ivrServiceProvider)
            {
                case IVRServiceProvider.Servetel:
                    if (callType == IVRType.Inbound)
                    {
                        url = "/Servetel/Inbound";
                    }
                    else if (callType == IVRType.Outbound)
                    {
                        url = "/Servetel/Outbound";
                    }
                    break;
                case IVRServiceProvider.MyOperator:
                    if (callType == IVRType.Inbound)
                    {
                        url = "/MyOperator/Inbound";
                    }
                    else if (callType == IVRType.Outbound)
                    {
                        url = "/MyOperator/Outbound";
                    }
                    break;
                case IVRServiceProvider.Cronberry:
                    if (callType == IVRType.Inbound)
                    {
                        url = "/Cronberry/Inbound";
                    }
                    else if (callType == IVRType.Outbound)
                    {
                        url = "/Cronberry/Outbound";
                    }
                    break;
                case IVRServiceProvider.Exotel:
                    if (callType == IVRType.Inbound)
                    {
                        url = "/Exotel/Inbound";
                    }
                    else if (callType == IVRType.Outbound)
                    {
                        url = "/Exotel/Outbound";
                    }
                    break;
                case IVRServiceProvider.VoicePanel:
                    if (callType == IVRType.Inbound)
                    {
                        url = "/VoicePanel/Inbound";
                    }
                    else if (callType == IVRType.Outbound)
                    {
                        url = "/VoicePanel/Outbound";
                    }
                    break;
                case IVRServiceProvider.QKonnect:
                    if (callType == IVRType.Inbound)
                    {
                        url = "/qkonnect/Inbound";
                    }
                    else if (callType == IVRType.Outbound)
                    {
                        url = "/qkonnect/Outbound";
                    }
                    break;
                case IVRServiceProvider.FreJun:
                    url = "/frejun/" + (tenant ?? string.Empty);
                    break;
                case IVRServiceProvider.TataTeleBusiness:
                    if (callType == IVRType.Inbound)
                    {
                        url = "/TataTeleBusiness/Inbound";
                    }
                    else if (callType == IVRType.Outbound)
                    {
                        url = "/TataTeleBusiness/Outbound";
                    }
                    break;
                case IVRServiceProvider.MCube:
                case IVRServiceProvider.MCubeClassic:
                    url = "/mcube";
                    break;
                case IVRServiceProvider.Knowlarity:
                    url = "/knowlarity";
                    break;
                case IVRServiceProvider.Kommuno:
                    if (callType == IVRType.Inbound)
                    {
                        url = "/Kommuno/Inbound";
                    }
                    else if (callType == IVRType.Outbound)
                    {
                        url = "/Kommuno/Outbound";
                    }
                    break;
                default:
                    //if (isPayloadInForm ?? false)
                    //{
                    //    url = $"/common/form/{serviceProvider}";
                    //}
                    //else
                    //{
                    //    url = $"/common/body/{serviceProvider}";
                    //}
                    url = GetIVRPayloadUrl(ivrPayloadMapping ?? new(), canSendApiKey ?? false, serviceProvider ?? string.Empty, isPayloadInForm ?? false, tenant ?? string.Empty, accountId ?? Guid.Empty);
                    break;
            }
            return url;
        }
        public static string GetWAPayloadUrl(IVRPayloadMapping ivrPayloadMapping, bool canSendApiKey, string serviceProvider, bool isPayloadInForm, string tenant, Guid accountId)
        {
            string url = string.Empty;
            switch (ivrPayloadMapping.MethodType)
            {
                case "GET":
                    if (canSendApiKey)
                    {
                        url = $"/common/query/{serviceProvider}";
                    }
                    else
                    {
                        url = $"/common/query/{serviceProvider}/{tenant}/{accountId.GenerateApiKey()}";
                    }
                    break;
                case "POST":
                    switch (ivrPayloadMapping.ContentType)
                    {
                        case "application/json":
                            if (canSendApiKey)
                            {
                                url = $"/common/body/{serviceProvider}";
                            }
                            else
                            {
                                url = $"/common/body/{serviceProvider}/{tenant}/{accountId.GenerateApiKey()}";
                            }
                            break;
                        case "x-www-form-urlencoded":
                        case "form-data":
                            if (canSendApiKey)
                            {
                                url = $"/common/form/{serviceProvider}";
                            }
                            else
                            {
                                url = $"/common/form/{serviceProvider}/{tenant}/{accountId.GenerateApiKey()}";
                            }
                            break;
                    }
                    break;
                default:
                    break;
            }
            return url;
        }
        public static string GetIVRPayloadUrl(IVRPayloadMapping ivrPayloadMapping, bool canSendApiKey, string serviceProvider, bool isPayloadInForm, string tenant, Guid accountId)
        {
            string url = string.Empty;
            switch (ivrPayloadMapping.MethodType)
            {
                case "GET":
                    if (canSendApiKey)
                    {
                        url = $"/common/query/{serviceProvider}";
                    }
                    else
                    {
                        url = $"/common/query/{serviceProvider}/{tenant}/{accountId.GenerateApiKey()}";
                    }
                    break;
                case "POST":
                    switch (ivrPayloadMapping.ContentType)
                    {
                        case "application/json":
                            if (canSendApiKey)
                            {
                                url = $"/common/body/{serviceProvider}";
                            }
                            else
                            {
                                url = $"/common/body/{serviceProvider}/{tenant}/{accountId.GenerateApiKey()}";
                            }
                            break;
                        case "x-www-form-urlencoded":
                        case "form-data":
                            if (canSendApiKey)
                            {
                                url = $"/common/form/{serviceProvider}";
                            }
                            else
                            {
                                url = $"/common/form/{serviceProvider}/{tenant}/{accountId.GenerateApiKey()}";
                            }
                            break;
                    }
                    break;
                default:
                    break;
            }
            return url;
        }
        public static string GetRequestBodyJsonFromFile(LeadSource leadSource, IVRServiceProvider ivrServiceProvider = IVRServiceProvider.None, Dictionary<string, string>? payload = null)
        {
            if (payload != null && leadSource == LeadSource.IVR && ivrServiceProvider != IVRServiceProvider.FreJun)
            {
                return JsonConvert.SerializeObject(payload, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented });
            }
  
            else if (payload != null && leadSource == LeadSource.WhatsApp)
            {
                return JsonConvert.SerializeObject(payload, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented });
            }
            var currentDir = Directory.GetCurrentDirectory();
            string path = string.Empty;

            if (leadSource == LeadSource.Website)
            {
                path = Path.Combine(currentDir, "Files/Integration/WebsiteIntegrationPayload.json");
            }
            else if (leadSource == LeadSource.IVR)
            {
                path = Path.Combine(currentDir, GetIVRPayloadFilePathByServiceProvider(ivrServiceProvider));
            }
            else if (leadSource == LeadSource.Facebook)
            {
                path = Path.Combine(currentDir, "Files/Integration/FacebookWebhookPayload.json");
            }
            else if (leadSource == LeadSource.Any)
            {
                path = Path.Combine(currentDir, "Files/Integration/CommonWebhookPayload.json");
            }
            else if (leadSource == LeadSource.RealEstateIndia)
            {
                path = Path.Combine(currentDir, "Files/Integration/RealStateIndiaIntegrationPayload.json");
            }
            else if(leadSource == LeadSource.Dubizzle || leadSource == LeadSource.Bayut)
            {
                path = Path.Combine(currentDir, "Files/Integration/BayutWAIntegrationPayload.json");
            }
            else if(leadSource == LeadSource.PropertyFinder)
            {
                path = Path.Combine(currentDir, "Files/Integration/PropertyFinderIntegrationPayload.json");
            }
            else
            {
                path = Path.Combine(currentDir, "Files/Integration/ListingSitesIntegrationPayload.json");
            }
            StreamReader r = new(path);
            return r.ReadToEnd();
        }
        public static string GetIVRPayloadFilePathByServiceProvider(IVRServiceProvider ivrServiceProvider = IVRServiceProvider.None)
        {
            switch (ivrServiceProvider)
            {
                case IVRServiceProvider.Servetel:
                    return "Files/Integration/ServetelIntegrationPayload.json";

                case IVRServiceProvider.MyOperator:
                    return "Files/Integration/MyOperatorIntegrationPayload.json";

                case IVRServiceProvider.Cronberry:
                    return "Files/Integration/CronberryIntegrationPayload.json";

                case IVRServiceProvider.Exotel:
                    return "Files/Integration/ExotelIntegrationPayload.json";

                case IVRServiceProvider.VoicePanel:
                    return "Files/Integration/VoicePanelIntegrationPayload.json";

                case IVRServiceProvider.QKonnect:
                    return "Files/Integration/QKonnectIntegrationPayload.json";
                case IVRServiceProvider.TataTeleBusiness:
                    return "Files/Integration/TataTeleBusinessIntegrationPayload.json";
                case IVRServiceProvider.FreJun:
                    return "Files/Integration/FreJunIntegrationPayload.json";

                case IVRServiceProvider.MCube:
                case IVRServiceProvider.MCubeClassic:
                    return "Files/Integration/MCubeIntegrationPayload.json";
                case IVRServiceProvider.Knowlarity:
                    return "Files/Integration/IVRIntegrationPayload.json";
                case IVRServiceProvider.Kommuno:
                    return "Files/Integration/KommunoIntegrationPayload.json";
                default:
                    return "Files/Integration/ServetelIntegrationPayload.json";
            }
        }
        private static string GenerateCURLForCronberry(IDictionary<string, string> excelIntegrationContent, Guid accountId, string tenant)
        {
            string result = string.Empty;
            string apiUrl = string.Empty;
            string apiKey = string.Empty;
            //string parameter = "?VirtualNumber=**********&CustomerNumber=**********&ReceiverNumber=**********&CallDate=2022-10-12T22%3A39%3A35.000000Z&Status=NoAnswered&RecordingPath=https%3A%2F%2Fwww.airmasterz.org%2Fstorage%2Frecordings%2F********-223935_123456981114_4567515194_Inbound.wav&UniqueId=**********.242258";
            string parameter = "?virtual_number=************&customer_number=**********&receiver_number=**********&call_date=2022-10-12T22:39:35.000000Z&status=NoAnswered&recording_path=https://www.airmasterz.org/storage/recordings/********-223935_123456981114_4567515194_Inbound.wav&unique_id=**********.242258";
            string content = "application/json";
            var accountIdBase64 = accountId.GenerateApiKey();
            if (excelIntegrationContent.Any(i => i.Key.Contains("Working Endpoint URL")))
            {
                var extApiUrl = (excelIntegrationContent.FirstOrDefault(i => i.Key.Contains("Working Endpoint URL")));
                apiUrl = extApiUrl.Value;
            }
            if (excelIntegrationContent.Any(i => i.Key.Contains("tenant")))
            {
                var extTenant = (excelIntegrationContent.FirstOrDefault(i => i.Key.Contains("tenant")));
                tenant = extTenant.Value;
            }
            apiUrl += $"{parameter}";
            string curl = $"curl -X 'GET' \\ '{apiUrl}' \\ -H 'accept: application/json'";
            return curl;
        }
        private static string GenerateCURLForExotel(IDictionary<string, string> excelIntegrationContent, Guid accountId, string tenant)
        {
            string result = string.Empty;
            string apiUrl = string.Empty;
            string apiKey = string.Empty;
            string parameter = "?callFrom=string&callSid=string&callTo=string&callType=string&created=string&currentTime=string&dialCallDuration=string&dialCallStatus=string&dialWhomNumber=string&direction=string&endTime=2023-07-21T06%3A45%3A48.213Z&flow_id=string&forwardedFrom=string&from=string&legs=string&outgoingPhoneNumber=string&processStatus=string&recordingAvailableBy=string&recordingUrl=string&startTime=2023-07-21T06%3A45%3A48.213Z&tenant_id=string&to=string";
            string content = "application/json";
            var accountIdBase64 = accountId.GenerateApiKey();
            if (excelIntegrationContent.Any(i => i.Key.Contains("Working Endpoint URL")))
            {
                var extApiUrl = (excelIntegrationContent.FirstOrDefault(i => i.Key.Contains("Working Endpoint URL")));
                apiUrl = extApiUrl.Value;
            }
            if (excelIntegrationContent.Any(i => i.Key.Contains("tenant")))
            {
                var extTenant = (excelIntegrationContent.FirstOrDefault(i => i.Key.Contains("tenant")));
                tenant = extTenant.Value;
            }
            apiUrl += $"{parameter}";
            string curl = $"curl -X 'GET' \\ '{apiUrl}' \\ -H 'accept: {content}'";
            return curl;
        }
        private static string GenerateCURL(IDictionary<string, string> excelIntegrationContent, bool? isPayloadInForm = false, Dictionary<string, string>? payloadForExcel = null, bool? canIncludeApiKey = null)
        {
            string result = string.Empty;
            string apiUrl = string.Empty;
            string apiKey = string.Empty;
            string parameter = string.Empty;
            string tenant = string.Empty;
            string content = "application/json";
            string formContent = "multipart/form-data";
            if (excelIntegrationContent.Any(i => i.Key.Contains("Working Endpoint URL")))
            {
                var extApiUrl = (excelIntegrationContent.FirstOrDefault(i => i.Key.Contains("Working Endpoint URL")));
                apiUrl = extApiUrl.Value;
            }
            if (excelIntegrationContent.Any(i => i.Key.Contains("API-Key")))
            {
                var extApiKey = (excelIntegrationContent.FirstOrDefault(i => i.Key.Contains("API-Key")));
                apiKey = extApiKey.Value;
            }
            if (excelIntegrationContent.Any(i => i.Key.Contains("tenant")))
            {
                var extTenant = (excelIntegrationContent.FirstOrDefault(i => i.Key.Contains("tenant")));
                tenant = extTenant.Value;
            }
            if (excelIntegrationContent.Any(i => i.Key.Contains("Parameters to be passed with values")))
            {
                var extParameter = (excelIntegrationContent.FirstOrDefault(i => i.Key.Contains("Parameters to be passed with values")));
                parameter = extParameter.Value;
            }
            string curl = string.Empty;
            if ((!canIncludeApiKey ?? true) && (payloadForExcel?.Any() ?? false))
            {
                curl = $"curl --location --request POST  {apiUrl} -H \"Content-Type: {content}\" -d '{parameter}'";
            }
            else
            {
                curl = $"curl --location --request POST  {apiUrl} -H \"API-Key: {apiKey}\" -H \"Content-Type: {content}\" -d '{parameter}'";
            }
            if (isPayloadInForm ?? false)
            {
                curl = $"curl --location --request POST  {apiUrl} -H \"API-Key: {apiKey}\" -H \"Content-Type: {formContent}\" {GetParamsForForm(payloadForExcel)}";
            }
            return curl;
        }

        private static string GetParamsForForm(Dictionary<string, string>? formParams)
        {
            string formattedParams = string.Empty;
            foreach (var param in formParams)
            {
                formattedParams += $"--form '{param.Key}=\"{param.Value}\"' \\\r\n";
            }
            return formattedParams;
        }

        private static string GenerateCURLWithoutTenant(IDictionary<string, string> excelIntegrationContent)
        {
            string result = string.Empty;
            string apiUrl = string.Empty;
            string apiKey = string.Empty;
            string parameter = string.Empty;
            string tenant = string.Empty;
            string content = "application/json";
            if (excelIntegrationContent.Any(i => i.Key.Contains("Working Endpoint URL")))
            {
                var extApiUrl = (excelIntegrationContent.FirstOrDefault(i => i.Key.Contains("Working Endpoint URL")));
                apiUrl = extApiUrl.Value;
            }
            if (excelIntegrationContent.Any(i => i.Key.Contains("API-Key")))
            {
                var extApiKey = (excelIntegrationContent.FirstOrDefault(i => i.Key.Contains("API-Key")));
                apiKey = extApiKey.Value;
            }
            //if (excelIntegrationContent.Any(i => i.Key.Contains("tenant")))
            //{
            //    var extTenant = (excelIntegrationContent.FirstOrDefault(i => i.Key.Contains("tenant")));
            //    tenant = extTenant.Value;
            //}
            if (excelIntegrationContent.Any(i => i.Key.Contains("Parameters to be passed with values")))
            {
                var extParameter = (excelIntegrationContent.FirstOrDefault(i => i.Key.Contains("Parameters to be passed with values")));
                parameter = extParameter.Value;
            }
            string curl = $"curl --location --request POST  {apiUrl} -H \"API-Key: {apiKey}\" -H \"Content-Type: {content}\" -d '{parameter}'";
            return curl;
        }
        public static IDictionary<string, string> CreateGoogleAdIntegrationTemplate(GoogleAdLeadFormIntegrationInfo dataParams, string currentTenant)
        {
            string workingEndpointOpenUrlValue = string.Empty;
            switch (env)
            {
                case "dev":
                    workingEndpointOpenUrlValue = IntegrationTemplateValues.DevWorkingEndpointOpenUrlValue;
                    break;
                case "qa":
                    workingEndpointOpenUrlValue = IntegrationTemplateValues.QAWorkingEndpointOpenUrlValue;
                    break;
                case "prd":
                    workingEndpointOpenUrlValue = IntegrationTemplateValues.PrdWorkingEndpointOpenUrlValue;
                    break;
                default:
                    break;
            }
            IDictionary<string, string> excelIntegrationContent = new Dictionary<string, string>();
            excelIntegrationContent.Add(IntegrationTemplateKeys.HeaderKey, IntegrationTemplateValues.HeaderValue);
            excelIntegrationContent.Add(IntegrationTemplateKeys.Method, IntegrationTemplateValues.PushIntegrationTypeValue);
            excelIntegrationContent.Add(IntegrationTemplateKeys.CRMNameKey, IntegrationTemplateValues.CRMNameValue);
            excelIntegrationContent.Add(IntegrationTemplateKeys.APIKey, dataParams.ApiKey);
            excelIntegrationContent.Add(IntegrationTemplateKeys.WorkingEndpointOpenUrlKey,
              string.Format(workingEndpointOpenUrlValue, 1, LeadSource.GoogleAds.ToString() + $"/LeadForm/{currentTenant}"));
            var result = GenerateCURLWithoutTenant(excelIntegrationContent);
            excelIntegrationContent.Add(IntegrationTemplateKeys.CURL, result);
            return excelIntegrationContent.Reverse().ToDictionary(i => i.Key, i => i.Value);
        }

        public static IDictionary<string, string> CreateIntegrationTemplate2(string tenant, IntegrationAccountInfo dataParams, IVRType callType = IVRType.Inbound, IVRServiceProvider ivrServiceProvider = IVRServiceProvider.None)
        {
            string workingEndpointOpenUrlValue = string.Empty;
            switch (env)
            {
                case "dev":
                    workingEndpointOpenUrlValue = IntegrationTemplateValues.DevWorkingEndpointOpenUrlValue;
                    break;
                case "qa":
                    workingEndpointOpenUrlValue = IntegrationTemplateValues.QAWorkingEndpointOpenUrlValue;
                    break;
                case "prd":
                    workingEndpointOpenUrlValue = IntegrationTemplateValues.PrdWorkingEndpointOpenUrlValue;
                    break;
                default:
                    break;
            }
            IDictionary<string, string> excelIntegrationContent = new Dictionary<string, string>();
            excelIntegrationContent.Add(IntegrationTemplateKeys.HeaderKey, IntegrationTemplateValues.HeaderValue);
            excelIntegrationContent.Add(IntegrationTemplateKeys.Method, IntegrationTemplateValues.PushIntegrationTypeValue);
            excelIntegrationContent.Add(IntegrationTemplateKeys.CRMNameKey, IntegrationTemplateValues.CRMNameValue);
            excelIntegrationContent.Add(IntegrationTemplateKeys.APIKey, dataParams.ApiKey);
            if (dataParams.LeadSource == LeadSource.GoogleAds)
            {
                excelIntegrationContent.Add(IntegrationTemplateKeys.WorkingEndpointOpenUrlKey,
               string.Format(workingEndpointOpenUrlValue, 1, LeadSource.GoogleAds.ToString() + "/LandingPage"));
            }
            else
            {
                excelIntegrationContent.Add(IntegrationTemplateKeys.WorkingEndpointOpenUrlKey,
               string.Format(workingEndpointOpenUrlValue, 1, dataParams.LeadSource.ToString() + (ivrServiceProvider == IVRServiceProvider.Servetel ? callType == IVRType.Inbound ? "/Servetel/Inbound" : "/Servetel/Outbound" :
                                                                                                 ivrServiceProvider == IVRServiceProvider.MyOperator ? "/MyOperator/Inbound" : string.Empty)));
            }
            excelIntegrationContent.Add(IntegrationTemplateKeys.PayloadKey, GetRequestBodyJsonFromFile(dataParams.LeadSource, ivrServiceProvider));
            var result = GenerateCURLWithoutTenant(excelIntegrationContent);
            excelIntegrationContent.Add(IntegrationTemplateKeys.CURL, result);
            return excelIntegrationContent.Reverse().ToDictionary(i => i.Key, i => i.Value);
        }
        public static string FormatLeadSources()
        {
            var integraitonLeadSources = Enum.GetValues<LeadSource>().ToList()
                .Except(new List<LeadSource>() { LeadSource.Direct, LeadSource.LeadPool, LeadSource.Any, LeadSource.Referral, LeadSource.Phonebook, LeadSource.CallLogs, LeadSource.PortfolioMicrosite, LeadSource.PropertyMicrosite }).ToList();
            StringBuilder stringBuilder = new();
            foreach (var source in integraitonLeadSources)
            {
                stringBuilder.AppendLine($"{source.ToString()}: {(int)source}");
            }
            return stringBuilder.ToString();
        }

        private static string GenerateCURLForForm(IDictionary<string, string> excelIntegrationContent, LeadSource? leadSource = null, bool? isPayloadInForm = null, Dictionary<string, string>? payloadForExcel = null, bool? canSendApiKey = null)
        {
            string result = string.Empty;
            string apiUrl = string.Empty;
            string apiKey = string.Empty;
            string parameter = string.Empty;
            string tenant = string.Empty;
            string curl = string.Empty;
            string content = "multipart/form-data";
            if (excelIntegrationContent.Any(i => i.Key.Contains("Working Endpoint URL")))
            {
                var extApiUrl = (excelIntegrationContent.FirstOrDefault(i => i.Key.Contains("Working Endpoint URL")));
                apiUrl = extApiUrl.Value;
            }
            if (excelIntegrationContent.Any(i => i.Key.Contains("API-Key")))
            {
                var extApiKey = (excelIntegrationContent.FirstOrDefault(i => i.Key.Contains("API-Key")));
                apiKey = extApiKey.Value;
            }
            parameter = "\r\n=\"--form 'name=\\\"Himashu Kumar\\\"' \\\r\n--form 'mobile=\\\"9988776655\\\"' \\\r\n--form 'email=\\\"<EMAIL>\\\"' \\\r\n--form 'inquiry_id=\\\"12345\\\"' \\\r\n--form 'subject=\\\"property Inquiry\\\"' \\\r\n--form 'details=\\\"property Inquiry details\\\"' \\\r\n--form 'property_id=\\\"54321\\\"' \\\r\n--form 'recv_date=\\\"2023-04-6\\\"' \\\r\n--form 'lookinf_for=\\\"3 bhk flat for Rent\\\"' \\\r\n--form 'address=\\\"1596 HSR Layout ,bengaluru\\\"' \\\r\n--form 'CampaignName=\\\"CampaignName\\\"' \\\r\n--form 'AgencyName=\\\"AgencyName\\\"' \\\r\n--form 'ChannelPartnerName=\\\"property Inquiry\\\"'\"\r\n";

           //parameter = "--form 'name=\"Himashu Kumar\"' \\\r\n--form 'mobile=\"9988776655\"' \\\r\n--form 'email=\"<EMAIL>\"' \\\r\n--form 'inquiry_id=\"12345\"' \\\r\n--form 'subject=\"property Inquiry\"' \\\r\n--form 'details=\"property Inquiry details\"' \\\r\n--form 'property_id=\"54321\"' \\\r\n--form 'recv_date=\"2023-04-6\"' \\\r\n--form 'lookinf_for=\"3 bhk flat for Rent\"'  \\\r\n--form 'address=\"1596 HSR Layout ,bengaluru\"'";
            if (leadSource == LeadSource.PropertyWala)
            {
                curl = $"curl --location '{apiUrl}'\\\r\n--header 'Content-Type: {content}' \\ \r\n{GetParamsForForm(excelIntegrationContent, leadSource.Value, apiKey)}";
            }
            else if (isPayloadInForm ?? false)
            {
                if (canSendApiKey ?? false)
                {
                    curl = $"curl --location '{apiUrl}'\\\r\n--header 'Api-Key: {apiKey}' \\ \r\n--header 'Content-Type: {content}' \\ \r\n{GetParamsForForm(excelIntegrationContent, leadSource ?? default)}";
                }
                else
                {
                    curl = $"curl --location '{apiUrl}'\\\r\n--header 'Content-Type: {content}' \\ \r\n{GetParamsForForm(excelIntegrationContent, leadSource ?? default)}";
                }
            }
            else
            {
                curl = $"curl --location '{apiUrl}'\\\r\n--header 'Api-Key: {apiKey}' \\ --header 'Content-Type: {content}' \\ \r\n{parameter}";
            }

            return curl;
        }

        private static string GetParamsForForm(IDictionary<string, string>? excelIntegrationContent, LeadSource leadSource, string? apiKey = null)
        {
            string? payloadString = excelIntegrationContent?.TryGetValue("Parameters to be passed with values", out string? value) ?? false ? value : string.Empty;
            if (string.IsNullOrWhiteSpace(payloadString))
            {
                throw new Exception("No Payload Parameters Found!");
            }
            var formParams = JsonConvert.DeserializeObject<Dictionary<string, object>>(payloadString);
            if (leadSource == LeadSource.PropertyWala && (formParams?.Any() ?? false))
            {
                formParams.Add("ApiKey", $"{apiKey ?? string.Empty}");
            }
            string formattedParams = string.Empty;
            foreach (var param in (formParams ?? new()))
            {
                formattedParams += $"--form '{param.Key}=\"{param.Value}\"' \\\r\n";
            }
            return formattedParams;
        }
        private static string GenerateCURLForIVR(IDictionary<string, string> excelIntegrationContent, LeadSource? leadSource = null, bool? isPayloadInForm = null, string? tenant = null, string? serviceProvider = null, IVRPayloadMapping? ivrPayloadMapping = null, bool? canSendApiKey = null, Guid? accountId = null, Dictionary<string, string>? payloadForExcel = null)
        {
            string curl = string.Empty;
            switch (ivrPayloadMapping?.MethodType)
            {
                case "GET":
                    curl = GenerateCURLWithQueryParams(excelIntegrationContent, accountId ?? Guid.Empty, tenant ?? string.Empty, payloadForExcel ?? new(), canSendApiKey ?? false);
                    break;
                case "POST":
                    switch (ivrPayloadMapping.ContentType)
                    {
                        case "application/json":
                            curl = GenerateCURL(excelIntegrationContent, false, payloadForExcel, canSendApiKey);
                            break;
                        case "x-www-form-urlencoded":
                        case "form-data":
                            curl = GenerateCURLForForm(excelIntegrationContent, leadSource, true, payloadForExcel, canSendApiKey);
                            break;
                    }
                    break;
                default:
                    break;
            }

            return curl;
        }
        private static string GenerateCURLWithQueryParams(IDictionary<string, string> excelIntegrationContent, Guid accountId, string tenant, Dictionary<string, string> payloadForExcel, bool canIncludeApiKey)
        {
            string result = string.Empty;
            string apiUrl = string.Empty;
            string apiKey = string.Empty;
            //string parameter = "?callFrom=string&callSid=string&callTo=string&callType=string&created=string&currentTime=string&dialCallDuration=string&dialCallStatus=string&dialWhomNumber=string&direction=string&endTime=2023-07-21T06%3A45%3A48.213Z&flow_id=string&forwardedFrom=string&from=string&legs=string&outgoingPhoneNumber=string&processStatus=string&recordingAvailableBy=string&recordingUrl=string&startTime=2023-07-21T06%3A45%3A48.213Z&tenant_id=string&to=string";
            string parameter = string.Empty;
            string content = "application/json";
            var accountIdBase64 = accountId.GenerateApiKey();
            if (excelIntegrationContent.Any(i => i.Key.Contains("Working Endpoint URL")))
            {
                var extApiUrl = (excelIntegrationContent.FirstOrDefault(i => i.Key.Contains("Working Endpoint URL")));
                apiUrl = extApiUrl.Value;
            }
            if (excelIntegrationContent.Any(i => i.Key.Contains("tenant")))
            {
                var extTenant = (excelIntegrationContent.FirstOrDefault(i => i.Key.Contains("tenant")));
                tenant = extTenant.Value;
            }
            if (excelIntegrationContent.Any(i => i.Key.Contains(IntegrationTemplateKeys.APIKey)))
            {
                var extApiKey = (excelIntegrationContent.FirstOrDefault(i => i.Key.Contains(IntegrationTemplateKeys.APIKey)));
                apiKey = extApiKey.Value;
            }
            parameter = GetUrlEncodedData(payloadForExcel);
            apiUrl += $"?{parameter}";
            string curl = string.Empty;
            if (canIncludeApiKey)
            {
                curl = $"curl -H \"accept: {content}\" -H 'API-Key: {apiKey}'  {apiUrl}";
            }
            else
            {
                curl = $"curl -H \"accept: {content}\"  {apiUrl}";
            }
            return curl;
        }
        private static string GetUrlEncodedData(IDictionary<string, string> bodyVariables)
        {
            StringBuilder sb = new();
            foreach (KeyValuePair<string, string> kvp in bodyVariables)
            {
                if (!string.IsNullOrEmpty(kvp.Key) && !string.IsNullOrEmpty(kvp.Value))
                {
                    if (sb.Length > 0) sb.Append('&');
                    sb.Append(kvp.Key);
                    sb.Append('=');
                    sb.Append(kvp.Value);
                }
            }
            var postDataString = sb.ToString();
            postDataString = postDataString.Replace("#", "");
            return postDataString;
        }

        private static string GenerateCURLForDubai(IDictionary<string, string> excelIntegrationContent, bool? isPayloadInForm = false, Dictionary<string, string>? payloadForExcel = null, bool? canIncludeApiKey = null)
        {
            string result = string.Empty;
            string apiUrl = string.Empty;
            string apiKey = string.Empty;
            string parameter = string.Empty;
            string tenant = string.Empty;
            string content = "application/json";
            string formContent = "multipart/form-data";
            if (excelIntegrationContent.Any(i => i.Key.Contains("Working Endpoint URL")))
            {
                var extApiUrl = (excelIntegrationContent.FirstOrDefault(i => i.Key.Contains("Working Endpoint URL")));
                apiUrl = extApiUrl.Value;
            }
            if (excelIntegrationContent.Any(i => i.Key.Contains("API-Key")))
            {
                var extApiKey = (excelIntegrationContent.FirstOrDefault(i => i.Key.Contains("API-Key")));
                apiKey = extApiKey.Value;
            }
            if (excelIntegrationContent.Any(i => i.Key.Contains("tenant")))
            {
                var extTenant = (excelIntegrationContent.FirstOrDefault(i => i.Key.Contains("tenant")));
                tenant = extTenant.Value;
            }
            if (excelIntegrationContent.Any(i => i.Key.Contains("Parameters to be passed with values")))
            {
                var extParameter = (excelIntegrationContent.FirstOrDefault(i => i.Key.Contains("Parameters to be passed with values")));
                parameter = extParameter.Value;
            }
            string curl = string.Empty;
            if (canIncludeApiKey ?? false)
            {
                curl = $"curl --location --request POST  {apiUrl} -H \"Content-Type: {content}\" -d '{parameter}'";
            }
            else
            {
                curl = $"curl --location --request POST  {apiUrl} -H \"API-Key: {apiKey}\" -H \"Content-Type: {content}\" -d '{parameter}'";
            }
            if (isPayloadInForm ?? false)
            {
                curl = $"curl --location --request POST  {apiUrl} -H \"API-Key: {apiKey}\" -H \"Content-Type: {formContent}\" {GetParamsForForm(payloadForExcel)}";
            }
            return curl;
        }
        public static string GetWAWorkingEndPointUrl()
        {
            switch (env)
            {
                case "dev":
                    return WorkingEndPoints.DevWorkingEndpointOpenUrlValue;
                case "qa":
                    return WorkingEndPoints.QAWorkingEndpointOpenUrlValue;
                case "prd":
                    return WorkingEndPoints.PrdWorkingEndpointOpenUrlValue;
                default:
                    return string.Empty;
            }
        }
    }
}