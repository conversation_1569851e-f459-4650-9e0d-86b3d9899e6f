﻿using Lrb.Application.TimeZone.Dto;

namespace Lrb.Application.UserDetails.Web
{
    public class UserBasicDetailsDto : ViewUserDto
    {
        public CreateTimeZoneDto? TimeZoneInfo { get; set; }
        public bool? ShouldShowTimeZone { get; set; }
    }
    public class ViewUserDto : IDto
    {
        public Guid Id { get; set; }
        public string FirstName { get; set; }
        public string LastName { get; set; }
        public string UserName { get; set; }
        public string? ImageUrl { get; set; }
        public string PhoneNumber { get; set; }
        public string? Email { get; set; }
        public double? ProfileCompletion { get; set; }
        public bool IsActive { get; set; } = true;
        public bool IsMFAEnabled { get; set; }
        public string? LicenseNo { get; set; }
        public string? OrganizationName { get; set; }
        public bool? IsGeoFenceActive { get; set; }
        public SubscriptionDetails? SubscriptionDetails { get; set; }

    }
    public class MFAEnabledUserDto : IDto
    {
        public List<ViewUserDto>? Admins { get; set; }
        public List<ViewUserDto>? MFAEnabledUsers { get; set; }
    }
    public class ViewUserDetailsDto : IDto
    {
        public string? UserName { get; set; }
        public string? FirstName { get; set; }
        public string? LastName { get; set; }
        public string? ImageUrl { get; set; }
        public string? AltPhoneNumber { get; set; }
        public string? AltEmail { get; set; }
        public string? Address { get; set; }
        public string? Email { get; set; }
        public BloodGroupType? BloodGroup { get; set; }
        public Gender? Gender { get; set; }
        public string? PermanentAddress { get; set; }
        public string? PhoneNumber { get; set; }
        public string? EmpNo { get; set; }
        public string? OfficeName { get; set; }
        public string? OfficeAddress { get; set; }
        public DepartmantDto? Department { get; set; }
        public DesignationDto? Designation { get; set; }
    }


    public class ViewUserFormattedDto : IDto
    {
        public Guid Id { get; set; }
        public string Name { get; set; }

        public string UserName { get; set; }
        public string? ImageUrl { get; set; }
        public string PhoneNumber { get; set; }
        public string? Email { get; set; }
        public double? ProfileCompletion { get; set; }
        public bool IsActive { get; set; } = true;
    }
    public class SubscriptionDetails
    {
        public int? ValidDays { get; set; }
        public bool? IsAdmin { get; set; }
        public DateTime? LicenseValidity { get; set; }
        public int? TotalSoldLicenses { get; set; }
    }
}
