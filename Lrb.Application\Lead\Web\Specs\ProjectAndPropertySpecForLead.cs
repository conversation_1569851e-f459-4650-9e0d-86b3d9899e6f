﻿namespace Lrb.Application.Lead.Web
{


    public class GetAllProjectsV2Spec : Specification<Domain.Entities.Project>
    {
        public GetAllProjectsV2Spec(List<string>? projectNames)
        {
            List<string>? projectNamesInLowerCase = projectNames?.Select(i => i.ToLower())?.ToList();
            Query.Where(i => !i.IsDeleted && !i.IsArchived && i.Name != null && i.Name != string.Empty && projectNamesInLowerCase != null && projectNamesInLowerCase.Any() && projectNamesInLowerCase.Contains(i.Name.ToLower()))
                .Include(i => i.Leads);
        }

        public GetAllProjectsV2Spec(List<Guid> ids)
        {
            Query.Where(i => !i.IsDeleted && !i.IsArchived && ids.Contains(i.Id));
        }
    }

    public class GetAllProjectsForIntegrationSpec : Specification<Domain.Entities.TempProjects>
    {
        public GetAllProjectsForIntegrationSpec(List<string>? projectNames)
        {
            List<string>? projectNamesInLowerCase = projectNames?.Select(i => i.ToLower())?.ToList();
            Query.Where(i => !i.IsDeleted && i.Name != null && i.Name != string.Empty && projectNamesInLowerCase != null && projectNamesInLowerCase.Any() && projectNamesInLowerCase.Contains(i.Name.ToLower()));
        }

        public GetAllProjectsForIntegrationSpec(List<Guid> ids)
        {
            Query.Where(i => !i.IsDeleted && ids.Contains(i.Id));
        }
    }

    public class V2GetAllProjectsForIntegrationSpec : Specification<Domain.Entities.Project>
    {
        public V2GetAllProjectsForIntegrationSpec(List<string>? projectNames)
        {
            List<string>? projectNamesInLowerCase = projectNames?.Select(i => i.ToLower())?.ToList();
            Query.Where(i => !i.IsDeleted && i.Name != null && i.Name != string.Empty && projectNamesInLowerCase != null && projectNamesInLowerCase.Any() && projectNamesInLowerCase.Contains(i.Name.ToLower()));
        }

        public V2GetAllProjectsForIntegrationSpec(List<Guid> ids)
        {
            Query.Where(i => !i.IsDeleted && ids.Contains(i.Id));
        }
    }



    public class GetAllProjectsSpec : Specification<Domain.Entities.TempProjects>
    {
        public GetAllProjectsSpec(List<string>? projectNames)
        {
            List<string>? projectNamesInLowerCase = projectNames?.Select(i => i.ToLower())?.ToList();
            Query.Where(i => !i.IsDeleted && i.Name != null && i.Name != string.Empty && projectNamesInLowerCase != null && projectNamesInLowerCase.Any() && projectNamesInLowerCase.Contains(i.Name.ToLower()))
                .Include(i => i.Leads);
        }

        public GetAllProjectsSpec(List<Guid> ids)
        {
            Query.Where(i => !i.IsDeleted && ids.Contains(i.Id));
        }
    }
    public class GetAllPropertiesSpec : Specification<Domain.Entities.Property>
    {
        public GetAllPropertiesSpec(List<string>? propertyTitles)
        {
            if (propertyTitles != null && propertyTitles.Count > 0)
                propertyTitles = propertyTitles?.Select(i => i.ToLower())?.ToList();
            Query.Where(i => !i.IsDeleted && i.Title != null && propertyTitles.Contains(i.Title.ToLower()))
                    .Include(i => i.Leads);
        }
    }

    public class GetAllPropertiesForIntegrationSpec : Specification<Domain.Entities.Property>
    {
        public GetAllPropertiesForIntegrationSpec(List<string>? propertyTitles)
        {
            if (propertyTitles != null && propertyTitles.Count > 0)
                propertyTitles = propertyTitles?.Select(i => i.ToLower())?.ToList();
            Query.Where(i => !i.IsDeleted && i.Title != null && propertyTitles.Contains(i.Title.ToLower()));
        }
    }
    public class GetPropertyByTitleSpec : Specification<Domain.Entities.Property>
    {
        public GetPropertyByTitleSpec(string? propertyTitle)
        {
            Query.Where(i => !i.IsDeleted && i.Title.ToLower().Trim() == propertyTitle.ToLower().Trim());
        }
        public GetPropertyByTitleSpec(List<string> properties)
        {
            Query.Where(i => !i.IsDeleted && properties.Contains(i.Title.ToLower().Trim()));
        }
    }
    public class GetAllDistinctPropertiesSpec : Specification<Domain.Entities.Property>
    {
        public GetAllDistinctPropertiesSpec()
        {
            Query.Where(i => !i.IsDeleted && !i.IsArchived && !string.IsNullOrEmpty(i.Title));
        }
    }


    public class GetAllNewProjectsV2Spec : Specification<Domain.Entities.Project>
    {
        public GetAllNewProjectsV2Spec()
        {
            Query.Where(i => !i.IsDeleted && !i.IsArchived && !string.IsNullOrEmpty(i.Name));
        }
        public GetAllNewProjectsV2Spec(List<string> projects)
        {
            Query.Where(i => !i.IsDeleted && !i.IsArchived && i.Name != null && projects.Contains(i.Name.Trim().ToLower()));
        }
    }

    public class GetAllTempProjectsSpec : Specification<Domain.Entities.TempProjects>
    {
        public GetAllTempProjectsSpec()
        {
            Query.Where(i => !i.IsDeleted && !string.IsNullOrEmpty(i.Name));
        }
        public GetAllTempProjectsSpec(List<string> projects)
        {
            Query.Where(i => !i.IsDeleted && i.Name != null && projects.Contains(i.Name.Trim().ToLower()));
        }
    }

    public class GetNewProjectsByIdV2Spec : Specification<Domain.Entities.Project>
    {
        public GetNewProjectsByIdV2Spec(Guid id)
        {
            Query.Where(i => !i.IsDeleted && !i.IsArchived && i.Id == id);
        }
        public GetNewProjectsByIdV2Spec(List<Guid> ids)
        {
            Query.Where(i => !i.IsDeleted  && ids.Contains(i.Id));
        }
        public GetNewProjectsByIdV2Spec(string? projectName)
        {
            Query.Where(i => !i.IsDeleted && !i.IsArchived && i.Name.ToLower().Trim() == projectName.ToLower().Trim());
        }
        public GetNewProjectsByIdV2Spec(List<string>? projectNames)
        {
            Query.Where(i => !i.IsDeleted && !i.IsArchived && projectNames != null && projectNames.Any() && projectNames.Contains(i.Name.ToLower().Trim()) );
        }
    }
    public class GetProjectsByNameSpec : Specification<Domain.Entities.Project>
    {
        public GetProjectsByNameSpec(string? projectName)
        {
            Query.Where(i => !i.IsDeleted && !i.IsArchived && i.Name.ToLower().Trim() == projectName.ToLower().Trim())
                .Include(i => i.UserAssignment);
        }
    }


    public class GetTempProjectsByIdSpec : Specification<Domain.Entities.TempProjects>
    {
        public GetTempProjectsByIdSpec(Guid id)
        {
            Query.Where(i => !i.IsDeleted && i.Id == id);
        }
        public GetTempProjectsByIdSpec(string? projectName)
        {
            Query.Where(i => !i.IsDeleted && i.Name.ToLower().Trim() == projectName.ToLower().Trim());
        }
        public GetTempProjectsByIdSpec(List<string> projectName)
        {
            Query.Where(i => !i.IsDeleted && projectName.Contains(i.Name.ToLower().Trim()));
        }
    }

    public class GetAllProjectsByLeadIdSpecV2 : Specification<Domain.Entities.Project>
    {
        public GetAllProjectsByLeadIdSpecV2(Guid leadId, string name)
        {
            Query.Where(i => !i.IsDeleted && !i.IsArchived && i.Name == name)
                .Where(i => i.Leads.Select(i => i.Id).Contains(leadId));
        }
    }
    public class GetAllTempProjectsByLeadIdSpec : Specification<Domain.Entities.TempProjects>
    {
        public GetAllTempProjectsByLeadIdSpec(Guid leadId, string name)
        {
            Query.Where(i => !i.IsDeleted && i.Name == name)
                .Where(i => i.Leads.Select(i => i.Id).Contains(leadId));
        }
    }
    public class GetAllPropertiesByLeadIdSpec : Specification<Domain.Entities.Property>
    {
        public GetAllPropertiesByLeadIdSpec(Guid leadId, string title)
        {
            Query.Where(i => !i.IsDeleted && i.Title == title)
                .Where(i => i.Leads.Select(i => i.Id).Contains(leadId));
        }
    }

    public class GetChannelPartnerByNameSpecs : Specification<Lrb.Domain.Entities.ChannelPartner>
    {
        public GetChannelPartnerByNameSpecs(string name)
        {
            Query.Where(i => !i.IsDeleted && i.FirmName.ToLower().Trim() == name.ToLower().Trim());
        }
    }


    public class ProjectsByIdSpecV2 : Specification<Lrb.Domain.Entities.Project>
    {
        public ProjectsByIdSpecV2(Guid id)
        {
            Query.Where(i => !i.IsDeleted && !i.IsArchived && i.Id == id)
                .Include(i => i.UserAssignment);
        }
        public ProjectsByIdSpecV2(List<Guid> ids)
        {
            Query.Where(i => !i.IsDeleted && !i.IsArchived && ids.Contains(i.Id))
                .Include(i => i.UserAssignment);
        }
    }

    public class TempProjectsByIdSpec : Specification<TempProjects>
    {
        public TempProjectsByIdSpec(Guid id)
        {
            Query.Where(i => !i.IsDeleted && i.Id == id)
                .Include(i => i.UserAssignment);
        }
    }
    public class GetAllChannelPartenerSpec : Specification<Domain.Entities.ChannelPartner>
    {
        public GetAllChannelPartenerSpec(List<string>? channelpartenerNames)
        {
            List<string>? namesInLowerCase = channelpartenerNames?.Select(i => i.ToLower())?.ToList();
            Query.Where(i => !i.IsDeleted && i.FirmName != null && i.FirmName != string.Empty && namesInLowerCase != null && namesInLowerCase.Any() && namesInLowerCase.Contains(i.FirmName.ToLower()));
        }

    }
    public class GetAllCampaignsSpec : Specification<Domain.Entities.Campaign>
    {
        public GetAllCampaignsSpec(List<string>? campaignNames)
        {
            List<string>? namesInLowerCase = campaignNames?.Select(i => i.ToLower())?.ToList();
            Query.Where(i => !i.IsDeleted && i.Name != null && i.Name != string.Empty && namesInLowerCase != null && namesInLowerCase.Any() && namesInLowerCase.Contains(i.Name.ToLower()));
        }
    }
    public class GetAllAgencySpec : Specification<Domain.Entities.Agency>
    {
        public GetAllAgencySpec(List<string>? agencyNames)
        {
            List<string>? namesInLowerCase = agencyNames?.Select(i => i.ToLower())?.ToList();
            Query.Where(i => !i.IsDeleted && i.Name != null && i.Name != string.Empty && namesInLowerCase != null && namesInLowerCase.Any() && namesInLowerCase.Contains(i.Name.ToLower()));
        }
    }
}
