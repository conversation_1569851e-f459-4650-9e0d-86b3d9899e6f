﻿using Lrb.Application.CustomFields.Mobile;
using Lrb.Application.CustomFilters.Mobile;
namespace Lrb.Application.CustomStatus.Mobile
{
    public class UpdateCustomStatusDto : BaseCustomStatusDto
    {
    }
    public class ViewCustomStatusDto : BaseCustomStatusDto
    {
        public List<ViewCustomFilterDto>? CustomFilters { get; set; }
        public List<CustomFieldDto>? CustomFields { get; set; }
        public bool IsLrbStatus { get; set; }
        public bool? ShouldUseForMeeting { get; set; }
        public bool ShouldBeHidden { get; set; }

    }
    public class BaseCustomStatusDto : IDto
    {
        public Guid Id { get; set; }
        public Guid? BaseId { get; set; }
        public int Level { get; set; }
        public string? Status { get; set; }
        public int OrderRank { get; set; }
        public string DisplayName { get; set; } = default!;
        public string? ActionName { get; set; }
        public Guid? MasterLeadStatusId { get; set; }
        public bool IsDefault { get; set; }
        public bool IsDefaultChild { get; set; }
        public bool ShouldUseForBooking { get; set; }
        public bool ShouldUseForBookingCancel { get; set; }
        public bool ShouldOpenAppointmentPage { get; set; }
        public IList<ViewCustomStatusDto> ChildTypes { get; set; }
        public BaseCustomStatusDto()
        {
            ChildTypes = new List<ViewCustomStatusDto>();
        }
    }
    public class CreateCustomFieldDto
    {
        public bool IsRequired { get; set; }
        public Guid FieldId { get; set; }
    }
    public class StatusNamesDto
    {
        public Guid Id { get; set; }
        public Guid? BaseId { get; set; }
        public int OrderRank { get; set; }
        public string DisplayName { get; set; } = default!;
        public bool IsScheduled { get; set; }
        public IList<StatusNamesDto> ChildTypes { get; set; }
        public StatusNamesDto()
        {
            ChildTypes = new List<StatusNamesDto>();
        }
    }
}
