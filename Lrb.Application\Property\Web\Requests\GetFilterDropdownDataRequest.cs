﻿using Lrb.Application.Property.Web.Dtos;
using Lrb.Application.Property.Web.Specs;

namespace Lrb.Application.Property.Web.Requests
{
    public class GetFilterDropdownDataRequest : PaginationFilter, IRequest<PagedResponse<FilterDropdownDataDto, string>>
    {

    }
    public class GetFilterDropdownDataRequestHandler : IRequestHandler<GetFilterDropdownDataRequest, PagedResponse<FilterDropdownDataDto, string>>
    {
        private readonly IRepositoryWithEvents<Domain.Entities.Property> _propertyRepo;
        public GetFilterDropdownDataRequestHandler(IRepositoryWithEvents<Domain.Entities.Property> propertyRepo)
        {
            _propertyRepo = propertyRepo;
        }
        public async Task<PagedResponse<FilterDropdownDataDto, string>> Handle(GetFilterDropdownDataRequest request, CancellationToken cancellationToken)
        {
            FilterDropdownDataDto responseDto = new();
            var properties = (await _propertyRepo.ListAsync(new GetPropertyForFilterSpec(request), cancellationToken));
            responseDto.Locality = properties.Select(i => (!string.IsNullOrEmpty(i.Address?.SubLocality) ? i.Address?.SubLocality + ", " : null) + (!string.IsNullOrEmpty(i.Address?.Locality) ? i.Address?.Locality : null)).Distinct().Where(i => !string.IsNullOrWhiteSpace(i)).ToList() ;
            responseDto.SubLocality = properties.Select(i => i.Address?.SubLocality ?? string.Empty).Distinct().Where(i => i != string.Empty).Where(i => !string.IsNullOrWhiteSpace(i)).ToList();
            responseDto.City = properties.Select(i => i.Address?.City ?? string.Empty).Distinct().Where(i => i != string.Empty).Where(i => !string.IsNullOrWhiteSpace(i)).ToList();
            responseDto.State = properties.Select(i => i.Address?.State ?? string.Empty).Distinct().Where(i => i != string.Empty).Where(i => !string.IsNullOrWhiteSpace(i)).ToList();
            responseDto.Country = properties.Select(i => i.Address?.Country ?? string.Empty).Distinct().Where(i => i != string.Empty).Where(i => !string.IsNullOrWhiteSpace(i)).ToList();

            return new PagedResponse<FilterDropdownDataDto, string>(new List<FilterDropdownDataDto>() { responseDto }, 1);
        }
    }
}
