﻿using Finbuckle.MultiTenant;
using Lrb.Application.Common.CustomEmail;
using Lrb.Application.Common.Email;
using Lrb.Application.Common.Persistence;
using Lrb.Application.Common.PushNotification;
using Lrb.Application.DailyStatusUpdates.Dtos;
using Lrb.Application.Email.Web;
using Lrb.Application.Identity.Users;
using Lrb.Application.Integration.Web.Helpers;
using Lrb.Application.Lead.Web;
using Lrb.Application.PushNotification.Web.Dtos;
using Lrb.Application.Utils;
using Lrb.Application.WA.Web;
using Lrb.Domain.Common.VariableMappings;
using Lrb.Domain.Constants;
using Lrb.Domain.Entities;
using Lrb.Domain.Entities.CustomEmail;
using Lrb.Domain.Enums;
using Mapster;
using Microsoft.AspNetCore.Http;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System.Globalization;
using System.Linq.Expressions;
using System.Text.Json;


namespace Lrb.Infrastructure.PushNotification
{
    public class TemplateNotificationService : ITemplateNotificationService
    {
        private readonly INotificationService _notificationService;
        private readonly IRepositoryWithEvents<CustomEmailTracker> _customEmailTracker;
        private readonly ICustomEmailService _customEmailService;
        private readonly IGraphEmailService _graphEmailService;
        private readonly IReadRepository<WATemplate> _waTemplateRepo;
        private readonly IReadRepository<EmailSettings> _emailSettingRepo;
        private readonly IReadRepository<Lead> _leadRepo;
        private readonly IReadRepository<EmailTemplates> _emailTemplateRepo;
        private readonly IReadRepository<CustomEmailInfo> _customEmailInfoRepo;
        private readonly IUserService _userService;
        private readonly INpgsqlRepository _npgsqlRepository;
        private readonly ITenantInfo? _tenantInfo;
        private readonly IRepositoryWithEvents<NotificationInfo> _notificationConfig;
        private readonly Serilog.ILogger _logger;
        private readonly IDapperRepository _dapperRepository;
        private readonly IRepositoryWithEvents<UserDetails> _userDetailsRepo;

        public TemplateNotificationService(IRepositoryWithEvents<CustomEmailTracker> customEmailTracker,
            INotificationService notificationService,
            ICustomEmailService customEmailService,
            IGraphEmailService graphEmailService,
            IReadRepository<WATemplate> waTemplateRepo,
            IReadRepository<EmailSettings> emailSettingRepo,
            IReadRepository<Lead> leadRepo,
            IReadRepository<EmailTemplates> emailTemplateRepo,
            IReadRepository<CustomEmailInfo> customEmailInfoRepo,
            IUserService userService,
            INpgsqlRepository npgsqlRepository,
            ITenantInfo? tenantInfo,
            IRepositoryWithEvents<NotificationInfo> notificationConfig,
            Serilog.ILogger logger,
            IDapperRepository dapperRepository,
            IRepositoryWithEvents<UserDetails> userDetailsRepo)
        {
            _customEmailTracker = customEmailTracker;
            _notificationService = notificationService;
            _customEmailService = customEmailService;
            _graphEmailService = graphEmailService;
            _waTemplateRepo = waTemplateRepo;
            _emailSettingRepo = emailSettingRepo;
            _leadRepo = leadRepo;
            _customEmailInfoRepo = customEmailInfoRepo;
            _emailTemplateRepo = emailTemplateRepo;
            _userService = userService;
            _npgsqlRepository = npgsqlRepository;
            _tenantInfo = tenantInfo;
            _notificationConfig = notificationConfig;
            _logger = logger;
            _dapperRepository = dapperRepository;
            _userDetailsRepo = userDetailsRepo;
        }

        public async Task<bool> ProcessTemplateNotificationAsync(NotificationInfoDto templateConfig, CancellationToken cancellationToken, string? phoneNumber = null, string? testHeaderValue = null, List<string>? bodyValues = null, string? campaignName = null)
        {
            switch (templateConfig.TemplateMode)
            {
                case TemplateMode.Email:
                    await ProcessEmailTemplateConfigAsync(templateConfig, cancellationToken);
                    break;
                case TemplateMode.WhatsApp:
                    await ProcessWATemplateConfigAsync(templateConfig, cancellationToken, phoneNumber, testHeaderValue, bodyValues, campaignName);
                    break;
            }

            return true;
        }

        private async Task ProcessWATemplateConfigAsync(NotificationInfoDto config, CancellationToken cancellationToken, string? phoneNumber = null, string? testHeaderValue = null, List<string>? testBodyValues = null, string? campaignName = null)
        {
            try
            {
                var waTemplate = await _waTemplateRepo.FirstOrDefaultAsync(new GetAllWATemplatesSpec(config.TemplateId));
                if (waTemplate == null)
                {
                    _logger.Warning($"WhatsApp template not found for ID: {config.TemplateId}");
                    return;
                }

                config.LeadIds = config.LeadIds?.Where(x => x != Guid.Empty)?.ToList();

                //this code it will work for test 
                if (config.LeadIds?.Count == 0 && !string.IsNullOrEmpty(phoneNumber))
                {
                    await _notificationService.SendWATemplateNotification(
                        phoneNumber: phoneNumber,
                        template: waTemplate,
                        bodyValues: testBodyValues,
                        headerValue: testHeaderValue,
                        isLeadNotification: false,
                        isSavedMessage: true);
                    return;
                }

                var userDetails = (!(config.IsAllUser ?? false)
                        ? (await _userService.GetListOfUsersByIdsAsync(config.UserIds?.Select(x => x.ToString()).ToList() ?? new List<string>(), cancellationToken))
                        : _userService.GetList()) ?? new();

                var variables = Application.Utils.StringHelper.GetVariablesFromContent(waTemplate.Message);
                var templateDetails = new Dictionary<string, List<string>>();
                var processedPhoneNumbers = new HashSet<string>();
                string headerValue = string.Empty;

                var leads = (config.LeadIds != null && config.LeadIds.Any())
                    ? await _leadRepo.ListAsync(new LeadsByIdsSpec(config.LeadIds), cancellationToken)
                    : new List<Lead>();

                // Process leads for IsLeadSpecific or IsUserSpecific notifications
                if (leads.Any())
                {
                    foreach (var lead in leads)
                    {
                        try
                        {
                            var bodyValues = new List<string>();
                            var leadDto = lead.Adapt<ViewLeadDto>();
                            UserDetailsDto? assignUser = null;
                            if (lead.AssignTo != Guid.Empty)
                            {
                                assignUser = (await _userService
                                    .GetListOfUsersByIdsAsync(new List<string> { lead.AssignTo.ToString() }, cancellationToken)).FirstOrDefault();
                            }
                            var assignUserKeyValues = assignUser != null ? GetKeyValuesFromObject(assignUser) : null;
                            var leadKeyValues = GetKeyValuesFromObject(leadDto);
                            if (config.IsLeadSpecific && !processedPhoneNumbers.Contains(lead.ContactNo))
                            {
                                ProcessTemplateVariables(variables, leadKeyValues, assignUserKeyValues, bodyValues, null, leadDto);
                                try
                                {
                                    if (variables.Contains("#projectMicrositeUrl#") && lead.Projects != null && lead.Projects.Any() && assignUser != null && !string.IsNullOrWhiteSpace(assignUser.UserName) && _tenantInfo?.Id != null)
                                    {
                                        var value = await GetMicrositeUrl("project", assignUser.UserName, lead.Projects[0].SerialNo, _tenantInfo.Id);
                                        var index = variables.IndexOf("#projectMicrositeUrl#");
                                        if (index != -1 && index < bodyValues.Count)
                                        {
                                            bodyValues[index] = value;
                                        }
                                    }
                                    if (variables.Contains("#propertyMicrositeUrl#") && lead.Properties != null && lead.Properties.Any() && assignUser != null && !string.IsNullOrWhiteSpace(assignUser.UserName) && _tenantInfo?.Id != null)
                                    {
                                        var value = await GetMicrositeUrl("property", assignUser.UserName, lead.Properties[0].SerialNo, _tenantInfo.Id);
                                        var index = variables.IndexOf("#propertyMicrositeUrl#");
                                        if (index != -1 && index < bodyValues.Count)
                                        {
                                            bodyValues[index] = value;
                                        }
                                    }
                                    if (variables.Contains("#listingMicrositeUrl#") && lead.Properties != null && lead.Properties.Any() && assignUser != null && !string.IsNullOrWhiteSpace(assignUser.UserName) && _tenantInfo?.Id != null)
                                    {
                                        var value = await GetMicrositeUrl("listing", assignUser.UserName, lead.Properties[0].SerialNo, _tenantInfo.Id);
                                        var index = variables.IndexOf("#listingMicrositeUrl#");
                                        if (index != -1 && index < bodyValues.Count)
                                        {
                                            bodyValues[index] = value;
                                        }
                                    }
                                }
                                catch(Exception ex)
                                {

                                }
                                templateDetails.Add(lead.ContactNo, bodyValues);
                                processedPhoneNumbers.Add(lead.ContactNo);

                                await _notificationService.SendWATemplateNotification(
                                    phoneNumber: lead.ContactNo,
                                    template: waTemplate,
                                    bodyValues: bodyValues,
                                    headerValue: headerValue,
                                    isSavedMessage: true,
                                    leadId: lead.Id,
                                    leadName: lead.Name,
                                    campaignName: campaignName,
                                    leadDto: leadDto,
                                    assignUser: assignUser,
                                    isLeadNotification: true);
                            }

                            if (config.IsUserSpecific)
                            {
                                List<UserDetailsDto> assignedUsers = (await _userService
                                    .GetListOfUsersByIdsAsync(new List<string> { lead.CreatedBy.ToString() }, cancellationToken));

                                if (lead.AssignTo == Guid.Empty)
                                {
                                    var adminIds = await _npgsqlRepository.GetAdminIdsAsync(_tenantInfo?.Id ?? string.Empty);
                                    assignedUsers = await _userService
                                        .GetListOfUsersByIdsAsync(adminIds.Select(x => x.ToString()).ToList(), cancellationToken);
                                }
                                if (assignUser != null)
                                {
                                    assignedUsers.Add(assignUser);
                                }

                                foreach (var user in assignedUsers)
                                {
                                    if (!string.IsNullOrEmpty(user.PhoneNumber) && !processedPhoneNumbers.Contains(user.PhoneNumber))
                                    {
                                        var userBodyValues = new List<string>();
                                        var userDetailsKeyValues = GetKeyValuesFromObject(user);
                                        ProcessTemplateVariables(variables, leadKeyValues, userDetailsKeyValues, userBodyValues, null, leadDto);
                                        templateDetails.TryAdd(user.PhoneNumber, userBodyValues);
                                        processedPhoneNumbers.Add(user.PhoneNumber);

                                        await _notificationService.SendWATemplateNotification(
                                            phoneNumber: user.PhoneNumber,
                                            template: waTemplate,
                                            bodyValues: userBodyValues,
                                            headerValue: headerValue,
                                            isSavedMessage: true,
                                            leadId: lead.Id,
                                            leadName: lead.Name,
                                            campaignName: campaignName,
                                            leadDto: leadDto,
                                            assignUser: user,
                                            isLeadNotification: false,
                                            userId: user.Id);
                                    }
                                }
                            }
                        }
                        catch (Exception ex)
                        {
                            _logger.Error($"ProcessWATemplateConfigAsync->Error processing lead {lead.ContactNo}: {ex.Message}");
                        }
                    }
                }
                // Process all users for IsUserSpecific or IsAllUser
                else if (config.IsUserSpecific || (config.IsAllUser ?? false))
                {
                    foreach (var userDetail in userDetails)
                    {
                        if (!string.IsNullOrEmpty(userDetail?.PhoneNumber) && !processedPhoneNumbers.Contains(userDetail.PhoneNumber))
                        {
                            var bodyValues = new List<string>();
                            var userDetailsKeyValues = GetKeyValuesFromObject(userDetail);
                            ProcessTemplateVariables(variables, new Dictionary<string, string>(), userDetailsKeyValues, bodyValues);
                            templateDetails.TryAdd(userDetail.PhoneNumber, bodyValues);
                            processedPhoneNumbers.Add(userDetail.PhoneNumber);

                            if (!string.IsNullOrEmpty(waTemplate.Header))
                            {
                                var keys = Application.Utils.StringHelper.GetVariablesFromContent(waTemplate.Header);
                                if (keys.Any())
                                {
                                    headerValue = userDetailsKeyValues?.FirstOrDefault(x => x.Key == keys.First()).Value ?? string.Empty;
                                }
                                if (waTemplate.HeaderValues != null)
                                {
                                    headerValue = waTemplate.HeaderValues.Values.FirstOrDefault() ?? string.Empty;
                                }
                            }

                            await _notificationService.SendWATemplateNotification(
                                phoneNumber: userDetail.PhoneNumber,
                                template: waTemplate,
                                bodyValues: bodyValues,
                                headerValue: headerValue,
                                isSavedMessage: true,
                                isLeadNotification: false,
                                userId: userDetail.Id);
                        }
                    }
                }

                // Process WhatsAppNotificationRecipients
                if (!string.IsNullOrWhiteSpace(config.WhatsAppNotificationRecipients))
                {
                    var json = config.WhatsAppNotificationRecipients;
                    if (json.StartsWith("WhatsAppNotificationRecipients=", StringComparison.OrdinalIgnoreCase))
                    {
                        json = json.Substring("WhatsAppNotificationRecipients=".Length);
                    }

                    var settings = new JsonSerializerOptions { PropertyNameCaseInsensitive = true };
                    var recipients = System.Text.Json.JsonSerializer.Deserialize<WhatsAppNotificationRecipients>(json, settings);

                    if (recipients != null)
                    {
                        // Process IsLeadEnabled
                        if (recipients.IsLeadEnabled && leads.Any())
                        {
                            foreach (var lead in leads)
                            {
                                try
                                {
                                    if (!processedPhoneNumbers.Contains(lead.ContactNo))
                                    {
                                        var bodyValues = new List<string>();
                                        var leadDto = lead.Adapt<ViewLeadDto>();
                                        UserDetailsDto? assignUser = null;
                                        if (lead.AssignTo != Guid.Empty)
                                        {
                                            assignUser = (await _userService
                                                .GetListOfUsersByIdsAsync(new List<string> { lead.AssignTo.ToString() }, cancellationToken)).FirstOrDefault();
                                        }
                                        var assignUserKeyValues = assignUser != null ? GetKeyValuesFromObject(assignUser) : null;
                                        var leadKeyValues = GetKeyValuesFromObject(leadDto);
                                        ProcessTemplateVariables(variables, leadKeyValues, assignUserKeyValues, bodyValues, null, leadDto);
                                        templateDetails.TryAdd(lead.ContactNo, bodyValues);
                                        processedPhoneNumbers.Add(lead.ContactNo);

                                        if (waTemplate.HeaderValues != null)
                                        {
                                            headerValue = waTemplate.HeaderValues.Values.FirstOrDefault() ?? string.Empty;
                                        }

                                        await _notificationService.SendWATemplateNotification(
                                            phoneNumber: lead.ContactNo,
                                            template: waTemplate,
                                            bodyValues: bodyValues,
                                            headerValue: headerValue,
                                            isSavedMessage: true,
                                            leadId: lead.Id,
                                            leadName: lead.Name,
                                            campaignName: campaignName,
                                            leadDto: leadDto,
                                            assignUser: assignUser,
                                            isLeadNotification: true);
                                    }
                                }
                                catch (Exception ex)
                                {
                                    _logger.Error($"ProcessWATemplateConfigAsync->Error processing lead (IsLeadEnabled) {lead.ContactNo}: {ex.Message}");
                                }
                            }
                        }

                        // Process IsAssignToEnabled
                        if (recipients.IsAssignToEnabled && leads.Any())
                        {
                            foreach (var lead in leads)
                            {
                                try
                                {
                                    var leadDto = lead.Adapt<ViewLeadDto>();
                                    var leadKeyValues = GetKeyValuesFromObject(leadDto);
                                    List<UserDetailsDto> assignedUsers = (await _userService
                                        .GetListOfUsersByIdsAsync(new List<string> { lead.CreatedBy.ToString() }, cancellationToken));

                                    if (lead.AssignTo == Guid.Empty)
                                    {
                                        var adminIds = await _npgsqlRepository.GetAdminIdsAsync(_tenantInfo?.Id ?? string.Empty);
                                        assignedUsers = await _userService
                                            .GetListOfUsersByIdsAsync(adminIds.Select(x => x.ToString()).ToList(), cancellationToken);
                                    }
                                    else
                                    {
                                        var assignUser = (await _userService
                                            .GetListOfUsersByIdsAsync(new List<string> { lead.AssignTo.ToString() }, cancellationToken)).FirstOrDefault();
                                        if (assignUser != null)
                                        {
                                            assignedUsers.Add(assignUser);
                                        }
                                    }

                                    foreach (var user in assignedUsers)
                                    {
                                        if (!string.IsNullOrEmpty(user.PhoneNumber) && !processedPhoneNumbers.Contains(user.PhoneNumber))
                                        {
                                            var bodyValues = new List<string>();
                                            var userDetailsKeyValues = GetKeyValuesFromObject(user);
                                            ProcessTemplateVariables(variables, leadKeyValues, userDetailsKeyValues, bodyValues, null, leadDto);
                                            templateDetails.TryAdd(user.PhoneNumber, bodyValues);
                                            processedPhoneNumbers.Add(user.PhoneNumber);

                                            await _notificationService.SendWATemplateNotification(
                                                phoneNumber: user.PhoneNumber,
                                                template: waTemplate,
                                                bodyValues: bodyValues,
                                                headerValue: headerValue,
                                                isSavedMessage: true,
                                                leadId: lead.Id,
                                                leadName: lead.Name,
                                                campaignName: campaignName,
                                                leadDto: leadDto,
                                                assignUser: user,
                                                isLeadNotification: false,
                                                userId: user.Id);
                                        }
                                    }
                                }
                                catch (Exception ex)
                                {
                                    _logger.Error($"ProcessWATemplateConfigAsync->Error processing assigned users (IsAssignToEnabled) for lead {lead.ContactNo}: {ex.Message}");
                                }
                            }
                        }

                        // Process IsAdminEnabled
                        if (recipients.IsAdminEnabled)
                        {
                            try
                            {
                                var adminIds = await _npgsqlRepository.GetAdminIdsAsync(_tenantInfo?.Id ?? string.Empty);
                                var adminUsers = await _userService
                                    .GetListOfUsersByIdsAsync(adminIds.Select(x => x.ToString()).ToList(), cancellationToken);

                                foreach (var user in adminUsers)
                                {
                                    if (!string.IsNullOrEmpty(user.PhoneNumber) && !processedPhoneNumbers.Contains(user.PhoneNumber))
                                    {
                                        var bodyValues = new List<string>();
                                        var userDetailsKeyValues = GetKeyValuesFromObject(user);
                                        ProcessTemplateVariables(variables, new Dictionary<string, string>(), userDetailsKeyValues, bodyValues);
                                        templateDetails.TryAdd(user.PhoneNumber, bodyValues);
                                        processedPhoneNumbers.Add(user.PhoneNumber);

                                        await _notificationService.SendWATemplateNotification(
                                            phoneNumber: user.PhoneNumber,
                                            template: waTemplate,
                                            bodyValues: bodyValues,
                                            headerValue: headerValue,
                                            isSavedMessage: true,
                                            isLeadNotification: false,
                                            userId: user.Id);
                                    }
                                }
                            }
                            catch (Exception ex)
                            {
                                _logger.Error($"ProcessWATemplateConfigAsync->Error processing admin users (IsAdminEnabled): {ex.Message}");
                            }
                        }

                        // Process IsManagerEnabled 
                        if (recipients.IsManagerEnabled)
                        {
                            try
                            {
                                var managerIds = await _dapperRepository.GetManagerIdsAsync(_tenantInfo?.Id ?? string.Empty);
                                var managerUsers = await _userService
                                    .GetListOfUsersByIdsAsync(managerIds.Select(x => x.ToString()).ToList(), cancellationToken);

                                foreach (var user in managerUsers)
                                {
                                    if (!string.IsNullOrEmpty(user.PhoneNumber) && !processedPhoneNumbers.Contains(user.PhoneNumber))
                                    {
                                        var bodyValues = new List<string>();
                                        var userDetailsKeyValues = GetKeyValuesFromObject(user);
                                        ProcessTemplateVariables(variables, new Dictionary<string, string>(), userDetailsKeyValues, bodyValues);
                                        templateDetails.TryAdd(user.PhoneNumber, bodyValues);
                                        processedPhoneNumbers.Add(user.PhoneNumber);

                                        await _notificationService.SendWATemplateNotification(
                                            phoneNumber: user.PhoneNumber,
                                            template: waTemplate,
                                            bodyValues: bodyValues,
                                            headerValue: headerValue,
                                            isSavedMessage: true,
                                            isLeadNotification: false,
                                            userId: user.Id);
                                    }
                                }
                            }
                            catch (Exception ex)
                            {
                                _logger.Error($"ProcessWATemplateConfigAsync->Error processing manager users (IsManagerEnabled): {ex.Message}");
                            }
                        }

                        // Process IsGeneralManagerEnabled 
                        if (recipients.IsGeneralManagerEnabled)
                        {
                            try
                            {
                                var generalManagerIds = new List<Guid>();

                                if (leads.Any())
                                {
                                    foreach (var lead in leads)
                                    {
                                        string userId = lead.AssignTo != Guid.Empty ? lead.AssignTo.ToString() : lead.CreatedBy.ToString();
                                        var leadGeneralManagerId = await _dapperRepository.GetGeneralManagerIdAsync(userId);
                                        if (leadGeneralManagerId != null)
                                        {
                                            generalManagerIds.Add(leadGeneralManagerId.Value);
                                        }
                                    }
                                }
                                generalManagerIds = generalManagerIds.Distinct().ToList();

                                var generalManagerUsers = await _userService
                                    .GetListOfUsersByIdsAsync(generalManagerIds.Select(x => x.ToString()).ToList(), cancellationToken);

                                foreach (var user in generalManagerUsers)
                                {
                                    if (!string.IsNullOrEmpty(user.PhoneNumber) && !processedPhoneNumbers.Contains(user.PhoneNumber))
                                    {
                                        var bodyValues = new List<string>();
                                        var userDetailsKeyValues = GetKeyValuesFromObject(user);
                                        ProcessTemplateVariables(variables, new Dictionary<string, string>(), userDetailsKeyValues, bodyValues);
                                        templateDetails.TryAdd(user.PhoneNumber, bodyValues);
                                        processedPhoneNumbers.Add(user.PhoneNumber);

                                        await _notificationService.SendWATemplateNotification(
                                            phoneNumber: user.PhoneNumber,
                                            template: waTemplate,
                                            bodyValues: bodyValues,
                                            headerValue: headerValue,
                                            isSavedMessage: true,
                                            isLeadNotification: false,
                                            userId: user.Id);
                                    }
                                }
                            }
                            catch (Exception ex)
                            {
                                _logger.Error($"ProcessWATemplateConfigAsync->Error processing general manager users (IsGeneralManagerEnabled): {ex.Message}");
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.Error($"ProcessWATemplateConfigAsync: Processing the WA template notification resulted in an error: {ex.Message}");
            }
        }

        private async Task<(bool isValid, EmailSettings emailSetting, CustomEmailInfo emailInfo, EmailTemplates emailTemplate)>
            EmailTemplateConfig(NotificationInfoDto config, CancellationToken cancellationToken)
        {
            // Execute queries sequentially to avoid DbContext threading issues
            var emailTemplate = (await _emailTemplateRepo.ListAsync(
                new GetEmailTemplateByEventSpec(config.TemplateId), cancellationToken)).FirstOrDefault();

            var emailSetting = (await _emailSettingRepo.ListAsync(cancellationToken)).FirstOrDefault();

            var emailInfo = (await _customEmailInfoRepo.ListAsync(cancellationToken))
                .FirstOrDefault(x => x.IsPrimary);

            var isValid = emailTemplate != null && emailSetting != null && emailInfo != null;

            return (isValid, emailSetting, emailInfo, emailTemplate);
        }

        private async Task ProcessEmailTemplateConfigAsync(NotificationInfoDto config, CancellationToken cancellationToken)
        {
            try
            {
                var (isValid, emailSetting, emailInfo, emailTemplate) = await EmailTemplateConfig(config, cancellationToken);

                if (!isValid)
                    return;

                List<IFormFile>? files = null;
                if (emailTemplate.AttachmentUrls?.Count > 0)
                {
                    files = await ConvertUrlToIFormFileAsync(emailTemplate.AttachmentUrls);
                }
                var userDetails = (!(config.IsAllUser ?? false)
                        ? (await _userService
                        .GetListOfUsersByIdsAsync(config.UserIds?.Select(x => x.ToString()).ToList() ?? new List<string>(), cancellationToken))
                      : _userService.GetList()) ?? new();

                var templateDetails = new Dictionary<string, string>();
                var variables = Application.Utils.StringHelper.GetVariablesFromContent(emailTemplate.Body);


                var leads = (config.LeadIds != null && config.LeadIds.Any())
                    ? await _leadRepo.ListAsync(new LeadsByIdsSpec(config.LeadIds), cancellationToken) : new List<Lead>();

                if (leads.Any())
                {
                    foreach (var lead in leads)
                    {
                        var bodyValues = new List<string>();
                        var leadDto = lead.Adapt<ViewLeadDto>();
                        var leadKeyValues = GetKeyValuesFromObject(leadDto);
                        UserDetailsDto? assignUser = new();
                        if (lead.AssignTo != Guid.Empty)
                        {
                            assignUser = (await _userService
                                     .GetListOfUsersByIdsAsync(new List<string> { lead.AssignTo.ToString() }, cancellationToken)).FirstOrDefault();
                        }
                        var assignUserKeyValues = assignUser != null ? GetKeyValuesFromObject(assignUser) : null;

                        if (config.IsLeadSpecific && !string.IsNullOrEmpty(lead.Email))
                        {
                            emailTemplate.Body = ProcessTemplateVariables(variables, leadKeyValues, assignUserKeyValues, bodyValues, emailTemplate.Body);
                            templateDetails.Add(lead.Email, emailTemplate.Body);
                        }
                        if (config.IsChannelPartnerSpecific && lead.ChannelPartners?.Any() == true)
                        {
                            emailTemplate.Body = ProcessTemplateVariables(variables, leadKeyValues, assignUserKeyValues, bodyValues, emailTemplate.Body);
                            var channnelPartnerEmails = lead.ChannelPartners.Where(i => !string.IsNullOrEmpty(i.Email)).Select(lead => lead.Email);
                            foreach (var email in channnelPartnerEmails)
                            {
                                templateDetails.Add(email, emailTemplate.Body);
                            }
                        }
                        if (config.IsUserSpecific && !string.IsNullOrEmpty(lead.Email))
                        {
                            List<UserDetailsDto> assignedUsers = new();
                            if (lead.AssignTo != Guid.Empty)
                            {
                                var adminIds = await _npgsqlRepository.GetAdminIdsAsync(_tenantInfo?.Id ?? string.Empty);
                                assignedUsers = await _userService
                                         .GetListOfUsersByIdsAsync((adminIds.Select(x => x.ToString()).ToList()), cancellationToken);
                            }
                            if (assignUser != null)
                            {
                                assignedUsers.Add(assignUser);
                            }
                            foreach (var user in assignedUsers)
                            {
                                if (!string.IsNullOrEmpty(user.Email))
                                {
                                    var userDetailsKeyValues = GetKeyValuesFromObject(user);
                                    emailTemplate.Body = ProcessTemplateVariables(variables, leadKeyValues, userDetailsKeyValues, bodyValues, emailTemplate.Body);
                                    templateDetails.TryAdd(user.Email, emailTemplate.Body);
                                }
                            }
                        }
                        await SendEmailAsync(emailSetting, emailInfo, emailTemplate, templateDetails.Keys.ToList(), files, lead.Id);
                    }
                }
                else if (config.IsUserSpecific || (config.IsAllUser ?? false))
                {
                    foreach (var userDetail in userDetails)
                    {
                        var bodyValues = new List<string>();
                        if (!string.IsNullOrEmpty(userDetail.Email))
                        {
                            var userDetailsKeyValues = GetKeyValuesFromObject(userDetail);
                            emailTemplate.Body = ProcessTemplateVariables(variables, new Dictionary<string, string>(), userDetailsKeyValues, bodyValues, emailTemplate.Body);
                            templateDetails.TryAdd(userDetail.Email, emailTemplate.Body);
                        }
                    }
                    await SendEmailAsync(emailSetting, emailInfo, emailTemplate, templateDetails.Keys.ToList(), files, null);
                }

            }
            catch (Exception ex)
            {
                _logger.Error($"ProcessEmailTemplateConfigAsync: Processing the Email template notification resulted in an error.{ex.Message}");
            }
        }
        private Dictionary<string, string> GetKeyValuesFromObject(object obj)
        {

            var settings = new JsonSerializerSettings
            {
                ReferenceLoopHandling = ReferenceLoopHandling.Ignore
            };

            string json = JsonConvert.SerializeObject(obj, settings);
            JObject jobject = JObject.Parse(json);
            return JTokenHelper.GetWithArrayKeysAndValues(jobject, "");
        }

        private string ProcessTemplateVariables(IEnumerable<string> variables,
            Dictionary<string, string> leadKeyValues,
            Dictionary<string, string>? userDetailsKeyValues,
            List<string> bodyValues,
            string? message = null,
            ViewLeadDto? leadDto = null
            )
        {


            foreach (var variable in variables)
            {
                if (TemplateVariableMapper.LeadVariableMapping.TryGetValue(variable, out var leadKeys))
                {
                    string? leadValue = null;
                    foreach (var leadKey in leadKeys.Split(","))
                    {
                        if (leadKeyValues.TryGetValue(leadKey ?? string.Empty, out var value) && !string.IsNullOrEmpty(value))
                        {
                            if ("#date#" == variable && DateTime.TryParse(value, out var scheduledDate))
                            {
                                var (formattedDate, _) = GetConvertedDateTimeForUserAsync(leadDto.AssignTo.Value, scheduledDate).GetAwaiter().GetResult();
                                value = formattedDate;
                            }
                            if ("#time#" == variable && DateTime.TryParse(value, out scheduledDate))
                            {
                                var (_, formattedTime) = GetConvertedDateTimeForUserAsync(leadDto.AssignTo.Value, scheduledDate).GetAwaiter().GetResult();
                                value = formattedTime;
                            }
                            if ("#leadsource#" == variable.Replace(" ", "").ToLowerInvariant() && leadDto != null)
                            {
                                value = leadDto.Enquiry?.LeadSource.ToString();
                            }
                            if (variable.Replace(" ", "").ToLowerInvariant().Contains("enquirytype") && leadDto != null)
                            {
                                value = leadDto.Enquiry?.EnquiryTypes?[0].ToString();
                            }
                            leadValue = string.IsNullOrEmpty(leadValue) ? value : " " + value;
                        }
                    }
                    bodyValues.Add(leadValue ?? "default");
                    message = !string.IsNullOrEmpty(message) ? message.Replace(variable, leadValue) : message;
                }
                else if (userDetailsKeyValues != null && TemplateVariableMapper.UserDetaislMapping.TryGetValue(variable, out var userKey))
                {
                    string userResult = string.Empty;
                    foreach (var user in userKey.Split(","))
                    {
                        if (userDetailsKeyValues.TryGetValue(user ?? string.Empty, out var value) && !string.IsNullOrEmpty(value))
                        {
                            userResult = string.IsNullOrEmpty(userResult ?? "default") ? value : userResult + " " + value;
                        }
                    }
                    if (!string.IsNullOrEmpty(userResult))
                    {
                        bodyValues.Add(userResult);
                        message = !string.IsNullOrEmpty(message) ? message.Replace(variable, userResult) : message;
                    }
                }
            }

            if (bodyValues.Count() < variables.Count())
            {
                int leftValues = variables.Count() - bodyValues.Count();

                var additionalValues = Enumerable.Range(0, leftValues)
                                                 .Select(_ => "default")
                                                 .ToList();

                bodyValues.AddRange(additionalValues);
            }

            return message ?? string.Empty;
        }

        private async Task SendEmailAsync(EmailSettings emailSetting,
            CustomEmailInfo emailInfo,
            EmailTemplates emailTemplate,
            List<string> toEmails, List<IFormFile>? files,
            Guid? leadId)
        {
            try
            {
                if (emailSetting.IsThirdPartyServiceActivated)
                {
                    var byteArray = Convert.FromBase64String(emailInfo?.Password ?? string.Empty);
                    string password = System.Text.Encoding.UTF8.GetString(byteArray);
                    await _customEmailService.SendEmailAsync(emailInfo?.From ?? string.Empty, toEmails, emailInfo.CC, emailInfo.BCC,
                        emailTemplate?.Subject ?? string.Empty, emailTemplate?.Body ?? string.Empty, emailInfo.ServerName,
                        emailInfo.Port, emailInfo.UserName, password ?? string.Empty, Domain.Enums.MailPriority.Normal, files, bodyType: emailTemplate?.BodyType);
                }
                else if (emailSetting.IsLRServiceActivated)
                {
                    await _graphEmailService.SendEmailAsync
                        (
                            LeadratEmails.NoReplyEmail,
                            emailTemplate.Subject,
                            emailTemplate.Body,
                            toEmails, null, null,
                            files, Microsoft.Graph.BodyType.Html
                        );
                }
            }
            catch (Exception ex)
            {
                _logger.Error($"SendEmailAsync: An error occurred while sending an email to the lead. Error: {ex.Message}. Details: {JsonConvert.SerializeObject(emailTemplate)}");
            }
        }
        public async Task<List<IFormFile>?> ConvertUrlToIFormFileAsync(List<string> mediaUrls)
        {
            List<IFormFile> formFiles = new List<IFormFile>();
            foreach (var mediaUrl in mediaUrls)
            {
                using (HttpClient client = new HttpClient())
                {
                    var response = await client.GetAsync(mediaUrl);
                    if (!response.IsSuccessStatusCode)
                    {
                        continue;
                    }

                    var fileStream = await response.Content.ReadAsStreamAsync();
                    var fileName = Path.GetFileName(mediaUrl);

                    var memoryStream = new MemoryStream();
                    await fileStream.CopyToAsync(memoryStream);
                    memoryStream.Position = 0;

                    var formFile = new FormFile(memoryStream, 0, memoryStream.Length, "file", fileName)
                    {
                        Headers = new HeaderDictionary(),
                        ContentType = response.Content.Headers.ContentType?.ToString()
                    };
                    formFiles.Add(formFile);
                }
            }
            return formFiles.Count > 0 ? formFiles : null;
        }
        private async Task<(string Date, string Time)> GetConvertedDateTimeForUserAsync(Guid userId, DateTime scheduledDate)
        {
            var userDetails = await _userDetailsRepo.FirstOrDefaultAsync(new Lrb.Application.UserDetails.Web.GetUserDetailsByIdSpec(userId));

            if (userDetails?.TimeZoneInfo is string json)
            {
                try
                {
                    using JsonDocument doc = JsonDocument.Parse(json);
                    JsonElement root = doc.RootElement;

                    string timeZoneId = root.GetProperty("TimeZoneId").GetString();
                    string baseUtcOffset = root.GetProperty("BaseUTcOffset").GetString();
                    TimeSpan offset = TimeSpan.Parse(baseUtcOffset);
                    if (!string.IsNullOrWhiteSpace(timeZoneId) && !string.IsNullOrWhiteSpace(baseUtcOffset))
                    {
                        DateTime? convertedDateTime = DateTimeExtensions.ToParticularTimeZone(
                            scheduledDate,
                            timeZoneId,
                            offset);

                        if (convertedDateTime.HasValue)
                        {
                            string formattedDate = convertedDateTime.Value.ToString("dd-MMM-yyyy", CultureInfo.InvariantCulture);
                            string formattedTime = convertedDateTime.Value.ToString("hh:mm tt", CultureInfo.InvariantCulture);
                            return (formattedDate, formattedTime);
                        }
                    }
                }
                catch (Exception ex)
                {
                    // Handled invalid JSON
                }
            }
            string fallbackDate = scheduledDate.ToString("dd-MMM-yyyy", CultureInfo.InvariantCulture);
            string fallbackTime = scheduledDate.ToString("HH:mm tt", CultureInfo.InvariantCulture);
            return (fallbackDate, fallbackTime);
        }

        private async Task<string> GetMicrositeUrl(string module, string userName, string serialNo, string tenantId)
        {
            try
            {
                var micrositeUrl = $"https://{tenantId}.leadrat.com/external/{module}-preview/{userName}/{serialNo}";
                return micrositeUrl;
            }
            catch (Exception ex)
            {
                return string.Empty;
            }
        }
    }
}
