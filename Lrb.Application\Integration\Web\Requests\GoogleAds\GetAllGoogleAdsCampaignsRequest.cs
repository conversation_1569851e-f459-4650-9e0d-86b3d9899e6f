﻿using Lrb.Application.Common.GoogleAds;
using Lrb.Domain.Entities.Integration;

namespace Lrb.Application.Integration.Web.Requests.GoogleAds
{
    public class GetAllGoogleAdsCampaignsRequest : PaginationFilter, IRequest<PagedResponse<GoogleAdsCampaignInfoDto, string>>
    {
        public Guid AccountId { get; set; }
        public string? SearchText { get; set; }
    }
    public class GetAllGoogleAdsCampaignsRequestHandler : IRequestHandler<GetAllGoogleAdsCampaignsRequest, PagedResponse<GoogleAdsCampaignInfoDto, string>>
    {
        private readonly IRepositoryWithEvents<GoogleAdsInfo> _googleAdsInfoRepo;
        public GetAllGoogleAdsCampaignsRequestHandler(IRepositoryWithEvents<GoogleAdsInfo> googleAdsInfoRepo)
        {
            _googleAdsInfoRepo = googleAdsInfoRepo;
        }

        public async Task<PagedResponse<GoogleAdsCampaignInfoDto, string>> Handle(GetAllGoogleAdsCampaignsRequest request, CancellationToken cancellationToken)
        {
            var allStoredAds = await _googleAdsInfoRepo.ListAsync(new GoogleAdsCampaignssByGoogleAccountRequestSpec(request), cancellationToken);
            var groupedByCampaign = allStoredAds
                .GroupBy(ad => ad.CampaignId)
                .Select(group =>
                {
                    var firstAd = group.FirstOrDefault();
                    return new GoogleAdsCampaignInfoDto
                    {
                        CampaignName = firstAd?.CampaignName,
                        CampaignId = group.Key,
                        GoogleAdsAuthResponseId = firstAd?.GoogleAuthResponseId ?? Guid.Empty,
                        LeadCount = group.Sum(ad => ad.LeadsCount),
                        CurrencyCode = firstAd?.CurrencyCode,
                        CountryCode = firstAd?.CountryCode,
                    };
                });
            var pagedCampaigns = groupedByCampaign.Skip(request.PageSize * (request.PageNumber - 1)).Take(request.PageSize).ToList();
            var totalCampaigns = groupedByCampaign.Count();
            return new PagedResponse<GoogleAdsCampaignInfoDto, string>(pagedCampaigns, totalCampaigns);
        }
    }
    public class GoogleAdsCampaignssByGoogleAccountRequestSpec : Specification<GoogleAdsInfo>
    {
        public GoogleAdsCampaignssByGoogleAccountRequestSpec(GetAllGoogleAdsCampaignsRequest filter)
        {
            if (!string.IsNullOrWhiteSpace(filter.SearchText))
            {
                var searchTextLower = filter.SearchText.ToLower();
                Query.Where(i => !i.IsDeleted && i.GoogleAuthResponseId == filter.AccountId &&
                                 (i.CampaignId.Contains(filter.SearchText) ||
                                  i.CampaignName.ToLower().Contains(searchTextLower)));
            }
            else
            {
                Query.Where(i => !i.IsDeleted && i.GoogleAuthResponseId == filter.AccountId);
            }
        }
    }
}
