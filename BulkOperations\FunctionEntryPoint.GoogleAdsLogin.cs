﻿using Google.Ads.GoogleAds;
using Google.Ads.GoogleAds.Config;
using Google.Ads.GoogleAds.Lib;
using Lrb.Application.Common.GoogleAds;
using Lrb.Application.Integration.Web.Requests.GoogleAds;
using Newtonsoft.Json;
using System.Text.Json;

namespace ExcelUpload
{
    public partial class FunctionEntryPoint : IFunctionEntryPoint
    {
        public async Task GoogleAdsLoginHandler(InputPayload input)
        {
            try
            {
                _logger.Information($"Google Ads API error for customer : {"login process started"}");
                var tenantId = input.TenantId;
                var request = JsonConvert.DeserializeObject<CreateGoogleAdsIntegrationUsingConsoleRequest>(input.JsonData ?? "null");
                var client = new HttpClient();
                var restrequest = new HttpRequestMessage(HttpMethod.Post, "https://oauth2.googleapis.com/token");
                var collection = new List<KeyValuePair<string, string>>();
                collection.Add(new("code", request.Code));
                collection.Add(new("client_id", _googleAdsSettings.ClientId ?? string.Empty));
                collection.Add(new("client_secret", _googleAdsSettings.ClientSecret ?? string.Empty));
                collection.Add(new("redirect_uri", _googleAdsSettings.RedirectUri ?? string.Empty));
                collection.Add(new("grant_type", "authorization_code"));
                var content = new FormUrlEncodedContent(collection);
                restrequest.Content = content;
                var response = await client.SendAsync(restrequest);
                response.EnsureSuccessStatusCode();
                Console.WriteLine(await response.Content.ReadAsStringAsync());
                var responseString = await response.Content.ReadAsStringAsync();
                using var jsonDoc = JsonDocument.Parse(responseString);
                var accessToken = jsonDoc.RootElement.GetProperty("access_token").GetString();
                var refreshToken = jsonDoc.RootElement.GetProperty("refresh_token").GetString();
                _logger.Error($"Google Ads API error for customer : {refreshToken}");
                Console.WriteLine("FunctionEntryPoint -> GoogleAdsLoginHandler() started");
                if (string.IsNullOrEmpty(request.CustomerId))
                {
                    GoogleAdsConfig cconfig = new GoogleAdsConfig()
                    {
                        DeveloperToken = _googleAdsSettings.DeveloperToken,
                        OAuth2ClientId = _googleAdsSettings.ClientId,
                        OAuth2ClientSecret = _googleAdsSettings.ClientSecret,
                        OAuth2RefreshToken = refreshToken
                    };

                    GoogleAdsClient cclient = new GoogleAdsClient(cconfig);

                    var customerService = cclient.GetService(Services.V18.CustomerService);
                    var accessibleCustomers = customerService.ListAccessibleCustomers();

                    // Step 4: Check each customer to see if it is a manager account
                    var googleAdsService = cclient.GetService(Services.V18.GoogleAdsService);

                    foreach (var resourceName in accessibleCustomers)
                    {
                        string customerId = resourceName.Split('/')[1];
                        string query = "SELECT customer.id, customer.manager FROM customer";
                        try
                        {
                            var response1 = googleAdsService.Search(customerId, query);

                            foreach (var row in response1)
                            {
                                if (row.Customer.Manager)
                                {
                                    request.CustomerId = row.Customer.Id.ToString();
                                    break;
                                }
                            }

                            if (!string.IsNullOrEmpty(request.CustomerId))
                                break; // Stop loop if manager found
                        }
                        catch (Exception ex)
                        {
                        }
                    }
                }
                if (request?.CustomerId == null || string.IsNullOrEmpty(request.CustomerId))
                {

                    throw new InvalidOperationException("No account found: CustomerId is null or empty");
                }
                if (!string.IsNullOrEmpty(tenantId) && request != null)
                {
                    var tInfo = await _repository.GetTenantInfoAsync(tenantId);

                    var dto = new GoogleAdsIntegrationDto
                    {
                        AccessToken = accessToken ?? string.Empty,
                        RefreshToken = refreshToken ?? string.Empty,
                        GoogleUserName = request.Email ?? string.Empty,
                        CustomerId = request.CustomerId ?? string.Empty,
                        TenantInfoDto = tInfo,
                        CurrentUserId = _currentUser.GetUserId()
                    };
                    _logger.Information($"Google " + JsonConvert.SerializeObject(tInfo) + JsonConvert.SerializeObject(dto));
                    if (IsValidGoogleAdsRequest(dto))
                    {
                        _logger.Information($"Google step 1");

                        if (!await IsGoogleAdsAccountExistsInDifferentTenant(dto))
                        {
                            _logger.Information($"Google step 2");

                            if (await IsGoogleAdsAccountExistsInSameTenant(dto))
                            {
                                _logger.Information($"Google step 3");

                                await UpdateGoogleAdsAuthResponse(dto);
                            }
                            else
                            {
                                _logger.Information($"Google step 4");
                                await CreateGoogleAdsAuthResponse(dto);
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                // Handle Google Ads specific errors
                _logger.Error($"Google Ads API error for customer : {ex.Message}");
            }

            Console.WriteLine("FunctionEntryPoint -> GoogleAdsLoginHandler() finished");
        }
    }
}
