﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Lrb.Application.Integration.Web.Dtos
{
    public class BayutNewWADto : IDto
    {
        public NewAgentClass? agent { get; set; }
        public NewListingClass? listing { get; set; }
        public string? id { get; set; }
        public string? message { get; set; }
        public string? received_at { get; set; }
        public NewEnquirerClass? enquirer { get; set; }
        public string? wam_id { get; set; }
        
    }
    public class NewAgentClass : IDto
    {
        public string? url { get; set; }
        public string? id { get; set; }
        public string? name { get; set; }
        public string? email { get; set; }
        public string? phone_number { get; set; }
    }
    public class NewListingClass : IDto
    {
        public string? url { get; set; }
        public string? reference { get; set; }
    }
    public class NewEnquirerClass : IDto
    {
        public string? name { get; set; }
        public string? contact_link { get; set; }
        public string? phone_number { get; set; }
    }
}
