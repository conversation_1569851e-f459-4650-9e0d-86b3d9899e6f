﻿using Lrb.Application.Identity.Roles;
using Lrb.Application.Identity.Users;
using Lrb.Application.MasterData.Specs;
using Lrb.Domain.Entities.AmenitiesAttributes;
using Lrb.Domain.Entities.MasterData;
using System;

namespace Lrb.Application.MasterData
{
    public class GetLastModifiedDatesRequest : IRequest<Response<Dictionary<FeatureName, DateTime?>>>
    {
    }
    public class GetLastModifiedDatesRequestHandler : IRequestHandler<GetLastModifiedDatesRequest, Response<Dictionary<FeatureName, DateTime?>>>
    {
        private readonly IReadRepository<Domain.Entities.UserDetails> _userDetailsRepo;
        private readonly IReadRepository<MasterAreaUnit> _masterAreaUnitRepo;
        //private readonly IReadRepository<MasterBuilderInfo> _masterBuilderInfoRepo;
        private readonly IReadRepository<MasterLeadSource> _masterLeadSourceRepo;
        //private readonly IReadRepository<MasterLeadStatus> _masterLeadStatusRepo;
        private readonly IReadRepository<CustomMasterAmenity> _masterPropertyAmenityRepo;
        private readonly IReadRepository<CustomMasterAttribute> _masterPropertyAttributeRepo;
        private readonly IReadRepository<MasterPropertyType> _masterPropertyTypeRepo;
        //private readonly IReadRepository<MasterUserService> _masterUserServiceRepo;
        private readonly IReadRepository<Domain.Entities.Profile> _profileRepository;
        private readonly IRoleService _roleService;
        private readonly ICurrentUser _currentUser;
        private readonly IUserService _userService;
        private readonly IReadRepository<Domain.Entities.Lead> _leadRepository;
        private readonly IReadRepository<MasterWhatsAppTemplate> _masterWhatsAppTemplateRepo;
        private readonly IReadRepository<WhatsAppTemplate> _whatsAppTemplateRepo;
        private readonly IDapperRepository _dapperRepo;
        private readonly IReadRepository<Domain.Entities.Location> _locationRepo;
        private readonly IReadRepository<CustomMasterLeadStatus> _customMasterLeadStatusRepo;
        private readonly IReadRepository<MobileCatalogDetail> _mobileCatelogDetailRepo;
        private readonly IReadRepository<Domain.Entities.Source> _sourceRepo;
        public GetLastModifiedDatesRequestHandler(IReadRepository<Domain.Entities.UserDetails> userDetailsRepository,
            IReadRepository<MasterAreaUnit> masterAreaUnitrepo,
            //IReadRepository<MasterBuilderInfo> masterBuilderInfoRepo,
            IReadRepository<MasterLeadSource> masterLeadSourceRepo,
            //IReadRepository<MasterLeadStatus> masterLeadStatusRepo,
            IReadRepository<CustomMasterAmenity> masterPropertyAmenityRepo,
            IReadRepository<CustomMasterAttribute> masterPropertyAttributeRepo,
            IReadRepository<MasterPropertyType> masterPropertyTypeRepo,
            //IReadRepository<MasterUserService> masterUserServiceRepo,
            IReadRepository<Profile> profileRepository,
            IRoleService roleService,
            ICurrentUser currentUser,
            IUserService userService,
            IReadRepository<Domain.Entities.Lead> leadRepository,
            IReadRepository<MasterWhatsAppTemplate> masterWhatsAppTemplateRepo,
            IReadRepository<WhatsAppTemplate> whatsAppTemplateRepo,
            IReadRepository<CustomMasterLeadStatus> customMasterLeadStatusRepo,
            IDapperRepository dapperRepo,
            IReadRepository<Domain.Entities.Location> locationRepo,
            IReadRepository<MobileCatalogDetail> mobileCatelogDetailRepo,

            IReadRepository<Domain.Entities.Source> sourceRepo)
        {
            _userDetailsRepo = userDetailsRepository;
            _masterAreaUnitRepo = masterAreaUnitrepo;
            //_masterBuilderInfoRepo = masterBuilderInfoRepo;
            _masterLeadSourceRepo = masterLeadSourceRepo;
            //_masterLeadStatusRepo = masterLeadStatusRepo;
            _masterPropertyAmenityRepo = masterPropertyAmenityRepo;
            _masterPropertyAttributeRepo = masterPropertyAttributeRepo;
            _masterPropertyTypeRepo = masterPropertyTypeRepo;
            //_masterUserServiceRepo = masterUserServiceRepo;
            _profileRepository = profileRepository;
            _roleService = roleService;
            _currentUser = currentUser;
            _userService = userService;
            _leadRepository = leadRepository;
            _masterWhatsAppTemplateRepo = masterWhatsAppTemplateRepo;
            _whatsAppTemplateRepo = whatsAppTemplateRepo;
            _dapperRepo = dapperRepo;
            _customMasterLeadStatusRepo = customMasterLeadStatusRepo;
            _locationRepo = locationRepo;
            _mobileCatelogDetailRepo = mobileCatelogDetailRepo;
            _sourceRepo = sourceRepo;
        }

        public async Task<Response<Dictionary<FeatureName, DateTime?>>> Handle(GetLastModifiedDatesRequest request, CancellationToken cancellationToken)
        {
            try
            {
                var currentUserId = _currentUser.GetUserId();
                var currentUser = (await _userService.GetListOfUsersByIdsAsync(new List<string>() { currentUserId.ToString() }, cancellationToken)).FirstOrDefault();
                Dictionary<FeatureName, DateTime?> result = new Dictionary<FeatureName, DateTime?>();
                var user = (await _userDetailsRepo.ListAsync(new GetUsersSpec(), cancellationToken)).FirstOrDefault();
                var masterAreaUnit = (await _masterAreaUnitRepo.ListAsync(new GetMasterAreaUnitSpec(), cancellationToken)).FirstOrDefault();
                //var masterBuilderInfo = (await _masterBuilderInfoRepo.ListAsync(new GetMasterBuilderInfoSpec(), cancellationToken)).FirstOrDefault();
                var masterLeadSource = (await _masterLeadSourceRepo.ListAsync(new GetMasterLeadSourceSpec(), cancellationToken)).FirstOrDefault();
                //var masterLeadStatus = (await _masterLeadStatusRepo.ListAsync(new GetMasterLeadStatusSpec(), cancellationToken)).FirstOrDefault();
                var masterPropertyAmenity = (await _masterPropertyAmenityRepo.ListAsync(new GetCustomAmenitySpec(), cancellationToken)).FirstOrDefault();
                var masterPropertyAttribute = (await _masterPropertyAttributeRepo.ListAsync(new GetMasterPropertyAttributeSpec(), cancellationToken)).FirstOrDefault();
                var masterPropertyType = (await _masterPropertyTypeRepo.ListAsync(new GetMasterPropertyTypeSpec(), cancellationToken)).FirstOrDefault();
                //var MasterUserService = (await _masterUserServiceRepo.ListAsync(new GetMasterUserServiceSpec(), cancellationToken)).FirstOrDefault();
                var orgProfile = (await _profileRepository.ListAsync(new GetOrgProfileSpec(), cancellationToken)).FirstOrDefault();
                //var customMastrLeadStatus = (await _customMasterLeadStatusRepo.ListAsync(new GetCustomMasterLeadStatusByLastModifiedSpec(), cancellationToken)).FirstOrDefault();
                var sources = (await _sourceRepo.FirstOrDefaultAsync(new GetLeadSourceSpec(), cancellationToken));
                var role = (await _roleService.ListAsync(cancellationToken)).FirstOrDefault();
                var location = (await _locationRepo.ListAsync(new GetLocationsSpec(), cancellationToken)).FirstOrDefault();
                var tenantId = _currentUser.GetTenant();
                if (!string.IsNullOrEmpty(tenantId))
                {
                    // var dateByAgencyName = (await _dapperRepo.GetLastModifiedDateByAgencyNameAsync(tenantId));
                    result.Add(FeatureName.AgencyNames, DateTime.MinValue);
                    var customMastrLeadStatus = (await _dapperRepo.GetLastModifiedDate(tenantId));
                    result.Add(FeatureName.CustomMasterLeadStatus, customMastrLeadStatus);
                    var customMasterDataStatus = (await _dapperRepo.GetProspectStatusLastModifiedDate(tenantId));
                    result.Add(FeatureName.CustomMasterDataStatus, customMasterDataStatus);
                }

                var whatsAppTemplate = (await _whatsAppTemplateRepo.FirstOrDefaultAsync(new GetWhatAppTemplateByModifiedOnSpec(), cancellationToken));
                var masterWhatsAppTemplate = (await _masterWhatsAppTemplateRepo.FirstOrDefaultAsync(new GetMasterWhatAppTemplateByModifiedOnSpec(), cancellationToken));
                var mobileCatelogDetail = (await _mobileCatelogDetailRepo.FirstOrDefaultAsync(new GetMobileCardViewSpec(), cancellationToken));
                result.Add(FeatureName.Users, user?.LastModifiedOn ?? DateTime.MinValue);
                result.Add(FeatureName.MasterAreaUnit, masterAreaUnit?.LastModifiedOn ?? DateTime.MinValue);
                //result.Add(FeatureName.MasterBuilderInfo, masterBuilderInfo?.LastModifiedOn ?? DateTime.MinValue);
                result.Add(FeatureName.MasterLeadSource, masterLeadSource?.LastModifiedOn ?? DateTime.MinValue);
                //result.Add(FeatureName.MasterLeadStatus, masterLeadStatus?.LastModifiedOn ?? DateTime.MinValue);
                result.Add(FeatureName.MasterPropertyAmenity, masterPropertyAmenity?.LastModifiedOn ?? DateTime.MinValue);
                result.Add(FeatureName.MasterPropertyAttribute, masterPropertyAttribute?.LastModifiedOn ?? DateTime.MinValue);
                result.Add(FeatureName.MasterPropertyType, masterPropertyType?.LastModifiedOn ?? DateTime.MinValue);
                //result.Add(FeatureName.MasterUserService, MasterUserService?.LastModifiedOn ?? DateTime.MinValue);
                result.Add(FeatureName.OrgProfile, orgProfile?.LastModifiedOn ?? DateTime.MinValue);
                result.Add(FeatureName.Roles, role?.LastModifiedOn ?? DateTime.MinValue);
                result.Add(FeatureName.CurrentUser, currentUser?.LastModifiedOn ?? DateTime.MinValue);
                result.Add(FeatureName.WhatsAppTemplates, whatsAppTemplate?.LastModifiedOn ?? DateTime.MinValue);
                result.Add(FeatureName.MasterWhatsAppTemplates, masterWhatsAppTemplate?.LastModifiedOn ?? DateTime.MinValue);
                result.Add(FeatureName.Locations, location?.LastModifiedOn ?? DateTime.MinValue);
                result.Add(FeatureName.MobileCatalogDetail, mobileCatelogDetail?.LastModifiedOn ?? DateTime.MinValue);
                result.Add(FeatureName.Sources, sources?.LastModifiedOn ?? DateTime.MinValue);
                return new(result);
            }
            catch (Exception ex)
            {
                return new() { Message = ex.Message };
            }
        }
    }
    public enum FeatureName
    {
        Users = 0,
        MasterAreaUnit,
        MasterBuilderInfo,
        MasterLeadSource,
        MasterLeadStatus,
        MasterPropertyAmenity,
        MasterPropertyAttribute,
        MasterPropertyType,
        MasterUserService,
        OrgProfile,
        Roles,
        CurrentUser,
        AgencyNames,
        WhatsAppTemplates,
        MasterWhatsAppTemplates,
        CustomMasterLeadStatus,
        Locations,
        MobileCatalogDetail,
        Sources,
        CustomMasterDataStatus
    }
}
