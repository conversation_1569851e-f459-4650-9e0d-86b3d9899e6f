﻿using Lrb.Application.Common.Facebook;
using Lrb.Application.Common.GoogleAds;
using Lrb.Application.Integration.Web.Dtos;
using Lrb.Application.Integration.Web.Specs;
using Lrb.Domain.Entities.Integration;

namespace Lrb.Application.Integration.Web.Requests.Facebook
{
    public class ToggleFacebookAdsAndFormsSubscriptionRequestV1 : IRequest<Response<bool>>
    {
        public Guid AccountId { get; set; }

        public bool IsSubscribed { get; set; }
    }
    public class ToggleFacebookAdsAndFormsSubscriptionRequestV1Handler : <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, IRequestHandler<ToggleFacebookAdsAndFormsSubscriptionRequestV1, Response<bool>>
    {
        public ToggleFacebookAdsAndFormsSubscriptionRequestV1Handler(
           IRepositoryWithEvents<FacebookAuthResponse> facebookAuthResponseRepo,
           IRepositoryWithEvents<FacebookConnectedPageAccount> facebookConnectedPageAccountRepo,
           IRepositoryWithEvents<FacebookLeadGenForm> facebookLeadGenFormRepo,
           IFacebookService facebookService,
           IJobService hangfireService,
           ITenantIndependentRepository repository,
           ICurrentUser currentUser,
           IRepositoryWithEvents<IntegrationAccountInfo> integrationAccInfoRepo,
           IRepositoryWithEvents<FacebookAdsInfo> fbAdsRepo, ILeadRepositoryAsync leadRepositoryAsync, IGoogleAdsService googleAdsService, IRepositoryWithEvents<GoogleAdsAuthResponse> googleAdsAuthResponseRepo,
           IRepositoryWithEvents<GoogleAdsInfo> googleAdsRepo,
           IRepositoryWithEvents<GoogleCampaign> googleCampaignsRepo)
           : base(facebookAuthResponseRepo,
                 facebookConnectedPageAccountRepo,
                 facebookLeadGenFormRepo,
                 facebookService,
                 hangfireService,
                 repository,
                 currentUser,
                 integrationAccInfoRepo,
                 fbAdsRepo, leadRepositoryAsync, googleAdsService, googleAdsAuthResponseRepo, googleAdsRepo,
                 googleCampaignsRepo)
        { }
        public async Task<Response<bool>> Handle(ToggleFacebookAdsAndFormsSubscriptionRequestV1 request, CancellationToken cancellationToken)
        {
            var accounts = await _facebookAuthResponseRepo.ListAsync(new GetAllFacebookAccountsSpec(request.AccountId), cancellationToken);
            var existingFacebookConnectedPageAccounts = await _facebookConnectedPageAccountRepo.ListAsync(new GetFacebookConnectedPageAccountSpecV1(request.AccountId), cancellationToken);
            var allStoredAds = await _fbAdsRepo.ListAsync(new FacebookAdsByFbAccountIdSpec(request.AccountId), cancellationToken);
            var formDtos = existingFacebookConnectedPageAccounts.Where(i => i.FacebookAuthResponseId == request.AccountId).SelectMany(i => i.FBLeadGenForms ?? new List<FacebookLeadGenForm>()).ToList();
            List<Guid>? adIds = allStoredAds.Where(i => i.IsSubscribed == request.IsSubscribed).Select(i => i.Id).ToList();

            List<Guid>? formIds = formDtos?.Where(i=>i.IsSubscribed==request.IsSubscribed).Select(i => i.Id).ToList();


            if (formIds?.Any() ?? false)
            {
                var forms = await _facebookLeadGenFormRepo.ListAsync(new FacebookLeadGenFormByIdSpec(formIds));
                if (forms.Any())
                {
                    forms.ForEach(x => x.IsSubscribed = !x.IsSubscribed);
                    await _facebookLeadGenFormRepo.UpdateRangeAsync(forms);
                }
            }
            if (adIds?.Any() ?? false)
            {
                var ads = await _fbAdsRepo.ListAsync(new FacebookAdsByIdsSpec(adIds));
                if (ads?.Count > 0)
                {
                    ads.ForEach(x => x.IsSubscribed = !x.IsSubscribed);
                    await _fbAdsRepo.UpdateRangeAsync(ads);
                }
            }
            return new(true);
        }
    }
}
