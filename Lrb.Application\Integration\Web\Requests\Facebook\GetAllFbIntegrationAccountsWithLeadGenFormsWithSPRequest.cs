﻿using Lrb.Application.Common.Facebook;
using Lrb.Application.Common.GoogleAds;
using Lrb.Application.Identity.Users;
using Lrb.Application.Integration.Web.Dtos;
using Lrb.Application.Integration.Web.Specs;
using Lrb.Domain.Entities.Integration;

namespace Lrb.Application.Integration.Web.Requests.Facebook
{
    public class GetAllFbIntegrationAccountsWithLeadGenFormsWithSPRequest : PaginationFilter, IRequest<PagedResponse<ViewFacebookAccountWithFormDto, string>>
    {

    }
    public class GetAllFbIntegrationAccountsWithLeadGenFormsRequestWithSPHandler : <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, IRequestHandler<GetAllFbIntegrationAccountsWithLeadGenFormsWithSPRequest, PagedResponse<ViewFacebookAccountWithFormDto, string>>
    {
        private readonly IRepositoryWithEvents<IntegrationAssignmentInfo> _integrationAssignmentInforepo;
        private readonly IRepositoryWithEvents<Lrb.Domain.Entities.Project> _projectRepo;
        private readonly IUserService _userService;
        private readonly IDapperRepository _dapperRepository;

        public GetAllFbIntegrationAccountsWithLeadGenFormsRequestWithSPHandler(
            IRepositoryWithEvents<FacebookAuthResponse> facebookAuthResponseRepo,
           IRepositoryWithEvents<FacebookConnectedPageAccount> facebookConnectedPageAccountRepo,
           IRepositoryWithEvents<FacebookLeadGenForm> facebookLeadGenFormRepo,
           IFacebookService facebookService,
           IJobService hangfireService,
           ITenantIndependentRepository repository,
           ICurrentUser currentUser,
           IRepositoryWithEvents<IntegrationAccountInfo> integrationAccInfoRepo,
           IRepositoryWithEvents<IntegrationAssignmentInfo> integrationAssignmentInforepo,
           IRepositoryWithEvents<FacebookAdsInfo> fbAdsRepo,
           IRepositoryWithEvents<Lrb.Domain.Entities.Project> projectRepo,
           IUserService userService,
           IDapperRepository dapperRepository,
           ILeadRepositoryAsync leadRepositoryAsync,
           IGoogleAdsService googleAdsService,
           IRepositoryWithEvents<GoogleAdsAuthResponse> googleAdsAuthResponseRepo,
           IRepositoryWithEvents<GoogleAdsInfo> googleAdsRepo,
           IRepositoryWithEvents<GoogleCampaign> googleCampaignsRepo
            ) : base(facebookAuthResponseRepo,
                 facebookConnectedPageAccountRepo,
                 facebookLeadGenFormRepo,
                 facebookService,
                 hangfireService,
                 repository,
                 currentUser,
                 integrationAccInfoRepo,
                 fbAdsRepo, leadRepositoryAsync, googleAdsService, googleAdsAuthResponseRepo, googleAdsRepo, googleCampaignsRepo)
        {
            _integrationAssignmentInforepo = integrationAssignmentInforepo;
            _projectRepo = projectRepo;
            _userService = userService;
            _dapperRepository = dapperRepository;
        }
        public async Task<PagedResponse<ViewFacebookAccountWithFormDto, string>> Handle(GetAllFbIntegrationAccountsWithLeadGenFormsWithSPRequest request, CancellationToken cancellationToken)
        {
            var accounts = await _facebookAuthResponseRepo.ListAsync(new GetAllFacebookIntegrationAccountsWithSPSpec(request), cancellationToken);
            var integrationInfo = await _integrationAccInfoRepo.ListAsync(new IntegrationAccInfoByFacebookIdOrIdSpec(accounts.Select(i => i.Id).ToList()));
            var existingFacebookConnectedPageAccounts = await _facebookConnectedPageAccountRepo.ListAsync(new GetFacebookConnectedPageAccountSpec(), cancellationToken);
            var existingPageIds = existingFacebookConnectedPageAccounts.Select(i => i.FacebookId).ToList();
            var count = await _facebookAuthResponseRepo.CountAsync(new CountFacebookIntegrationAccountSpec(), cancellationToken);
            var viewFacebookFormDataDtos = accounts.Adapt<List<ViewFacebookAccountWithFormDto>>();
            foreach (var accDto in viewFacebookFormDataDtos)
            {
                accDto.LeadCount = integrationInfo.FirstOrDefault(i => i.FacebookAccountId == accDto.Id)?.LeadCount ?? 0;
                accDto.LeadSource = LeadSource.Facebook;
                accDto.IsAutomated = integrationInfo.FirstOrDefault(i => i.FacebookAccountId == accDto.Id)?.IsAutomated ?? false;
                //var storedAds = await _fbAdsRepo.ListAsync(new FacebookAdsByFbAccountIdSpec(accDto.Id), cancellationToken);
                var adsInfoDtos = (await _dapperRepository.QueryStoredProcedureFromReadReplicaAsync<FacebookAdsInfoDto>("LeadratBlack", "ProjectsAndPropertiesByAdsFunc", new
                {
                    fb_acc_id = accDto.Id
                }));
                accDto.Ads = adsInfoDtos.ToList();
                var pageidsInAds = accDto.Ads?.Select(i => i.PageId);
                var connectedPages = existingFacebookConnectedPageAccounts?.Where(i => i.FacebookAuthResponseId == accDto.Id).ToList();
                if (connectedPages?.Any() ?? false)
                {
                    var allFormsBySp = (await _dapperRepository.QueryStoredProcedureFromReadReplicaAsync<FacebookLeadGenFormDto>("LeadratBlack", "ProjectsAndPropertiesByConnectedPagesFunc", new
                    {
                        fb_acc_id = accDto.Id
                    }));
                    accDto.AllForms = allFormsBySp.ToList();
                    accDto.ExternalForms = allFormsBySp?.Where(i => !pageidsInAds?.Contains(i.PageId) ?? false);
                }
            }

            return new(viewFacebookFormDataDtos, count);
        }
    }
}
