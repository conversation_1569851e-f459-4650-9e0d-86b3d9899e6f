
using Dapper;
using Lrb.Infrastructure.Persistence;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using Npgsql;
using RestSharp;
using System.ComponentModel;

namespace ListingBackgroundJobs.Repositories
{
    public class ListingRepository : IListingRepository
    {
        private readonly DatabaseSettings _settings;
        private readonly string _baseUri = "https://atlas.propertyfinder.com";

        public ListingRepository(IOptions<DatabaseSettings> options)
        {
            _settings = options.Value;
        }

        public async Task SetPropertyListingStatus()
        {
            var tenants = await GetAllTenantsAsync();
            try
            {
                foreach (var tenant in tenants)
                {
                    await SetPropertyListingStatus(tenant?.Id ?? string.Empty, string.IsNullOrEmpty(tenant?.ConnectionString) ? _settings.ConnectionString : tenant.ConnectionString);
                }
            }
            catch (Exception ex)
            {
                throw;
            }
        }

        public async Task<bool> SetPropertyListingStatus(string tenant, string connectionString)
        {
            var pfSource = await GetAllListingSource(tenant, connectionString);
            if (pfSource == null)
            {
                return false;
            }
            var listingAccounts = await GetAllListingAccount(tenant, connectionString, pfSource.Id);
            if (listingAccounts?.Any() ?? false)
            {
                foreach (var account in listingAccounts)
                {
                    var pfProperties = await FetchAllPropertyFromPortal(account?.BaseUri ?? string.Empty, account?.ApiKey ?? string.Empty, account?.SecretKey ?? string.Empty);
                    if (pfProperties.Results?.Any() ?? false)
                    {
                        var listedProperties = pfProperties.Results;
                        var refusedProperties = listedProperties.Where(i => i.Portals?.Propertyfinder?.IsLive == false).ToList();
                        await UpdateListingStatus(refusedProperties, tenant, connectionString);
                    }
                }
                return true;
            }
            return false;
        }

        #region Generate Auth Token
        public async Task<string> GeneratePFAuthToken(string apiKey, string secretKey)
        {
            try
            {
                var options = new RestClientOptions(_baseUri ?? string.Empty)
                {
                    MaxTimeout = -1,
                };
                var client = new RestClient(options);
                var request = new RestRequest("/v1/auth/token", Method.Post);
                request.AddHeader("Content-Type", "application/json");
                var body = new PFNewAuthDto()
                {
                    apiKey = apiKey,
                    apiSecret = secretKey
                };
                var jsonBody = JsonConvert.SerializeObject(body);
                request.AddStringBody(jsonBody, DataFormat.Json);
                RestResponse response = await client.PostAsync(request);
                var data = JsonConvert.DeserializeObject<PFAuthResponseDto>(response?.Content ?? string.Empty);

                return data?.accessToken ?? string.Empty;
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException("Failed to generate Property Finder auth token.", ex);
            }
        }
        #endregion

        #region PF Property Api
        public async Task<PFRootObject> FetchAllPropertyFromPortal(string baseUrl, string apiKey, string secretKey)
        {
            try
            {
                var options = new RestClientOptions(_baseUri)
                {
                    MaxTimeout = -1,
                };
                var client = new RestClient(options);
                var token = await GeneratePFAuthToken(apiKey, secretKey);
                var request = new RestRequest("/v1/listings?perPage=100&page=1&draft=true", Method.Get);
                request.AddHeader("Authorization", $"Bearer {token}");
                RestResponse response = await client.ExecuteAsync(request);
                if (response.IsSuccessful)
                {
                    var data = JsonConvert.DeserializeObject<PFRootObject>(response?.Content ?? string.Empty);
                    return data ?? new();
                }
                return null;
            }
            catch (Exception ex)
            {
                return new();
            }
        }
        #endregion

        #region Set Refused Properties
        public async Task<bool> UpdateListingStatus(List<Result> listedProperties, string tenantId, string connectionString)
        {
            try
            {
                var conn = new NpgsqlConnection(connectionString);
                foreach (var property in listedProperties)
                {
                    var query = $"Select * from \"LeadratBlack\".\"Properties\" where \"PFListingId\" = '{property.Id}' and \"TenantId\" = '{tenantId}' and \"IsDeleted\" = false and \"IsArchived\" = false and \"ShouldVisisbleOnListing\" = true";
                    var lrbProperty = (await conn.QueryAsync<ListedPropertyDto>(query)).FirstOrDefault();
                    if (lrbProperty != null)
                    {
                        var updateQuery = $"Update \"LeadratBlack\".\"Properties\" Set \"ShouldVisisbleOnListing\" = false, \"ListingStatus\" = 3, \"Message\" = '{property?.State?.Reasons?.FirstOrDefault()?.En}' where \"Id\" = '{lrbProperty.Id}'";
                        await conn.ExecuteAsync(updateQuery);
                    }
                }
                return true;
            }
            catch
            {
                return false;
            }

        }
        #endregion

        #region Get All Listing Source
        public async Task<ListingSource> GetAllListingSource(string tenant, string? connectionString)
        {
            try
            {
                NpgsqlConnection? conn = null;
                var query = $"Select * from \"LeadratBlack\".\"CustomListingSources\" where \"TenantId\" = '{tenant}' and \"DisplayName\" = 'PropertyFinder' and \"IsDeleted\" = false";
                conn = new NpgsqlConnection(connectionString);
                var result = await conn.QueryAsync<ListingSource>(query);
                return result.FirstOrDefault();
            }
            catch
            {
                return new();
            }
        }
        #endregion

        #region Get All Lrb Listing Account
        public async Task<List<ListingAccount>> GetAllListingAccount(string tenant, string? connectionString, Guid sourceId)
        {
            try
            {
                NpgsqlConnection? conn = null;
                var query = $"Select * from \"LeadratBlack\".\"ListingIntegrationInfos\" where \"TenantId\" = '{tenant}' and \"IsDeleted\" = false and \"ListingSourceId\" = '{sourceId}'";
                conn = new NpgsqlConnection(connectionString);
                var result = await conn.QueryAsync<ListingAccount>(query);
                return result.ToList();
            }
            catch
            {
                return new();
            }

        }
        #endregion

        #region Get All Tenant
        public async Task<List<TenantInfo>> GetAllTenantsAsync()
        {
            try
            {
                var tenantQuery = "Select \"Id\",\"ConnectionString\" from \"MultiTenancy\".\"Tenants\" where \"IsActive\" = 'true' ";
                var conn = new NpgsqlConnection(_settings.ConnectionString);
                var result = await conn.QueryAsync<TenantInfo>(tenantQuery);
                return result.ToList();
            }
            catch
            {
                return new();
            }
        }
        #endregion

        #region Tennant Dto
        public class TenantInfo
        {
            public string? ConnectionString { get; set; }
            public string? Id { get; set; }
        }
        #endregion

        #region Lrb Listing Account Info
        public class ListingAccount
        {
            public string? AccountName { get; set; }
            public string? BaseUri { get; set; }
            public string? ApiKey { get; set; }
            public string? SecretKey { get; set; }
        }
        #endregion

        #region Listing Source
        public class ListingSource
        {
            public Guid Id { get; set; }
            public string? DisplayName { get; set; }
        }
        #endregion

        #region PF Property Response Dto
        public class PFRootObject
        {
            public Pagination? Pagination { get; set; }
            public List<Result>? Results { get; set; }
        }

        public class Pagination
        {
            public int? NextPage { get; set; }
            public int? Page { get; set; }
            public int? PerPage { get; set; }
            public int? PrevPage { get; set; }
            public int? Total { get; set; }
            public int? TotalPages { get; set; }
        }

        public class Result
        {
            public AssignedTo? AssignedTo { get; set; }
            public string? Id { get; set; }
            public Portals? Portals { get; set; }
            public string? Reference { get; set; }
            public State? State { get; set; }
            public Title? Title { get; set; }
        }

        public class AssignedTo
        {
            public int? Id { get; set; }
        }

        public class Portals
        {
            public PropertyFinder? Propertyfinder { get; set; }
        }

        public class PropertyFinder
        {
            public bool? IsLive { get; set; }
            public string? Name { get; set; }
        }

        public class State
        {
            public List<StateReason>? Reasons { get; set; }
            public string? Stage { get; set; }
            public string? Type { get; set; }
        }

        public class StateReason
        {
            public string? En { get; set; }
        }

        public class Title
        {
            public string? En { get; set; }
        }

        public class PFAuthResponseDto
        {
            public string? accessToken { get; set; }
        }
        #endregion

        #region Property Dto
        public class ListedPropertyDto
        {
            public Guid Id { get; set; }
            public string? PFListingId { get; set; }
            public string? SerialNo { get; set; }
            public bool? ShouldVisisbleOnListing { get; set; }
            public ListingStatus ListingStatus { get; set; }
        }

        public enum ListingStatus
        {
            None,
            [Description("Approved")]
            Approved,
            [Description("Draft")]
            Draft,
            [Description("Refused")]
            Refused,
            [Description("Sold")]
            Sold,
            [Description("Archived")]
            Archived
        }
        #endregion

        #region Auth Dto
        public class PFNewAuthDto
        {
            public string? apiKey { get; set; }
            public string? apiSecret { get; set; }
        }
        #endregion
    }
}
