﻿using Lrb.Application.Common.Persistence.New_Implementation;
using Lrb.Application.Identity.Users;
using Lrb.Application.Property.Web.Specs;
using Lrb.Domain.Entities.AmenitiesAttributes;
using Lrb.Domain.Entities.ErrorModule;
using Lrb.Domain.Entities.MasterData;
using Newtonsoft.Json;

namespace Lrb.Application.Property.Web
{
    public class GetAllPropertyRequest : PaginationFilter, IRequest<PagedResponse<ViewPropertyDto, PropertyCountDto>>
    {
        public PropertyDimensionDto? PropertySize { get; set; }
        public List<string>? Locations { get; set; }
        public List<string>? Cities { get; set; }
        public List<string>? States { get; set; }
        public EnquiryType? EnquiredFor { get; set; }
        public double? NoOfBHK { get; set; }
        public List<double>? BHKs { get; set; }
        public string? Ratings { get; set; }
        public PropertyStatus? PropertyStatus { get; set; }
        public List<Guid>? PropertyTypes { get; set; }
        public List<Guid>? PropertySubTypes { get; set; }
        public Guid? BasePropertyTypeId { get; set; }
        public string? PropertySearch { get; set; }
        public PropertyDateType? DateType { get; set; }
        public DateTime? FromDate { get; set; }
        public DateTime? ToDate { get; set; }
        public long? MinPrice { get; set; }
        public long? MaxPrice { get; set; }
        public List<Guid>? Amenities { get; set; }
        public List<BHKType>? BHKTypes { get; set; }
        public DateTime? FromPossessionDate { get; set; }
        public DateTime? ToPossessionDate { get; set; }
        public List<string>? Projects { get; set; }
        public List<FurnishStatus>? FurnishStatuses { get; set; }
        public List<SaleType>? SaleTypes { get; set; }
        public string? OwnerName { get; set; }
        public string? PropertyTitle { get; set; }
        public Facing? Facing { get; set; }
        public List<int>? NoOfBathrooms { get; set; }
        public List<int>? NoOfLivingrooms { get; set; }
        public List<int>? NoOfBedrooms { get; set; }
        public List<int>? NoOfUtilites { get; set; }
        public List<int>? NoOfKitchens { get; set; }
        public List<int>? NoOfBalconies { get; set; }
        public List<int>? NoOfFloor { get; set; }
        public int? FloorNumber { get; set; }
        public long? MaxBudget { get; set; }
        public long? MinBudget { get; set; }
        public List<string>? OwnerNames { get; set; }
        public bool? IsWithTeam { get; set; }
        public List<Guid>? UserIds { get; set; }
        public ViewAssignmentsPermission? Permission { get; set; }
        public string? Currency { get; set; }
        public string? SerialNo { get; set; }
        public List<Guid>? ListingOnBehalf { get; set; }

        public PossesionType? PossesionType { get; set; }
        public DateTime? FromPossesionDate { get; set; }
        public DateTime? ToPossesionDate { get; set; }



        public long? FromMinPrice { get; set; }
        public long? ToMinPrice { get; set; }
        public long? FromMaxPrice { get; set; }
        public long? ToMaxPrice { get; set; }
        public long? MinLeadCount { get; set; }
        public long? MaxLeadCount { get; set; }
        public long? MinProspectCount { get; set; }
        public long? MaxProspectCount { get; set; }
        public List<string>? NoOfFloors { get; set; }
        public List<int>? Parking { get; set; }
        public List<string>? Countries { get; set; }


    }
    public class GetAllPropertyRequestHandler : IRequestHandler<GetAllPropertyRequest, PagedResponse<ViewPropertyDto, PropertyCountDto>>
    {
        private readonly IUserService _userService;
        private readonly IReadRepository<Domain.Entities.Property> _propertyRepo;
        private readonly IReadRepository<CustomMasterAttribute> _masterPropertyAttributeRepo;
        private readonly IReadRepository<MasterAreaUnit> _masterAreaUnitRepo;
        private readonly IReadRepository<CustomMasterAmenity> _masterPropertyAmenityRepo;
        private readonly IReadRepository<MasterPropertyType> _masterPropertyTypeRepo;
        private readonly IReadRepository<PropertyDimension> _propertyDimensionRepo;
        private readonly ILeadRepositoryAsync _leadRepositoryAsync;
        private readonly IDapperRepository _dapperRepository;
        private readonly ICurrentUser _currentUser;
        private readonly IRepositoryWithEvents<Domain.Entities.GlobalSettings> _globalSettingsRepository;
        protected readonly IPropertyRepository _efPropertyRepository;


        public GetAllPropertyRequestHandler(
            IUserService userService,
            IReadRepository<Domain.Entities.Property> propertyRepo,
            IReadRepository<CustomMasterAttribute> masterPropertyAttributeRepo,
            IReadRepository<MasterAreaUnit> masterAreaUnitRepo,
            IReadRepository<CustomMasterAmenity> masterPropertyAmenityRepo,
            IReadRepository<MasterPropertyType> masterPropertyTypeRepo,
            IReadRepository<PropertyDimension> propertyDimensionRepo,
            ILeadRepositoryAsync leadRepositoryAsync,
            IDapperRepository dapperRepository,
            ICurrentUser currentUser,
            IRepositoryWithEvents<Domain.Entities.GlobalSettings> globalSettingsRepository,
            IPropertyRepository efPropertyRepository

            )
        {
            _userService = userService;
            _propertyRepo = propertyRepo;
            _masterPropertyAttributeRepo = masterPropertyAttributeRepo;
            _masterAreaUnitRepo = masterAreaUnitRepo;
            _masterPropertyAmenityRepo = masterPropertyAmenityRepo;
            _masterPropertyTypeRepo = masterPropertyTypeRepo;
            _propertyDimensionRepo = propertyDimensionRepo;
            _leadRepositoryAsync = leadRepositoryAsync;
            _dapperRepository = dapperRepository;
            _currentUser = currentUser;
            _globalSettingsRepository = globalSettingsRepository;
            _efPropertyRepository = efPropertyRepository;
        }

        public async Task<PagedResponse<ViewPropertyDto, PropertyCountDto>> Handle(GetAllPropertyRequest request, CancellationToken cancellationToken)
        {
            if (request != null && request.Permission == ViewAssignmentsPermission.None)
            {
                return new PagedResponse<ViewPropertyDto, PropertyCountDto>(null, 0, null);
            }
            CustomMasterAttribute? masterPropertyAttribute = null;
            CustomMasterAmenity? masterPropertyAmenity = null;
            MasterPropertyType? masterPropertyType = null;
            if (!string.IsNullOrWhiteSpace(request.PropertySearch))
            {
                masterPropertyAttribute = (await _masterPropertyAttributeRepo.ListAsync(new CustomMasterPropertyAttributeSpec(request?.PropertySearch?.Trim().ToLower() ?? string.Empty), cancellationToken)).FirstOrDefault();
                masterPropertyAmenity = (await _masterPropertyAmenityRepo.ListAsync(new GetCustomAmenitySpec(request?.PropertySearch?.Trim().ToLower() ?? string.Empty), cancellationToken)).FirstOrDefault();
                masterPropertyType = (await _masterPropertyTypeRepo.ListAsync(new GetMasterPropertyTypeSpec(request?.PropertySearch?.Trim().ToLower() ?? string.Empty), cancellationToken)).FirstOrDefault();
            }
            //var masterPropertyAttributes = (await _masterPropertyAttributeRepo.ListAsync(new GetAllMasterPropertyAttributeSpec(), cancellationToken));
            List<Guid>? propertyDimensionIds = new();
            

            List<int> noOfAttributes = Enumerable.Range(1, 5).ToList();
            NumericAttributesDto numericAttributeDto = InitializationOfNumericAttributes(noOfAttributes, request);
            var tenantId = _currentUser.GetTenant();
            var currentUserId = _currentUser.GetUserId();
            List<Guid>? userIds = new();
            List<Guid>? filterIds = new();
            List<Guid>? teamUserIds = new();
            bool showAllProperties = false;
            //var isAdmin = await _dapperRepository.IsAdminAsync(currentUserId, tenantId ?? string.Empty);

            try
            {
                switch (request.Permission)
                {
                    case ViewAssignmentsPermission.View:
                        if (request.UserIds?.Any() ?? false)
                        {
                            filterIds.AddRange(request.UserIds);
                            if (request.IsWithTeam ?? false)
                            {
                                teamUserIds = (await _dapperRepository.GetSubordinateIdsWithOnlyReporteesAsync(request.UserIds, tenantId ?? string.Empty))?.ToList() ?? new();
                                filterIds.AddRange(teamUserIds);
                            }
                            userIds.AddRange(filterIds);
                        }
                        else
                        {
                            userIds = (await _dapperRepository.GetAllUserIdsAsync(tenantId ?? string.Empty)).ToList();
                            showAllProperties = true;
                        }
                        break;
                    case ViewAssignmentsPermission.ViewAssigned:
                        userIds.Add(currentUserId);
                        break;
                    default:
                        if (request.UserIds?.Any() ?? false)
                        {
                            filterIds.AddRange(request.UserIds);
                            if (request.IsWithTeam ?? false)
                            {
                                teamUserIds = (await _dapperRepository.GetSubordinateIdsWithOnlyReporteesAsync(request.UserIds, tenantId ?? string.Empty))?.ToList() ?? new();
                                filterIds.AddRange(teamUserIds);
                            }
                            userIds.AddRange(filterIds);
                        }
                        else
                        {
                            userIds = (await _dapperRepository.GetAllUserIdsAsync(tenantId ?? string.Empty)).ToList();
                            showAllProperties = true;
                        }
                        break;
                }
            }
            catch (Exception ex)
            {
                var error = new LrbError()
                {
                    ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                    ErrorSource = ex?.Source,
                    StackTrace = ex?.StackTrace,
                    InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                    ErrorModule = "GetAllPropertyRequestHandler -> Handle()"
                };
                await _leadRepositoryAsync.AddErrorAsync(error);
            }
            var residentialPropertyType = (await _masterPropertyTypeRepo.FirstOrDefaultAsync(new GetMasterPropertyTypeByTypeSpec("residential")));
            var agriculturalPropertyType = (await _masterPropertyTypeRepo.FirstOrDefaultAsync(new GetMasterPropertyTypeByTypeSpec("agricultural")));
            var commercialPropertyType = (await _masterPropertyTypeRepo.FirstOrDefaultAsync(new GetMasterPropertyTypeByTypeSpec("commercial")));
            PropertyTypeBaseId propertyTypeIds = new();
            propertyTypeIds.ResidentialBaseId = residentialPropertyType.Id;
            propertyTypeIds.AgricultureBaseId = agriculturalPropertyType.Id;
            propertyTypeIds.CommercialBaseId = commercialPropertyType.Id;
            List<CustomPropertyAttributeDto> attributes = new();
            if (request?.NoOfFloors != null  || request?.NoOfKitchens != null || request?.NoOfUtilites != null || request?.NoOfBedrooms != null || request?.NoOfLivingrooms != null || request?.NoOfBalconies != null || request?.NoOfBathrooms != null || request?.Parking != null)
            {
                attributes = await _dapperRepository.GetAttributeDetails(tenantId ?? string.Empty);
            }
            (IEnumerable<Lrb.Domain.Entities.Property> allProperties, int totalCount,int residentialCount, int agricultureCount, int commercialCount) propertiesWithCount = new();
            propertiesWithCount = await _efPropertyRepository.GetAllPropertiesForWebAsync(request, masterPropertyAttribute?.Id ?? null, masterPropertyAmenity?.Id ?? null, masterPropertyType?.Id ?? null, propertyDimensionIds, numericAttributeDto, userIds, showAllProperties, propertyTypeIds, tenantId,attributes);
            // var properties = await _propertyRepo.ListAsync(new PropertyByCustomFilterSpec(request, masterPropertyAttribute?.Id ?? null, masterPropertyAmenity?.Id ?? null, masterPropertyType?.Id ?? null, propertyDimensionIds, numericAttributeDto, userIds, showAllProperties));
            //var totalCount = await _propertyRepo.CountAsync(new PropertyCountByCustomFilterSpec(request, masterPropertyAttribute?.Id ?? null, masterPropertyAmenity?.Id ?? null, masterPropertyType?.Id ?? null, propertyDimensionIds, numericAttributeDto, userIds, showAllProperties));
            // PropertyCountDto propertyCountDto = await GetPropertyCounts(request, masterPropertyAttribute, masterPropertyAmenity, masterPropertyType, propertyDimensionIds, userIds, showAllProperties);
            PropertyCountDto propertyCountDto = new();
            propertyCountDto.AgriculturalPropertiesCount = propertiesWithCount.agricultureCount;
            propertyCountDto.ResidentialPropertiesCount = propertiesWithCount.residentialCount;
            propertyCountDto.CommercialPropertiesCount = propertiesWithCount.commercialCount;
            propertyCountDto.AllPropertiesCount = propertiesWithCount.totalCount;

            //Adding total count as per the filter chosen
            int totalCount = request.BasePropertyTypeId == commercialPropertyType.Id ? propertiesWithCount.commercialCount :
                              request.BasePropertyTypeId == agriculturalPropertyType.Id ? propertiesWithCount.agricultureCount :
                              request.BasePropertyTypeId == residentialPropertyType.Id ? propertiesWithCount.residentialCount :
                              propertiesWithCount.totalCount;

            List<ViewPropertyDto>? propertyDtos = new List<ViewPropertyDto>();
            try
            {
                if (propertiesWithCount.allProperties?.Any() ?? false)
                {
                    propertyDtos = propertiesWithCount.allProperties.Adapt<List<ViewPropertyDto>>();
                }
                //foreach(var prop in propertyDtos)
                //{
                //    prop.Project = prop?.Projects?.LastOrDefault() ?? null;
                //}
            }
            catch (Exception ex)
            {
                var error = new LrbError()
                {
                    ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                    ErrorSource = ex?.Source,
                    StackTrace = ex?.StackTrace,
                    InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                    ErrorModule = "GetAllPropertyRequestHandler -> Handle()"
                };
                await _leadRepositoryAsync.AddErrorAsync(error);
            }
            #region Dormant
            //List<ViewPropertyDto> resultPropertyDtos = new List<ViewPropertyDto>();

            //foreach (var propertyDto in propertyDtos)
            //{
            //    if (propertyDto != null && (propertyDto.Attributes?.Any() ?? false))
            //    {
            //        //var property = properties.Where(i => i.Id == propertyDto.Id).FirstOrDefault();
            //        //if (property != null && (property.Amenities?.Any() ?? false))
            //        //{
            //        //    propertyDto.Amenities = property.Amenities.Select(i => i.MasterPropertyAmenityId).ToList();
            //        //}
            //        List<PropertyAttributeDto> attributes = new();
            //        foreach (var attribute in propertyDto.Attributes)
            //        {
            //            var masterAttribute = masterPropertyAttributes.Where(i => i.Id == attribute.MasterPropertyAttributeId).FirstOrDefault();
            //            if (masterAttribute != null)
            //            {
            //                attribute.AttributeName = masterAttribute.AttributeName;
            //                attributes.Add(attribute);
            //            }
            //            else
            //            {
            //                attributes.Add(attribute);
            //            }
            //        }
            //        propertyDto.Attributes = attributes;
            //        if (propertyDto != null && propertyDto.Dimension != null && propertyDto.Dimension.AreaUnitId != Guid.Empty)
            //        {
            //          var dimension = (await _masterAreaUnitRepo.ListAsync(new GetMasterAreaUnitByAreaIdSpec(propertyDto.Dimension.AreaUnitId), cancellationToken)).FirstOrDefault();
            //            propertyDto.Dimension.Unit = dimension?.Unit ?? null;
            //        }
            //        resultPropertyDtos.Add(propertyDto);
            //    }
            //    else if (propertyDto != null)
            //    {
            //        if (propertyDto != null && propertyDto.Dimension != null && propertyDto.Dimension.AreaUnitId != Guid.Empty)
            //        {
            //           var dimension = (await _masterAreaUnitRepo.ListAsync(new GetMasterAreaUnitByAreaIdSpec(propertyDto.Dimension.AreaUnitId), cancellationToken)).FirstOrDefault();
            //            propertyDto.Dimension.Unit = dimension?.Unit ?? null;
            //        }
            //        resultPropertyDtos.Add(propertyDto);
            //    }
            //}
            //List<ViewPropertyDto> resultDtos = resultPropertyDtos.Where(i => i.Status != PropertyStatus.Sold).ToList();
            //resultDtos.AddRange(resultPropertyDtos.Where(i => i.Status == PropertyStatus.Sold).ToList());
            //List<ViewPropertyDto> resultDtos = propertyDtos.Where(i => i.Status != PropertyStatus.Sold).ToList();
            //resultDtos.AddRange(propertyDtos.Where(i => i.Status == PropertyStatus.Sold).ToList());
            #endregion

            return new PagedResponse<ViewPropertyDto, PropertyCountDto>(propertyDtos, totalCount, propertyCountDto);
        }
        private NoOfAttributeFilterDto FilterNumericAttributesV1(List<string>? requestValues, List<int> noOfAttributes)
        {
            NoOfAttributeFilterDto noOfAttributesDto = new NoOfAttributeFilterDto();
            var noOfFloorFilterList = new List<int>();
            var selectedAttributes = new List<string>();
            if (requestValues != null)
            {
                if (requestValues.Contains("Ground Floor"))
                {
                    selectedAttributes.Add("Ground Floor");
                }
                if (requestValues.Contains("5"))
                {
                    noOfFloorFilterList = noOfAttributes.Where(i => !requestValues.Contains(i.ToString())).ToList();
                    selectedAttributes.AddRange(noOfFloorFilterList.Select(i => i.ToString()));
                    noOfAttributesDto.IsMaxValueIncluded = true;
                }
                else
                {
                    noOfFloorFilterList = noOfAttributes.Where(i => requestValues.Contains(i.ToString())).ToList();
                    selectedAttributes.AddRange(noOfFloorFilterList.Select(i => i.ToString()));
                    noOfAttributesDto.IsMaxValueIncluded = false;
                }
                noOfAttributesDto.NoOfAttributes = selectedAttributes;
            }

            return noOfAttributesDto;
        }

        private NoOfAttributeFilterDto FilterNumericAttributes(List<int>? requestValues, List<int> noOfAttributes)
        {
            NoOfAttributeFilterDto noOfAttributesDto = new NoOfAttributeFilterDto();
            var noOfFloorFilterList = new List<int>();

            if (requestValues != null)
            {
                if (requestValues.Contains(5))
                {
                    noOfFloorFilterList = noOfAttributes.Where(i => !requestValues.Contains(i)).ToList();
                    noOfAttributesDto.NoOfAttributes = noOfFloorFilterList.Select(i => i.ToString()).ToList();
                    noOfAttributesDto.IsMaxValueIncluded = true;
                }
                else
                {
                    noOfFloorFilterList = noOfAttributes.Where(i => requestValues.Contains(i)).ToList();
                    noOfAttributesDto.NoOfAttributes = noOfFloorFilterList.Select(i => i.ToString()).ToList();
                    noOfAttributesDto.IsMaxValueIncluded = false;
                }
            }

            return noOfAttributesDto;
        }

        private NumericAttributesDto InitializationOfNumericAttributes(List<int> noOfAttributes, GetAllPropertyRequest request)
        {
            return new NumericAttributesDto
            {
                NoOfFloor = FilterNumericAttributes(request.NoOfFloor, noOfAttributes),
                NoOfBathrooms = FilterNumericAttributes(request.NoOfBathrooms, noOfAttributes),
                NoOfBedrooms = FilterNumericAttributes(request.NoOfBedrooms, noOfAttributes),
                NoOfKitchens = FilterNumericAttributes(request.NoOfKitchens, noOfAttributes),
                NoOfUtilites = FilterNumericAttributes(request.NoOfUtilites, noOfAttributes),
                NoOfLivingrooms = FilterNumericAttributes(request.NoOfLivingrooms, noOfAttributes),
                NoOfBalconies = FilterNumericAttributes(request.NoOfBalconies, noOfAttributes),
                NoOfFloors= FilterNumericAttributesV1(request.NoOfFloors, noOfAttributes),
                Parking = FilterNumericAttributes(request.Parking, noOfAttributes),

            };
        }

        private async Task<PropertyCountDto> GetPropertyCounts(GetAllPropertyRequest request,
        MasterPropertyAttribute? masterPropertyAttribute,
        MasterPropertyAmenity? masterPropertyAmenity,
        MasterPropertyType? masterPropertyType,
        List<Guid>? propertyDimensionIds,
        List<Guid>? userIds,
        bool showAllProperties
        )

        {
            List<int> noOfAttributes = Enumerable.Range(1, 5).ToList();
            NumericAttributesDto numericAttributeDto = InitializationOfNumericAttributes(noOfAttributes, request);
            PropertyCountDto propertyCountDto = new PropertyCountDto();
            var basePropertyTypeId = request.BasePropertyTypeId;
            var residentialPropertyType = (await _masterPropertyTypeRepo.ListAsync(new GetMasterPropertyTypeByTypeSpec("residential"))).FirstOrDefault();
            var agriculturalPropertyType = (await _masterPropertyTypeRepo.ListAsync(new GetMasterPropertyTypeByTypeSpec("agricultural"))).FirstOrDefault();
            var commercialPropertyType = (await _masterPropertyTypeRepo.ListAsync(new GetMasterPropertyTypeByTypeSpec("commercial"))).FirstOrDefault();
            if (residentialPropertyType != null)
            {
                request.BasePropertyTypeId = residentialPropertyType.Id;
                propertyCountDto.ResidentialPropertiesCount = await _propertyRepo.CountAsync(new PropertyCountByCustomFilterSpec(request, masterPropertyAttribute?.Id ?? null, masterPropertyAmenity?.Id ?? null, masterPropertyType?.Id ?? null, propertyDimensionIds, numericAttributeDto, userIds, showAllProperties));
            }
            if (agriculturalPropertyType != null)
            {
                request.BasePropertyTypeId = agriculturalPropertyType.Id;
                propertyCountDto.AgriculturalPropertiesCount = await _propertyRepo.CountAsync(new PropertyCountByCustomFilterSpec(request, masterPropertyAttribute?.Id ?? null, masterPropertyAmenity?.Id ?? null, masterPropertyType?.Id ?? null, propertyDimensionIds, numericAttributeDto, userIds, showAllProperties));
            }
            if (commercialPropertyType != null)
            {
                request.BasePropertyTypeId = commercialPropertyType.Id;
                propertyCountDto.CommercialPropertiesCount = await _propertyRepo.CountAsync(new PropertyCountByCustomFilterSpec(request, masterPropertyAttribute?.Id ?? null, masterPropertyAmenity?.Id ?? null, masterPropertyType?.Id ?? null, propertyDimensionIds, numericAttributeDto, userIds, showAllProperties));
            }
            request.BasePropertyTypeId = default;
            propertyCountDto.AllPropertiesCount = await _propertyRepo.CountAsync(new PropertyCountByCustomFilterSpec(request, masterPropertyAttribute?.Id ?? null, masterPropertyAmenity?.Id ?? null, masterPropertyType?.Id ?? null, propertyDimensionIds, numericAttributeDto, userIds, showAllProperties));
            request.BasePropertyTypeId = basePropertyTypeId;
            return propertyCountDto;
        }
    }
    public enum PropertyDateType
    {
        None = 0,
        CreatedDate,
        ModifiedDate,
        PossessionDate
    }
    public static class PropertySearchHelper
    {
        public async static Task<float> GetConversionFactor(Guid areaUnitId, IReadRepository<MasterAreaUnit>? masterAreaUnitRepo)
        {
            if (masterAreaUnitRepo != null)
            {
                var masterAreaUnit = await masterAreaUnitRepo.GetByIdAsync(areaUnitId);
                if (masterAreaUnit != null)
                {
                    return masterAreaUnit.ConversionFactor;
                }
                return default;
            }
            return default;
        }
    }

    public class NumericAttributesDto
    {
        public NoOfAttributeFilterDto? NoOfBathrooms { get; set; }
        public NoOfAttributeFilterDto? NoOfFloor { get; set; }
        public NoOfAttributeFilterDto? NoOfBedrooms { get; set; }
        public NoOfAttributeFilterDto? NoOfKitchens { get; set; }
        public NoOfAttributeFilterDto? NoOfUtilites { get; set; }
        public NoOfAttributeFilterDto? NoOfLivingrooms { get; set; }
        public NoOfAttributeFilterDto? NoOfBalconies { get; set; }
        public NoOfAttributeFilterDto? NoOfFloors { get; set; }
        public NoOfAttributeFilterDto? Parking { get; set; }




    }
    public class NoOfAttributeFilterDto
    {
        public List<string>? NoOfAttributes { get; set; }
        public bool IsMaxValueIncluded { get; set; }
    }
    public class PropertyTypeBaseId
    {
        public Guid? ResidentialBaseId { get; set; }
        public Guid? AgricultureBaseId { get; set; }
        public Guid? CommercialBaseId { get; set; }
    }

}
