﻿using Lrb.Application.Common.Persistence;
using Lrb.Application.Project.Mobile.Specs;
using Lrb.Application.Project.Web.Dtos;

namespace Lrb.Application.Project.Mobile.Requests
{
    public class GetAllProjectsWithIdAndNameRequest : IRequest<Response<List<ProjectDto>>>
    {
    }
    public class GetAllProjectsWithIdAndNameRequestHandler : IRequestHandler<GetAllProjectsWithIdAndNameRequest, Response<List<ProjectDto>>>
    {
        private readonly IDapperRepository _dapperRepository;
        private readonly ICurrentUser _currentUser;
        public GetAllProjectsWithIdAndNameRequestHandler(IDapperRepository dapperRepository, ICurrentUser currentUser)
        {
            _dapperRepository = dapperRepository;
            _currentUser = currentUser;
        }
        public async Task<Response<List<ProjectDto>>> Handle(GetAllProjectsWithIdAndNameRequest request, CancellationToken cancellationToken)
        {
            return new((await _dapperRepository.GetProjectsWithIdsAndNames<ProjectDto>(_currentUser.GetTenant() ?? string.Empty)).ToList());
        }
    }
}
