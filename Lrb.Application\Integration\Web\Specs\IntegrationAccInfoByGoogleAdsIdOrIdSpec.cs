﻿namespace Lrb.Application.Integration.Web.Specs
{
    public class IntegrationAccInfoByGoogleAdsIdOrIdSpec : Specification<IntegrationAccountInfo>
    {
        public IntegrationAccInfoByGoogleAdsIdOrIdSpec(Guid googleId)
        {
            Query
                .Include(i => i.UserAssignment)
                     .ThenInclude(i => i.UserAssignmentConfigurations)
                .Include(i => i.Assignment)
                    .ThenInclude(i => i.Location)
                        .ThenInclude(i => i.UserAssignment)
                .Include(i => i.Assignment)
                    .ThenInclude(i => i.Project)
                        .ThenInclude(i => i.UserAssignment)
                .Where(i => (i.GoogleadLeadFormId == googleId || i.Id == googleId) && !i.IsDeleted);
        }
        public IntegrationAccInfoByGoogleAdsIdOrIdSpec(List<Guid> googleIds)
        {
            Query
                .Include(i => i.UserAssignment)
                     .ThenInclude(i => i.UserAssignmentConfigurations)
                .Include(i => i.Assignment)
                    .ThenInclude(i => i.Location)
                        .ThenInclude(i => i.UserAssignment)
                .Include(i => i.Assignment)
                    .ThenInclude(i => i.Project)
                        .ThenInclude(i => i.UserAssignment)
                .Where(i => (googleIds.Contains(i.Id) || googleIds.Contains(i.GoogleadLeadFormId)) && !i.IsDeleted);
        }
    }
}
