﻿using Lrb.Application.GlobalSettings.Mobile.Dto;
using Newtonsoft.Json;

namespace Lrb.Application.GlobalSettings.Mobile.Requests
{
    public class GetCallSettingsRequest : IRequest<Response<CallSettings>>
    {
        public string? TenantId { get; set; }
    }
    public class GetCallSettingsRequestHandler : IRequestHandler<GetCallSettingsRequest, Response<CallSettings>>
    {
        private readonly IDapperRepository _dapperRepository;

        public GetCallSettingsRequestHandler(IDapperRepository dapperRepository)
        {
            _dapperRepository = dapperRepository;
        }

        public async Task<Response<CallSettings>> Handle(GetCallSettingsRequest request, CancellationToken cancellationToken)
        {
            //var globalSettings = await _globalSettingsRepository.FirstOrDefaultAsync(new GetGlobalSettingsSpec(), cancellationToken);
            try
            {
                var callSettingsdto = await _dapperRepository.GetCallSettings(request.TenantId ?? string.Empty);
                CallSettings callSettings = new();
                if (!string.IsNullOrWhiteSpace(callSettingsdto.CallSettings))
                {
                    callSettings = JsonConvert.DeserializeObject<CallSettings>(callSettingsdto.CallSettings) ?? new();
                    callSettings.ShouldViewOnlyAssigned = callSettingsdto.IsAssignedCallLogsEnabled;
                }
                return new(callSettings);
            }
            catch (Exception ex)
            {
                return new();
            }
        }
    }
}
