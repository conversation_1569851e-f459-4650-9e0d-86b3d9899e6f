﻿<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <UserSecretsId>58cb3940-58e5-46bf-b14d-64a4c5c5fbb5</UserSecretsId>
    <DockerDefaultTargetOS>Linux</DockerDefaultTargetOS>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="AWS.Logger.Core" Version="3.3.3" />
    <PackageReference Include="AWS.Logger.SeriLog" Version="3.4.3" />
    <PackageReference Include="Dapper" Version="2.1.35" />
    <PackageReference Include="Google.Apis.Auth" Version="1.70.0" />
    <PackageReference Include="Microsoft.AspNetCore.SignalR.Client" Version="8.0.5" />
    <PackageReference Include="Microsoft.AspNetCore.SignalR.Client.Core" Version="8.0.5" />
    <PackageReference Include="Microsoft.Azure.Cosmos" Version="3.40.0" />
    <PackageReference Include="Microsoft.VisualStudio.Azure.Containers.Tools.Targets" Version="1.19.6" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="Npgsql" Version="8.0.3" />
    <PackageReference Include="RestSharp" Version="111.0.0" />
    <PackageReference Include="Serilog.AspNetCore" Version="8.0.1" />
    <PackageReference Include="Swashbuckle.AspNetCore" Version="6.4.0" />
  </ItemGroup>

  <ItemGroup>
    <Content Update="appsettings.dev.json">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
    </Content>
    <Content Update="appsettings.prd.json">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
    </Content>
    <Content Update="appsettings.qa.json">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
    </Content>
  </ItemGroup>

  <ItemGroup>
    <Using Include="Lrb.WhatsApp.ChatHub" />
    <Using Include="Lrb.WhatsApp.Constants" />
    <Using Include="Lrb.WhatsApp.DTOs" />
    <Using Include="Lrb.WhatsApp.Entities" />
    <Using Include="Lrb.WhatsApp.Enums" />
    <Using Include="Lrb.WhatsApp.Host" />
    <Using Include="Lrb.WhatsApp.Middleware" />
    <Using Include="Lrb.WhatsApp.Persistance" />
    <Using Include="Lrb.WhatsApp.Services" />
    <Using Include="Microsoft.AspNetCore.SignalR" />
  </ItemGroup>

</Project>
