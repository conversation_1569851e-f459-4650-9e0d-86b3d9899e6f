﻿using Lrb.Application.Common.Facebook;
using Lrb.Application.Common.GoogleAds;
using Lrb.Application.Identity.Users;
using Lrb.Application.Integration.Web.Dtos;
using Lrb.Application.Integration.Web.Specs;
using Lrb.Domain.Entities.Integration;

namespace Lrb.Application.Integration.Web.Requests.Facebook
{
    public class GetAllFbIntegrationAccountsWithLeadGenFormsRequest : PaginationFilter, IRequest<PagedResponse<ViewFacebookAccountWithFormDto, string>>
    {
    }
    public class GetAllFbIntegrationAccountsWithLeadGenFormsRequestHandler : <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, IRequestHandler<GetAllFbIntegrationAccountsWithLeadGenFormsRequest, PagedResponse<ViewFacebookAccountWithFormDto, string>>
    {
        private readonly IRepositoryWithEvents<IntegrationAssignmentInfo> _integrationAssignmentInforepo;
        private readonly IRepositoryWithEvents<Lrb.Domain.Entities.Project> _projectRepo;
        private readonly IUserService _userService;

        public GetAllFbIntegrationAccountsWithLeadGenFormsRequestHandler(
           IRepositoryWithEvents<FacebookAuthResponse> facebookAuthResponseRepo,
           IRepositoryWithEvents<FacebookConnectedPageAccount> facebookConnectedPageAccountRepo,
           IRepositoryWithEvents<FacebookLeadGenForm> facebookLeadGenFormRepo,
           IFacebookService facebookService,
           IJobService hangfireService,
           ITenantIndependentRepository repository,
           ICurrentUser currentUser,
           IRepositoryWithEvents<IntegrationAccountInfo> integrationAccInfoRepo,
           IRepositoryWithEvents<IntegrationAssignmentInfo> integrationAssignmentInforepo,
           IRepositoryWithEvents<FacebookAdsInfo> fbAdsRepo,
           IRepositoryWithEvents<Lrb.Domain.Entities.Project> projectRepo,
           IUserService userService,
           ILeadRepositoryAsync leadRepositoryAsync,
           IGoogleAdsService googleAdsService, IRepositoryWithEvents<GoogleAdsAuthResponse> googleAdsAuthResponseRepo, IRepositoryWithEvents<GoogleAdsInfo> googleAdsRepo, IRepositoryWithEvents<GoogleCampaign> googleCampaignsRepo)
           : base(facebookAuthResponseRepo,
                 facebookConnectedPageAccountRepo,
                 facebookLeadGenFormRepo,
                 facebookService,
                 hangfireService,
                 repository,
                 currentUser,
                 integrationAccInfoRepo,
                 fbAdsRepo,
                 leadRepositoryAsync,
                 googleAdsService, googleAdsAuthResponseRepo, googleAdsRepo, googleCampaignsRepo)
        {
            _integrationAssignmentInforepo = integrationAssignmentInforepo;
            _projectRepo = projectRepo;
            _userService = userService;
        }
        public async Task<PagedResponse<ViewFacebookAccountWithFormDto, string>> Handle(GetAllFbIntegrationAccountsWithLeadGenFormsRequest request, CancellationToken cancellationToken)
        {
            var accounts = await _facebookAuthResponseRepo.ListAsync(new GetAllFacebookIntegrationAccountsSpec(request), cancellationToken);
            var integrationInfo = await _integrationAccInfoRepo.ListAsync(new IntegrationAccInfoByFacebookIdOrIdSpec(accounts.Select(i => i.Id).ToList()));
            var existingFacebookConnectedPageAccounts = await _facebookConnectedPageAccountRepo.ListAsync(new GetFacebookConnectedPageAccountSpec(), cancellationToken);
            var existingPageIds = existingFacebookConnectedPageAccounts.Select(i => i.FacebookId).ToList();
            var count = await _facebookAuthResponseRepo.CountAsync(new CountFacebookIntegrationAccountSpec(), cancellationToken);
            var viewFacebookFormDataDtos = accounts.Adapt<List<ViewFacebookAccountWithFormDto>>();
            var allStoredAds = await _fbAdsRepo.ListAsync(new FacebookAdsByFbAccountIdSpec(viewFacebookFormDataDtos.Select(i => i.Id)?.ToList() ?? new()), cancellationToken);
            viewFacebookFormDataDtos?.ForEach(accDto =>
            {
                var selectedIntegrationInfo = integrationInfo.FirstOrDefault(i => i.FacebookAccountId == accDto.Id);
                accDto.LeadCount = selectedIntegrationInfo?.LeadCount ?? 0;
                accDto.AccountId = selectedIntegrationInfo?.Id ?? Guid.Empty;
                accDto.LeadSource = LeadSource.Facebook;
                accDto.IsAutomated = selectedIntegrationInfo?.IsAutomated ?? false;
                var storedAds = allStoredAds?.Where(i => i.FacebookAuthResponseId == accDto.Id).ToList() ?? new();
                accDto.Ads = storedAds.Adapt<List<FacebookAdsInfoDto>>();
                accDto.Ads = accDto.Ads.OrderBy(i => i.Status).ThenBy(i => !i.IsSubscribed).ThenBy(i => i.LastModifiedOn).ToList();
                var pageidsInAds = accDto.Ads?.Select(i => i.PageId);
                var connectedPages = existingFacebookConnectedPageAccounts?.Where(i => i.FacebookAuthResponseId == accDto.Id).ToList();
                if (connectedPages?.Any() ?? false)
                {
                    var forms = new List<FacebookLeadGenForm>();
                    connectedPages?.ForEach(i => forms.AddRange(i.FBLeadGenForms ?? new List<FacebookLeadGenForm>()));
                    var formDtos = forms?.Adapt<List<FacebookLeadGenFormDto>>();
                    formDtos = formDtos?.OrderBy(i => i.Status).ThenBy(i => !i.IsSubscribed).ThenBy(i => i.LastModifiedOn).ToList();
                    pageidsInAds = pageidsInAds?.Where(i => !string.IsNullOrWhiteSpace(i))?.ToList();
                    if (pageidsInAds != null && pageidsInAds.Any())
                    {
                        formDtos?.ForEach(form =>
                        {
                            form.PageName = existingFacebookConnectedPageAccounts?.FirstOrDefault(j => j.FacebookId == form.PageId)?.Name;
                            if (!pageidsInAds.Contains(form.PageId))
                            {
                                form.Name = $"{form.Name} [EXTERNAL FORM]";
                            }
                        });
                    }
                    else
                    {
                        formDtos?.ForEach(form =>
                        {
                            form.PageName = existingFacebookConnectedPageAccounts?.FirstOrDefault(j => j.FacebookId == form.PageId)?.Name;
                            form.Name = $"{form.Name} [EXTERNAL FORM]";
                        });
                    }
                    accDto.AllForms = formDtos;
                    accDto.ExternalForms = formDtos;
                }
            });
            return new(viewFacebookFormDataDtos ?? new List<ViewFacebookAccountWithFormDto>(), count);
        }

        public static List<FacebookLeadGenFormDto>? AppendTagToFbForms(List<FacebookLeadGenFormDto>? fbForms, List<string?>? pageidsInAds)
        {
            if (fbForms != null)
            {
                pageidsInAds = pageidsInAds?.Where(i => !string.IsNullOrWhiteSpace(i))?.ToList();
                if (pageidsInAds != null && pageidsInAds.Any())
                {
                    var allForms = new List<FacebookLeadGenFormDto>();
                    var externalForms = fbForms.Where(i => !pageidsInAds.Contains(i.PageId)).Select(i => { i.Name = $"{i.Name} [EXTERNAL FORM]"; return i; }).ToList();
                    var inhouseForms = fbForms.Where(i => pageidsInAds.Contains(i.PageId)).ToList();
                    //   externalForms.ForEach(i => i.Name = $"{i.Name} [EXTERNAL FORM]");
                    allForms.AddRange(externalForms);
                    allForms.AddRange(inhouseForms);
                    return allForms;
                }
                else
                {
                    fbForms.ForEach(i => i.Name = $"{i.Name} [EXTERNAL FORM]");
                    return fbForms;
                }
            }
            return fbForms;
        }
    }

}
