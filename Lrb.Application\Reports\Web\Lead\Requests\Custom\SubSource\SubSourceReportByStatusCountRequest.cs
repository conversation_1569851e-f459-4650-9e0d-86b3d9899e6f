﻿using Lrb.Application.Utils;
using Lrb.Shared.Extensions;

namespace Lrb.Application.Reports.Web
{
    public class SubSourceReportByStatusCountRequest : IRequest<int>
    {
        public string? SearchText { get; set; }
        public List<LeadSource>? Sources { get; set; } = new();
        public DateTime? FromDate { get; set; }
        public DateTime? ToDate { get; set; }
        public DateType? DateType { get; set; }
        public bool IsWithTeam { get; set; }
        public List<string>? Projects { get; set; }
        public List<Guid>? UserIds { get; set; }
        public List<string>? AgencyNames { get; set; }
        public List<string>? SubSources { get; set; }
        public ReportPermission? ReportPermission { get; set; }
        public List<string>? Localites { get; set; }
        public List<string>? States { get; set; }
        public List<string>? Cities { get; set; }
        public int PageNumber { get; set; }
        public int PageSize { get; set; } = int.MaxValue;
        public DateTime? FromDateForSubSource { get; set; }
        public DateTime? ToDateForSubSource { get; set; }
        public List<string>? Countries { get; set; } = new();
    }
    public class SubSourceReportByStatusCountRequestHandler : IRequestHandler<SubSourceReportByStatusCountRequest, int>
    {
        private readonly IDapperRepository _dapperRepository;
        private readonly ICurrentUser _currentUser;

        public SubSourceReportByStatusCountRequestHandler(IDapperRepository dapperRepository, ICurrentUser currentUser)
        {
            _dapperRepository = dapperRepository;
            _currentUser = currentUser;
        }

        public async Task<int> Handle(SubSourceReportByStatusCountRequest request, CancellationToken cancellationToken)
        {
            var tenantId = _currentUser.GetTenant();
            var userId = _currentUser.GetUserId();
            List<Guid> teamUserIds = new();
            List<Guid> permittedUserIds = new();
            request.FromDate = request.FromDate.HasValue ? request.FromDate.Value.ConvertFromDateToUtc() : null;
            request.ToDate = request.ToDate.HasValue ? request.ToDate.Value.ConvertToDateToUtc() : null;
            var totalCount = (await _dapperRepository.QueryStoredProcedureCountFromReadReplicaAsync("LeadratBlack", "Lead_SubSourceReportByStatusCount", new
            {
                fromdate = request.FromDate,
                todate = request.ToDate,
                datetype = request.DateType,
                sources = request?.Sources?.ConvertAll(i => (int)i),
                userids = teamUserIds,
                projects = request?.Projects?.ConvertAll<string>(i => i.Replace(" ", "").ToLower()),
                tenantid = tenantId,
                searchtext = string.IsNullOrEmpty(request.SearchText) ? null : request.SearchText.Replace(" ", "").ToLower(),
                subsources = request?.SubSources?.ConvertAll<string>(i => i.Replace(" ", "").ToLower()),
                agencynames = request?.AgencyNames?.ConvertAll<string>(i => i.Replace(" ", "").ToLower()),
                localites = request?.Localites?.ConvertAll<string>(i => i.LrbNormalize()),
                cities = request?.Cities?.ConvertAll<string>(i => i.LrbNormalize()),
                states = request?.States?.ConvertAll<string>(i => i.LrbNormalize()),
                fromdateformeetingorvisit = request.FromDateForSubSource,
                todateformeetingorvisit = request.ToDateForSubSource,
                countries = request?.Countries?.ConvertAll<string>(i => i.Replace(" ", "").ToLower())
            }));
            return totalCount;
        }
    }
}