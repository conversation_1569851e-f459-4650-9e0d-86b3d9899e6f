﻿using Lrb.Application.Common.Facebook;
using Lrb.Application.Common.GoogleAds;
using Lrb.Domain.Entities.Integration;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Lrb.Application.Integration.Web.Requests.Facebook
{
    public class GetAccountSubscriptionStatusRequest : IRequest<Response<bool>>
    {
        public Guid AccountId { get; set; }
    }
    public class GetAccountSubscriptionStatusRequestHandler : <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, IRequestHandler<GetAccountSubscriptionStatusRequest, Response<bool>>
    {
        public GetAccountSubscriptionStatusRequestHandler(
           IRepositoryWithEvents<FacebookAuthResponse> facebookAuthResponseRepo,
           IRepositoryWithEvents<FacebookConnectedPageAccount> facebookConnectedPageAccountRepo,
           IRepositoryWithEvents<FacebookLeadGenForm> facebookLeadGenFormRepo,
           IFacebookService facebookService,
           IJobService hangfireService,
           ITenantIndependentRepository repository,
           ICurrentUser currentUser,
           IRepositoryWithEvents<IntegrationAccountInfo> integrationAccInfoRepo,
           IRepositoryWithEvents<FacebookAdsInfo> fbAdsRepo, ILeadRepositoryAsync leadRepositoryAsync,
           IGoogleAdsService googleAdsService,
           IRepositoryWithEvents<GoogleAdsAuthResponse> googleAdsAuthResponseRepo,
           IRepositoryWithEvents<GoogleAdsInfo> googleAdsRepo,
           IRepositoryWithEvents<GoogleCampaign> googleCampaignsRepo)
           : base(facebookAuthResponseRepo,
                 facebookConnectedPageAccountRepo,
                 facebookLeadGenFormRepo,
                 facebookService,
                 hangfireService,
                 repository,
                 currentUser,
                 integrationAccInfoRepo,
                 fbAdsRepo, leadRepositoryAsync,
                 googleAdsService,
                 googleAdsAuthResponseRepo, googleAdsRepo, googleCampaignsRepo)
        { }
        public async Task<Response<bool>> Handle(GetAccountSubscriptionStatusRequest request, CancellationToken cancellationToken)
        {

            var accounts = await _facebookAuthResponseRepo.ListAsync(new GetAllFacebookAccountsSpec(request.AccountId), cancellationToken);
            var existingFacebookConnectedPageAccounts = await _facebookConnectedPageAccountRepo.ListAsync(new GetFacebookConnectedPageAccountSpecV1(request.AccountId), cancellationToken);
            var allStoredAds = await _fbAdsRepo.ListAsync(new FacebookAdsByFbAccountIdSpec(request.AccountId), cancellationToken);
            List<Guid>? adIds = allStoredAds.Select(i => i.Id).ToList();
            var formDtos = existingFacebookConnectedPageAccounts.Where(i => i.FacebookAuthResponseId == request.AccountId).SelectMany(i => i.FBLeadGenForms ?? new List<FacebookLeadGenForm>()).ToList();
            List<Guid>? formIds = formDtos?.Select(i => i.Id).ToList();

            bool isSubscribed = true;

            if (formIds?.Any() ?? false)
            {
                var forms = await _facebookLeadGenFormRepo.ListAsync(new FacebookLeadGenFormByIdSpec(formIds));
                isSubscribed = forms.All(i => i.IsSubscribed);
            }
            if (adIds?.Any() ?? false)
            {
                var ads = await _fbAdsRepo.ListAsync(new FacebookAdsByIdsSpec(adIds));
                isSubscribed = isSubscribed && ads.All(i => i.IsSubscribed);
            }

            return new Response<bool>(isSubscribed);
        }
    }
}