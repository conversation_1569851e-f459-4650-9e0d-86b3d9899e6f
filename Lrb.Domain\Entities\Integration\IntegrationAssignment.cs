﻿namespace Lrb.Domain.Entities.Integration
{
    public class IntegrationAssignment : AuditableEntity, IAggregateRoot
    {
        public Lrb.Domain.Entities.Project? Project { get; set; }
        public Location? Location { get; set; }
        public Property? Property { get; set; }
        public Campaign? Campaign { get; set; }
        public Agency? Agency { get; set; }
        public ChannelPartner? ChannelPartner { get; set; }


    }
}
