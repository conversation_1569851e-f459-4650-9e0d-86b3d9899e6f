﻿using Lrb.Application.Automation.Dtos;
using Lrb.Application.Automation.Helpers;
using Lrb.Application.Automation.Specs;
using Lrb.Application.Common.Atomation;
using Lrb.Application.Common.Interfaces;
using Lrb.Application.Common.Persistence;
using Lrb.Application.Identity.Users;
using Lrb.Domain.Entities;
using Lrb.Domain.Entities.Automation;
using Mapster;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Lrb.Infrastructure.Atomation
{
    public class UserAssignmentMetricsHandler : IUserAssignmentMetricsService
    {
        private readonly IRepositoryWithEvents<UserAssignmentMetrics> _userAssignmentMetricsRepo;
        private readonly IRepositoryWithEvents<UserAssignmentHistory> _userAssignmentHistoryRepo;
        private readonly IUserService _userService;
        private readonly ICurrentUser _currentUser;
        private readonly IRepositoryWithEvents<UserAssignment> _userAssignmentRepo;
        public UserAssignmentMetricsHandler(
            IRepositoryWithEvents<UserAssignmentMetrics> userAssignmentMetricsRepo,
            IRepositoryWithEvents<UserAssignmentHistory> userAssignmentHistoryRepo,
            IUserService userService,
            ICurrentUser currentUser,
            IRepositoryWithEvents<UserAssignment> userAssignmentRepo)
        {
            _userAssignmentHistoryRepo = userAssignmentHistoryRepo;
            _userAssignmentMetricsRepo = userAssignmentMetricsRepo;
            _userService = userService;
            _currentUser = currentUser;
            _userAssignmentRepo = userAssignmentRepo;
        }

        public async Task<List<UserAssignmentMetrics>> GetUserAssignmentMetrics(List<UserAssignmentConfigDto> userAssignmentConfigs, AccountInfoDto accountInfo)
        {
            try
            {
                var currentUser = _currentUser.GetUserId();
                var existingUserAssignmentMetrics = await _userAssignmentMetricsRepo.ListAsync(new GetUserAssignmentMetricsSpec(accountInfo.UserAssignmentId ?? Guid.Empty));
                List<UserAssignmentMetricsDto> userAssignmentMetricsDtos = new List<UserAssignmentMetricsDto>();
                var userIds = userAssignmentConfigs.Select(i => i.UserId ?? Guid.Empty).ToList();
                var existingUserIds = existingUserAssignmentMetrics.Select(i => i.UserId).ToList() ?? new();
                if (existingUserIds?.Any() ?? false)
                {
                    userIds.Add(currentUser);
                    userIds.AddRange(existingUserIds);
                    userIds = userIds = userIds.Where(i => i != Guid.Empty).Distinct().ToList();
                }
                else if(userIds?.Any() ?? false)
                {
                    userIds.Add(currentUser);
                    userIds = userIds = userIds.Where(i => i != Guid.Empty).Distinct().ToList();
                }
                var userDetails = await _userService.GetUserBasicDetailsAsync(userIds, CancellationToken.None);
                var selectedUserIds = userDetails?.Where(i => i.IsActive && i.IsAutomationEnabled).Select(i => i.UserId).ToList() ?? new List<Guid>(); ;
                existingUserAssignmentMetrics = existingUserAssignmentMetrics.Where(i => selectedUserIds.Contains(i.UserId)).ToList();
                if (existingUserAssignmentMetrics?.Any() ?? false)
                {
                    existingUserAssignmentMetrics.ForEach(i =>
                    {
                        i.Percentage = null; i.DeficitValue = null; i.TotalLeadsReceived = null;
                        var userAssignmentMetricsDto = SetUserAssignmentMetricsView(i, userDetails?.FirstOrDefault(user => user.UserId == i.UserId) ?? new());
                        userAssignmentMetricsDtos.Add(userAssignmentMetricsDto);
                    });
                }
                var newUserAssignmentMetrics = new List<UserAssignmentMetrics>();
                existingUserAssignmentMetrics ??= new List<UserAssignmentMetrics>();
                userAssignmentConfigs.ForEach(userAssignment =>
                {
                    var existingUser = existingUserAssignmentMetrics.FirstOrDefault(i => i.UserId == userAssignment.UserId);
                    if (existingUser != null)
                    {
                        existingUser.Percentage = userAssignment.Percentage;
                        var userAssignmentMetricsDto = SetUserAssignmentMetricsView(existingUser, userDetails?.FirstOrDefault(user => user.UserId == existingUser.UserId) ?? new());
                        userAssignmentMetricsDtos.Add(userAssignmentMetricsDto);
                    }
                    else
                    {
                        var newUserAssignmentMetric = new UserAssignmentMetrics()
                        {
                            Percentage = userAssignment.Percentage,
                            UserAssignmentId = accountInfo.UserAssignmentId ?? Guid.Empty,
                            UserId = userAssignment.UserId ?? Guid.Empty,
                        };
                        newUserAssignmentMetrics.Add(newUserAssignmentMetric);
                        var userAssignmentMetricsDto = SetUserAssignmentMetricsView(newUserAssignmentMetric, userDetails?.FirstOrDefault(user => user.UserId == newUserAssignmentMetric.UserId) ?? new());
                        userAssignmentMetricsDtos.Add(userAssignmentMetricsDto);
                    }
                });
                if (existingUserAssignmentMetrics?.Any() ?? false)
                {
                    newUserAssignmentMetrics.AddRange(existingUserAssignmentMetrics);
                }
                var currentUserDetail = userDetails?.FirstOrDefault(i => i.UserId == currentUser);
                var existingUserAssignmentHistory = await _userAssignmentHistoryRepo.FirstOrDefaultAsync(new GetUserAssignmentHistoryByIdSpec(accountInfo.Id), CancellationToken.None);
                var newUserAssignmentHistory = UserAssignmentHistoryHelper.UserAssignmentHistoryMapper(accountInfo, currentUserDetail, userAssignmentMetricsDtos);
                if (existingUserAssignmentHistory != null)
                {
                    existingUserAssignmentHistory = UserAssignmentHistoryHelper.GetUpdatedUserAssignmentHistory(existingUserAssignmentHistory, newUserAssignmentHistory, currentUserDetail.UserId);
                    await _userAssignmentHistoryRepo.UpdateAsync(existingUserAssignmentHistory);
                }
                else
                {
                    await _userAssignmentHistoryRepo.AddAsync(newUserAssignmentHistory);

                }
                return newUserAssignmentMetrics;
            }
            catch (Exception ex)
            {
                return new();
            }
        }
        public async Task<Guid?> DetermineUserAndSaveInfoAsync(AccountInfoDto accountInfo)
        {
            try
            {
                var currentUser = _currentUser.GetUserId();
                long minLeadsCount = int.MaxValue;
                double? minDeficitValue = double.MinValue;
                var existingUserAssignmentMetrics = await _userAssignmentMetricsRepo.ListAsync(new GetUserAssignmentMetricsSpec(accountInfo.UserAssignmentId ?? Guid.Empty));
                var userToBeAssign = Guid.Empty;
                if (existingUserAssignmentMetrics != null)
                {
                    List<UserAssignmentMetricsDto> userAssignmentMetricsDtos = new List<UserAssignmentMetricsDto>();
                    var userIds = existingUserAssignmentMetrics.Select(i => i.UserId).ToList();
                    if (userIds.Any())
                    {
                        userIds.Add(currentUser);
                    }
                    var userDetails = (await _userService.GetUserBasicDetailsAsync(userIds, CancellationToken.None));
                    var selectedUserIds = userDetails?.Select(i => i.UserId).ToList() ?? new List<Guid>();
                    var notActiveUsers = existingUserAssignmentMetrics.Where(i => !selectedUserIds.Contains(i.UserId)).ToList();
                    double unallocatedPercentage = notActiveUsers?.Sum(i => i.Percentage ?? 0) ?? 0;

                    int baseAdd = (int)(unallocatedPercentage / selectedUserIds.Count);
                    int remainder = (int)(unallocatedPercentage % selectedUserIds.Count);
                    int count = 0;
                    existingUserAssignmentMetrics.ForEach(i =>
                    {
                        if (!notActiveUsers?.Any(j => j.UserId == i.UserId) ?? false)
                        {
                            int extra = (count < remainder) ? 1 : 0;
                            var existingPercentage = i.Percentage ?? 0;
                            i.Percentage = existingPercentage + baseAdd + extra;
                            var metricsInfo = GetDeficitValue(i, minDeficitValue, accountInfo.TotalLeadsCount);
                            i.Percentage = existingPercentage;
                            if (metricsInfo.Item2)
                            {
                                if (minLeadsCount > i.TotalLeadsReceived)
                                {
                                    userToBeAssign = i.UserId;
                                }
                            }
                            else if (minDeficitValue < metricsInfo.Item1)
                            {
                                userToBeAssign = i.UserId;
                                minDeficitValue = metricsInfo.Item1;
                                minLeadsCount = i.TotalLeadsReceived ?? 0;
                            }
                            i.DeficitValue = metricsInfo.Item1;
                            count++;
                        }
                        //else
                        //{
                        //    i.Percentage = 0;
                        //}
                        
                    });
                    double? previousDeficitValue = int.MinValue;
                    double? tieBrakerDeficit = int.MinValue;
                    UserAssignmentMetricsDto? previousUserConfig = new();
                    existingUserAssignmentMetrics.ForEach(i =>
                    {
                        if (i.UserId == userToBeAssign)
                        {
                            i.TotalLeadsReceived = (i.TotalLeadsReceived ?? 0) + 1;
                        }
                        var userAssignmentMetricsDto = SetUserAssignmentMetricsView(i, userDetails?.FirstOrDefault(user => user.UserId == i.UserId) ?? new());
                        if (previousDeficitValue == userAssignmentMetricsDto.DeficitValue)
                        {
                            userAssignmentMetricsDto.IsTieBreaker = true;
                            previousUserConfig.IsTieBreaker = true;
                        }
                        previousDeficitValue = userAssignmentMetricsDto.DeficitValue;
                        previousUserConfig = userAssignmentMetricsDto;
                        userAssignmentMetricsDtos.Add(userAssignmentMetricsDto);
                    });
                    await _userAssignmentMetricsRepo.UpdateRangeAsync(existingUserAssignmentMetrics);
                    var currentUserDetail = userDetails?.FirstOrDefault(i => i.UserId == currentUser);
                    var existingUserAssignmentHistory = await _userAssignmentHistoryRepo.FirstOrDefaultAsync(new GetUserAssignmentHistoryByIdSpec(accountInfo.Id), CancellationToken.None);
                    var newUserAssignmentHistory = UserAssignmentHistoryHelper.UserAssignmentHistoryMapper(accountInfo, currentUserDetail, userAssignmentMetricsDtos);
                    if (existingUserAssignmentHistory != null)
                    {
                        existingUserAssignmentHistory = UserAssignmentHistoryHelper.GetUpdatedUserAssignmentHistory(existingUserAssignmentHistory, newUserAssignmentHistory, currentUserDetail?.UserId ?? Guid.Empty);
                        await _userAssignmentHistoryRepo.UpdateAsync(existingUserAssignmentHistory);
                    }
                    return userToBeAssign;
                }
                return userToBeAssign == Guid.Empty ? null : userToBeAssign;
            }
            catch (Exception ex)
            {
                return Guid.Empty;
            }
        }
        public (double?, bool) GetDeficitValue(UserAssignmentMetrics userMetric, double? deficitValue,long? totalLeadsCount)
        {
            var expectedLeads = (userMetric.Percentage / 100) * totalLeadsCount;
            var newDeficit = expectedLeads - (userMetric.TotalLeadsReceived ?? 0 );
            deficitValue = deficitValue.HasValue ? Math.Round(deficitValue.Value, 2) : (double?)null;
            newDeficit = newDeficit.HasValue ? Math.Round(newDeficit.Value, 2) : (double?)null;
            if (newDeficit != null && deficitValue != null && newDeficit == deficitValue)
            {
                return (newDeficit, true);
            }
            return (newDeficit, false);
        }
        public UserAssignmentMetricsDto SetUserAssignmentMetricsView(UserAssignmentMetrics userAssignmentsMetrics, Lrb.Application.UserDetails.Web.UserDetailsDto userDetail)
        {
            var userAssignmentsMetricsDto = userAssignmentsMetrics.Adapt<UserAssignmentMetricsDto>();
            userAssignmentsMetricsDto.UserName = userDetail.FirstName + " " + userDetail.LastName;
            return userAssignmentsMetricsDto;
        }
    }
}
