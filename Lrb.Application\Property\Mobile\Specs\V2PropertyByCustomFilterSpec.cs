﻿using Lrb.Application.Property.Mobile.Requests;
using Lrb.Application.Property.Web;
using Lrb.Application.Utils;
using Lrb.Domain.Entities.MasterData;
using System;
using System.Text.RegularExpressions;

namespace Lrb.Application.Property.Mobile.Specs
{
    public class V2PropertyByCustomFilterSpec : EntitiesByPaginationFilterSpec<Domain.Entities.Property>
    {
        public V2PropertyByCustomFilterSpec(
           V2GetAllPropertyRequest filter, Guid? masterPropertyAttributeId, Guid? masterPropertyAmenityId, Guid? masterPropertyTypeId, List<Guid>? propertyDimensionIds, NumericAttributesDto1 numericAttributesDto, List<CustomPropertyAttributeDto>? attributes = null
 ) : base(filter)
        {
            Query
            .Include(i => i.Address)
            .Include(i => i.MonetaryInfo)
            .Include(i => i.PropertyType)
            .Include(i => i.PropertyOwnerDetails)
            .Include(i => i.Dimension)
            .Include(i => i.TagInfo)
            .Include(i => i.Attributes)
            .Include(i => i.Amenities)
            .Include(i => i.Galleries.Where(j => !j.IsDeleted))
            .Include(i => i.Project)
            .OrderByDescending(i => i.LastModifiedOn)
            .Where(i => !i.IsDeleted && !i.IsArchived);
            if (filter.EnquiredFor != null)
            {
                Query.Where(i => i.EnquiredFor == filter.EnquiredFor);
            }
            if (filter.PropertyStatus != null)
            {
                Query.Where(i => i.Status == filter.PropertyStatus);
            }
            if (filter.Locations?.Any() ?? false)
            {

                filter.Locations = filter.Locations.Select(i => i.ToLower()).ToList();
                Query.Where(i => i.Address != null &&
                   (
                       (i.Address.SubLocality != null && i.Address.Locality != null &&
                        filter.Locations.Contains(i.Address.SubLocality.ToLower() + ", " + i.Address.Locality.ToLower())) ||
                       (i.Address.SubLocality == null && i.Address.Locality != null &&
                        filter.Locations.Contains(", " + i.Address.Locality.ToLower())) ||
                       (i.Address.SubLocality != null && i.Address.Locality == null &&
                        filter.Locations.Contains(i.Address.SubLocality.ToLower() + ", "))
                   )
               );

            }
            if (filter.Cities?.Any() ?? false)
            {
                Query.Where(i => i.Address != null && i.Address.City != null && filter.Cities.Select(i => i.ToLower()).Contains(i.Address.City.ToLower()));
            }
            if (filter.States?.Any() ?? false)
            {
                Query.Where(i => i.Address != null && i.Address.State != null && filter.States.Select(i => i.ToLower()).Contains(i.Address.State.ToLower()));
            }
            if (filter.Countries?.Any() ?? false)
            {
                Query.Where(i => i.Address != null && i.Address.Country != null && filter.Countries.Select(i => i.ToLower()).Contains(i.Address.Country.ToLower()));
            }
            if (filter?.BHKTypes?.Any() ?? false)
            {
                Query.Where(i => filter.BHKTypes.Contains(i.BHKType));
            }
            if (filter?.FurnishStatuses?.Any() ?? false)
            {
                Query.Where(i => filter.FurnishStatuses.Contains(i.FurnishStatus));
            }

            if (filter.Facing != null)
            {
                Query.Where(i => i.Facing == filter.Facing);

            }
            if (!string.IsNullOrEmpty(filter.PropertyTitle))
            {
                string propertyTitle = filter.PropertyTitle.ToLower().Replace(" ", "");
                Query.Where(i => i.Title.Replace(" ", "").ToLower() == propertyTitle);
            }
            if (!string.IsNullOrEmpty(filter.OwnerName))
            {
                string ownerName = filter.OwnerName.ToLower().Replace(" ", "");
                Query.Where(i => i.OwnerDetails.Name.Replace(" ", "").ToLower() == ownerName);
            }

            if (filter?.OwnerNames?.Any() ?? false)
            {
                filter.OwnerNames = filter.OwnerNames.ConvertAll(i => i.ToLower().Trim().Replace(" ", ""));
                Query.Where(e => e.PropertyOwnerDetails.Any(i => filter.OwnerNames.Contains(i.Name.ToLower().Trim().Replace(" ", "") ?? string.Empty)));
            }

            //if (filter?.Projects?.Any() ?? false)
            //{
            //    var projectNames = filter.Projects.ConvertAll(i => i.ToLower());
            //    Query.Where(i => i.Projects != null && i.Projects.Any(i => projectNames.Contains(i.Name.ToLower())));
            //}
            if (filter?.Projects?.Any() ?? false)
            {
                var projectNames = filter.Projects.ConvertAll(i => i.ToLower());
                Query.Where(i => i.Project != null && projectNames.Contains(i.Project.Name.ToLower()));
            }
            if (filter.NoOfBHK != default)
            {
                Query.Where(i => i.NoOfBHKs == filter.NoOfBHK);
            }
            if (filter.Ratings != default)
            {
                Query.Where(i => i.Rating == filter.Ratings);
            }
            if (filter.FloorNumber != default)
            {
                Query.Where(i => i.Attributes != null && i.Attributes.Any(i => i.MasterPropertyAttributeId == Guid.Parse("c146a273-e342-4d66-aeee-6b2e6106d225") && i.Value == filter.FloorNumber.ToString()));

            }
            if (filter.BasePropertyTypeId != default)
            {
                Query.Where(i => i.PropertyType != null && i.PropertyType.BaseId == filter.BasePropertyTypeId);
            }
            if (filter.PropertyTypes?.Any() ?? false)
            {
                Query.Where(i => i.PropertyType != null && i.PropertyType.BaseId != null && filter.PropertyTypes.Contains(i.PropertyType.BaseId ?? default));
            }
            if (filter.PropertySubTypes?.Any() ?? false)
            {
                Query.Where(i => i.PropertyType != null && filter.PropertySubTypes.Contains(i.PropertyType.Id));
            }
            if (filter.SubPropertyTypeIds?.Any() ?? false)
            {
                Query.Where(i => i.PropertyType != null && filter.SubPropertyTypeIds.Contains(i.PropertyType.Id));
            }
            if (filter.MinBudget != null && filter.MaxBudget != null)
            {
                Query.Where(i => i.MonetaryInfo != null && i.MonetaryInfo.ExpectedPrice >= filter.MinBudget && i.MonetaryInfo.ExpectedPrice <= filter.MaxBudget);
            }
            else if (filter.MinBudget != null && filter.MaxBudget == null)
            {
                Query.Where(i => i.MonetaryInfo != null && i.MonetaryInfo.ExpectedPrice >= filter.MinBudget);
            }
            else if (filter.MinBudget == null && filter.MaxBudget != null)
            {
                Query.Where(i => i.MonetaryInfo != null && i.MonetaryInfo.ExpectedPrice <= filter.MaxBudget);
            }

            /* if (filter.PriceRange != null && filter.PriceRange.Any())
             {
                 foreach (var item in filter.PriceRange)
                 {
                     switch (item)
                     {
                         case PropertyPriceFilter.UptoTenLakhs:
                             Query.Where(i => i.MonetaryInfo != null && i.MonetaryInfo.ExpectedPrice >= 0 && i.MonetaryInfo.ExpectedPrice < 1000000);
                             break;
                         case PropertyPriceFilter.TenToTwentyLakhs:
                             Query.Where(i => i.MonetaryInfo != null && i.MonetaryInfo.ExpectedPrice >= 1000000 && i.MonetaryInfo.ExpectedPrice < 2000000);
                             break;
                         case PropertyPriceFilter.TwentyToThirtyLakhs:
                             Query.Where(i => i.MonetaryInfo != null && i.MonetaryInfo.ExpectedPrice >= 2000000 && i.MonetaryInfo.ExpectedPrice < 3000000);
                             break;
                         case PropertyPriceFilter.ThirtyToFortyLakhs:
                             Query.Where(i => i.MonetaryInfo != null && i.MonetaryInfo.ExpectedPrice >= 3000000 && i.MonetaryInfo.ExpectedPrice < 4000000);
                             break;
                         case PropertyPriceFilter.FortyToFiftyLakhs:
                             Query.Where(i => i.MonetaryInfo != null && i.MonetaryInfo.ExpectedPrice >= 4000000 && i.MonetaryInfo.ExpectedPrice < 5000000);
                             break;
                         case PropertyPriceFilter.FiftyLakhsToOneCrore:
                             Query.Where(i => i.MonetaryInfo != null && i.MonetaryInfo.ExpectedPrice >= 5000000 && i.MonetaryInfo.ExpectedPrice < 10000000);
                             break;
                         case PropertyPriceFilter.AboveOneCrore:
                             Query.Where(i => i.MonetaryInfo != null && i.MonetaryInfo.ExpectedPrice >= 10000000);
                             break;
                         default:
                             break;
                     }

                 }
             }*/
            if (filter.FromPossessionDate != default || filter.ToPossessionDate != default)
            {

                if (filter.FromPossessionDate != null && filter.ToPossessionDate != null)
                {
                    Query.Where(i => i.PossessionDate != null && i.PossessionDate.Value >= filter.FromPossessionDate.Value && i.PossessionDate.Value < filter.ToPossessionDate.Value);
                }
                else if (filter.FromPossessionDate != null && filter.ToPossessionDate == null)
                {
                    Query.Where(i => i.PossessionDate != null && i.PossessionDate.Value >= filter.FromPossessionDate.Value);
                }
                else if (filter.FromPossessionDate == null && filter.ToPossessionDate != null)
                {
                    Query.Where(i => i.PossessionDate != null && i.PossessionDate.Value <= filter.ToPossessionDate.Value);
                }
            }

            if (filter.MinPrice != null && filter.MaxPrice != null)
            {
                Query.Where(i => i.MonetaryInfo != null && i.MonetaryInfo.ExpectedPrice >= filter.MinPrice && i.MonetaryInfo.ExpectedPrice <= filter.MaxPrice);
            }
            else if (filter.MinPrice != null && filter.MaxPrice == null)
            {
                Query.Where(i => i.MonetaryInfo != null && i.MonetaryInfo.ExpectedPrice >= filter.MinPrice);
            }
            else if (filter.MinPrice == null && filter.MaxPrice != null)
            {
                Query.Where(i => i.MonetaryInfo != null && i.MonetaryInfo.ExpectedPrice <= filter.MaxPrice);
            }
            if (filter.Amenities != null && filter.Amenities.Any())
            {
                Query.Where(i => i.Amenities.Any(a => filter.Amenities.Contains(a.MasterPropertyAmenityId)));
            }
            if (filter.PropertySize != null && filter.PropertySize != default)
            {
                if (filter.PropertySize.Area != default && filter.PropertySize.ConversionFactor != default)
                {
                    Query.Where(i => propertyDimensionIds.Any() && propertyDimensionIds.Contains(i.Dimension.Id));
                }
                else if (filter.PropertySize.Area != default && filter.PropertySize.ConversionFactor == default)
                {
                    Query.Where(i => i.Dimension.Area == filter.PropertySize.Area || i.Dimension.AreaInSqMtr == filter.PropertySize.Area);
                }
            }
            if (filter.DateType.HasValue && (filter.FromDate != default || filter.ToDate != default))
            {
                filter.FromDate = filter.FromDate.HasValue ? filter.FromDate.Value.ConvertFromDateToUtc() : null;
                filter.ToDate = filter.ToDate.HasValue ? filter.ToDate.Value.ConvertToDateToUtc() : null;

                switch (filter.DateType)
                {
                    case (Requests.PropertyDateType?)PropertyDateType.CreatedDate:
                        if (filter.FromDate == null && filter.ToDate != null)
                        {
                            Query.Where(i => i.CreatedOn <= filter.ToDate.Value);
                        }
                        else if (filter.FromDate != null && filter.ToDate == null)
                        {
                            Query.Where(i => i.CreatedOn >= filter.FromDate.Value);
                        }
                        else if (filter.FromDate != null && filter.ToDate != null)
                        {
                            Query.Where(i => i.CreatedOn >= filter.FromDate.Value && i.CreatedOn <= filter.ToDate.Value);
                        }
                        break;
                    case (Requests.PropertyDateType?)PropertyDateType.ModifiedDate:
                        if (filter.FromDate == null && filter.ToDate != null)
                        {
                            Query.Where(i => i.LastModifiedOn != null && i.LastModifiedOn.Value <= filter.ToDate.Value);
                        }
                        else if (filter.FromDate != null && filter.ToDate == null)
                        {
                            Query.Where(i => i.LastModifiedOn != null && i.LastModifiedOn.Value >= filter.FromDate.Value);
                        }
                        else if (filter.FromDate != null && filter.ToDate != null)
                        {
                            Query.Where(i => i.LastModifiedOn != null && i.LastModifiedOn.Value >= filter.FromDate.Value && i.LastModifiedOn.Value <= filter.ToDate.Value);
                        }
                        break;
                    case (Requests.PropertyDateType?)PropertyDateType.PossessionDate:
                        if (filter.FromDate == null && filter.ToDate != null)
                        {
                            Query.Where(i => i.PossessionDate != null && i.PossessionDate.Value <= filter.ToDate.Value);
                        }
                        else if (filter.FromDate != null && filter.ToDate == null)
                        {
                            Query.Where(i => i.PossessionDate != null && i.PossessionDate.Value >= filter.FromDate.Value);
                        }
                        else if (filter.FromDate != null && filter.ToDate != null)
                        {
                            Query.Where(i => i.PossessionDate != null && i.PossessionDate.Value >= filter.FromDate.Value && i.PossessionDate.Value < filter.ToDate.Value.AddDays(1));
                        }
                        break;
                    default:
                        break;
                }
            }


            if (!string.IsNullOrWhiteSpace(filter.PropertySearch))
            {
                var minBudget = (Int64.TryParse(Regex.Matches(filter.PropertySearch, @"\d+")?.FirstOrDefault()?.Value ?? string.Empty, out long min) ? (long?)min : null);
                if (minBudget != null) { minBudget = Convert.ToInt64(minBudget * 0.8); }
                var maxBudget = (Int64.TryParse(Regex.Matches(filter.PropertySearch, @"\d+")?.FirstOrDefault()?.Value ?? string.Empty, out long max) ? (long?)max : null);
                if (maxBudget != null) { maxBudget = Convert.ToInt64(maxBudget * 1.2); }
                var noOfBHK = double.TryParse(Regex.Matches(filter.PropertySearch, @"\d+")?.FirstOrDefault()?.Value ?? string.Empty, out double nB) ? (double?)nB : null;
                var saleType = (Enum.TryParse<SaleType>((Enum.GetNames<SaleType>()).Where(i => filter.PropertySearch.Contains(i.ToLower())).ToList().FirstOrDefault(), true, out SaleType sT)) ? (SaleType?)sT : null;
                var enquiryType = (Enum.TryParse<EnquiryType>((Enum.GetNames<EnquiryType>()).Where(i => filter.PropertySearch.Contains(i.ToLower())).ToList().FirstOrDefault(), true, out EnquiryType eT)) ? (EnquiryType?)eT : null;
                var furnishStatus = (Enum.TryParse<FurnishStatus>((Enum.GetNames<FurnishStatus>()).Where(i => filter.PropertySearch.Contains(i.ToLower())).ToList().FirstOrDefault(), true, out FurnishStatus fS)) ? (FurnishStatus?)fS : null;
                var propertyStatus = (Enum.TryParse<PropertyStatus>((Enum.GetNames<PropertyStatus>()).Where(i => filter.PropertySearch.Contains(i.ToLower())).ToList().FirstOrDefault(), true, out PropertyStatus pS)) ? (PropertyStatus?)pS : null;
                var bHKType = Enum.TryParse<BHKType>((Enum.GetNames<BHKType>()).Where(i => filter.PropertySearch.Contains(i.ToLower())).ToList().FirstOrDefault(), true, out BHKType bK) ? (BHKType?)bK : null;
                var facing = Enum.TryParse<Facing>((Enum.GetNames<Facing>()).Where(i => filter.PropertySearch.Contains(i.ToLower())).ToList().FirstOrDefault(), true, out Facing fc) ? (Facing?)fc : null;

                Query.Where(
                i => (i.Title + " " +
                i.Rating + " " +
                i.Address.SubLocality + " " +
                i.Address.Locality + " " +
                i.Address.District + " " +
                i.Address.City + " " +
                i.Address.State + " " +
                i.Address.Country + " " +
                i.Address.PostalCode + " " +
                i.PropertyType.Type + " " +
                i.PropertyType.DisplayName + " " +
                i.OwnerDetails.Name + " " +
                i.OwnerDetails.Phone + " " +
                i.OwnerDetails.Email + " " +
                i.AboutProperty + " "
                ).ToLower().Contains(filter.PropertySearch.ToLower()) ||
                (i.SaleType == saleType && saleType != null) ||
                (i.EnquiredFor == enquiryType && enquiryType != null) ||
                (i.FurnishStatus == furnishStatus && furnishStatus != null) ||
                (i.Status == propertyStatus && propertyStatus != null) ||
                (i.BHKType == bHKType && bHKType != null) ||
                (i.Facing == facing && facing != null) ||
                (i.Attributes.Any(i => i.MasterPropertyAttributeId == masterPropertyAttributeId) && masterPropertyAttributeId != null) ||
                (i.Amenities.Any(i => i.MasterPropertyAmenityId == masterPropertyAmenityId) && masterPropertyAmenityId != null) ||
                ((i.MonetaryInfo.ExpectedPrice >= minBudget && i.MonetaryInfo.ExpectedPrice <= maxBudget) && minBudget != null && maxBudget != null) ||
                ((i.PropertyType.BaseId == masterPropertyTypeId || i.PropertyType.Id == masterPropertyTypeId) && masterPropertyTypeId != null));
            }
            if (filter.NoOfFloors?.Any() ?? false && numericAttributesDto?.NoOfFloors?.NoOfAttributes != null && attributes != null)
            {
                var attribute = attributes?.FirstOrDefault(i => i.AttributeDisplayName == "Total Floors");
                if (attribute != null)
                {
                    if (numericAttributesDto.NoOfFloors.IsMaxValueIncluded)
                    {
                        Query.Where(i => i.Attributes.Any(i => i.MasterPropertyAttributeId == attribute.Id &&
                     !numericAttributesDto.NoOfFloors.NoOfAttributes.Contains(i.Value) && !string.IsNullOrEmpty(i.Value) && i.Value != "0" && i.Value != "Ground Floor"));
                    }
                    else
                    {
                        Query.Where(i => i.Attributes.Any(i => i.MasterPropertyAttributeId == attribute.Id &&
                           numericAttributesDto.NoOfFloors.NoOfAttributes.Contains(i.Value) && !string.IsNullOrEmpty(i.Value) && i.Value != "0"));
                    }
                }
            }
            if (filter.NoOfBedrooms?.Any() ?? false && numericAttributesDto?.NoOfBedrooms?.NoOfAttributes != null && attributes != null)
            {
                var attribute = attributes?.FirstOrDefault(i => i.AttributeDisplayName == "No. of Bed Rooms");
                if (attribute != null)
                {
                    if (numericAttributesDto.NoOfBedrooms.IsMaxValueIncluded)
                    {
                        Query.Where(i => i.Attributes.Any(i => i.MasterPropertyAttributeId == attribute.Id &&
                     !numericAttributesDto.NoOfBedrooms.NoOfAttributes.Contains(i.Value) && !string.IsNullOrEmpty(i.Value) && i.Value != "0"));
                    }
                    else
                    {
                        Query.Where(i => i.Attributes.Any(i => i.MasterPropertyAttributeId == attribute.Id &&
                           numericAttributesDto.NoOfBedrooms.NoOfAttributes.Contains(i.Value) && !string.IsNullOrEmpty(i.Value) && i.Value != "0"));
                    }
                }
            }
            if (filter.NoOfKitchens?.Any() ?? false && numericAttributesDto?.NoOfKitchens?.NoOfAttributes != null && attributes != null)
            {
                var attribute = attributes?.FirstOrDefault(i => i.AttributeDisplayName == "No. of Kitchens");
                if (attribute != null)
                {
                    if (numericAttributesDto.NoOfKitchens.IsMaxValueIncluded)
                    {
                        Query.Where(i => i.Attributes.Any(i => i.MasterPropertyAttributeId == attribute.Id &&
                     !numericAttributesDto.NoOfKitchens.NoOfAttributes.Contains(i.Value) && !string.IsNullOrEmpty(i.Value) && i.Value != "0"));
                    }
                    else
                    {
                        Query.Where(i => i.Attributes.Any(i => i.MasterPropertyAttributeId == attribute.Id &&
                            numericAttributesDto.NoOfKitchens.NoOfAttributes.Contains(i.Value) && !string.IsNullOrEmpty(i.Value) && i.Value != "0"));
                    }
                }
            }

            if (filter.NoOfUtilites?.Any() ?? false && numericAttributesDto?.NoOfUtilites?.NoOfAttributes != null && attributes != null)
            {
                var attribute = attributes?.FirstOrDefault(i => i.AttributeDisplayName == "No. of Utilities");
                if (attribute != null)
                {
                    if (numericAttributesDto.NoOfUtilites.IsMaxValueIncluded)
                    {
                        Query.Where(i => i.Attributes.Any(i => i.MasterPropertyAttributeId == attribute.Id &&
                     !numericAttributesDto.NoOfUtilites.NoOfAttributes.Contains(i.Value) && !string.IsNullOrEmpty(i.Value) && i.Value != "0"));
                    }
                    else
                    {
                        Query.Where(i => i.Attributes.Any(i => i.MasterPropertyAttributeId == attribute.Id &&
                           numericAttributesDto.NoOfUtilites.NoOfAttributes.Contains(i.Value) && !string.IsNullOrEmpty(i.Value) && i.Value != "0"));
                    }
                }
            }
            if (filter.NoOfLivingrooms?.Any() ?? false && numericAttributesDto?.NoOfLivingrooms?.NoOfAttributes != null && attributes != null)
            {
                var attribute = attributes?.FirstOrDefault(i => i.AttributeDisplayName == "No. of Drawing or Living Rooms");
                if (attribute != null)
                {
                    if (numericAttributesDto.NoOfLivingrooms.IsMaxValueIncluded)
                    {
                        Query.Where(i => i.Attributes.Any(i => i.MasterPropertyAttributeId == attribute.Id &&
                     !numericAttributesDto.NoOfLivingrooms.NoOfAttributes.Contains(i.Value) && !string.IsNullOrEmpty(i.Value) && i.Value != "0"));
                    }
                    else
                    {
                        Query.Where(i => i.Attributes.Any(i => i.MasterPropertyAttributeId == attribute.Id &&
                           numericAttributesDto.NoOfLivingrooms.NoOfAttributes.Contains(i.Value) && !string.IsNullOrEmpty(i.Value) && i.Value != "0"));
                    }
                }

            }
            if (filter.NoOfBalconies?.Any() ?? false && numericAttributesDto?.NoOfBalconies?.NoOfAttributes != null && attributes != null)
            {
                var attribute = attributes?.FirstOrDefault(i => i.AttributeDisplayName == "No. of Balconies");
                if (attribute != null)
                {
                    if (numericAttributesDto.NoOfBalconies.IsMaxValueIncluded)
                    {
                        Query.Where(i => i.Attributes.Any(i => i.MasterPropertyAttributeId == attribute.Id &&
                     !numericAttributesDto.NoOfBalconies.NoOfAttributes.Contains(i.Value) && !string.IsNullOrEmpty(i.Value) && i.Value != "0"));
                    }
                    else
                    {
                        Query.Where(i => i.Attributes.Any(i => i.MasterPropertyAttributeId == attribute.Id &&
                           numericAttributesDto.NoOfBalconies.NoOfAttributes.Contains(i.Value) && !string.IsNullOrEmpty(i.Value) && i.Value != "0"));
                    }
                }
            }
            if (filter.NoOfBathrooms?.Any() ?? false && numericAttributesDto?.NoOfBathrooms?.NoOfAttributes != null && attributes != null)
            {
                var attribute = attributes?.FirstOrDefault(i => i.AttributeDisplayName == "No. of Bath Rooms");
                if (attribute != null)
                {
                    if (numericAttributesDto.NoOfBathrooms.IsMaxValueIncluded)
                    {
                        Query.Where(i => i.Attributes.Any(i => i.MasterPropertyAttributeId == attribute.Id &&
                     !numericAttributesDto.NoOfBathrooms.NoOfAttributes.Contains(i.Value) && !string.IsNullOrEmpty(i.Value) && i.Value != "0" && i.Value != "Ground Floor"));
                    }
                    else
                    {
                        Query.Where(i => i.Attributes.Any(i => i.MasterPropertyAttributeId == attribute.Id &&
                           numericAttributesDto.NoOfBathrooms.NoOfAttributes.Contains(i.Value) && !string.IsNullOrEmpty(i.Value) && i.Value != "0"));
                    }
                }
            }
            if (filter.Parking?.Any() ?? false && numericAttributesDto?.Parking?.NoOfAttributes != null && attributes != null)
            {
                var attribute = attributes?.FirstOrDefault(i => i.AttributeDisplayName == "Parking");
                if (attribute != null)
                {
                    if (numericAttributesDto.Parking.IsMaxValueIncluded)
                    {
                        Query.Where(i => i.Attributes.Any(i => i.MasterPropertyAttributeId == attribute.Id &&
                     !numericAttributesDto.Parking.NoOfAttributes.Contains(i.Value) && !string.IsNullOrEmpty(i.Value) && i.Value != "0"));
                    }
                    else
                    {
                        Query.Where(i => i.Attributes.Any(i => i.MasterPropertyAttributeId == attribute.Id &&
                           numericAttributesDto.Parking.NoOfAttributes.Contains(i.Value) && !string.IsNullOrEmpty(i.Value) && i.Value != "0"));
                    }
                }
            }
            if (filter.PropertySize != null && filter.PropertySize != default)
            {
                if (filter.PropertySize.CarpetArea != default && filter.PropertySize.ConversionFactor != default)
                {
                    Query.Where(i => propertyDimensionIds.Any() && propertyDimensionIds.Contains(i.Dimension.Id));
                }
                else if (filter.PropertySize.CarpetArea == 999)
                {
                    Query.Where(i => i.Dimension.CarpetArea <= filter.PropertySize.CarpetArea || i.Dimension.AreaInSqMtr <= filter.PropertySize.CarpetArea);

                }
                else if (filter.PropertySize.CarpetArea != default && filter.PropertySize.ConversionFactor == default)
                {
                    Query.Where(i => i.Dimension.CarpetArea >= filter.PropertySize.CarpetArea || i.Dimension.AreaInSqMtr >= filter.PropertySize.CarpetArea);
                }
            }
            if (filter.PropertySize != null && filter.PropertySize != default)
            {
                if (filter.PropertySize.BuildUpArea != default && filter.PropertySize.ConversionFactor != default)
                {
                    Query.Where(i => propertyDimensionIds.Any() && propertyDimensionIds.Contains(i.Dimension.Id));
                }

                else if (filter.PropertySize.BuildUpArea != default && filter.PropertySize.ConversionFactor == default)
                {
                    Query.Where(i => i.Dimension.BuildUpArea == filter.PropertySize.BuildUpArea || i.Dimension.AreaInSqMtr == filter.PropertySize.BuildUpArea);
                }
            }
            if (filter.PropertySize != null && filter.PropertySize != default)
            {

                if (filter.PropertySize.SaleableArea != default && filter.PropertySize.ConversionFactor != default)
                {
                    Query.Where(i => propertyDimensionIds.Any() && propertyDimensionIds.Contains(i.Dimension.Id));
                }

                else if (filter.PropertySize.SaleableArea != default && filter.PropertySize.ConversionFactor == default)
                {
                    Query.Where(i => i.Dimension.SaleableArea == filter.PropertySize.SaleableArea || i.Dimension.AreaInSqMtr == filter.PropertySize.SaleableArea);
                }
            }
            if (filter.PropertySize != null && filter.PropertySize != default)
            {

                if (filter.PropertySize.NetArea != default && filter.PropertySize.ConversionFactor != default)
                {
                    Query.Where(i => propertyDimensionIds.Any() && propertyDimensionIds.Contains(i.Dimension.Id));
                }

                else if (filter.PropertySize.NetArea != default && filter.PropertySize.ConversionFactor == default)
                {
                    Query.Where(i => i.Dimension.NetArea == filter.PropertySize.NetArea || i.Dimension.AreaInSqMtr == filter.PropertySize.NetArea);
                }
            }
            if (filter.ListingOnBehalf?.Any() ?? false)
            {
                Query.Where(i => i.ListingOnBehalf != null && i.ListingOnBehalf.Any(j => filter.ListingOnBehalf.Contains(j)));
            }
            if (filter?.PossesionType != null && filter?.PossesionType != PossesionType.None)
            {
                switch (filter?.PossesionType)
                {
                    case PossesionType.UnderConstruction:
                        Query.Where(i => i.PossesionType == PossesionType.UnderConstruction);
                        break;

                    case PossesionType.SixMonth:
                        Query.Where(i => i.PossesionType == PossesionType.SixMonth);
                        break;

                    case PossesionType.Year:
                        Query.Where(i => i.PossesionType == PossesionType.Year);
                        break;

                    case PossesionType.TwoYears:
                        Query.Where(i => i.PossesionType == PossesionType.TwoYears);
                        break;

                    case PossesionType.CustomDate:
                        DateTime? tempToPossesionDate = filter?.ToPossesionDate?.ConvertToDateToUtc();
                        DateTime? tempFrompossesionDate = filter?.FromPossesionDate?.ConvertFromDateToUtc();
                        Query.Where(i => i.PossessionDate != null && i.PossessionDate >= tempFrompossesionDate.Value && i.PossessionDate <= tempToPossesionDate.Value);

                        break;
                }

            }
        }
        public class V2PropertyCountByCustomFilterSpec : Specification<Domain.Entities.Property>
        {
            public V2PropertyCountByCustomFilterSpec(
                V2GetAllPropertyRequest filter, Guid? masterPropertyAttributeId, Guid? masterPropertyAmenityId, Guid? masterPropertyTypeId, List<Guid>? propertyDimensionIds, NumericAttributesDto1 numericAttributesDto, List<CustomPropertyAttributeDto>? attributes = null)
            {
                Query
                .Include(i => i.Address)
                .Include(i => i.MonetaryInfo)
                .Include(i => i.PropertyType)
                .Include(i => i.PropertyOwnerDetails)
                .Include(i => i.Dimension)
                .Include(i => i.TagInfo)
                .Include(i => i.Attributes)
                .Include(i => i.Amenities)
                .Include(i => i.Galleries.Where(j => !j.IsDeleted))
                .Where(i => !i.IsDeleted && !i.IsArchived)
                .OrderByDescending(i => i.LastModifiedOn);
                if (filter.EnquiredFor != null)
                {
                    Query.Where(i => i.EnquiredFor == filter.EnquiredFor);
                }
                if (filter.PropertyStatus != null)
                {
                    Query.Where(i => i.Status == filter.PropertyStatus);
                }

                if (filter.NoOfBHK != default)
                {
                    Query.Where(i => i.NoOfBHKs == filter.NoOfBHK);
                }
                if (filter.Ratings != default)
                {
                    Query.Where(i => i.Rating == filter.Ratings);
                }
                if (filter.BasePropertyTypeId != default)
                {
                    Query.Where(i => i.PropertyType != null && i.PropertyType.BaseId == filter.BasePropertyTypeId);
                }
                if (filter.Facing != null)
                {
                    Query.Where(i => i.Facing == filter.Facing);

                }
                if (filter.PropertyTypes?.Any() ?? false)
                {
                    Query.Where(i => i.PropertyType != null && i.PropertyType.BaseId != null && filter.PropertyTypes.Contains(i.PropertyType.BaseId ?? default));
                }
                if (filter.PropertySubTypes?.Any() ?? false)
                {
                    Query.Where(i => i.PropertyType != null && filter.PropertySubTypes.Contains(i.PropertyType.Id));
                }
                /*if (filter.PriceRange != null && filter.PriceRange.Any())
                {
                    foreach (var item in filter.PriceRange)
                    {
                        switch (item)
                        {
                            case PropertyPriceFilter.UptoTenLakhs:
                                Query.Where(i => i.MonetaryInfo != null && i.MonetaryInfo.ExpectedPrice >= 0 && i.MonetaryInfo.ExpectedPrice < 1000000);
                                break;
                            case PropertyPriceFilter.TenToTwentyLakhs:
                                Query.Where(i => i.MonetaryInfo != null && i.MonetaryInfo.ExpectedPrice >= 1000000 && i.MonetaryInfo.ExpectedPrice < 2000000);
                                break;
                            case PropertyPriceFilter.TwentyToThirtyLakhs:
                                Query.Where(i => i.MonetaryInfo != null && i.MonetaryInfo.ExpectedPrice >= 2000000 && i.MonetaryInfo.ExpectedPrice < 3000000);
                                break;
                            case PropertyPriceFilter.ThirtyToFortyLakhs:
                                Query.Where(i => i.MonetaryInfo != null && i.MonetaryInfo.ExpectedPrice >= 3000000 && i.MonetaryInfo.ExpectedPrice < 4000000);
                                break;
                            case PropertyPriceFilter.FortyToFiftyLakhs:
                                Query.Where(i => i.MonetaryInfo != null && i.MonetaryInfo.ExpectedPrice >= 4000000 && i.MonetaryInfo.ExpectedPrice < 5000000);
                                break;
                            case PropertyPriceFilter.FiftyLakhsToOneCrore:
                                Query.Where(i => i.MonetaryInfo != null && i.MonetaryInfo.ExpectedPrice >= 5000000 && i.MonetaryInfo.ExpectedPrice < 10000000);
                                break;
                            case PropertyPriceFilter.AboveOneCrore:
                                Query.Where(i => i.MonetaryInfo != null && i.MonetaryInfo.ExpectedPrice >= 10000000);
                                break;
                            default:
                                break;
                        }

                    }
                }*/
                if (filter.PropertySize != null && filter.PropertySize != default)
                {
                    if (filter.PropertySize.Area != default && filter.PropertySize.ConversionFactor != default)
                    {
                        //Query.Where(i => (i.Dimension.AreaInSqMtr == (filter.PropertySize.Area * filter.PropertySize.ConversionFactor)));
                        Query.Where(i => propertyDimensionIds.Any() && propertyDimensionIds.Contains(i.Dimension.Id));
                    }
                    else if (filter.PropertySize.Area != default && filter.PropertySize.ConversionFactor == default)
                    {
                        Query.Where(i => i.Dimension.Area == filter.PropertySize.Area || i.Dimension.AreaInSqMtr == filter.PropertySize.Area);
                    }
                }
                if (filter.PropertySize != null && filter.PropertySize != default)
                {
                    if (filter.PropertySize.CarpetArea != default && filter.PropertySize.ConversionFactor != default)
                    {
                        Query.Where(i => propertyDimensionIds.Any() && propertyDimensionIds.Contains(i.Dimension.Id));
                    }

                    else if (filter.PropertySize.CarpetArea != default && filter.PropertySize.ConversionFactor == default)
                    {
                        Query.Where(i => i.Dimension.CarpetArea == filter.PropertySize.CarpetArea || i.Dimension.AreaInSqMtr == filter.PropertySize.CarpetArea);
                    }
                }
                if (filter.PropertySize != null && filter.PropertySize != default)
                {
                    if (filter.PropertySize.BuildUpArea != default && filter.PropertySize.ConversionFactor != default)
                    {
                        Query.Where(i => propertyDimensionIds.Any() && propertyDimensionIds.Contains(i.Dimension.Id));
                    }

                    else if (filter.PropertySize.BuildUpArea != default && filter.PropertySize.ConversionFactor == default)
                    {
                        Query.Where(i => i.Dimension.BuildUpArea == filter.PropertySize.BuildUpArea || i.Dimension.AreaInSqMtr == filter.PropertySize.BuildUpArea);
                    }
                }
                if (filter.PropertySize != null && filter.PropertySize != default)
                {

                    if (filter.PropertySize.SaleableArea != default && filter.PropertySize.ConversionFactor != default)
                    {
                        Query.Where(i => propertyDimensionIds.Any() && propertyDimensionIds.Contains(i.Dimension.Id));
                    }

                    else if (filter.PropertySize.SaleableArea != default && filter.PropertySize.ConversionFactor == default)
                    {
                        Query.Where(i => i.Dimension.SaleableArea == filter.PropertySize.SaleableArea || i.Dimension.AreaInSqMtr == filter.PropertySize.SaleableArea);
                    }
                }
                if (filter.PropertySize != null && filter.PropertySize != default)
                {

                    if (filter.PropertySize.NetArea != default && filter.PropertySize.ConversionFactor != default)
                    {
                        Query.Where(i => propertyDimensionIds.Any() && propertyDimensionIds.Contains(i.Dimension.Id));
                    }

                    else if (filter.PropertySize.NetArea != default && filter.PropertySize.ConversionFactor == default)
                    {
                        Query.Where(i => i.Dimension.NetArea == filter.PropertySize.NetArea || i.Dimension.AreaInSqMtr == filter.PropertySize.NetArea);
                    }
                }
                if (filter.Amenities != null && filter.Amenities.Any())
                {
                    Query.Where(i => i.Amenities.Any(a => filter.Amenities.Contains(a.MasterPropertyAmenityId)));
                }
                if (filter.FloorNumber != default)
                {
                    Query.Where(i => i.Attributes != null && i.Attributes.Any(i => i.MasterPropertyAttributeId == Guid.Parse("c146a273-e342-4d66-aeee-6b2e6106d225") && i.Value == filter.FloorNumber.ToString()));

                }
                if (!string.IsNullOrWhiteSpace(filter.PropertySearch))
                {
                    var minBudget = (Int64.TryParse(Regex.Matches(filter.PropertySearch, @"\d+")?.FirstOrDefault()?.Value ?? string.Empty, out long min) ? (long?)min : null);
                    if (minBudget != null) { minBudget = Convert.ToInt64(minBudget * 0.8); }
                    var maxBudget = (Int64.TryParse(Regex.Matches(filter.PropertySearch, @"\d+")?.FirstOrDefault()?.Value ?? string.Empty, out long max) ? (long?)max : null);
                    if (maxBudget != null) { maxBudget = Convert.ToInt64(maxBudget * 1.2); }
                    var noOfBHK = double.TryParse(Regex.Matches(filter.PropertySearch, @"\d+")?.FirstOrDefault()?.Value ?? string.Empty, out double nB) ? (double?)nB : null;
                    var saleType = (Enum.TryParse<SaleType>((Enum.GetNames<SaleType>()).Where(i => filter.PropertySearch.Contains(i.ToLower())).ToList().FirstOrDefault(), true, out SaleType sT)) ? (SaleType?)sT : null;
                    var enquiryType = (Enum.TryParse<EnquiryType>((Enum.GetNames<EnquiryType>()).Where(i => filter.PropertySearch.Contains(i.ToLower())).ToList().FirstOrDefault(), true, out EnquiryType eT)) ? (EnquiryType?)eT : null;
                    var furnishStatus = (Enum.TryParse<FurnishStatus>((Enum.GetNames<FurnishStatus>()).Where(i => filter.PropertySearch.Contains(i.ToLower())).ToList().FirstOrDefault(), true, out FurnishStatus fS)) ? (FurnishStatus?)fS : null;
                    var propertyStatus = (Enum.TryParse<PropertyStatus>((Enum.GetNames<PropertyStatus>()).Where(i => filter.PropertySearch.Contains(i.ToLower())).ToList().FirstOrDefault(), true, out PropertyStatus pS)) ? (PropertyStatus?)pS : null;
                    var bHKType = Enum.TryParse<BHKType>((Enum.GetNames<BHKType>()).Where(i => filter.PropertySearch.Contains(i.ToLower())).ToList().FirstOrDefault(), true, out BHKType bK) ? (BHKType?)bK : null;
                    var facing = Enum.TryParse<Facing>((Enum.GetNames<Facing>()).Where(i => filter.PropertySearch.Contains(i.ToLower())).ToList().FirstOrDefault(), true, out Facing fc) ? (Facing?)fc : null;

                    Query.Where(
                    i => (i.Title + " " +
                    i.Rating + " " +
                    i.Address.SubLocality + " " +
                    i.Address.Locality + " " +
                    i.Address.District + " " +
                    i.Address.City + " " +
                    i.Address.State + " " +
                    i.Address.Country + " " +
                    i.Address.PostalCode + " " +
                    i.PropertyType.Type + " " +
                    i.PropertyType.DisplayName + " " +
                    i.OwnerDetails.Name + " " +
                    i.OwnerDetails.Phone + " " +
                    i.OwnerDetails.Email + " " +
                    i.AboutProperty + " "
                    ).ToLower().Contains(filter.PropertySearch.ToLower()) ||
                    (i.SaleType == saleType && saleType != null) ||
                    (i.EnquiredFor == enquiryType && enquiryType != null) ||
                    (i.FurnishStatus == furnishStatus && furnishStatus != null) ||
                    (i.Status == propertyStatus && propertyStatus != null) ||
                    (i.BHKType == bHKType && bHKType != null) ||
                    (i.Facing == facing && facing != null) ||
                    (i.Attributes.Any(i => i.MasterPropertyAttributeId == masterPropertyAttributeId) && masterPropertyAttributeId != null) ||
                    (i.Amenities.Any(i => i.MasterPropertyAmenityId == masterPropertyAmenityId) && masterPropertyAmenityId != null) ||
                    ((i.MonetaryInfo.ExpectedPrice >= minBudget && i.MonetaryInfo.ExpectedPrice <= maxBudget) && minBudget != null && maxBudget != null) ||
                    ((i.PropertyType.BaseId == masterPropertyTypeId || i.PropertyType.Id == masterPropertyTypeId) && masterPropertyTypeId != null));
                }
                if (filter.MinPrice != null && filter.MaxPrice != null)
                {
                    Query.Where(i => i.MonetaryInfo != null && i.MonetaryInfo.ExpectedPrice >= filter.MinPrice && i.MonetaryInfo.ExpectedPrice <= filter.MaxPrice);
                }
                else if (filter.MinPrice != null && filter.MaxPrice == null)
                {
                    Query.Where(i => i.MonetaryInfo != null && i.MonetaryInfo.ExpectedPrice >= filter.MinPrice);
                }
                else if (filter.MinPrice == null && filter.MaxPrice != null)
                {
                    Query.Where(i => i.MonetaryInfo != null && i.MonetaryInfo.ExpectedPrice <= filter.MaxPrice);
                }
                if (filter.NoOfFloors?.Any() ?? false && numericAttributesDto?.NoOfFloors?.NoOfAttributes != null && attributes != null)
                {
                    var attribute = attributes?.FirstOrDefault(i => i.AttributeDisplayName == "Total Floors");
                    if (attribute != null)
                    {
                        if (numericAttributesDto.NoOfFloors.IsMaxValueIncluded)
                        {
                            Query.Where(i => i.Attributes.Any(i => i.MasterPropertyAttributeId == attribute.Id &&
                         !numericAttributesDto.NoOfFloors.NoOfAttributes.Contains(i.Value) && !string.IsNullOrEmpty(i.Value) && i.Value != "0" && i.Value != "Ground Floor"));
                        }
                        else
                        {
                            Query.Where(i => i.Attributes.Any(i => i.MasterPropertyAttributeId == attribute.Id &&
                               numericAttributesDto.NoOfFloors.NoOfAttributes.Contains(i.Value) && !string.IsNullOrEmpty(i.Value) && i.Value != "0"));
                        }
                    }
                }
                if (filter.NoOfBedrooms?.Any() ?? false && numericAttributesDto?.NoOfBedrooms?.NoOfAttributes != null && attributes != null)
                {
                    var attribute = attributes?.FirstOrDefault(i => i.AttributeDisplayName == "No. of Bed Rooms");
                    if (attribute != null)
                    {
                        if (numericAttributesDto.NoOfBedrooms.IsMaxValueIncluded)
                        {
                            Query.Where(i => i.Attributes.Any(i => i.MasterPropertyAttributeId == attribute.Id &&
                         !numericAttributesDto.NoOfBedrooms.NoOfAttributes.Contains(i.Value) && !string.IsNullOrEmpty(i.Value) && i.Value != "0"));
                        }
                        else
                        {
                            Query.Where(i => i.Attributes.Any(i => i.MasterPropertyAttributeId == attribute.Id &&
                               numericAttributesDto.NoOfBedrooms.NoOfAttributes.Contains(i.Value) && !string.IsNullOrEmpty(i.Value) && i.Value != "0"));
                        }
                    }
                }
                if (filter.NoOfKitchens?.Any() ?? false && numericAttributesDto?.NoOfKitchens?.NoOfAttributes != null && attributes != null)
                {
                    var attribute = attributes?.FirstOrDefault(i => i.AttributeDisplayName == "No. of Kitchens");
                    if (attribute != null)
                    {
                        if (numericAttributesDto.NoOfKitchens.IsMaxValueIncluded)
                        {
                            Query.Where(i => i.Attributes.Any(i => i.MasterPropertyAttributeId == attribute.Id &&
                         !numericAttributesDto.NoOfKitchens.NoOfAttributes.Contains(i.Value) && !string.IsNullOrEmpty(i.Value) && i.Value != "0"));
                        }
                        else
                        {
                            Query.Where(i => i.Attributes.Any(i => i.MasterPropertyAttributeId == attribute.Id &&
                                numericAttributesDto.NoOfKitchens.NoOfAttributes.Contains(i.Value) && !string.IsNullOrEmpty(i.Value) && i.Value != "0"));
                        }
                    }
                }

                if (filter.NoOfUtilites?.Any() ?? false && numericAttributesDto?.NoOfUtilites?.NoOfAttributes != null && attributes != null)
                {
                    var attribute = attributes?.FirstOrDefault(i => i.AttributeDisplayName == "No. of Utilities");
                    if (attribute != null)
                    {
                        if (numericAttributesDto.NoOfUtilites.IsMaxValueIncluded)
                        {
                            Query.Where(i => i.Attributes.Any(i => i.MasterPropertyAttributeId == attribute.Id &&
                         !numericAttributesDto.NoOfUtilites.NoOfAttributes.Contains(i.Value) && !string.IsNullOrEmpty(i.Value) && i.Value != "0"));
                        }
                        else
                        {
                            Query.Where(i => i.Attributes.Any(i => i.MasterPropertyAttributeId == attribute.Id &&
                               numericAttributesDto.NoOfUtilites.NoOfAttributes.Contains(i.Value) && !string.IsNullOrEmpty(i.Value) && i.Value != "0"));
                        }
                    }
                }
                if (filter.NoOfLivingrooms?.Any() ?? false && numericAttributesDto?.NoOfLivingrooms?.NoOfAttributes != null && attributes != null)
                {
                    var attribute = attributes?.FirstOrDefault(i => i.AttributeDisplayName == "No. of Drawing or Living Rooms");
                    if (attribute != null)
                    {
                        if (numericAttributesDto.NoOfLivingrooms.IsMaxValueIncluded)
                        {
                            Query.Where(i => i.Attributes.Any(i => i.MasterPropertyAttributeId == attribute.Id &&
                         !numericAttributesDto.NoOfLivingrooms.NoOfAttributes.Contains(i.Value) && !string.IsNullOrEmpty(i.Value) && i.Value != "0"));
                        }
                        else
                        {
                            Query.Where(i => i.Attributes.Any(i => i.MasterPropertyAttributeId == attribute.Id &&
                               numericAttributesDto.NoOfLivingrooms.NoOfAttributes.Contains(i.Value) && !string.IsNullOrEmpty(i.Value) && i.Value != "0"));
                        }
                    }

                }
                if (filter.NoOfBalconies?.Any() ?? false && numericAttributesDto?.NoOfBalconies?.NoOfAttributes != null && attributes != null)
                {
                    var attribute = attributes?.FirstOrDefault(i => i.AttributeDisplayName == "No. of Balconies");
                    if (attribute != null)
                    {
                        if (numericAttributesDto.NoOfBalconies.IsMaxValueIncluded)
                        {
                            Query.Where(i => i.Attributes.Any(i => i.MasterPropertyAttributeId == attribute.Id &&
                         !numericAttributesDto.NoOfBalconies.NoOfAttributes.Contains(i.Value) && !string.IsNullOrEmpty(i.Value) && i.Value != "0"));
                        }
                        else
                        {
                            Query.Where(i => i.Attributes.Any(i => i.MasterPropertyAttributeId == attribute.Id &&
                               numericAttributesDto.NoOfBalconies.NoOfAttributes.Contains(i.Value) && !string.IsNullOrEmpty(i.Value) && i.Value != "0"));
                        }
                    }
                }
                if (filter.NoOfBathrooms?.Any() ?? false && numericAttributesDto?.NoOfBathrooms?.NoOfAttributes != null && attributes != null)
                {
                    var attribute = attributes?.FirstOrDefault(i => i.AttributeDisplayName == "No. of Bath Rooms");
                    if (attribute != null)
                    {
                        if (numericAttributesDto.NoOfBathrooms.IsMaxValueIncluded)
                        {
                            Query.Where(i => i.Attributes.Any(i => i.MasterPropertyAttributeId == attribute.Id &&
                         !numericAttributesDto.NoOfBathrooms.NoOfAttributes.Contains(i.Value) && !string.IsNullOrEmpty(i.Value) && i.Value != "0" && i.Value != "Ground Floor"));
                        }
                        else
                        {
                            Query.Where(i => i.Attributes.Any(i => i.MasterPropertyAttributeId == attribute.Id &&
                               numericAttributesDto.NoOfBathrooms.NoOfAttributes.Contains(i.Value) && !string.IsNullOrEmpty(i.Value) && i.Value != "0"));
                        }
                    }
                }
                if (filter.Parking?.Any() ?? false && numericAttributesDto?.Parking?.NoOfAttributes != null && attributes != null)
                {
                    var attribute = attributes?.FirstOrDefault(i => i.AttributeDisplayName == "Parking");
                    if (attribute != null)
                    {
                        if (numericAttributesDto.Parking.IsMaxValueIncluded)
                        {
                            Query.Where(i => i.Attributes.Any(i => i.MasterPropertyAttributeId == attribute.Id &&
                         !numericAttributesDto.Parking.NoOfAttributes.Contains(i.Value) && !string.IsNullOrEmpty(i.Value) && i.Value != "0"));
                        }
                        else
                        {
                            Query.Where(i => i.Attributes.Any(i => i.MasterPropertyAttributeId == attribute.Id &&
                               numericAttributesDto.Parking.NoOfAttributes.Contains(i.Value) && !string.IsNullOrEmpty(i.Value) && i.Value != "0"));
                        }
                    }
                }
                if (filter.FromPossessionDate != default || filter.ToPossessionDate != default)
                {

                    if (filter.FromPossessionDate == null && filter.ToPossessionDate != null)
                    {
                        Query.Where(i => i.PossessionDate != null && i.PossessionDate.Value <= filter.ToPossessionDate.Value);
                    }
                    else if (filter.FromPossessionDate != null && filter.ToPossessionDate == null)
                    {
                        Query.Where(i => i.PossessionDate != null && i.PossessionDate.Value >= filter.FromPossessionDate.Value);
                    }
                    else if (filter.FromPossessionDate != null && filter.ToPossessionDate != null)
                    {
                        Query.Where(i => i.PossessionDate != null && i.PossessionDate.Value >= filter.FromPossessionDate.Value && i.PossessionDate.Value < filter.ToPossessionDate.Value);

                    }
                }
                if (filter.MinBudget != null && filter.MaxBudget != null)
                {
                    Query.Where(i => i.MonetaryInfo != null && i.MonetaryInfo.ExpectedPrice >= filter.MinBudget && i.MonetaryInfo.ExpectedPrice <= filter.MaxBudget);
                }
                else if (filter.MinBudget != null && filter.MaxBudget == null)
                {
                    Query.Where(i => i.MonetaryInfo != null && i.MonetaryInfo.ExpectedPrice >= filter.MinBudget);
                }
                else if (filter.MinBudget == null && filter.MaxBudget != null)
                {
                    Query.Where(i => i.MonetaryInfo != null && i.MonetaryInfo.ExpectedPrice <= filter.MaxBudget);
                }
                if (filter.SubPropertyTypeIds?.Any() ?? false)
                {
                    Query.Where(i => i.PropertyType != null && filter.SubPropertyTypeIds.Contains(i.PropertyType.Id));
                }
                //if (filter?.Projects?.Any() ?? false)
                //{
                //    var projectNames = filter.Projects.ConvertAll(i => i.ToLower());
                //    Query.Where(i => i.Projects != null && i.Projects.Any(i => projectNames.Contains(i.Name.ToLower())));
                //}
                if (filter?.Projects?.Any() ?? false)
                {
                    var projectNames = filter.Projects.ConvertAll(i => i.ToLower());
                    Query.Where(i => i.Project != null && projectNames.Contains(i.Project.Name.ToLower()));
                }
                if (filter?.BHKTypes?.Any() ?? false)
                {
                    Query.Where(i => filter.BHKTypes.Contains(i.BHKType));
                }
                if (filter?.FurnishStatuses?.Any() ?? false)
                {
                    Query.Where(i => filter.FurnishStatuses.Contains(i.FurnishStatus));
                }
                if (filter.Locations?.Any() ?? false)
                {

                    filter.Locations = filter.Locations.Select(i => i.ToLower()).ToList();
                    Query.Where(i => i.Address != null &&
                       (
                           (i.Address.SubLocality != null && i.Address.Locality != null &&
                            filter.Locations.Contains(i.Address.SubLocality.ToLower() + ", " + i.Address.Locality.ToLower())) ||
                           (i.Address.SubLocality == null && i.Address.Locality != null &&
                            filter.Locations.Contains(", " + i.Address.Locality.ToLower())) ||
                           (i.Address.SubLocality != null && i.Address.Locality == null &&
                            filter.Locations.Contains(i.Address.SubLocality.ToLower() + ", "))
                       )
                   );

                }
                if (filter.Cities?.Any() ?? false)
                {
                    Query.Where(i => i.Address != null && i.Address.City != null && filter.Cities.Select(i => i.ToLower()).Contains(i.Address.City.ToLower()));
                }
                if (filter.States?.Any() ?? false)
                {
                    Query.Where(i => i.Address != null && i.Address.State != null && filter.States.Select(i => i.ToLower()).Contains(i.Address.State.ToLower()));
                }
                if (filter.Countries?.Any() ?? false)
                {
                    Query.Where(i => i.Address != null && i.Address.Country != null && filter.Countries.Select(i => i.ToLower()).Contains(i.Address.Country.ToLower()));
                }
                if (!string.IsNullOrEmpty(filter.PropertyTitle))
                {
                    Query.Where(i => i.Title.ToLower() == filter.PropertyTitle.ToLower());
                }
                if (!string.IsNullOrEmpty(filter.OwnerName))
                {
                    Query.Where(i => i.OwnerDetails.Name.ToLower() == filter.OwnerName.ToLower());
                }
                if (filter?.OwnerNames?.Any() ?? false)
                {
                    filter.OwnerNames = filter.OwnerNames.ConvertAll(i => i.ToLower().Trim().Replace(" ", ""));
                    Query.Where(e => e.PropertyOwnerDetails.Any(i => filter.OwnerNames.Contains(i.Name.ToLower().Trim().Replace(" ", "") ?? string.Empty)));
                }
                if (filter.ListingOnBehalf?.Any() ?? false)
                {
                    Query.Where(i => i.ListingOnBehalf != null && i.ListingOnBehalf.Any(j => filter.ListingOnBehalf.Contains(j)));
                }
                #region PossesionType
                if (filter?.PossesionType != null && filter?.PossesionType != PossesionType.None)
                {
                    switch (filter?.PossesionType)
                    {
                        case PossesionType.UnderConstruction:
                            Query.Where(i => i.PossesionType == PossesionType.UnderConstruction);
                            break;

                        case PossesionType.SixMonth:
                            Query.Where(i => i.PossesionType == PossesionType.SixMonth);
                            break;

                        case PossesionType.Year:
                            Query.Where(i => i.PossesionType == PossesionType.Year);
                            break;

                        case PossesionType.TwoYears:
                            Query.Where(i => i.PossesionType == PossesionType.TwoYears);
                            break;

                        case PossesionType.CustomDate:
                            DateTime? tempToPossesionDate = filter?.ToPossesionDate?.ConvertToDateToUtc();
                            DateTime? tempFrompossesionDate = filter?.FromPossesionDate?.ConvertFromDateToUtc();
                            Query.Where(i => i.PossessionDate != null && i.PossessionDate >= tempFrompossesionDate.Value && i.PossessionDate <= tempToPossesionDate.Value);

                            break;
                    }

                }
                #endregion
            }
        }
        public class PropertyMonetaryInfoSpec : Specification<PropertyMonetaryInfo>
        {
            public PropertyMonetaryInfoSpec(int allPrice)
            {
                Query.Where(i => i.ExpectedPrice >= allPrice);
            }
            public PropertyMonetaryInfoSpec(long lessPrice, long highPrice)
            {
                Query.Where(i => i.ExpectedPrice >= lessPrice && i.ExpectedPrice < highPrice);
            }

        }

        public class BaseAddressSpec : Specification<Lrb.Domain.Entities.Address>
        {
            public BaseAddressSpec(string city)
            {
                Query.Where(i => i.City == city);
            }
        }

        public class SubPropertyTypeByIdSpec : Specification<MasterPropertyType>
        {
            public SubPropertyTypeByIdSpec(Guid? id)
            {
                Query.Where(i => i.Id == id && i.BaseId != null);
            }
        }

        public class BasePropertyTypeByIdSpec : Specification<MasterPropertyType>
        {
            public BasePropertyTypeByIdSpec(Guid? id)
            {
                Query.Where(i => i.BaseId == id);
            }
        }
    }
}

