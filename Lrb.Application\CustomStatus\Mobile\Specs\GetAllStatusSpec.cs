﻿using Lrb.Application.CustomStatus.Mobile.Request;
using Lrb.Domain.Entities.MasterData;

namespace Lrb.Application.CustomStatus.Mobile
{
    public class GetAllStatusSpec : EntitiesByPaginationFilterSpec<CustomMasterLeadStatus>
    {
        public GetAllStatusSpec(GetAllStatusRequest filter, Guid userId) : base(filter)
        {
            Query.Where(i => !i.IsDeleted)
                .Include(i => i.CustomFields)
                .ThenInclude(i => i.Field)
                .OrderByDescending(i => i.LastModifiedOn);
            if (userId != Guid.Empty)
            {
                Query.Where(i => !i.Teams.Any() || i.Teams.Any(j => j.UserIds.Contains(userId) || j.Manager == userId));
            }
        }
    }

    public class GetAllStatusByIdSpec : Specification<CustomMasterLeadStatus>
    {
        public GetAllStatusByIdSpec(Guid id)
        {
            Query.Where(i => !i.IsDeleted && i.Id == id)
                .Include(i => i.CustomFields)
                .ThenInclude(i => i.Field)
                .OrderByDescending(i => i.LastModifiedOn);
        }
    }

    public class GetAllStatusCountSpec : Specification<CustomMasterLeadStatus>
    {
        public GetAllStatusCountSpec(Guid userId)
        {
            Query.Where(i => !i.IsDeleted);
            if (userId != Guid.Empty)
            {
                Query.Where(i => !i.Teams.Any() || i.Teams.Any(j => j.UserIds.Contains(userId) || j.Manager == userId));
            }
        }
    }

    public class GetAllStatusByNameSpec : Specification<CustomMasterLeadStatus>
    {
        public GetAllStatusByNameSpec(string name)
        {
            Query.Where(i => !i.IsDeleted && i.DisplayName == name.ToLower() && !i.ShouldBeHidden);
        }
        public GetAllStatusByNameSpec(string name, bool shouldBeHidden)
        {
            Query.Where(i => !i.IsDeleted && i.DisplayName == name.ToLower());
        }
    }
    public class GetAllStatusAnonymous : Specification<CustomMasterLeadStatus>
    {
        public GetAllStatusAnonymous()
        {
            Query.Where(i => !i.IsDeleted)
                .OrderByDescending(i => i.LastModifiedOn);
        }
    }
}
