﻿using Lrb.Application.Lead.Web.Dtos;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Lrb.Application.Lead.Web.Requests.GetRequests
{
    public class GetLeadNoteHistoryByIdRequest : IRequest<Response<List<LeadHistoryDto>>>
    {
        public Guid Id { get; set; }
        public bool? CanAccessAllLeads { get; set; }
        public GetLeadNoteHistoryByIdRequest(Guid id)
        {
            Id = id;
        }
    }
    public class GetLeadNoteHistoryByIdRequestHandler : IRequestHandler<GetLeadNoteHistoryByIdRequest, Response<List<LeadHistoryDto>>>
    {
        private readonly IReadRepository<LeadHistory> _leadHistoryRepo;
        private readonly IReadRepository<Domain.Entities.Lead> _leadRepository;
        private readonly IDapperRepository _dapperRepository;
        private readonly ICurrentUser _currentUser;
        private readonly ILeadRepositoryAsync _leadRepositoryAsync;

        public GetLeadNoteHistoryByIdRequestHandler(IReadRepository<LeadHistory> leadHistoryRepo,
                                                    IReadRepository<Domain.Entities.Lead> leadRepository,
                                                    IDapperRepository dapperRepository,
                                                    ICurrentUser currentUser,
                                                    ILeadRepositoryAsync leadRepositoryAsync)
        {
            _leadHistoryRepo = leadHistoryRepo;
            _leadRepository = leadRepository;
            _dapperRepository = dapperRepository;
            _currentUser = currentUser;
            _leadRepositoryAsync = leadRepositoryAsync;
        }

        public async Task<Response<List<LeadHistoryDto>>> Handle(GetLeadNoteHistoryByIdRequest request, CancellationToken cancellationToken)
        {
            var lead = await _leadRepository.FirstOrDefaultAsync(new LeadByIdForHistorySpec(request.Id));
            var currentUserId = _currentUser.GetUserId();
            var tenantId = _currentUser.GetTenant();
            List<Guid> subIds = new();
            if (lead == null)
            {
                throw new NotFoundException("lead not found by this id");
            }
            List<LeadNotesHistoryDto> leadHistories = null;
            var isAdmin = await _dapperRepository.IsAdminAsync((_currentUser?.GetUserId() ?? Guid.Empty), _currentUser?.GetTenant() ?? string.Empty);
            if (isAdmin)
            {
                
                leadHistories = await _dapperRepository.GetLeadNotesHistoryByLeadId(request.Id, tenantId);
            }
            else
            {
                subIds = (await _dapperRepository.GetSubordinateIdsAsync(currentUserId, tenantId ?? string.Empty, request?.CanAccessAllLeads, isAdmin))?.ToList() ?? new();
                subIds.AddRange(new List<Guid>() { currentUserId, lead.AssignTo });
                if (lead?.SecondaryUserId != null && lead.SecondaryUserId != Guid.Empty)
                {
                    subIds.Add(lead?.SecondaryUserId ?? Guid.Empty);
                }
                subIds = subIds?.Where(i => i != Guid.Empty)?.Distinct()?.ToList() ?? new List<Guid>();
                leadHistories = await _dapperRepository.GetLeadNotesHistoryByLeadId(request.Id, tenantId ?? string.Empty, subIds);
            }
            if (!leadHistories.Any())
            {
                throw new NotFoundException("Lead History Not Found by this Id");
            }
            var leadHistoryDto = LeadHistoryHelper.FormLeadHistoryViewModelOfNotesNew(leadHistories);
            return new Response<List<LeadHistoryDto>>(leadHistoryDto);
        }
    }
}
