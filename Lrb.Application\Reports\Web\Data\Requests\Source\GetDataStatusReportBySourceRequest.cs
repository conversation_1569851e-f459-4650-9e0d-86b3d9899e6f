﻿using Lrb.Application.Utils;
using Lrb.Shared.Extensions;

namespace Lrb.Application.Reports.Web
{
    public class GetDataStatusReportBySourceRequest : IRequest<PagedResponse<ViewDataSourceDto, string>>
    {
        public DateTime? FromDate { get; set; }
        public DateTime? ToDate { get; set; }
        public DataManagement.Web.Request.ProspectDateType? DateType { get; set; }
        public bool IsWithTeam { get; set; }
        public List<Guid>? UserIds { get; set; }
        public string? SearchText { get; set; }
        public List<Guid>? SourceIds { get; set; }
        public List<string>? SubSources { get; set; }
        public List<string>? Localites { get; set; }
        public List<string>? States { get; set; }
        public List<string>? Cities { get; set; }
        public List<string>? Projects { get; set; }
        public UserStatus? UserStatus { get; set; }
        public ReportPermission? ReportPermission { get; set; }
        public int PageNumber { get; set; } = 1;
        public int PageSize { get; set; } = int.MaxValue;
        public List<string>? Countries { get; set; }
    }
    public class GetDataStatusReportBySourceRequestHandler : IRequestHandler<GetDataStatusReportBySourceRequest, PagedResponse<ViewDataSourceDto, string>>
    {
        private readonly IDapperRepository _dapperRepository;
        private readonly ICurrentUser _currentUser;

        public GetDataStatusReportBySourceRequestHandler(IDapperRepository dapperRepository, ICurrentUser currentUser)
        {
            _dapperRepository = dapperRepository;
            _currentUser = currentUser;
        }
        public async Task<PagedResponse<ViewDataSourceDto, string>> Handle(GetDataStatusReportBySourceRequest request, CancellationToken cancellationToken)
        {
            var tenantId = _currentUser.GetTenant();
            var userId = _currentUser.GetUserId();
            List<Guid> teamUserIds = new();
            List<Guid> permittedUserIds = new();
            var isAdmin = await _dapperRepository.IsAdminAsync(userId, tenantId ?? string.Empty);
            if (isAdmin)
            {
                permittedUserIds = (await _dapperRepository.GetAllUserIdsAsync(tenantId ?? string.Empty)).ToList();
            }
            else if (request.ReportPermission != null)
            {
                switch (request.ReportPermission)
                {
                    case ReportPermission.All:
                        permittedUserIds = (await _dapperRepository.GetAllUserIdsAsync(tenantId ?? string.Empty)).ToList();
                        break;
                    case ReportPermission.Reportees:
                        permittedUserIds = (await _dapperRepository.GetSubordinateIdsWithOnlyReporteesAsync(new List<Guid>() { userId }, tenantId ?? string.Empty)).ToList();
                        break;
                }
            }
            if (request?.UserIds?.Any() ?? false)
            {
                if (request?.IsWithTeam ?? false)
                {
                    teamUserIds = (await _dapperRepository.GetSubordinateIdsWithOnlyReporteesAsync(request.UserIds, tenantId ?? string.Empty)).ToList();
                }
                else
                {
                    teamUserIds = request?.UserIds ?? new List<Guid>();
                }
            }
            else
            {
                if (!isAdmin)
                {
                    teamUserIds = permittedUserIds;
                }

            }
            if (teamUserIds.Any())
            {
                teamUserIds = teamUserIds.Where(userId => permittedUserIds.Contains(userId)).ToList();
            }
            else
            {
                teamUserIds = permittedUserIds;
            }
            request.FromDate = request.FromDate.HasValue ? request.FromDate.Value.ConvertFromDateToUtc() : null;
            request.ToDate = request.ToDate.HasValue ? request.ToDate.Value.ConvertToDateToUtc() : null;
            List<ViewDataSourceDto> result = new List<ViewDataSourceDto>();
            var res = (await _dapperRepository.QueryStoredProcedureFromReadReplicaAsync<DataSourceDto>("LeadratBlack", "Data_GetDataReportBySource", new
            {
                fromdate = request.FromDate,
                todate = request.ToDate,
                datetype = request.DateType,
                tenantid = tenantId,
                userids = teamUserIds,
                searchtext = string.IsNullOrEmpty(request.SearchText) ? null : request.SearchText.Replace(" ", "").ToLower(),
                projects = request?.Projects?.ConvertAll<string>(i => i.Replace(" ", "").ToLower()),
                subsources = request?.SubSources?.ConvertAll<string>(i => i.Replace(" ", "").ToLower()),
                sources = request?.SourceIds,
                pagesize = request?.PageSize,
                pagenumber = request?.PageNumber,
                localites = request?.Localites?.ConvertAll<string>(i => i.LrbNormalize()),
                cities = request?.Cities?.ConvertAll<string>(i => i.LrbNormalize()),
                states = request?.States?.ConvertAll<string>(i => i.LrbNormalize()),
                countries = request?.Countries?.ConvertAll<string>(i => i.Replace(" ", "").ToLower())
            })).ToList();
            var data = res.GroupBy(i => new Tuple<Guid, string>(i.Id, i?.Name ?? string.Empty)).ToDictionary(i => i.Key, j => j.ToList());
            foreach (var item in data)
            {
                var value = new ViewDataSourceDto();
                value.Id = item.Key.Item1;
                value.Name = item.Key.Item2;
                value.ConvertedDataCount = item.Value?.Select(j => j.ConvertedDataCount)?.Sum(i => i) ?? 0;
                var allItems = item.Value?.Where(i => i.StatusId != Guid.Empty)?.ToList();
                if(allItems?.Any() ?? false)
                {
                    value.Data = allItems.Adapt<List<ViewDataReportBySourceDto>>();
                }
                result.Add(value);
            }
            return new(result, 0);
        }
    }
}
