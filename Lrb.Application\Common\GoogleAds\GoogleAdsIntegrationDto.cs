﻿using Lrb.Application.Common.Facebook;
using Lrb.Application.Notifications.Dtos;
using Newtonsoft.Json;
using System.Text.Json.Serialization;

namespace Lrb.Application.Common.GoogleAds
{
    public class GoogleAdsIntegrationDto
    {
        public Guid GoogleAdsAuthResponseId { get; set; }
        public string AccessToken { get; set; } = default!;
        public string RefreshToken { get; set; } = default!;
        public string CustomerId { get; set; } = default!;
        public string? GoogleUserName { get; set; }
        public LrbTenantInfoDto? TenantInfoDto { get; set; }
        public Guid CurrentUserId { get; set; }
    }
    public class GoogleAdsUserAccessTokenResponse
    {
        [JsonProperty("access_token")]
        public string access_token { get; set; } = default!;

        [JsonProperty("token_type")]
        public string token_type { get; set; } = default!;

        [JsonProperty("expires_in")]
        public long expires_in { get; set; }
    }
    public class GoogleAdsAdDto
    {
        public string id { get; set; }
        public string name { get; set; }
        public string effective_status { get; set; }
        public string adset_id { get; set; }
        public string adset_name { get; set; }
        public string campaign_id { get; set; }
        public string campaign_name { get; set; }
        public string ad_account_name { get; set; }
        public string ad_account_id { get; set; }
        public string countryCode { get; set; } = default!;
        public string CurrencyCode { get; set; } = default!;
        public string CustomerId { get; set; } = default!;
    }

    public class GoogleAdsAdAccountInfo
    {
        public string Id { get; set; }
        public string account_id { get; set; }
        public string Name { get; set; }
        public string customer_id { get; set; }
        public int account_status { get; set; }

    }

    public class GoogleAdsAdAccountsResponse
    {
        public List<GoogleAdsAdAccountInfo?>? data { get; set; }
        public GoogleAdAccountsPaging? paging { get; set; }
    }
    public class GoogleAdAccountsPaging
    {
        public string? NextPageToken { get; set; }
    }
    public class GoogleAdResponse
    {
        public List<GoogleAdsAdDto> data { get; set; }
        public GoogleAdAccountsPaging? paging { get; set; } // Placeholder
    }


    public class GoogleAdOrFormWithUserOrPageTokenDto : IDto
    {
        public Guid Id { get; set; }
        public string? GoogleAdsId { get; set; }
        public string? Type { get; set; }
        public string? Status { get; set; }
        public Guid AutomationId { get; set; }
        public string? AgencyName { get; set; }
        public string? Agency { get; set; }
        public string? AssignedUserIds { get; set; }
        public string? ProjectIds { get; set; }
        public string? TenantId { get; set; }
        public Guid GoogleAuthResponseId { get; set; }
        public string? GoogleAdsUserOrAccessToken { get; set; }
    }


    public class GoogleAdsBulkLeadDto : IDto
    {
        public GoogleAdsBulkLeadDto()
        {
            data = new();
        }
        public List<GoogleAdsLeadDto>? data { get; set; }
        public GoogleAdAccountsPaging? paging { get; set; }

    }
    public class GoogleAdsInfoDto
    {
        public string? AdId { get; set; }
        public string? AdName { get; set; }
        public string? Status { get; set; }
        public string? AdSetName { get; set; }
        public string? AdSetId { get; set; }
        public int LeadsCount { get; set; }
        public string? CountryCode { get; set; }
        public string? CurrencyCode { get; set; }
    }
    public class GoogleAdsAuthResponseDto : IDto
    {
        public Guid Id { get; set; }
        public string AccountName { get; set; } = default!;
        public string? CustomerId { get; set; }
        public long? LeadCount { get; set; }
        public string? Status { get; set; }
    }
    public class GoogleAdsLeadDto : IDto
    {
        [JsonProperty("id")]
        [JsonPropertyName("id")]
        public string Id { get; set; } = default!;

        [JsonProperty("platform")]
        [JsonPropertyName("platform")]
        public string? Platform { get; set; }

        [JsonProperty("ad_id")]
        [JsonPropertyName("ad_id")]
        public string? AdId { get; set; }

        [JsonProperty("ad_name")]
        [JsonPropertyName("ad_name")]
        public string? AdName { get; set; }

        [JsonProperty("adset_id")]
        [JsonPropertyName("adset_id")]
        public string? AdsetId { get; set; }


        [JsonProperty("adset_name")]
        [JsonPropertyName("adset_name")]
        public string? AdsetName { get; set; }

        [JsonProperty("campaign_id")]
        [JsonPropertyName("campaign_id")]
        public string? CampaignId { get; set; }

        [JsonProperty("campaign_name")]
        [JsonPropertyName("campaign_name")]
        public string? CampaignName { get; set; }

        [JsonProperty("created_time")]
        [JsonPropertyName("created_time")]
        public DateTime CreatedTime { get; set; }

        [JsonProperty("field_data")]
        [JsonPropertyName("field_data")]
        public List<LeadFormData>? FieldData { get; set; }

        [JsonProperty("form_id")]
        [JsonPropertyName("form_id")]
        public string? FormId { get; set; }

        [JsonProperty("is_organic")]
        [JsonPropertyName("is_organic")]
        public bool IsOrganic { get; set; }
        public string? CountryCode { get; set; }
    }
    public class GoogleAdsCampaignInfoDto : IDto
    {
        public string? Status { get; set; }
        public string? CampaignName { get; set; }
        public string? CampaignId { get; set; }
        public Guid GoogleAdsAuthResponseId { get; set; }
        public int LeadCount { get; set; }
        public string? CurrencyCode { get; set; }
        public string? CountryCode { get; set; }
    }
}
