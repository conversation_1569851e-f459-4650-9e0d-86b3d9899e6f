﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Lrb.Application.Integration.Mobile
{
    public static class AccountIdHelper
    {
        public static Guid GetAccountId(this string api)
        {
            if (!string.IsNullOrWhiteSpace(api))
            {
                if (!Guid.TryParse(api, out Guid accountId))
                {
                    var bytes = Convert.FromBase64String(api);
                    var idString = System.Text.Encoding.UTF8.GetString(bytes);
                    accountId = Guid.Parse(idString.Split("/").LastOrDefault());
                }
                return accountId;
            }
            return Guid.Empty;
        }
    }
}
