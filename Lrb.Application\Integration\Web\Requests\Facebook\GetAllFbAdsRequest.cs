﻿using DocumentFormat.OpenXml.Wordprocessing;
using Lrb.Application.Common.Facebook;
using Lrb.Application.Common.GoogleAds;
using Lrb.Application.DailyStatusUpdates.Dtos;
using Lrb.Application.Identity.Users;
using Lrb.Application.Integration.Web.Dtos;
using Lrb.Application.Integration.Web.Specs;
using Lrb.Application.UserDetails.Web.Dtos;
using Lrb.Domain.Entities.Integration;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Lrb.Application.Integration.Web.Requests.Facebook
{
    public class GetAllFbAdsRequest : PaginationFilter, IRequest<PagedResponse<FacebookAdsInfoDto, string>>
    {
        public Guid AccountId { get; set; }
        public string? SearchText { get; set; }
    }
    public class GetAllFbAdsRequestHandler : FBCommon<PERSON><PERSON>ler, IRequestHandler<GetAllFbAdsRequest, PagedResponse<FacebookAdsInfoDto, string>>
    {
        private readonly IRepositoryWithEvents<IntegrationAssignmentInfo> _integrationAssignmentInforepo;
        private readonly IRepositoryWithEvents<Lrb.Domain.Entities.Project> _projectRepo;
        private readonly IUserService _userService;

        public GetAllFbAdsRequestHandler(
           IRepositoryWithEvents<FacebookAuthResponse> facebookAuthResponseRepo,
           IRepositoryWithEvents<FacebookConnectedPageAccount> facebookConnectedPageAccountRepo,
           IRepositoryWithEvents<FacebookLeadGenForm> facebookLeadGenFormRepo,
           IFacebookService facebookService,
           IJobService hangfireService,
           ITenantIndependentRepository repository,
           ICurrentUser currentUser,
           IRepositoryWithEvents<IntegrationAccountInfo> integrationAccInfoRepo,
           IRepositoryWithEvents<IntegrationAssignmentInfo> integrationAssignmentInforepo,
           IRepositoryWithEvents<FacebookAdsInfo> fbAdsRepo,
           IRepositoryWithEvents<Lrb.Domain.Entities.Project> projectRepo,
           IUserService userService,
           ILeadRepositoryAsync leadRepositoryAsync, IGoogleAdsService googleAdsService,
           IRepositoryWithEvents<GoogleAdsAuthResponse> googleAdsAuthResponseRepo,
           IRepositoryWithEvents<GoogleAdsInfo> googleAdsRepo,
           IRepositoryWithEvents<GoogleCampaign> googleCampaignsRepo)
           : base(facebookAuthResponseRepo,
                 facebookConnectedPageAccountRepo,
                 facebookLeadGenFormRepo,
                 facebookService,
                 hangfireService,
                 repository,
                 currentUser,
                 integrationAccInfoRepo,
                 fbAdsRepo,
                 leadRepositoryAsync, googleAdsService, googleAdsAuthResponseRepo,
                 googleAdsRepo,
                 googleCampaignsRepo)
        {
            _integrationAssignmentInforepo = integrationAssignmentInforepo;
            _projectRepo = projectRepo;
            _userService = userService;
        }
        public async Task<PagedResponse<FacebookAdsInfoDto, string>> Handle(GetAllFbAdsRequest request, CancellationToken cancellationToken)
        {

            //var accounts = await _facebookAuthResponseRepo.ListAsync(new GetAllFacebookAccountsSpec(request.AccountId), cancellationToken);
            var allStoredAds = await _fbAdsRepo.ListAsync(new FacebookAdsByFbAccountRequestSpec(request), cancellationToken);
            var adsCount = await _fbAdsRepo.CountAsync(new FacebookAdsByFbAccountIdSpec(request.AccountId,request.SearchText ?? string.Empty), cancellationToken);
            var ads = allStoredAds.Adapt<List<FacebookAdsInfoDto>>();
            return new PagedResponse<FacebookAdsInfoDto, string>(ads, adsCount);


        }
    }
}