﻿using Lrb.Shared.Utils;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Lrb.Application.Lead.Web.Requests.UpdationRequests
{
    public class UpdateBulkChannelPartnerListRequest : IRequest<Response<int>>
    {
        public List<string>? ChannelPartenerNames { get; set; }
        public List<Guid> Ids { get; set; } = default!;
        public bool? ShouldRemoveExistingChannelPartener { get; set; }
        public Guid? CurrentUserId { get; set; }
        public string? TenantId { get; set; }


    }
    public class UpdateBulkChannelPartnerListRequestHandler : LeadCommonRequestHandler, IRequestHandler<UpdateBulkChannelPartnerListRequest, Response<int>>
    {
        public UpdateBulkChannelPartnerListRequestHandler(IServiceProvider serviceProvider) : base(serviceProvider, typeof(UpdateBulkChannelPartnerListRequest).Name, "Handle")
        {
        }
        public async Task<Response<int>> Handle(UpdateBulkChannelPartnerListRequest request, CancellationToken cancellationToken)
        {
            try
            {
                List<Domain.Entities.ChannelPartner> channelPartener = await _cpRepository.ListAsync(new GetAllChannelPartenerSpec(request?.ChannelPartenerNames?.ConvertAll(i => i.ToLower()) ?? new()), cancellationToken);

                List<Domain.Entities.Lead> existingLeads = await _leadRepo.ListAsync(new LeadByIdSpec(request?.Ids ?? new()), cancellationToken);

                if (channelPartener.Count >= 0 && existingLeads.Count > 0)
                {
                    await UpdateChannelPartenerForMultipleLeadsAsync(existingLeads, channelPartener, request?.ShouldRemoveExistingChannelPartener ?? false, cancellationToken, request?.CurrentUserId,request?.TenantId);

                    return new(existingLeads.Count());
                }
                else
                {
                    return new(existingLeads.Count());
                }
            }
            catch (Exception ex)
            {
                await AddLrbErrorAsync(ex, $"{typeof(UpdateBulkChannelPartnerListRequestHandler).Name} - Handle()");
                throw;
            }
        }
    }
}