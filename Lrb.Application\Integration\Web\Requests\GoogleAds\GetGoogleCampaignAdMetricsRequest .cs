﻿using DocumentFormat.OpenXml.Spreadsheet;
using Google.Ads.GoogleAds;
using Google.Ads.GoogleAds.Config;
using Google.Ads.GoogleAds.Lib;
using Google.Ads.GoogleAds.V18.Services;
using Lrb.Application.Common.GoogleAds;
using Lrb.Application.Dashboard.Web;
using Lrb.Application.Integration.Web.Specs;
using Lrb.Domain.Entities.Integration;

namespace Lrb.Application.Integration.Web.Requests.GoogleAds
{
    public class GetGoogleCampaignAdMetricsRequest : IRequest<Response<List<GoogleCampaignAdMetricsDto>>>
    {
        public DateTime? FromDate { get; set; }
        public DateTime? ToDate { get; set; }
        public List<string>? CampaignIds { get; set; } = new();
    }
    public class GetGoogleCampaignAdMetricsRequestHandler : IRequestHandler<GetGoogleCampaignAdMetricsRequest, Response<List<GoogleCampaignAdMetricsDto>>>
    {
        private readonly IRepositoryWithEvents<FacebookConnectedPageAccount> _fbPageRepo;
        private readonly IRepositoryWithEvents<IntegrationAccountInfo> _integrationRepo;
        private readonly IRepositoryWithEvents<Lrb.Domain.Entities.Lead> _leadRepo;
        private readonly ICurrentUser _currentUser;
        private readonly IRepositoryWithEvents<IntegrationAccountInfo> _integrationAccInfoRepo;
        private readonly IRepositoryWithEvents<Lrb.Domain.Entities.Lead> _leadRepository;
        private readonly IDapperRepository _dapperRepository;
        private readonly IRepositoryWithEvents<GoogleAdsInfo> _googleAdsInfos;
        private readonly IGoogleAdsService _googleAdsService;
        private readonly IRepositoryWithEvents<GoogleAdsAuthResponse> _googleAdsAuthResponse;
        public GetGoogleCampaignAdMetricsRequestHandler(
            IRepositoryWithEvents<FacebookConnectedPageAccount> fbPageRepo,
            IRepositoryWithEvents<IntegrationAccountInfo> integrationRepo,
            IRepositoryWithEvents<Lrb.Domain.Entities.Lead> leadRepo,
            ICurrentUser currentUser,
            IRepositoryWithEvents<IntegrationAccountInfo> integrationAccInfoRepo,
            IRepositoryWithEvents<Domain.Entities.Lead> leadRepository,
            IDapperRepository dapperRepository,
            IRepositoryWithEvents<GoogleAdsInfo> googleAdsInfos,
            IGoogleAdsService googleAdsService,
            IRepositoryWithEvents<GoogleAdsAuthResponse> googleAdsAuthResponse)
        {
            _fbPageRepo = fbPageRepo;
            _integrationRepo = integrationRepo;
            _leadRepo = leadRepo;
            _currentUser = currentUser;
            _integrationAccInfoRepo = integrationAccInfoRepo;
            _leadRepository = leadRepository;
            _dapperRepository = dapperRepository;
            _googleAdsInfos = googleAdsInfos;
            _googleAdsService = googleAdsService;
            _googleAdsAuthResponse = googleAdsAuthResponse;

        }

        public async Task<Response<List<GoogleCampaignAdMetricsDto>>> Handle(GetGoogleCampaignAdMetricsRequest request, CancellationToken cancellationToken)
        {
            /*     var config = new GoogleAdsConfig()
                 {
                     DeveloperToken = "MbxO3SRTUqNOgk24CR-RFw",
                     OAuth2ClientId = "1082815071055-s58t5qb27nv20pidohtm17ldgig0h2c4.apps.googleusercontent.com",
                     OAuth2ClientSecret = "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",
                     OAuth2RefreshToken = "1//0gUIuAss0Cn-DCgYIARAAGBASNwF-L9IrZY4sQczEL2zEGla86zm3qaWpAdhzzg_RJzjvC_Zh5vRW3nm-u9FYxdv0x5ohIfPBdw0",
                     LoginCustomerId = "**********"
                 };*/
            var authResponse = await _googleAdsAuthResponse.FirstOrDefaultAsync(new GetGoogleAdsAuthResponseByIdSpec(_googleAdsService.LoginCustomerId ?? string.Empty));
            if (authResponse == null)
            {
                throw new NotFoundException("No Google Ads account found for this user.");
            }
            var config = new GoogleAdsConfig()
            {
                DeveloperToken = _googleAdsService.DeveloperToken,
                OAuth2ClientId = _googleAdsService.ClientId,
                OAuth2ClientSecret = _googleAdsService.ClientSecret,
                OAuth2RefreshToken = authResponse.RefreshToken,
                LoginCustomerId = _googleAdsService.LoginCustomerId
            };
            var tenantId = _currentUser.GetTenant();
            GoogleAdsClient client = new(config);
            var customerService = client.GetService(Services.V18.CustomerService);
            var service = client.GetService(Services.V18.GoogleAdsService);
            var customerIds = await _googleAdsInfos.ListAsync(new GetCustomerIdsByCampaignsSpecs(request.CampaignIds ?? null));
            var distinctCustomerIds = customerIds
    .Where(x => x.CustomerId != null)
    .Select(x => x.CustomerId.ToString())
    .Distinct()
    .ToList();
            var from = request.FromDate?.ToString("yyyy-MM-dd") ?? DateTime.UtcNow.AddDays(-7).ToString("yyyy-MM-dd");
            var to = request.ToDate?.ToString("yyyy-MM-dd") ?? DateTime.UtcNow.ToString("yyyy-MM-dd");
            string campaignFilter = (request.CampaignIds != null && request.CampaignIds.Any()) ? $"AND campaign.id IN ({string.Join(",", request.CampaignIds.Select(id => id.Trim()))})" : "";
            string query = $@"
                        SELECT
                          campaign.id,
                          campaign.name,
                          metrics.cost_micros,
                          metrics.conversions,
                          metrics.conversions_value,
                          metrics.clicks
                        FROM campaign
                        WHERE
                        segments.date BETWEEN '{from}' AND '{to}'
                        {campaignFilter}";
            var metricsList = new List<GoogleCampaignAdMetricsDto>();
                var revenueResults = (await _dapperRepository.QueryStoredProcedureFromReadReplicaAsync<GoogleAdRevenueResult>(
                    "LeadratBlack", "GetGoogleAdsAdRevenueByCampaigns",
                    new { p_tenantid = tenantId, p_campaignids = request.CampaignIds?.ConvertAll<string>(i => i.Replace(" ", "").ToLower()), })).ToList();
            foreach (var customerId in distinctCustomerIds)
            {
                var searchRequest = new SearchGoogleAdsRequest
                {
                    CustomerId = customerId,
                    Query = query
                };

                var response = service.SearchAsync(searchRequest);
                await foreach (var row in response)
                {
                    decimal spend = row.Metrics?.CostMicros != null ? row.Metrics.CostMicros / 1_000_000m : 0m;
                    double leads = row.Metrics?.Conversions ?? 0;

                    double? cpl = leads > 0 ? (double)spend / leads : null;

                    var revenue = revenueResults.FirstOrDefault(r => r.CampaignId == row.Campaign?.Id.ToString())?.TotalRevenue ?? 0;
                    var investment = row.Metrics?.CostMicros / 1_000_000m ?? 0;

                    decimal? roi = null;

                    if (revenue > 0 && investment > 0)
                    {
                        roi = Math.Round(((revenue - investment) / investment) * 100m, 2);
                    }


                    var dto = new GoogleCampaignAdMetricsDto
                    {
                        CampaignId = row.Campaign?.Id ?? 0,
                        CampaignName = row.Campaign?.Name ?? "N/A",
                        AdId = row.AdGroupAd?.Ad?.Id ?? 0,
                        Budget = row.CampaignBudget?.AmountMicros != null ? row.CampaignBudget.AmountMicros / 1_000_000m : 0m,
                        Cost = row.Metrics?.CostMicros != null ? row.Metrics.CostMicros / 1_000_000m : 0m,
                        LeadCount = row.Metrics?.Conversions ?? 0,
                        ConversionsValue = row.Metrics?.ConversionsValue ?? 0,
                        Clicks = row.Metrics?.Clicks ?? 0,
                        AverageCpc = row.Metrics?.AverageCpc / 1_000_000 ?? 0,
                        CostPerLead = cpl.HasValue ? Math.Round((decimal)cpl.Value, 2) : null,
                        TotalRevenue = revenue,
                        RoiPercentage = roi > 0 ? Math.Round(roi.Value, 2) : 0
                    };

                    metricsList.Add(dto);
                }
            }

            return new Response<List<GoogleCampaignAdMetricsDto>>
            {
                Data = metricsList,
                Message = "Metrics fetched successfully"
            };
        }
    }
    public class GoogleCampaignAdMetricsDto
    {
        public long CampaignId { get; set; }
        public string CampaignName { get; set; } = string.Empty;

        public long AdId { get; set; }

        public decimal Budget { get; set; }
        public decimal Cost { get; set; }
        public double LeadCount { get; set; }
        public double ConversionsValue { get; set; }
        public double Clicks { get; set; }
        public double AverageCpc { get; set; }
        public decimal? CostPerLead { get; set; }
        public decimal? RoiPercentage { get; set; }
        public decimal? TotalRevenue { get; set; }
        public string? Status { get; set; }
    }
    public class GoogleAdRevenueResult
    {
        public string CampaignId { get; set; }
        public decimal TotalRevenue { get; set; }
    }

}
