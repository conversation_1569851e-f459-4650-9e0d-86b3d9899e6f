﻿using Lrb.Application.Automation.Dtos;
using Lrb.Application.Automation.Helpers;
using Lrb.Application.Common.Atomation;
using Lrb.Application.Common.GoogleAd;
using Lrb.Application.Common.LeadRotation;
using Lrb.Application.Common.PushNotification;
using Lrb.Application.CustomStatus.Web;
using Lrb.Application.GlobalSettings.Web;
using Lrb.Application.Identity.Users;
using Lrb.Application.Integration.Web.Helpers;
using Lrb.Application.Lead.Web;
using Lrb.Application.Lead.Web.Dtos;
using Lrb.Application.Lead.Web.Mappings;
using Lrb.Application.Lead.Web.Specs;
using Lrb.Application.Project.Web.Specs;
using Lrb.Application.TempProject.Specs;
using Lrb.Domain.Entities;
using Lrb.Domain.Entities.ErrorModule;
using Lrb.Domain.Entities.Integration;
using Lrb.Domain.Entities.MasterData;
using Newtonsoft.Json;
using Serilog;
using static Lrb.Application.CustomMasterLeadSubStatus.Web.Request.MasterLeadSubStatusByLevelAndBaseIdSpec;

namespace Lrb.Application.Integration.Web.Requests
{
    public class ProcessGoogleAdWebhookRequest : IRequest<Response<bool>>
    {
        public GoogleAdWebhookDto? Dto { get; set; }
        public string? ApiKey { get; set; }
    }
    public class ProcessGoogleAdWebhookRequestHandler : IRequestHandler<ProcessGoogleAdWebhookRequest, Response<bool>>
    {
        private readonly ICurrentUser _currentUser;
        //private readonly IRepositoryWithEvents<MasterLeadStatus> _leadStatusRepo;
        private readonly IRepositoryWithEvents<Domain.Entities.Lead> _leadRepo;
        private readonly ILogger _logger;
        private readonly IRepositoryWithEvents<LeadHistory> _leadHistoryRepo;
        private readonly IRepositoryWithEvents<GoogleAdLeadFormData> _googleLeadFormRepo;
        private readonly IRepositoryWithEvents<IntegrationAccountInfo> _integrationAccInfoRepo;
        private readonly INpgsqlRepository _npgsqlRepo;
        private readonly IRepositoryWithEvents<Lrb.Domain.Entities.Project> _projectRepo;
        private readonly IUserService _userService;
        private readonly IRepositoryWithEvents<Domain.Entities.UserDetails> _userDetailsRepo;
        private readonly INotificationSenderService _notificationSenderService;
        private readonly IRepositoryWithEvents<DuplicateLeadFeatureInfo> _duplicateInfoRepo;
        private readonly IRepositoryWithEvents<IntegrationAssignment> _integrationAssignmentRepo;
        private readonly IRepositoryWithEvents<AssignmentModule> _assignmentModuleRepo;
        private readonly IRepositoryWithEvents<UserAssignment> _userAssignmentRepo;
        private readonly ILeadRepositoryAsync _leadRepositoryAsync;
        private bool _isDupicateUnassigned = false;
        private readonly IRepositoryWithEvents<CustomMasterLeadStatus> _customLeadStatusRepo;
        private readonly IRepositoryWithEvents<Address> _addressRepo;
        private readonly IRepositoryWithEvents<Domain.Entities.GlobalSettings> _globalSettingsRepo;
        private readonly IMediator _mediator;
        private readonly ILeadRotationService _leadRotationService;
        private readonly IRepositoryWithEvents<LeadAssignment> _leadAssignmentRepo;
        private readonly IRepositoryWithEvents<Domain.Entities.LeadEnquiry> _leadEnquiryRepo;
        private readonly IUserAssignmentMetricsService _userAssignmentMetricsService;
        public ProcessGoogleAdWebhookRequestHandler(ICurrentUser currentUser,
            IRepositoryWithEvents<Domain.Entities.Lead> leadRepo,
            //IRepositoryWithEvents<MasterLeadStatus> leadStatusRepo,
            ILogger logger,
            IRepositoryWithEvents<LeadHistory> leadHistoryRepo,
            IRepositoryWithEvents<GoogleAdLeadFormData> googleLeadFormRepo,
            IRepositoryWithEvents<IntegrationAccountInfo> integrationAccInfoRepo,
            IRepositoryWithEvents<Lrb.Domain.Entities.Project> projectRepo,
            INpgsqlRepository npgsqlRepo,
            IUserService userService,
            IRepositoryWithEvents<Domain.Entities.UserDetails> userDetailsRepo,
            INotificationSenderService notificationSenderService,
            IRepositoryWithEvents<DuplicateLeadFeatureInfo> duplicateInfoRepo,
            IRepositoryWithEvents<IntegrationAssignment> integrationAssignmentRepo,
            IRepositoryWithEvents<AssignmentModule> assignmentModuleRepo,
            IRepositoryWithEvents<UserAssignment> userAssignmentRepo,
            ILeadRepositoryAsync leadRepositoryAsync,
            IRepositoryWithEvents<CustomMasterLeadStatus> customLeadStatusRepo,
            IRepositoryWithEvents<Address> addressRepo,
            IRepositoryWithEvents<Domain.Entities.GlobalSettings> globalSettingsRepo,
            IMediator mediator,
            ILeadRotationService leadRotationService,
            IRepositoryWithEvents<LeadEnquiry> leadEnquiryRepo,
            IRepositoryWithEvents<LeadAssignment> leadAssignmentRepo,
            IUserAssignmentMetricsService userAssignmentMetricsService)
        {
            _currentUser = currentUser;
            _leadRepo = leadRepo;
            //_leadStatusRepo = leadStatusRepo;
            _leadHistoryRepo = leadHistoryRepo;
            _logger = logger;
            _googleLeadFormRepo = googleLeadFormRepo;
            _integrationAccInfoRepo = integrationAccInfoRepo;
            _npgsqlRepo = npgsqlRepo;
            _projectRepo = projectRepo;
            _userService = userService;
            _userDetailsRepo = userDetailsRepo;
            _notificationSenderService = notificationSenderService;
            _duplicateInfoRepo = duplicateInfoRepo;
            _integrationAssignmentRepo = integrationAssignmentRepo;
            _assignmentModuleRepo = assignmentModuleRepo;
            _userAssignmentRepo = userAssignmentRepo;
            _leadRepositoryAsync = leadRepositoryAsync;
            _customLeadStatusRepo = customLeadStatusRepo;
            _addressRepo = addressRepo;
            _globalSettingsRepo = globalSettingsRepo;
            _mediator = mediator;
            _leadRotationService = leadRotationService;
            _leadEnquiryRepo = leadEnquiryRepo;
            _leadAssignmentRepo = leadAssignmentRepo;
            _userAssignmentMetricsService = userAssignmentMetricsService;
        }
        public async Task<Response<bool>> Handle(ProcessGoogleAdWebhookRequest request, CancellationToken cancellationToken)
        {
            try
            {
                Domain.Entities.GlobalSettings? globalSettings = await _globalSettingsRepo.FirstOrDefaultAsync(new GetGlobalSettingsSpec(), cancellationToken);
                var GoogleAdId = AccountIdHelper.GetAccountId(request.Dto.google_key);
                var integrationAccountInfo = (await _integrationAccInfoRepo.ListAsync(new IntegrationAccInfoByGoogleAdIdOrId(GoogleAdId), cancellationToken)).FirstOrDefault();
                _logger.Information("Started processing googleAd webhook request, Dto: " + JsonConvert.SerializeObject(request.Dto));
                var lead = request.Dto.MapToLead();
                if (lead != null)
                {
                    var countryCode = globalSettings?.Countries?.FirstOrDefault()?.DefaultCallingCode ?? "+91";
                    lead.ContactNo = ListingSitesHelper.ConcatenatePhoneNumberV2(countryCode, lead.ContactNo, globalSettings, integrationAccountInfo?.CountryCode ?? string.Empty);
                    if (string.IsNullOrWhiteSpace(lead.ContactNo?.Trim()))
                    {
                        throw new Exception("ContactNumber  cannot be null or empty");
                    }
                    List<Domain.Entities.Lead> duplicateLeads = null;
                    var duplicateFeatureInfo = (await _duplicateInfoRepo.ListAsync(cancellationToken)).FirstOrDefault();
                    if (duplicateFeatureInfo != null && duplicateFeatureInfo.IsFeatureAdded)
                    {
                        if (!duplicateFeatureInfo.AllowAllDuplicates)
                        {
                            var duplicateLeadSpecDto = request.Adapt<DuplicateLeadSpecDto>();
                            duplicateLeadSpecDto.SubSource = integrationAccountInfo?.AccountName?.ToLower() ?? string.Empty;
                            duplicateLeads = await _leadRepo.ListAsync(new DuplicateFeatureSpec(duplicateFeatureInfo, duplicateLeadSpecDto), cancellationToken);
                        }
                    }
                    else
                    {
                        duplicateLeads ??= new();
                        var leadContacts = new List<string>() { (lead.ContactNo?.Length >= 1 ? lead.ContactNo : "invalid ContactNo") ?? "invalid ContactNo", (lead.AlternateContactNo?.Length >= 1 ? lead.AlternateContactNo : "invalid ContactNo") ?? "invalid ContactNo" };
                        var duplicateLead = await _leadRepo.FirstOrDefaultAsync(new LeadByContactNoSpec(leadContacts), cancellationToken);
                        if (duplicateLead != null)
                        {
                            duplicateLeads.Add(duplicateLead);
                        }
                    }
                    bool? duplicateCheckResponse;
                    if (duplicateLeads?.Any() ?? false)
                    {
                        var duplicateCheckRequest = lead.Adapt<CheckDuplicateLeadRequest>();
                        duplicateCheckRequest.ApiKey = request.ApiKey;
                        duplicateCheckResponse = (await _mediator.Send(duplicateCheckRequest)).Data;
                    }
                    if (!duplicateLeads?.Any() ?? true)
                    {
                        // var newStatus = (await _leadStatusRepo.ListAsync(cancellationToken)).Where(i => i.Status == "new");
                        var customStatus = await _customLeadStatusRepo.FirstOrDefaultAsync(new GetDefaultStatusSpec(), cancellationToken);
                        string name = lead?.Name?.Trim();
                        lead.LeadNumber = name[0].ToString().ToUpper() + new Random().Next(1000, 9999).ToString() + DateTime.UtcNow.ToString("FFFFFFF");
                        //lead.Status = newStatus?.FirstOrDefault();
                        lead.CustomLeadStatus = customStatus ?? (await _customLeadStatusRepo.FirstOrDefaultAsync(new MasterLeadSubStatusByNameSpec(new List<string>() { "new" }), cancellationToken));
                        lead.TagInfo = new();
                        lead.AgencyName = integrationAccountInfo?.AgencyName;
                        lead.Agencies = integrationAccountInfo?.Agency != null ? new List<Domain.Entities.Agency>() { integrationAccountInfo.Agency } : lead.Agencies;
                        if (lead?.Enquiries?.Any() ?? false)
                        {
                            lead.Enquiries.FirstOrDefault().SubSource = integrationAccountInfo?.AccountName?.ToLower();
                            if (string.IsNullOrWhiteSpace(lead.Enquiries?.FirstOrDefault()?.Currency ?? string.Empty))
                            {
                                lead.Enquiries.FirstOrDefault().Currency = globalSettings?.Countries?.FirstOrDefault()?.DefaultCurrency ?? "INR";
                            }
                        }
                        lead.AccountId = integrationAccountInfo == null ? Guid.Empty : integrationAccountInfo.Id;
                        if (integrationAccountInfo != null)
                        {
                            integrationAccountInfo.LeadCount++;
                            await _integrationAccInfoRepo.UpdateAsync(integrationAccountInfo);
                        }

                        IntegrationAssignment? integrationAssignmentDetails = null;
                        integrationAssignmentDetails = await IntegrationAssignmentHelper.GetIntegrationAssignmentDetails(integrationAccountInfo.Id, LeadSource.GoogleAds, _integrationAssignmentRepo, integrationAccRepo: _integrationAccInfoRepo);
                        //Add enquired location from assigned locaiton in the integration account.
                        if (integrationAssignmentDetails?.Location != null)
                        {
                            Address? address = null;
                            var primaryEnquiry = lead.Enquiries?.FirstOrDefault(i => i.IsPrimary);
                            if (primaryEnquiry != null)
                            {
                                var assignedLocation = integrationAssignmentDetails.Location;
                                var existingAddress = await _addressRepo.FirstOrDefaultAsync(new GetAddressByLocaitonIdSpec(assignedLocation.Id), cancellationToken);
                                if (existingAddress != null)
                                {
                                    address = existingAddress;
                                }
                                else
                                {
                                    address = assignedLocation.MapToAddress();
                                    address.Location = assignedLocation;
                                    await _addressRepo.AddAsync(address);
                                }
                                if (address != null)
                                {
                                    //primaryEnquiry.Address = address;
                                    primaryEnquiry.Addresses = new List<Address> { address };
                                }
                            }
                        }

                        #region Automation
                        (UserAssignment? UserAssignment, Lrb.Domain.Entities.Project? Project, int? Priority) userAssignmentAndProject = new();
                        var globalSetting = (await _globalSettingsRepo.ListAsync(new GetGlobalSettingsSpec(), cancellationToken)).FirstOrDefault();

                        var existingLead = await _leadRepo.FirstOrDefaultAsync(new GetRootLeadSpec(lead.ContactNo, lead.AlternateContactNo), cancellationToken);
                        UserDetailsDto? assignedUser = null;
                        if (existingLead != null && existingLead.AssignTo != Guid.Empty)
                        {
                            try
                            {
                                assignedUser = await _userService.GetAsync(existingLead?.AssignTo.ToString() ?? Guid.Empty.ToString(), cancellationToken);
                            }
                            catch (Exception ex)
                            {
                                throw ex;
                            }
                        }

                        if ((globalSetting?.IsStickyAgentEnabled ?? false) && existingLead != null && existingLead.AssignTo != default && assignedUser?.IsActive == true)
                        {
                            lead.AssignTo = existingLead.AssignTo;
                        }
                        else
                        {

                            var project = await _projectRepo.FirstOrDefaultAsync(new GetProjectByIdSpecs(lead.Projects?.FirstOrDefault(i => i != null)?.Id ?? Guid.Empty), cancellationToken);
                            userAssignmentAndProject = await IntegrationAssignmentHelper.GetMostPrioritizedUserAssignmentAndPriorityAsync(integrationAccountInfo.Id, LeadSource.GoogleAds, _integrationAssignmentRepo, _assignmentModuleRepo, globalSetting, integrationAccRepo: _integrationAccInfoRepo, projectWithAssignment: project);
                            List<Domain.Entities.Lead> existingLeads = await _leadRepo.ListAsync(new LeadByContactNoSpec(new List<string>() { lead.ContactNo ?? "Invalid Number" })) ?? new();
                            (Guid AssignTo, bool IsDupicateUnassigned) assignToRes = userAssignmentAndProject.UserAssignment != null ? await userAssignmentAndProject.UserAssignment.GetUserIdAsync(_userAssignmentRepo, _userDetailsRepo, _userService, existingLeads) : (Guid.Empty, false);
                            if (userAssignmentAndProject.UserAssignment?.CategoryType == AssignmentCategoryType.PercentageBased && integrationAccountInfo?.UserAssignment != null)
                            {
                                try
                                {
                                    integrationAccountInfo.UserAssignment.TotalLeadsCount = (integrationAccountInfo?.UserAssignment?.TotalLeadsCount ?? 0) + 1;
                                    var assignTo = await _userAssignmentMetricsService.DetermineUserAndSaveInfoAsync(integrationAccountInfo.Adapt<AccountInfoDto>());
                                    lead.AssignTo = lead.AssignTo == Guid.Empty ? assignTo ?? lead.AssignTo : lead.AssignTo;
                                }
                                catch (Exception ex) { }
                            }
                            else
                            {
                                var assignmentModules = (await _assignmentModuleRepo.ListAsync(default)).OrderBy(i => i.Priority).LastOrDefault();
                                if (assignToRes.AssignTo == Guid.Empty && userAssignmentAndProject.Priority < assignmentModules?.Priority)
                                {
                                    bool isAssigned = true;
                                    while (isAssigned)
                                    {
                                        userAssignmentAndProject = await IntegrationAssignmentHelper.GetMostPrioritizedUserAssignmentAndPriorityAsync(integrationAccountInfo.Id, LeadSource.GoogleAds, _integrationAssignmentRepo, _assignmentModuleRepo, globalSetting, integrationAccRepo: _integrationAccInfoRepo, projectWithAssignment: project, priority: userAssignmentAndProject.Priority);
                                        assignToRes = userAssignmentAndProject.UserAssignment != null ? await userAssignmentAndProject.UserAssignment?.GetUserIdAsync(_userAssignmentRepo, _userDetailsRepo, _userService, existingLeads) : (Guid.Empty, false);

                                        if (assignToRes.AssignTo != Guid.Empty)
                                        {
                                            isAssigned = false;
                                        }
                                        else if (assignToRes.AssignTo == Guid.Empty && userAssignmentAndProject.Priority < assignmentModules?.Priority && userAssignmentAndProject.Priority != null)
                                        {
                                            userAssignmentAndProject.Priority = userAssignmentAndProject.Priority;
                                        }
                                        else
                                        {
                                            isAssigned = false;
                                        }
                                    }
                                }

                                lead.AssignTo = assignToRes.AssignTo;
                            }

                            // Set OriginalOwner to the assigned user when first assigned
                            if (lead.AssignTo != Guid.Empty && lead.OriginalOwner == null)
                            {
                                lead.OriginalOwner = lead.AssignTo;
                            }

                            var contactWithCode = ListingSitesHelper.ConcatenatePhoneNumber(globalSettings?.Countries?.FirstOrDefault()?.DefaultCallingCode, lead.ContactNo);
                            if ((globalSetting?.IsDualOwnershipEnabled ?? false) && (userAssignmentAndProject.UserAssignment?.IsDualAssignmentEnabled ?? false))
                            {
                                (Guid AssignTo, bool IsDupicateUnassigned) secondaryAssignTo = userAssignmentAndProject.UserAssignment != null ? await userAssignmentAndProject.UserAssignment?.GetSecondaryUserIdAsync(_userAssignmentRepo, _userDetailsRepo, _userService, _leadRepo, lead, contactWithCode) : (Guid.Empty, false);
                                lead.SecondaryUserId = secondaryAssignTo.AssignTo;
                            }
                            _logger.Information("ProcessFacebookWebhookRequestHandler -> Mapped Lead after assignment : " + JsonConvert.SerializeObject(lead, new JsonSerializerSettings() { Formatting = Formatting.Indented, ReferenceLoopHandling = ReferenceLoopHandling.Ignore }));
                            _isDupicateUnassigned = assignToRes.IsDupicateUnassigned;
                        }
                        try
                        {
                            IntegrationAssignment integrationAssignment = await IntegrationAssignmentHelper.GetAssignedProjLocAsyncV1(source: LeadSource.GoogleAds, intgrAccId: integrationAccountInfo.Id, integrationAccRepo: _integrationAccInfoRepo);

                            // (Lrb.Domain.Entities.Project? AssignedProject, Location? AssignedLocation) = await IntegrationAssignmentHelper.GetAssignedProjLocAsync(source: LeadSource.GoogleAds, intgrAccId: integrationAccountInfo.Id, integrationAccRepo: _integrationAccInfoRepo);
                            var projectToAssign = userAssignmentAndProject.Project ?? integrationAssignment.Project;
                            if (lead.Projects != null && projectToAssign != null && projectToAssign?.IsArchived == false && projectToAssign?.IsDeleted == false)
                            {
                                lead.Projects.Add(projectToAssign);
                            }
                            else if (projectToAssign != null && projectToAssign?.IsArchived == false && projectToAssign?.IsDeleted == false)
                            {
                                lead.Projects ??= new List<Lrb.Domain.Entities.Project>() { projectToAssign };
                            }
                            if (lead.Agencies != null && integrationAssignment?.Agency != null && integrationAssignment?.Agency?.IsDeleted == false)
                            {
                                lead.Agencies.Add(integrationAssignment?.Agency);
                            }
                            else if (integrationAssignment?.Agency != null && integrationAssignment?.Agency?.IsDeleted == false)
                            {
                                lead.Agencies ??= new List<Lrb.Domain.Entities.Agency>() { integrationAssignment?.Agency };
                            }

                            if (lead.Campaigns != null && integrationAssignment?.Campaign != null && integrationAssignment?.Campaign?.IsDeleted == false)
                            {
                                lead.Campaigns.Add(integrationAssignment?.Campaign);
                            }
                            else if (integrationAssignment?.Campaign != null && integrationAssignment?.Campaign?.IsDeleted == false)
                            {
                                lead.Campaigns ??= new List<Lrb.Domain.Entities.Campaign>() { integrationAssignment?.Campaign };
                            }
                            if (lead.ChannelPartners != null && integrationAssignment?.ChannelPartner != null && integrationAssignment?.ChannelPartner?.IsDeleted == false)
                            {
                                lead.ChannelPartners.Add(integrationAssignment?.ChannelPartner);
                            }
                            else if (integrationAssignment?.ChannelPartner != null && integrationAssignment?.ChannelPartner?.IsDeleted == false)
                            {
                                lead.ChannelPartners ??= new List<Lrb.Domain.Entities.ChannelPartner>() { integrationAssignment?.ChannelPartner };
                            }

                            if (lead.Properties != null && integrationAssignment?.Property != null && integrationAssignment?.Property?.IsDeleted == false)
                            {
                                lead.Properties.Add(integrationAssignment?.Property);
                            }
                            else if (integrationAssignment?.Property != null && integrationAssignment?.Property?.IsDeleted == false && integrationAssignment?.Property?.IsArchived == false)
                            {
                                lead.Properties ??= new List<Lrb.Domain.Entities.Property>() { integrationAssignment?.Property };
                            }
                        }
                        catch
                        {
                        }
                        #endregion
                        if (lead.AssignTo != Guid.Empty)
                        {
                            lead.AssignDate = DateTime.UtcNow;
                        }
                        #region DuplicateDetails
                        var parentLead = await _leadRepo.FirstOrDefaultAsync(new GetRootLeadSpec(lead.ContactNo, lead.AlternateContactNo), cancellationToken);
                        if (parentLead != null)
                        {
                            lead = lead.AddDuplicateDetail(parentLead.ChildLeadsCount, parentLead.Id);
                            parentLead.ChildLeadsCount += 1;
                            try
                            {
                                await _leadRepo.UpdateAsync(parentLead);
                            }
                            catch (Exception ex)
                            {
                            }

                        }
                        #endregion
                        try
                        {
                            await _leadRepo.AddAsync(lead);
                        }
                        catch (Exception ex)
                        {
                            throw ex;
                        }
                        #region CreateDuplicateLead
                        var totalLeadsCount = 0;
                        var mobileWithCode = ListingSitesHelper.ConcatenatePhoneNumber(globalSettings?.Countries?.FirstOrDefault()?.DefaultCallingCode, lead.ContactNo);
                        try
                        {
                            var project = await _projectRepo.FirstOrDefaultAsync(new GetProjectByIdSpecs(lead.Projects?.FirstOrDefault(i => i != null)?.Id ?? Guid.Empty), cancellationToken);
                            userAssignmentAndProject = await IntegrationAssignmentHelper.GetMostPrioritizedUserAssignmentAndPriorityAsync(integrationAccountInfo.Id, LeadSource.GoogleAds, _integrationAssignmentRepo, _assignmentModuleRepo, globalSetting, integrationAccRepo: _integrationAccInfoRepo, projectWithAssignment: project);
                            if ((duplicateFeatureInfo?.IsFeatureAdded ?? false) && (!globalSetting?.IsStickyAgentEnabled ?? false) && (userAssignmentAndProject.UserAssignment?.IsDuplicateAssignmentEnabled ?? false))
                            {
                                var duplicateLeadAssignmentsIds = userAssignmentAndProject.UserAssignment?.DuplicateUserIds != null ? await userAssignmentAndProject.UserAssignment?.GetUserIdListAsync(_userAssignmentRepo, _userDetailsRepo, _userService, lead) : (new List<Guid>());
                                if (duplicateLeadAssignmentsIds?.Any() ?? false)
                                {
                                    if (userAssignmentAndProject.UserAssignment?.ShouldCreateMultipleDuplicates ?? false)
                                    {
                                        totalLeadsCount = await DuplicateLeadHelper.CreateDuplicateLeadsAsync(lead, lead.Id, _leadRepo, _leadEnquiryRepo, _leadRepositoryAsync, duplicateLeadAssignmentsIds, cancellationToken: cancellationToken, mobileWithCode);
                                    }
                                    else
                                    {
                                        totalLeadsCount = await DuplicateLeadHelper.CreateDuplicateLeadAsync(userAssignmentAndProject.UserAssignment, lead, lead.Id, _leadRepo, _leadEnquiryRepo, _leadRepositoryAsync, duplicateLeadAssignmentsIds, cancellationToken: cancellationToken, mobileWithCode);
                                        await _userAssignmentRepo.UpdateAsync(userAssignmentAndProject.UserAssignment);
                                    }
                                }

                            }
                        }
                        catch (Exception ex) { }
                        try
                        {
                            if ((duplicateFeatureInfo?.IsFeatureAdded ?? false) && (!globalSetting?.IsStickyAgentEnabled ?? true) && (globalSetting?.IsDualOwnershipEnabled ?? false) && (userAssignmentAndProject.UserAssignment?.IsDualAssignmentEnabled ?? false))
                            {
                                var replicatedLeads = await _leadRepo.ListAsync(new GetDuplicateLeadSpec(lead.Id), cancellationToken);
                                try
                                {
                                    if (replicatedLeads?.Any() ?? false && userAssignmentAndProject.UserAssignment != null)
                                    {
                                        await UserAssignmentHelper.AssignSecondaryUserIdsToDuplicateLeadsAsync(userAssignmentAndProject.UserAssignment, _userAssignmentRepo, _userDetailsRepo, _userService, _leadRepo, replicatedLeads, mobileWithCode);
                                        await _leadRepo.UpdateRangeAsync(replicatedLeads);
                                    }
                                }
                                catch (Exception ex)
                                {
                                    var error = new LrbError()
                                    {
                                        ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                                        ErrorSource = ex?.Source,
                                        StackTrace = ex?.StackTrace,
                                        InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                                        ErrorModule = "ProcessGoogleAdWebhookRequestHandler -> Handle() -> AddAsync()"
                                    };
                                    await _leadRepositoryAsync.AddErrorAsync(error);
                                }
                            }
                        }
                        catch (Exception ex) { }
                        #endregion
                        if (integrationAccountInfo != null)
                        {
                            integrationAccountInfo.TotalLeadCount = integrationAccountInfo?.TotalLeadCount + totalLeadsCount + 1;
                            await _integrationAccInfoRepo.UpdateAsync(integrationAccountInfo);
                        }
                        var googleFormDto = request.Dto.Adapt<GoogleAdLeadFormData>();
                        try
                        {
                            googleFormDto.LeadId = lead.Id;
                            await _googleLeadFormRepo.AddAsync(googleFormDto);
                        }
                        catch (Exception e)
                        {

                        }
                        var leadDto = lead.Adapt<ViewLeadDto>();
                        await leadDto.SetUsersInViewLeadDtoAsync(_userService, cancellationToken);
                        var leadHsitory = LeadHistoryHelper.LeadHistoryMapper(leadDto);
                        try
                        {
                            await _leadHistoryRepo.AddAsync(leadHsitory);
                        }
                        catch { }
                        #region DuplicateLead History
                        try
                        {
                            if ((duplicateFeatureInfo?.IsFeatureAdded ?? false) && (!globalSetting?.IsStickyAgentEnabled ?? true) && (userAssignmentAndProject.UserAssignment?.IsDuplicateAssignmentEnabled ?? false))
                            {
                                var totalDuplicateLeads = await _leadRepo.ListAsync(new GetDuplicateLeadSpec(lead.Id), cancellationToken);
                                if (totalDuplicateLeads?.Any() ?? false)
                                {
                                    await DuplicateLeadHelper.CreateDuplicateLeadHistoryAsync(totalDuplicateLeads, _leadHistoryRepo, _leadRepositoryAsync, _userService, cancellationToken);
                                }
                            }

                        }
                        catch (Exception ex) { }
                        #endregion

                        #region Assignment History
                        try
                        {
                            if (lead.AssignTo != Guid.Empty)
                            {
                                await ListingSitesHelper.CreateLeadAssignmentHistory(lead, _leadAssignmentRepo, cancellationToken);
                            }
                        }
                        catch (Exception ex) { }

                        #endregion

                        #region Push Notification
                        try
                        {
                            //Domain.Entities.GlobalSettings? globalSettings = await _globalSettingsRepo.FirstOrDefaultAsync(new GetGlobalSettingsSpec(), cancellationToken);
                            NotificationSettings? notificationSettings = JsonConvert.DeserializeObject<NotificationSettings>(globalSettings?.NotificationSettings ?? string.Empty);
                            List<string> notificationResponses = new();
                            string? tenantId = await _npgsqlRepo.GetTenantId(integrationAccountInfo?.Id ?? default);
                            List<Guid> adminIds = await _npgsqlRepo.GetAdminIdsAsync(tenantId ?? string.Empty);
                            if (lead.AssignTo == default || lead.AssignTo == Guid.Empty)
                            {
                                _logger.Information($"ProcessGoogleAdWebhookRequest -> tenantId : {tenantId} , adminIds : " + JsonConvert.SerializeObject(adminIds));
                                if (adminIds.Any())
                                {
                                    List<string> notificationSchduleResponse = new();
                                    if (_isDupicateUnassigned)
                                    {
                                        notificationSchduleResponse = await _notificationSenderService.ScheduleNotificationsAsync(Event.DuplicateUnAssigment, lead, null, null, null, null, null, adminIds);
                                    }
                                    else
                                    {
                                        notificationSchduleResponse = await _notificationSenderService.ScheduleNotificationsAsync(Event.LeadFromIntegration, lead, null, null, null, null, null, adminIds);
                                    }
                                    notificationResponses.AddRange(notificationSchduleResponse);
                                }
                            }
                            else if (lead.AssignTo != Guid.Empty)
                            {
                                var user = await _userService.GetAsync(lead.AssignTo.ToString(), cancellationToken);
                                if (user != null)
                                {
                                    List<string> notificationSchduleResponse = await _notificationSenderService.ScheduleNotificationsAsync(Event.LeadFromIntegration, lead, lead.AssignTo, user.FirstName + " " + user.LastName, topics: new List<string> { lead.CreatedBy.ToString(), lead.LastModifiedBy.ToString() });
                                    notificationResponses.AddRange(notificationSchduleResponse);
                                }
                                List<Guid> userWithManagerIds = new();
                                if (notificationSettings?.IsManagerEnabled ?? false)
                                {
                                    List<Guid> managerIds = await _npgsqlRepo.GetReportingManagerUserIdsAsync(new List<Guid> { lead.AssignTo });
                                    userWithManagerIds.AddRange(managerIds);
                                }
                                if (notificationSettings?.IsAdminEnabled ?? false)
                                {
                                    userWithManagerIds.AddRange(adminIds);
                                }
                                if (user != null && userWithManagerIds.Any())
                                {
                                    userWithManagerIds = userWithManagerIds.Distinct().ToList();
                                    userWithManagerIds.Remove(lead.AssignTo);
                                    List<string> notificationSchduleResponse = await _notificationSenderService.ScheduleNotificationsAsync(Domain.Enums.Event.NotifiyManagerAndAdmins, lead, null, null, topics: new List<string> { lead.CreatedBy.ToString(), lead.LastModifiedBy.ToString() }, userIds: userWithManagerIds);
                                    notificationResponses.AddRange(notificationSchduleResponse);
                                }
                            }
                            _logger.Information($"ProcessGoogleAdWebhookRequest -> NotificationSchedulingResponses JobIds : " + JsonConvert.SerializeObject(notificationResponses));
                            if ((duplicateFeatureInfo?.IsFeatureAdded ?? false) && (!globalSetting?.IsStickyAgentEnabled ?? true) && (userAssignmentAndProject.UserAssignment?.IsDuplicateAssignmentEnabled ?? false))
                            {
                                var allduplicateLeads = await _leadRepo.ListAsync(new GetDuplicateLeadSpec(lead.Id), cancellationToken);
                                if (allduplicateLeads?.Any() ?? false)
                                {
                                    foreach (var duplicatelead in allduplicateLeads)
                                    {
                                        try
                                        {
                                            if (duplicatelead.AssignTo != Guid.Empty && duplicatelead.AssignTo != null)
                                            {
                                                var user = await _userService.GetAsync(lead.AssignTo.ToString(), cancellationToken);
                                                if (user != null)
                                                {
                                                    List<string> notificationSchduleResponse = await _notificationSenderService.ScheduleNotificationsAsync(Domain.Enums.Event.LeadFromIntegration, duplicatelead, duplicatelead.AssignTo, user.FirstName + " " + user.LastName, topics: new List<string> { duplicatelead.CreatedBy.ToString(), lead.LastModifiedBy.ToString() });
                                                    notificationResponses.AddRange(notificationSchduleResponse);
                                                }
                                                List<Guid> userWithManagerIds = new();
                                                if (notificationSettings?.IsManagerEnabled ?? false)
                                                {
                                                    List<Guid> managerIds = await _npgsqlRepo.GetReportingManagerUserIdsAsync(new List<Guid> { duplicatelead.AssignTo });
                                                    userWithManagerIds.AddRange(managerIds);
                                                }
                                                if (notificationSettings?.IsAdminEnabled ?? false)
                                                {
                                                    userWithManagerIds.AddRange(adminIds);
                                                }
                                                if (user != null && userWithManagerIds.Any())
                                                {
                                                    userWithManagerIds = userWithManagerIds.Distinct().ToList();
                                                    userWithManagerIds.Remove(duplicatelead.AssignTo);
                                                    List<string> notificationSchduleResponse = await _notificationSenderService.ScheduleNotificationsAsync(Domain.Enums.Event.NotifiyManagerAndAdmins, duplicatelead, null, null, topics: new List<string> { duplicatelead.CreatedBy.ToString(), duplicatelead.LastModifiedBy.ToString() }, userIds: userWithManagerIds);
                                                    notificationResponses.AddRange(notificationSchduleResponse);
                                                }
                                            }
                                            _logger.Information($"ProcessGoogleAdWebhookRequest -> NotificationSchedulingResponses JobIds : " + JsonConvert.SerializeObject(notificationResponses));


                                        }
                                        catch (Exception ex)
                                        {
                                            _logger.Information($"ProcessGoogleAdWebhookRequest -> Exception -> PushNotification : " + JsonConvert.SerializeObject(ex, new JsonSerializerSettings() { Formatting = Formatting.Indented, ReferenceLoopHandling = ReferenceLoopHandling.Ignore }));
                                            var error = new LrbError()
                                            {
                                                ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                                                ErrorSource = ex?.Source,
                                                StackTrace = ex?.StackTrace,
                                                InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                                            };
                                            await _leadRepositoryAsync.AddErrorAsync(error);

                                        }
                                    }
                                }
                            }
                        }
                        catch (Exception ex)
                        {
                            _logger.Information($"ProcessGoogleAdWebhookRequest -> Exception -> PushNotification : " + JsonConvert.SerializeObject(ex, new JsonSerializerSettings() { Formatting = Formatting.Indented, ReferenceLoopHandling = ReferenceLoopHandling.Ignore }));
                        }
                        #endregion
                        try
                        {
                            #region Lead Rotation
                            if ((existingLead != null && existingLead.AssignTo == lead.AssignTo) && (globalSetting?.IsStickyAgentOverriddenEnabled ?? false) && (globalSetting?.IsLeadRotationEnabled ?? false))
                            {
                                if (lead.AssignTo != Guid.Empty && ((userAssignmentAndProject.UserAssignment != null) && userAssignmentAndProject.UserAssignment?.UserAssignmentType == UserAssignmentType.Team))
                                {
                                    await _leadRotationService.ScheduleTeamLeadRotation(lead.Id, accountId: integrationAccountInfo?.Id);
                                }
                            }
                            else if ((globalSetting != null && globalSetting.IsLeadRotationEnabled) && existingLead == null && ((userAssignmentAndProject.UserAssignment != null) && userAssignmentAndProject.UserAssignment?.UserAssignmentType == UserAssignmentType.Team))
                            {
                                if (lead.AssignTo != Guid.Empty)
                                {
                                    await _leadRotationService.ScheduleTeamLeadRotation(lead.Id, accountId: integrationAccountInfo?.Id);
                                }
                            }
                        }
                        catch (Exception ex) { }
                        #endregion
                    }
                }
                _logger.Information("Finished processing googleAd webhook request");
                return new(true, "Successful");
            }
            catch (Exception ex)
            {
                var error = new LrbError()
                {
                    ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                    ErrorSource = ex?.Source,
                    StackTrace = ex?.StackTrace,
                    InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                    ErrorModule = "ProcessGoogleAdWebhookRequestHandler -> Handle()"
                };
                await _leadRepositoryAsync.AddErrorAsync(error);
                _logger.Error("ProcessGoogleAdWebhookRequestHandler -> Error: " + JsonConvert.SerializeObject(ex));
                return new(JsonConvert.SerializeObject(ex));
            }

        }
    }
}
