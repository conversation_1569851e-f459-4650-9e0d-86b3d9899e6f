﻿using DocumentFormat.OpenXml.Drawing;
using DocumentFormat.OpenXml.Spreadsheet;
using Lrb.Application.Common.Facebook;
using Lrb.Application.Common.GoogleAds;
using Lrb.Application.Identity.Users;
using Lrb.Application.Integration.Web.Dtos;
using Lrb.Application.Integration.Web.Specs;
using Lrb.Domain.Entities;
using Lrb.Domain.Entities.Integration;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Lrb.Application.Integration.Web.Requests.Facebook
{
    public class GetAllFbFormsRequest :  PaginationFilter, IRequest<PagedResponse<FacebookLeadGenFormDto, string>>
    {
        public Guid AccountId { get; set; }
        public string? SearchText { get; set; }
    }
    public class GetAllFbFormsRequestHandler : F<PERSON>om<PERSON><PERSON><PERSON><PERSON>, IRequestHandler<GetAllFbFormsRequest, PagedResponse<FacebookLeadGenFormDto, string>>
    {
        private readonly IRepositoryWithEvents<IntegrationAssignmentInfo> _integrationAssignmentInforepo;
        private readonly IRepositoryWithEvents<Lrb.Domain.Entities.Project> _projectRepo;
        private readonly IUserService _userService;

        public GetAllFbFormsRequestHandler(
           IRepositoryWithEvents<FacebookAuthResponse> facebookAuthResponseRepo,
           IRepositoryWithEvents<FacebookConnectedPageAccount> facebookConnectedPageAccountRepo,
           IRepositoryWithEvents<FacebookLeadGenForm> facebookLeadGenFormRepo,
           IFacebookService facebookService,
           IJobService hangfireService,
           ITenantIndependentRepository repository,
           ICurrentUser currentUser,
           IRepositoryWithEvents<IntegrationAccountInfo> integrationAccInfoRepo,
           IRepositoryWithEvents<IntegrationAssignmentInfo> integrationAssignmentInforepo,
           IRepositoryWithEvents<FacebookAdsInfo> fbAdsRepo,
           IRepositoryWithEvents<Lrb.Domain.Entities.Project> projectRepo,
           IUserService userService,
           ILeadRepositoryAsync leadRepositoryAsync,
           IGoogleAdsService googleAdsService,
           IRepositoryWithEvents<GoogleAdsAuthResponse> googleAdsAuthResponseRepo, IRepositoryWithEvents<GoogleAdsInfo> googleAdsRepo,
           IRepositoryWithEvents<GoogleCampaign> googleCampaignsRepo)
           : base(facebookAuthResponseRepo,
                 facebookConnectedPageAccountRepo,
                 facebookLeadGenFormRepo,
                 facebookService,
                 hangfireService,
                 repository,
                 currentUser,
                 integrationAccInfoRepo,
                 fbAdsRepo,
                 leadRepositoryAsync, googleAdsService, googleAdsAuthResponseRepo, googleAdsRepo,
                 googleCampaignsRepo)
        {
            _integrationAssignmentInforepo = integrationAssignmentInforepo;
            _projectRepo = projectRepo;
            _userService = userService;
        }
        public async Task<PagedResponse<FacebookLeadGenFormDto, string>> Handle(GetAllFbFormsRequest request, CancellationToken cancellationToken)
        {
            var accounts = await _facebookAuthResponseRepo.ListAsync(new GetAllFacebookIntegrationAccountsSpec(request), cancellationToken);
            var existingFacebookConnectedPageAccounts = await _facebookConnectedPageAccountRepo.ListAsync(new GetFacebookConnectedPageAccountSpecV1(request.AccountId), cancellationToken);
            var forms = await _facebookLeadGenFormRepo.ListAsync(new GetAllFacebookFormsSpec(request), cancellationToken);
            var viewFacebookFormDataDtos = accounts.Adapt<List<ViewFacebookAccountWithFormDto>>();
            var allStoredAds = await _fbAdsRepo.ListAsync(new FacebookAdsByFbAccountIdSpec(viewFacebookFormDataDtos.Select(i => i.Id)?.ToList() ?? new()), cancellationToken);
            List<FacebookLeadGenFormDto> formDtos = new();
            var totalCount = await _facebookLeadGenFormRepo.CountAsync(new GetFacebookFormsCountByAccIdSpec(request.AccountId,request?.SearchText ?? string.Empty ), cancellationToken);
            if (forms?.Any() ?? false)
            {
                formDtos = forms.Adapt<List<FacebookLeadGenFormDto>>();
                var pageidsInAds = viewFacebookFormDataDtos.SelectMany(acc => acc.Ads ?? new List<FacebookAdsInfoDto>()) .Select(ad => ad.PageId).Where(id => !string.IsNullOrWhiteSpace(id)).ToList();
                foreach (var form in formDtos)
                {
                    form.PageName = existingFacebookConnectedPageAccounts
                        .FirstOrDefault(j => j.FacebookId == form.PageId)?.Name;
                    if (!pageidsInAds.Contains(form.PageId))
                    {
                        form.Name = $"{form.Name} [EXTERNAL FORM]";
                    }
                }
                
            }

            return new PagedResponse<FacebookLeadGenFormDto, string>(formDtos, totalCount);
        }

       
    }
}