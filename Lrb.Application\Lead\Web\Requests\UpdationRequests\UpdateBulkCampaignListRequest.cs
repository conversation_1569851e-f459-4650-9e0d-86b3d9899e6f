﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Lrb.Application.Lead.Web.Requests.UpdationRequests
{
    public class UpdateBulkCampaignListRequest : IRequest<Response<int>>
    {
        public List<string>? CampaignNames { get; set; }
        public List<Guid> Ids { get; set; } = default!;
        public bool? ShouldRemoveExistingCampaign { get; set; }
        public Guid? CurrentUserId { get; set; }
        public string? TenantId { get; set; }


    }
    public class UpdateBulkCampaignListRequestHandler : LeadCommonRequestHandler, IRequestHandler<UpdateBulkCampaignListRequest, Response<int>>
    {
        public UpdateBulkCampaignListRequestHandler(IServiceProvider serviceProvider) : base(serviceProvider, typeof(UpdateBulkCampaignListRequestHandler).Name, "Handle")
        {
        }
        public async Task<Response<int>> Handle(UpdateBulkCampaignListRequest request, CancellationToken cancellationToken)
        {
            try
            {
                List<Domain.Entities.Campaign> campaigns = await _campaignRepo.ListAsync(new GetAllCampaignsSpec(request?.CampaignNames?.ConvertAll(i => i.ToLower()) ?? new()), cancellationToken);

                List<Domain.Entities.Lead> existingLeads = await _leadRepo.ListAsync(new LeadByIdSpec(request?.Ids ?? new()), cancellationToken);

                if (campaigns.Count >= 0 && existingLeads.Count > 0)
                {
                    await UpdateCampaignForMultipleLeadsAsync(existingLeads, campaigns, request?.ShouldRemoveExistingCampaign ?? false, cancellationToken, request?.CurrentUserId, request?.TenantId);

                    return new(existingLeads.Count());
                }
                else
                {
                    return new(existingLeads.Count());
                }
            }
            catch (Exception ex)
            {
                await AddLrbErrorAsync(ex, $"{typeof(UpdateBulkCampaignListRequestHandler).Name} - Handle()");
                throw;
            }
        }
    }
}