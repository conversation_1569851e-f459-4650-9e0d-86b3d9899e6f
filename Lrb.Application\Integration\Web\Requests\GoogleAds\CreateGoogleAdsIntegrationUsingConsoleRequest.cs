﻿using Google.Ads.GoogleAds.Config;
using Lrb.Application.Common.AWS_Batch;
using Lrb.Application.Common.GoogleAds;
using Lrb.Application.Common.ServiceBus;
using Newtonsoft.Json;
using System.Text.Json;

namespace Lrb.Application.Integration.Web.Requests.GoogleAds
{
    public class CreateGoogleAdsIntegrationUsingConsoleRequest : IRequest<Response<bool>>
    {
        public string? AccessToken { get; set; } = default!;
        public string? RefreshToken { get; set; } = default!;
        public string? DeveloperToken { get; set; } = default!;
        public string? Email { get; set; } = default!;
        public string? CustomerId { get; set; } = default!;
        public string? LoginCustomerId { get; set; }
        public string? Code { get; set; } = default!;
    }
    public class CreateGoogleAdsIntegrationUsingConsoleRequestHandler : IRequestHandler<CreateGoogleAdsIntegrationUsingConsoleRequest, Response<bool>>
    {
        private readonly IAWSBatchService _batchService;
        private readonly ICurrentUser _currentUser;
        private readonly IServiceBus _serviceBus;
        private readonly IGoogleAdsService _GoogleAdsService;

        public CreateGoogleAdsIntegrationUsingConsoleRequestHandler(
            IAWSBatchService batchService,
            ICurrentUser currentUser,
            IServiceBus serviceBus,
            IGoogleAdsService GoogleAdsService)
        {
            _batchService = batchService;
            _currentUser = currentUser;
            _serviceBus = serviceBus;
            _GoogleAdsService = GoogleAdsService;
        }

        public async Task<Response<bool>> Handle(CreateGoogleAdsIntegrationUsingConsoleRequest request, CancellationToken cancellationToken)
        {
            var tenantId = _currentUser.GetTenant();
            request.DeveloperToken = _GoogleAdsService.DeveloperToken;
            request.CustomerId = request.LoginCustomerId;
            if (!string.IsNullOrEmpty(tenantId))
            {
                try
                {
                    var input = new Lead.Web.Export.InputPayload(
                        TrackerId: Guid.Empty,
                        TenantId: tenantId,
                        CurrentUserId: _currentUser.GetUserId(),
                        Type: "googleadslogin",
                        JsonData: JsonConvert.SerializeObject(request)
                    );

                    var stringArgument = JsonConvert.SerializeObject(input);
                    var cmdArgs = new List<string> { stringArgument };

                    await _serviceBus.RunExcelUploadJobAsync(cmdArgs);

                    return new(true, "Integration with Google Ads account has begun..., this may take a few minutes!");
                }
                catch (Exception ex)
                {
                    return new(false, "Invalid request!");
                }  
            }

            return new(false, "Invalid request!");
        }
    }
}
