﻿using Lrb.Application.Common.Interfaces;
using Lrb.Application.Common.Persistence;
using Lrb.Application.Property.Mobile.Dtos;
using Lrb.Application.Property.Mobile.Specs;
using Lrb.Application.Property.Web;
using Lrb.Domain.Entities.AmenitiesAttributes;
using Lrb.Domain.Entities.ErrorModule;
using Lrb.Domain.Entities.MasterData;
using Newtonsoft.Json;
using static Lrb.Application.Property.Mobile.Specs.V2PropertyByCustomFilterSpec;

namespace Lrb.Application.Property.Mobile.Requests
{
    public class V2GetAllPropertyRequest : PaginationFilter, IRequest<PagedResponse<V2GetAllPropertyDTO, string>>
    {
        public PropertyDimensionDto? PropertySize { get; set; }
        public EnquiryType? EnquiredFor { get; set; }
        public double? NoOfBHK { get; set; }
        public string? Ratings { get; set; }
        public PropertyStatus? PropertyStatus { get; set; }
        public List<Guid>? PropertyTypes { get; set; }
        public List<Guid>? PropertySubTypes { get; set; }
        public Guid? BasePropertyTypeId { get; set; }
        public Guid? SubPropertyTypeId { get; set; }
        //public List<PropertyPriceFilter>? PriceRange { get; set; }
        public string? PropertySearch { get; set; }
        public long? MinPrice { get; set; }
        public long? MaxPrice { get; set; }
        public long? MinBudget { get; set; }
        public long? MaxBudget { get; set; }
        public PropertyDateType? DateType { get; set; }
        public List<string>? Locations { get; set; }
        public List<string>? Cities { get; set; }
        public List<string>? States { get; set; }
        public List<Guid>? SubPropertyTypeIds { get; set; }
        public List<Guid>? Amenities { get; set; }
        public DateTime? FromDate { get; set; }
        public DateTime? ToDate { get; set; }
        public List<BHKType>? BHKTypes { get; set; }
        public List<string>? Projects { get; set; }
        public List<FurnishStatus>? FurnishStatuses { get; set; }
        public List<SaleType>? SaleTypes { get; set; }
        public string? OwnerName { get; set; }
        public string? PropertyTitle { get; set; }
        public DateTime? FromPossessionDate { get; set; }
        public DateTime? ToPossessionDate { get; set; }
        public Facing? Facing { get; set; }
        public List<int>? NoOfBathrooms { get; set; }
        public List<int>? NoOfLivingrooms { get; set; }
        public List<int>? NoOfBedrooms { get; set; }
        public List<int>? NoOfUtilites { get; set; }
        public List<int>? NoOfKitchens { get; set; }
        public List<int>? NoOfBalconies { get; set; }
        public List<int>? NoOfFloor { get; set; }
        public int? FloorNumber { get; set; }
        public List<string>? OwnerNames { get; set; }
        public List<Guid>? ListingOnBehalf { get; set; }

        public PossesionType? PossesionType { get; set; }
        public DateTime? FromPossesionDate { get; set; }
        public DateTime? ToPossesionDate { get; set; }
        public List<string>? NoOfFloors { get; set; }
        public List<int>? Parking { get; set; }
        public List<string>? Countries { get; set; }




    }
    public class V2GetAllPropertyRequestHandler : IRequestHandler<V2GetAllPropertyRequest, PagedResponse<V2GetAllPropertyDTO, string>>
    {
        private readonly IReadRepository<Domain.Entities.Property> _propertyRepo;
        private readonly IReadRepository<CustomMasterAttribute> _masterPropertyAttributeRepo;
        private readonly IReadRepository<CustomMasterAmenity> _masterPropertyAmenityRepo;
        private readonly IReadRepository<MasterPropertyType> _masterPropertyTypeRepo;
        private readonly IReadRepository<MasterAreaUnit> _masterAreaUnitRepo;
        private readonly IReadRepository<PropertyDimension> _propertyDimensionRepo;
        private readonly ILeadRepositoryAsync _leadRepositoryAsync;
        private readonly IDapperRepository _dapperRepository;
        private readonly ICurrentUser _currentUser;


        public V2GetAllPropertyRequestHandler(
            IReadRepository<Domain.Entities.Property> propertyRepo,
            IReadRepository<CustomMasterAttribute> masterPropertyAttributeRepo,
            IReadRepository<CustomMasterAmenity> masterPropertyAmenityRepo,
            IReadRepository<MasterPropertyType> masterPropertyTypeRepo,
            IReadRepository<MasterAreaUnit> masterAreaUnitRepo,
            IReadRepository<PropertyDimension> propertyDimensionRepo,
            ILeadRepositoryAsync leadRepositoryAsync, IDapperRepository dapperRepository, ICurrentUser currentUser
            )
        {
            _propertyRepo = propertyRepo;
            _masterPropertyAttributeRepo = masterPropertyAttributeRepo;
            _masterPropertyAmenityRepo = masterPropertyAmenityRepo;
            _masterPropertyTypeRepo = masterPropertyTypeRepo;
            _masterAreaUnitRepo = masterAreaUnitRepo;
            _propertyDimensionRepo = propertyDimensionRepo;
            _leadRepositoryAsync = leadRepositoryAsync;
            _dapperRepository = dapperRepository;
            _currentUser = currentUser;
        }

        public async Task<PagedResponse<V2GetAllPropertyDTO, string>> Handle(V2GetAllPropertyRequest request, CancellationToken cancellationToken)
        {
            CustomMasterAttribute? masterPropertyAttribute = new();
            CustomMasterAmenity? masterPropertyAmenity = new();
            MasterPropertyType? masterPropertyType = new();
            if (!string.IsNullOrWhiteSpace(request.PropertySearch))
            {
                masterPropertyAttribute = (await _masterPropertyAttributeRepo.ListAsync(new GetMasterPropertyAttributeSpec(request?.PropertySearch?.Trim().ToLower() ?? string.Empty), cancellationToken)).FirstOrDefault();
                masterPropertyAmenity = (await _masterPropertyAmenityRepo.ListAsync(new GetMasterAmenitySpec(request?.PropertySearch?.Trim().ToLower() ?? string.Empty), cancellationToken)).FirstOrDefault();
                masterPropertyType = (await _masterPropertyTypeRepo.ListAsync(new GetMasterPropertyTypeSpec(request?.PropertySearch?.Trim().ToLower() ?? string.Empty), cancellationToken)).FirstOrDefault();
            }

            List<Guid>? propertyDimensionIds = new();
            var tenantId = _currentUser.GetTenant();
            List<int> noOfAttributes = Enumerable.Range(1, 5).ToList();
            NumericAttributesDto1 numericAttributeDto = InitializationOfNumericAttributes(noOfAttributes, request);
            List<CustomPropertyAttributeDto> attributes = new();
            if (request?.NoOfFloors != null|| request?.NoOfKitchens != null || request?.NoOfUtilites != null || request?.NoOfBedrooms != null || request?.NoOfLivingrooms != null || request?.NoOfBalconies != null || request?.NoOfBathrooms != null || request?.Parking != null)
            {
                attributes = await _dapperRepository.GetAttributeDetails(tenantId ?? string.Empty);
            }
            var properties = await _propertyRepo.ListAsync(new V2PropertyByCustomFilterSpec(request, masterPropertyAttribute?.Id ?? null, masterPropertyAmenity?.Id ?? null, masterPropertyType?.Id ?? null, propertyDimensionIds, numericAttributeDto, attributes));
            var totalCount = await _propertyRepo.CountAsync(new V2PropertyCountByCustomFilterSpec(request, masterPropertyAttribute?.Id ?? null, masterPropertyAmenity?.Id ?? null, masterPropertyType?.Id ?? null, propertyDimensionIds, numericAttributeDto, attributes));
            List<V2GetAllPropertyDTO> getAllPropertyDTOs = new List<V2GetAllPropertyDTO>();
            try
            {
                getAllPropertyDTOs = properties.Adapt<List<V2GetAllPropertyDTO>>();
                //foreach (var prop in getAllPropertyDTOs)
                //{
                //    prop.Project = prop?.Projects?.LastOrDefault() ?? null;
                //}
            }
            catch (Exception ex)
            {
                var error = new LrbError()
                {
                    ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                    ErrorSource = ex?.Source,
                    StackTrace = ex?.StackTrace,
                    InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                    ErrorModule = "GetAllPropertyRequestHandler -> Handle()"
                };
                await _leadRepositoryAsync.AddErrorAsync(error);
            }
            List<V2GetAllPropertyDTO> resultDtos = getAllPropertyDTOs.Where(i => i.Status != PropertyStatus.Sold).ToList();
            resultDtos.AddRange(getAllPropertyDTOs.Where(i => i.Status == PropertyStatus.Sold).ToList());
            return new PagedResponse<V2GetAllPropertyDTO, string>(getAllPropertyDTOs, totalCount);
        }



        private NoOfAttributeFilterDto FilterNumericAttributesV1(List<string>? requestValues, List<int> noOfAttributes)
        {
            NoOfAttributeFilterDto noOfAttributesDto = new NoOfAttributeFilterDto();
            var noOfFloorFilterList = new List<int>();
            var selectedAttributes = new List<string>();
            if (requestValues != null)
            {
                if (requestValues.Contains("Ground Floor"))
                {
                    selectedAttributes.Add("Ground Floor");
                }
                if (requestValues.Contains("5"))
                {
                    noOfFloorFilterList = noOfAttributes.Where(i => !requestValues.Contains(i.ToString())).ToList();
                    selectedAttributes.AddRange(noOfFloorFilterList.Select(i => i.ToString()));
                    noOfAttributesDto.IsMaxValueIncluded = true;
                }
                else
                {
                    noOfFloorFilterList = noOfAttributes.Where(i => requestValues.Contains(i.ToString())).ToList();
                    selectedAttributes.AddRange(noOfFloorFilterList.Select(i => i.ToString()));
                    noOfAttributesDto.IsMaxValueIncluded = false;
                }
                noOfAttributesDto.NoOfAttributes = selectedAttributes;
            }

            return noOfAttributesDto;
        }

        private NoOfAttributeFilterDto FilterNumericAttributes(List<int>? requestValues, List<int> noOfAttributes)
        {
            NoOfAttributeFilterDto noOfAttributesDto = new NoOfAttributeFilterDto();
            var noOfFloorFilterList = new List<int>();

            if (requestValues != null)
            {
                if (requestValues.Contains(5))
                {
                    noOfFloorFilterList = noOfAttributes.Where(i => !requestValues.Contains(i)).ToList();
                    noOfAttributesDto.NoOfAttributes = noOfFloorFilterList.Select(i => i.ToString()).ToList();
                    noOfAttributesDto.IsMaxValueIncluded = true;
                }
                else
                {
                    noOfFloorFilterList = noOfAttributes.Where(i => requestValues.Contains(i)).ToList();
                    noOfAttributesDto.NoOfAttributes = noOfFloorFilterList.Select(i => i.ToString()).ToList();
                    noOfAttributesDto.IsMaxValueIncluded = false;
                }
            }

            return noOfAttributesDto;
        }

        private NumericAttributesDto1 InitializationOfNumericAttributes(List<int> noOfAttributes, V2GetAllPropertyRequest request)
        {
            return new NumericAttributesDto1
            {
                NoOfFloor = FilterNumericAttributes(request.NoOfFloor, noOfAttributes),
                NoOfBathrooms = FilterNumericAttributes(request.NoOfBathrooms, noOfAttributes),
                NoOfBedrooms = FilterNumericAttributes(request.NoOfBedrooms, noOfAttributes),
                NoOfKitchens = FilterNumericAttributes(request.NoOfKitchens, noOfAttributes),
                NoOfUtilites = FilterNumericAttributes(request.NoOfUtilites, noOfAttributes),
                NoOfLivingrooms = FilterNumericAttributes(request.NoOfLivingrooms, noOfAttributes),
                NoOfBalconies = FilterNumericAttributes(request.NoOfBalconies, noOfAttributes),
                NoOfFloors = FilterNumericAttributesV1(request.NoOfFloors, noOfAttributes),
                Parking = FilterNumericAttributes(request.Parking, noOfAttributes),



            };
        }
    }
    public enum PropertyDateType
    {
        None = 0,
        CreatedDate,
        ModifiedDate,
        PossessionDate
    }

    public static class PropertySearchHelper
    {
        public async static Task<float> GetConversionFactor(Guid areaUnitId, IReadRepository<MasterAreaUnit>? masterAreaUnitRepo)
        {
            if (masterAreaUnitRepo != null)
            {
                var masterAreaUnit = await masterAreaUnitRepo.GetByIdAsync(areaUnitId);
                if (masterAreaUnit != null)
                {
                    return masterAreaUnit.ConversionFactor;
                }
                return default;
            }
            return default;
        }
    }

    public class NumericAttributesDto1
    {
        public NoOfAttributeFilterDto? NoOfBathrooms { get; set; }
        public NoOfAttributeFilterDto? NoOfFloor { get; set; }
        public NoOfAttributeFilterDto? NoOfBedrooms { get; set; }
        public NoOfAttributeFilterDto? NoOfKitchens { get; set; }
        public NoOfAttributeFilterDto? NoOfUtilites { get; set; }
        public NoOfAttributeFilterDto? NoOfLivingrooms { get; set; }
        public NoOfAttributeFilterDto? NoOfBalconies { get; set; }
        public NoOfAttributeFilterDto? NoOfFloors { get; set; }
        public NoOfAttributeFilterDto? Parking { get; set; }




    }
    public class NoOfAttributeFilterDto
    {
        public List<string>? NoOfAttributes { get; set; }
        public bool IsMaxValueIncluded { get; set; }
    }
}

