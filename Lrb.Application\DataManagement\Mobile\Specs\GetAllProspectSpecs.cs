﻿using Lrb.Application.DataManagement.Mobile.Dtos;
using Lrb.Application.DataManagement.Mobile.Requests;
using Lrb.Application.Lead.Mobile.Dtos.v1;

namespace Lrb.Application.DataManagement.Mobile.Specs
{
    public class GetAllProspectSpecs : EntitiesByPaginationFilterSpec<Prospect>
    {
        public GetAllProspectSpecs(GetAllProspectRequest filter) : base(filter)
        {
            Query.Where(i => !i.IsDeleted)
                .Include(i => i.Address)
                .ThenInclude(i => i.Location)
                .ThenInclude(i => i.Zone)
                .ThenInclude(i => i.City)
                //.Include(i => i.Enquiries)
                //.ThenInclude(i => i.Address)
                .Include(i => i.Enquiries)
                .ThenInclude(i => i.Addresses)
                .ThenInclude(i => i.Location)
                .ThenInclude(i => i.Zone)
                .ThenInclude(i => i.City)
                .Include(i => i.Enquiries)
                .ThenInclude(i => i.PropertyType)
                .Include(i => i.Enquiries)
                .ThenInclude(i => i.Source)
                .Include(i => i.ChannelPartners)
                .Include(i => i.Projects)
                .Include(i => i.Properties)
                .Include(i => i.Status)
                .Include(i => i.Enquiries)
                .ThenInclude(i => i.PropertyTypes);
            //if (!string.IsNullOrWhiteSpace(filter.SearchByNameOrNumber))
            //{
            //    Query.Where(i => i.Name.ToLower().Trim().Replace(" ", "")
            //    .Contains(filter.SearchByNameOrNumber.ToLower().Trim().Replace(" ", ""))
            //    || i.ContactNo.Contains(filter.SearchByNameOrNumber));
            //}
        }
    }
    public class GetAllProspectsOfflineSpecs : EntitiesByPaginationFilterSpec<Prospect, GetAllOfflineProspectsDto>
    {
        public GetAllProspectsOfflineSpecs(GetAllProspectsOfflineRequest filter) : base(filter)
        {
            Query.Select(i => new GetAllOfflineProspectsDto
            {
                Id = i.Id,
                Name = i.Name,
                ContactNo = i.ContactNo,
                AlternateContactNo = i.AlternateContactNo,
                AssignTo = i.AssignTo,
                IsDeleted = i.IsDeleted,
                LastModifiedOn = i.LastModifiedOn,
                IsArchived = i.IsArchived
            });
            Query.OrderByDescending(i => i.LastModifiedOn);
            if (filter.SendOnlyAssignedLeads == true)
            {
                Query.Where(i => i.AssignTo == filter.UserId);
            }
        }
    }

    public class GetAllProspectsByLastModifiedRangeSpec : EntitiesByPaginationFilterSpec<Domain.Entities.Prospect, GetAllOfflineProspectsDto>
    {
        public GetAllProspectsByLastModifiedRangeSpec(GetAllProspectsByLastModifiedRequest filter) : base(filter)
        {

            if (filter.DateRangeFrom != null && filter.DateRangeTo != null)
            {
                Query.Where(i => i.LastModifiedOn >= filter.DateRangeFrom && i.LastModifiedOn <= filter.DateRangeTo);
            }
            if (filter.SendOnlyAssignedLeads == true)
            {
                Query.Where(i => i.AssignTo == filter.UserId);
            }
            if ((filter.DateRangeFrom != null && filter.DateRangeTo != null) ||(filter.SendOnlyAssignedLeads == true))
            {
                Query.Select(i => new GetAllOfflineProspectsDto
                {
                    Id = i.Id,
                    Name = i.Name,
                    ContactNo = i.ContactNo,
                    AlternateContactNo = i.AlternateContactNo,
                    AssignTo = i.AssignTo,
                    IsDeleted = i.IsDeleted,
                    LastModifiedOn = i.LastModifiedOn,
                    IsArchived = i.IsArchived
                });
            }
        }
    }
}
