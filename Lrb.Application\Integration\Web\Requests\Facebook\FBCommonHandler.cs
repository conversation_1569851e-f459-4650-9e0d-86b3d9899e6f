﻿using Azure.Core;
using Lrb.Application.Common.Facebook;
using Lrb.Application.Common.GoogleAds;
using Lrb.Application.Integration.Web.Specs;
using Lrb.Domain.Entities.ErrorModule;
using Lrb.Domain.Entities.Integration;
using Newtonsoft.Json;

namespace Lrb.Application.Integration.Web.Requests.Facebook
{
    public class FBCommonHandler
    {
        protected readonly IRepositoryWithEvents<FacebookAuthResponse> _facebookAuthResponseRepo;
        protected readonly IRepositoryWithEvents<FacebookConnectedPageAccount> _facebookConnectedPageAccountRepo;
        protected readonly IRepositoryWithEvents<FacebookLeadGenForm> _facebookLeadGenFormRepo;
        protected readonly IJobService _hangfireService;
        protected readonly IFacebookService _facebookService;
        protected readonly ICurrentUser _currentUser;
        protected readonly ITenantIndependentRepository _repository;
        protected readonly IRepositoryWithEvents<IntegrationAccountInfo> _integrationAccInfoRepo;
        protected readonly IRepositoryWithEvents<FacebookAdsInfo> _fbAdsRepo;
        private readonly ILeadRepositoryAsync _leadRepositoryAsync;
        protected readonly IGoogleAdsService _googleAdsService;
        protected readonly IRepositoryWithEvents<GoogleAdsAuthResponse> _googleAdsAuthResponseRepo;
        protected readonly IRepositoryWithEvents<GoogleAdsInfo> _googleAdsRepo;
        protected readonly IRepositoryWithEvents<GoogleCampaign> _googleCampaignsRepo;
        public FBCommonHandler(IRepositoryWithEvents<FacebookAuthResponse> facebookAuthResponseRepo, IRepositoryWithEvents<FacebookConnectedPageAccount> facebookConnectedPageAccountRepo, IRepositoryWithEvents<FacebookLeadGenForm> facebookLeadGenFormRepo, IFacebookService facebookService, IJobService hangfireService, ITenantIndependentRepository repository, ICurrentUser currentUser, IRepositoryWithEvents<IntegrationAccountInfo> integrationAccInfoRepo, IRepositoryWithEvents<FacebookAdsInfo> fbAdsRepo, ILeadRepositoryAsync leadRepositoryAsync, IGoogleAdsService googleAdsService, IRepositoryWithEvents<GoogleAdsAuthResponse> googleAdsAuthResponseRepo, IRepositoryWithEvents<GoogleAdsInfo> googleAdsRepo, IRepositoryWithEvents<GoogleCampaign> googleCampaignsRepo)
        {
            _facebookAuthResponseRepo = facebookAuthResponseRepo;
            _facebookConnectedPageAccountRepo = facebookConnectedPageAccountRepo;
            _facebookLeadGenFormRepo = facebookLeadGenFormRepo;
            _facebookService = facebookService;
            _hangfireService = hangfireService;
            _repository = repository;
            _currentUser = currentUser;
            _integrationAccInfoRepo = integrationAccInfoRepo;
            _fbAdsRepo = fbAdsRepo;
            _leadRepositoryAsync = leadRepositoryAsync;
            _googleAdsService = googleAdsService;
            _googleAdsAuthResponseRepo = googleAdsAuthResponseRepo;
            _googleAdsRepo = googleAdsRepo;
            _googleCampaignsRepo = googleCampaignsRepo;
        }

        protected bool IsValidRequest(FacebookIntegrationDto request)
        {
            return request != null &&
                   !string.IsNullOrEmpty(request.TenantInfoDto.Identifier) &&
                   !string.IsNullOrEmpty(request.AccessToken) &&
                   !string.IsNullOrEmpty(request.FacebookUserId);
        }

        protected async Task<bool> IsAccountExistsInDifferentTenant(FacebookIntegrationDto request)
        {
            bool isAccountExists = await _facebookService.IsFacebookAccountExists(request.FacebookUserId);
            if (isAccountExists)
            {
                var tId = await _facebookService.GetTenantIdByFacebookAccountId(request.FacebookUserId);
                if (tId != request.TenantInfoDto.Identifier) return true;
            }
            return false;
        }

        protected async Task<bool> IsAccountExistsInSameTenant(FacebookIntegrationDto request)
        {
            bool isAccountExists = await _facebookService.IsFacebookAccountExists(request.FacebookUserId);
            if (isAccountExists)
            {
                var tId = await _facebookService.GetTenantIdByFacebookAccountId(request.FacebookUserId);
                if (tId == request.TenantInfoDto.Identifier) return true;
            }
            return false;
        }

        protected async Task UpdateFacebookAuthResponse(FacebookIntegrationDto request)
        {
            var profileInfo = await _facebookService.GetFacebookAccountNameByIdAsync(request.FacebookUserId, request.AccessToken);
            if (profileInfo != null && !string.IsNullOrEmpty(profileInfo.name))
            {
                request.FacebookAccountName = profileInfo.name;
            }

            var existingAccount = (await _facebookAuthResponseRepo.ListAsync(new GetFacebookAuthResponseByFBIdSpec(request.FacebookUserId), CancellationToken.None)).FirstOrDefault();
            existingAccount.FacebookAccountName = profileInfo.name;
            existingAccount.AccessToken = request.AccessToken;
            existingAccount.LongLivedUserAccessToken = (await _facebookService.GetLongLivedTokenAsync(request.AccessToken)).access_token;

            await _facebookAuthResponseRepo.UpdateAsync(existingAccount, CancellationToken.None);
            await UpdateAdsDetailsInDB(existingAccount.LongLivedUserAccessToken, existingAccount.Id);
            await UpdateFacebookConnectedPagesAccount(existingAccount);
        }

        protected async Task UpdateFacebookConnectedPagesAccount(FacebookAuthResponse facebookAuthResponseObj)
        {
            if (facebookAuthResponseObj != null && facebookAuthResponseObj.Id != default)
            {
                // retrieve existing FacebookConnectedPageAccounts from database
                var existingFacebookConnectedPageAccounts = await _facebookConnectedPageAccountRepo.ListAsync(new GetFacebookConnectedPageAccountByFBAuthIdSpec(facebookAuthResponseObj.Id));
                // compare the existing records with the response received from Facebook API
                var fbConnectedPagesResponse = await _facebookService.FacebookConnectedPageAccountsAsync(facebookAuthResponseObj.LongLivedUserAccessToken);

                foreach (var responsePageAccount in fbConnectedPagesResponse)
                {
                    var existingPageAccount = existingFacebookConnectedPageAccounts.FirstOrDefault(x => x.FacebookId == responsePageAccount.id);
                    if (existingPageAccount == null)
                    {
                        // page account doesn't exist in the database, add it
                        await AddFacebookConnectedPageAccountToDB(responsePageAccount, facebookAuthResponseObj);
                    }
                    else
                    {
                        // page account exists in the database, update it
                        await UpdateFacebookConnectedPageAccountInDB(existingPageAccount, responsePageAccount, facebookAuthResponseObj);

                        // remove the existing page account from the list, so that it's not processed again
                        existingFacebookConnectedPageAccounts.Remove(existingPageAccount);
                    }
                }

                // remove the page accounts that don't exist in the response
                foreach (var existingPageAccount in existingFacebookConnectedPageAccounts)
                {
                    await RemoveFacebookConnectedPageAccountFromDB(existingPageAccount);
                }
            }
        }

        protected async Task CreateFacebookAuthResponse(FacebookIntegrationDto request)
        {
            //Create
            var facebookAuthResponse = request.Adapt<FacebookAuthResponse>();
            facebookAuthResponse.LongLivedUserAccessToken = (await _facebookService.GetLongLivedUserAccessTokenAsync(request.AccessToken))?.access_token ?? string.Empty;
            var profileInfo = await _facebookService.GetFacebookAccountNameByIdAsync(request.FacebookUserId, request.AccessToken);
            if (profileInfo != null && !string.IsNullOrEmpty(profileInfo.name))
            {
                facebookAuthResponse.FacebookAccountName = profileInfo.name;
            }
            var result = await _facebookAuthResponseRepo.AddAsync(facebookAuthResponse);
            if (result != null)
            {
                try
                {
                    var integrationAccInfo = new IntegrationAccountInfo()
                    {
                        Id = Guid.NewGuid(),
                        AccountName = profileInfo.name,
                        LeadSource = LeadSource.Facebook,
                        LicenseId = Guid.NewGuid(),
                        JsonTemplate = "",
                        FacebookAccountId = facebookAuthResponse.Id
                    };
                    //var existingAds = await _fbAdsRepo.ListAsync(new FacebookAdsByFbAccountidSpec(facebookAuthResponse.Id), CancellationToken.None);
                    await _integrationAccInfoRepo.AddAsync(integrationAccInfo);
                    await StoreAdsDetailsInDB(facebookAuthResponse.LongLivedUserAccessToken, facebookAuthResponse.Id);
                    await CreateFacebookConnectedPageAccounts(facebookAuthResponse);
                }
                catch (Exception ex)
                {
                    var error = new LrbError()
                    {
                        ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                        ErrorSource = ex?.Source,
                        StackTrace = ex?.StackTrace,
                        InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                        ErrorModule = "FBCommonHandler -> CreateFacebookAuthResponse()"
                    };
                    await _leadRepositoryAsync.AddErrorAsync(error);
                }

            }
        }

        protected async Task CreateFacebookConnectedPageAccounts(FacebookAuthResponse? facebookAuthResponseObj)
        {
            if (facebookAuthResponseObj != null && facebookAuthResponseObj.Id != default)
            {
                //get FacebookConnectedPageAccounts , account for pagination:todo
                var fbConnectedPagesResponse = await _facebookService.FacebookConnectedPageAccountsAsync(facebookAuthResponseObj.LongLivedUserAccessToken);

                if (fbConnectedPagesResponse != null && fbConnectedPagesResponse.Any())
                {
                    //save all pages
                    foreach (var page in fbConnectedPagesResponse)
                    {
                        await AddFacebookConnectedPageAccountToDB(page, facebookAuthResponseObj);
                    }
                }
            }
        }

        protected async Task AddFacebookConnectedPageAccountToDB(FbConnectedPageAccountData responsePageAccount, FacebookAuthResponse facebookAuthResponseObj)
        {
            try
            {
                var connectedPageEntity = responsePageAccount.Adapt<FacebookConnectedPageAccount>();
                connectedPageEntity.FacebookAuthResponseId = facebookAuthResponseObj.Id;
                connectedPageEntity.LongLivedPageAccessToken = (await _facebookService.GetLongLivedPageAccessToken(responsePageAccount.id, facebookAuthResponseObj.LongLivedUserAccessToken))?.access_token ?? string.Empty;

                var fbConnectedPageAccount = await _facebookConnectedPageAccountRepo.AddAsync(connectedPageEntity);

                if (fbConnectedPageAccount != null)
                {
                    bool subscriptionResponse = await _facebookService.CreateSuscriptionByAccountIdAsync(responsePageAccount.id, fbConnectedPageAccount.LongLivedPageAccessToken);

                    if (subscriptionResponse)
                    {
                        await CreateLeadgenForms(fbConnectedPageAccount);
                    }
                }
            }
            catch (Exception e)
            {
                Amazon.Runtime.Internal.Util.Logger.GetLogger(this.GetType()).Error(e, "");
                var error = new LrbError()
                {
                    ErrorMessage = e?.Message ?? e?.InnerException?.Message,
                    ErrorSource = e?.Source,
                    StackTrace = e?.StackTrace,
                    InnerException = JsonConvert.SerializeObject(e?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                    ErrorModule = "FBCommonHandler -> AddFacebookConnectedPageAccountToDB()"
                };
                await _leadRepositoryAsync.AddErrorAsync(error);
            }
        }

        protected async Task RemoveFacebookConnectedPageAccountFromDB(FacebookConnectedPageAccount connectedPageAccount)
        {
            await _facebookConnectedPageAccountRepo.DeleteAsync(connectedPageAccount);
        }

        protected async Task UpdateFacebookConnectedPageAccountInDB(FacebookConnectedPageAccount existingPageAccount, FbConnectedPageAccountData responsePageAccount, FacebookAuthResponse facebookAuthResponseObj)
        {
            try
            {
                existingPageAccount.LongLivedPageAccessToken = (await _facebookService.GetLongLivedPageAccessToken(responsePageAccount.id, facebookAuthResponseObj.LongLivedUserAccessToken))?.access_token ?? string.Empty;
                existingPageAccount.Name = responsePageAccount.name;

                await _facebookConnectedPageAccountRepo.UpdateAsync(existingPageAccount);
                bool subscriptionResponse = await _facebookService.CreateSuscriptionByAccountIdAsync(responsePageAccount.id, existingPageAccount.LongLivedPageAccessToken);
                if (subscriptionResponse)
                {
                    await UpdateLeadgenForms(existingPageAccount);
                }
            }
            catch (Exception e)
            {
                Amazon.Runtime.Internal.Util.Logger.GetLogger(this.GetType()).Error(e, "");
                var error = new LrbError()
                {
                    ErrorMessage = e?.Message ?? e?.InnerException?.Message,
                    ErrorSource = e?.Source,
                    StackTrace = e?.StackTrace,
                    InnerException = JsonConvert.SerializeObject(e?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                    ErrorModule = "FBCommonHandler -> UpdateFacebookConnectedPageAccountInDB()"
                };
                await _leadRepositoryAsync.AddErrorAsync(error);
            }
        }

        protected async Task CreateLeadgenForms(FacebookConnectedPageAccount fbConnectedPageAccount)
        {
            //get FacebookLeadGenForms by accounts, subscribe, save , get leadgenform fields
            //Note: Token is different

            FbLeadGenFormsInfoPaginatedResponse pagingObj = null;
            string afterToken = string.Empty;

            do
            {
                var leadGenFormsResponse = await _facebookService.GetFacebookLeadGenFormsAsync(fbConnectedPageAccount.FacebookId,
                    fbConnectedPageAccount.LongLivedPageAccessToken, fbConnectedPageAccount.PageToken, afterToken);

                if (leadGenFormsResponse != null && leadGenFormsResponse.data.Any())
                {
                    pagingObj = leadGenFormsResponse;
                    afterToken = leadGenFormsResponse.paging.cursors.after;
                    foreach (var form in leadGenFormsResponse.data)
                    {
                        try
                        {
                            var leadgenFromEntity = form.Adapt<FacebookLeadGenForm>();
                            leadgenFromEntity.FacebookConnectedPageAccountId = fbConnectedPageAccount.Id;
                            var leadgenFormInsertResponse = _facebookLeadGenFormRepo.AddAsync(leadgenFromEntity);
                        }
                        catch (Exception e)
                        {
                            Amazon.Runtime.Internal.Util.Logger.GetLogger(this.GetType()).Error(e, "");
                            var error = new LrbError()
                            {
                                ErrorMessage = e?.Message ?? e?.InnerException?.Message,
                                ErrorSource = e?.Source,
                                StackTrace = e?.StackTrace,
                                InnerException = JsonConvert.SerializeObject(e?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                                ErrorModule = "FBCommonHandler -> CreateLeadgenForms()"
                            };
                            await _leadRepositoryAsync.AddErrorAsync(error);
                        }
                    }
                }
                else
                {
                    break;
                }
            }
            while (pagingObj?.data.Count > 0);
        }

        protected async Task AddFacebookLeadGenFormToDB(FbLeadGenFormInfoData responseLeadGenForm, Guid fbConnectedPageAccountId)
        {
            var leadgenFromEntity = responseLeadGenForm.Adapt<FacebookLeadGenForm>();
            leadgenFromEntity.FacebookConnectedPageAccountId = fbConnectedPageAccountId;
            leadgenFromEntity.IsSubscribed = true;
            var leadgenFormInsertResponse = await _facebookLeadGenFormRepo.AddAsync(leadgenFromEntity);
        }

        protected async Task UpdateFacebookLeadGenFormInDB(FacebookLeadGenForm existingLeadGenForm, FbLeadGenFormInfoData responseLeadGenForm)
        {
            existingLeadGenForm.LeadsCount = responseLeadGenForm.leads_count;
            existingLeadGenForm.Name = responseLeadGenForm.name;
            existingLeadGenForm.FacebookId = responseLeadGenForm.id;
            existingLeadGenForm.PageId = responseLeadGenForm.page_id;
            existingLeadGenForm.Status = responseLeadGenForm.status;
            await _facebookLeadGenFormRepo.UpdateAsync(existingLeadGenForm);
        }

        protected async Task RemoveFacebookLeadGenFormFromDB(FacebookLeadGenForm leadGenForm)
        {
            await _facebookLeadGenFormRepo.DeleteAsync(leadGenForm);
        }

        protected async Task RemoveFacebookLeadGenFormsFromDB(List<FacebookLeadGenForm> leadGenForm)
        {
            await _facebookLeadGenFormRepo.DeleteRangeAsync(leadGenForm);
        }

        protected async Task UpdateLeadgenForms(FacebookConnectedPageAccount fbConnectedPageAccount)
        {
            // retrieve existing FacebookLeadGenForms from database
            var existingFacebookLeadGenForms = await _facebookLeadGenFormRepo.ListAsync(new FacebookLeadGenFormByConnectedAccIdSpec(fbConnectedPageAccount.Id));

            FbLeadGenFormsInfoPaginatedResponse pagingObj = null;
            string afterToken = string.Empty;

            do
            {
                var leadGenFormsResponse = await _facebookService.GetFacebookLeadGenFormsAsync(fbConnectedPageAccount.FacebookId,
                    fbConnectedPageAccount.LongLivedPageAccessToken, fbConnectedPageAccount.PageToken, afterToken);

                if (leadGenFormsResponse != null && leadGenFormsResponse.data.Any())
                {
                    pagingObj = leadGenFormsResponse;
                    afterToken = leadGenFormsResponse.paging.cursors.after;
                    foreach (var form in leadGenFormsResponse.data)
                    {
                        try
                        {
                            var existingLeadGenForm = existingFacebookLeadGenForms.FirstOrDefault(x => x.FacebookId == form.id);
                            if (existingLeadGenForm == null)
                            {
                                // lead gen form doesn't exist in the database, add it
                                await AddFacebookLeadGenFormToDB(form, fbConnectedPageAccount.Id);
                            }
                            else
                            {
                                // lead gen form exists in the database, update it
                                await UpdateFacebookLeadGenFormInDB(existingLeadGenForm, form);
                                // remove the existing lead gen form from the list, so that it's not processed again
                                existingFacebookLeadGenForms.Remove(existingLeadGenForm);
                            }
                        }
                        catch (Exception e)
                        {
                            Amazon.Runtime.Internal.Util.Logger.GetLogger(this.GetType()).Error(e, "");
                            var error = new LrbError()
                            {
                                ErrorMessage = e?.Message ?? e?.InnerException?.Message,
                                ErrorSource = e?.Source,
                                StackTrace = e?.StackTrace,
                                InnerException = JsonConvert.SerializeObject(e?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                                ErrorModule = "FBCommonHandler -> UpdateLeadgenForms()"
                            };
                            await _leadRepositoryAsync.AddErrorAsync(error);
                        }
                    }
                }
                else
                {
                    break;
                }
            }
            while (pagingObj?.data.Count > 0);

            // remove the lead gen forms that don't exist in the response
            await RemoveFacebookLeadGenFormsFromDB(existingFacebookLeadGenForms);
        }

        protected async Task StoreAdsDetailsInDB(string accessToken, Guid fbAccountId)
        {
            var activeAds = new List<FacebookAdDto?>();
            var existingAds = await _fbAdsRepo.ListAsync(new FacebookAdsByFbAccountIdSpec(fbAccountId), CancellationToken.None);
            await _fbAdsRepo.DeleteRangeAsync(existingAds);
            var adAccounts = await _facebookService.GetAllFacebookAdAccountsAsync(accessToken);
            if (adAccounts?.data.Any() ?? false)
            {
                foreach (var account in adAccounts.data)
                {
                    var ads = await _facebookService.GetAllFacebookAdsAsync(account.id, accessToken);
                    activeAds.AddRange(ads?.data ?? new());
                }
            }
            if (activeAds?.Any() ?? false)
            {
                List<FacebookAdsInfo> facebookAds = new();
                activeAds.ForEach(async i =>
                {
                    facebookAds.Add(new FacebookAdsInfo()
                    {
                        AdId = i?.id,
                        AdName = i?.name,
                        AdAccountId = i?.ad_account_id,
                        AdAccountName = i?.ad_account_name,
                        AdSetId = i?.adset_id,
                        AdSetName = i?.adset?.name,
                        CampaignId = i?.campaign_id,
                        CampaignName = i?.campaign?.name,
                        Status = i?.effective_status,
                        FacebookAuthResponseId = fbAccountId,
                        IsSubscribed = true,
                        PageId = i?.creative?.object_story_spec?.page_id
                    });
                });
                await _fbAdsRepo.AddRangeAsync(facebookAds);
            }
        }
        protected async Task<List<FacebookAdsInfo?>?> StoreAndReturnAdsDetailsInDB(string accessToken, Guid fbAccountId)
        {
            List<FacebookAdsInfo> facebookAds = new();
            var activeAds = new List<FacebookAdDto?>();
            var existingAds = await _fbAdsRepo.ListAsync(new FacebookAdsByFbAccountIdSpec(fbAccountId), CancellationToken.None);
            await _fbAdsRepo.DeleteRangeAsync(existingAds);
            var adAccounts = await _facebookService.GetAllFacebookAdAccountsAsync(accessToken);
            if (adAccounts?.data.Any() ?? false)
            {
                foreach (var account in adAccounts.data)
                {
                    var ads = await _facebookService.GetAllFacebookAdsAsync(account.id, accessToken);
                    activeAds.AddRange(ads?.data ?? new());
                }
            }
            if (activeAds?.Any() ?? false)
            {
                activeAds.ForEach(async i =>
                {
                    facebookAds.Add(new FacebookAdsInfo()
                    {
                        AdId = i?.id,
                        AdName = i?.name,
                        AdAccountId = i?.ad_account_id,
                        AdAccountName = i?.ad_account_name,
                        AdSetId = i?.adset_id,
                        AdSetName = i?.adset?.name,
                        CampaignId = i?.campaign_id,
                        CampaignName = i?.campaign?.name,
                        Status = i?.effective_status,
                        FacebookAuthResponseId = fbAccountId,
                        IsSubscribed = true,
                        PageId = i?.creative?.object_story_spec?.page_id
                    });
                });
                await _fbAdsRepo.AddRangeAsync(facebookAds);
            }
            return facebookAds;
        }
        protected async Task UpdateAdsDetailsInDB(string accessToken, Guid fbAccountId)
        {
            var allAdsResponses = new List<FacebookAdDto?>();
            var adAccounts = await _facebookService.GetAllFacebookAdAccountsAsync(accessToken);
            if (adAccounts?.data.Any() ?? false)
            {
                foreach (var account in adAccounts.data)
                {
                    var ads = await _facebookService.GetAllFacebookAdsAsync(account.id, accessToken);
                    allAdsResponses.AddRange(ads?.data ?? new());
                }
            }
            var existingAds = await _fbAdsRepo.ListAsync(new FacebookAdsByFbAccountIdSpec(fbAccountId), CancellationToken.None);
            var allAdIds = allAdsResponses?.Select(i => i.id) ?? new List<string>();
            var existingAdIds = existingAds?.Select(i => i.AdId) ?? new List<string>();
            var adsToRemove = existingAds?.Where(i => !(allAdIds).Contains(i.AdId))?.ToList() ?? new();
            var adsToAdd = allAdsResponses?.Where(i => !existingAdIds.Contains(i.id))?.ToList() ?? new();
            var existingAdsToUpdate = existingAds?.Where(i => allAdIds.Contains(i.AdId))?.ToList() ?? new();
            if (adsToRemove.Any())
            {
                await _fbAdsRepo.DeleteRangeAsync(adsToRemove);
            }
            if (adsToAdd.Any())
            {
                IEnumerable<FacebookAdsInfo> adEntities = new List<FacebookAdsInfo>();
                adEntities = adsToAdd.Select(i => new FacebookAdsInfo()
                {
                    AdId = i?.id,
                    AdName = i?.name,
                    AdAccountId = i?.ad_account_id,
                    AdAccountName = i?.ad_account_name,
                    AdSetId = i?.adset_id,
                    AdSetName = i?.adset?.name,
                    CampaignId = i?.campaign_id,
                    CampaignName = i?.campaign?.name,
                    Status = i?.effective_status,
                    FacebookAuthResponseId = fbAccountId,
                    IsSubscribed = true,
                    PageId = i?.creative?.object_story_spec?.page_id
                });
                adEntities = await _fbAdsRepo.AddRangeAsync(adEntities);
            }
            if (existingAdsToUpdate.Any())
            {
                IEnumerable<FacebookAdsInfo> adEntities = new List<FacebookAdsInfo>();
                foreach (var adEntity in existingAdsToUpdate)
                {
                    var adResponse = allAdsResponses?.FirstOrDefault(i => i.id == adEntity.AdId);
                    if (adResponse != null)
                    {
                        adEntity.Status = adResponse?.effective_status;
                        adEntity.AdName = adResponse?.name;
                        adEntity.AdAccountId = adResponse?.ad_account_id;
                        adEntity.AdAccountName = adResponse?.ad_account_name;
                        adEntity.AdSetId = adResponse?.adset_id;
                        adEntity.AdSetName = adResponse?.adset?.name;
                        adEntity.CampaignId = adResponse?.campaign_id;
                        adEntity.CampaignName = adResponse?.campaign?.name;
                    }
                }
                await _fbAdsRepo.UpdateRangeAsync(adEntities);
            }
        }

        protected bool IsValidGoogleAdsRequest(GoogleAdsIntegrationDto request)
        {
            return request != null &&
                   !string.IsNullOrEmpty(request.TenantInfoDto.Identifier) &&
                   !string.IsNullOrEmpty(request.RefreshToken);
        }
        protected async Task<bool> IsGoogleAdsAccountExistsInDifferentTenant(GoogleAdsIntegrationDto request)
        {
            bool isAccountExists = await _googleAdsService.IsGoogleAdsAccountExists(request.CustomerId, null);

            if (isAccountExists)
            {
                var tId = await _googleAdsService.GetTenantIdByGoogleAdsAccountId(request.CustomerId);
                if (tId != request.TenantInfoDto?.Identifier) 
                { return true; }
            }
            return false;
        }
        protected async Task<bool> IsGoogleAdsAccountExistsInSameTenant(GoogleAdsIntegrationDto request)
        {
            bool isAccountExists = await _googleAdsService.IsGoogleAdsAccountExists(request.CustomerId);

            if (isAccountExists)
            {
                var tId = await _googleAdsService.GetTenantIdByGoogleAdsAccountId(request.CustomerId);
                if (tId == request.TenantInfoDto?.Identifier)
                { return true; }
            }
            return false;
        }
        protected async Task UpdateGoogleAdsAuthResponse(GoogleAdsIntegrationDto request)
        {
            var profileInfo = await _googleAdsService.GetCustomerDetailsAsync(request.CustomerId, request.RefreshToken);
            if (profileInfo != null && !string.IsNullOrEmpty(profileInfo.DescriptiveName))
            {
                request.GoogleUserName = profileInfo.DescriptiveName;
            }

            var existingAccount = (await _googleAdsAuthResponseRepo.FirstOrDefaultAsync(new GetGoogleAdsAuthResponseByIdSpec(request.CustomerId), CancellationToken.None));
            existingAccount.AccessToken = request.AccessToken;
            existingAccount.RefreshToken = request.RefreshToken;
            // existingAccount.LongLivedUserAccessToken = (await _facebookService.GetLongLivedTokenAsync(request.AccessToken)).access_token;
            await _googleAdsAuthResponseRepo.UpdateAsync(existingAccount, CancellationToken.None);
            await UpdateGoogleAdsDetailsInDB(existingAccount.RefreshToken, existingAccount.Id, request.CustomerId);
        }

        protected async Task CreateGoogleAdsAuthResponse(GoogleAdsIntegrationDto request)
        {
            var googleadsAuthResponse = request.Adapt<GoogleAdsAuthResponse>();
            googleadsAuthResponse.LongLivedUserAccessToken = request.RefreshToken;
            var profileInfo = await _googleAdsService.GetCustomerDetailsAsync(request.CustomerId, request.RefreshToken);
            if (profileInfo != null && !string.IsNullOrEmpty(profileInfo.DescriptiveName))
            {
                googleadsAuthResponse.AccountName = profileInfo.DescriptiveName;
            }
        
                var result = await _googleAdsAuthResponseRepo.AddAsync(googleadsAuthResponse);
            
 
            if (result != null)
            {
                try
                {
                    var integrationAccInfo = new IntegrationAccountInfo()
                    {
                        Id = Guid.NewGuid(),
                        AccountName = profileInfo?.DescriptiveName,
                        LeadSource = LeadSource.GoogleAdsCampaign,
                        LicenseId = Guid.NewGuid(),
                        JsonTemplate = "",
                        GoogleadLeadFormId = googleadsAuthResponse.Id
                    };
                    //var existingAds = await _fbAdsRepo.ListAsync(new FacebookAdsByFbAccountidSpec(facebookAuthResponse.Id), CancellationToken.None);
                    await _integrationAccInfoRepo.AddAsync(integrationAccInfo);
                    await StoreGoogleAdsDetailsInDB(request.RefreshToken, googleadsAuthResponse.Id, request.CustomerId);
                    // await CreateFacebookConnectedPageAccounts(facebookAuthResponse);
                }
                catch (Exception ex)
                {
                    throw;
                }
            }
            else
            {
                throw new Exception("Failed to create Google Ads Auth Response. Please try again later.");
            }
        }
        protected async Task StoreGoogleAdsDetailsInDB(string accessToken, Guid googleAccountId, string? customerId)
        {
            var activeAds = new List<GoogleAdsAdDto?>();
            var adAccounts = await _googleAdsService.GetAllGoogleAdAccountsAsync(customerId,accessToken);
            if (adAccounts?.data.Any() ?? false)
            {
                foreach (var account in adAccounts.data)
                {
                    var ads = await _googleAdsService.GetAllGoogleAdsAsync(account.Id, accessToken);
                    activeAds.AddRange(ads?.data ?? new());
                }
            }
            if (activeAds?.Any() ?? false)
            {
                List<GoogleAdsInfo> googleAds = new();
                activeAds.ForEach(async i =>
                {
                    googleAds.Add(new GoogleAdsInfo()
                    {
                        AdId = i?.id,
                        AdName = i?.name,
                        AdAccountId = i?.ad_account_id,
                        AdAccountName = i?.ad_account_name,
                        AdSetId = i?.adset_id,
                        AdSetName = i?.adset_name,
                        CampaignId = i?.campaign_id,
                        CampaignName = i?.campaign_name,
                        Status = i?.effective_status,
                        GoogleAuthResponseId = googleAccountId,
                        IsSubscribed = true,
                        CountryCode = i?.countryCode,
                        CurrencyCode  = i?.CurrencyCode,
                        CustomerId =i?.CustomerId,
                       
                    });
                });
                List<GoogleCampaign> googleCampaigns = googleAds
                    .GroupBy(a => a.CampaignId)
                    .Select(g => new GoogleCampaign()
                    {
                        CampaignId = g.Key,
                        CampaignName = g.First().CampaignName,
                        GoogleAuthResponseId = g.First().GoogleAuthResponseId,
                        IsActive = true
                    })
                    .ToList();
                
                if (googleCampaigns.Any())
                {
                   await _googleCampaignsRepo.AddRangeAsync(googleCampaigns);
                }
                if (googleAds.Any())
                {
                    await _googleAdsRepo.AddRangeAsync(googleAds);
                }
            }
        }


        protected async Task UpdateGoogleAdsDetailsInDB(string accessToken, Guid googleAccountId, string? customerId)
        {
            var activeAds = new List<GoogleAdsAdDto?>();
            var existingAds = await _googleAdsRepo.ListAsync(new GoogleAdAdsByAccountIdSpec(googleAccountId), CancellationToken.None);
            await _googleAdsRepo.DeleteRangeAsync(existingAds);
            var adAccounts = await _googleAdsService.GetAllGoogleAdAccountsAsync(customerId, accessToken);
            if (adAccounts?.data.Any() ?? false)
            {
                foreach (var account in adAccounts.data)
                {
                    var ads = await _googleAdsService.GetAllGoogleAdsAsync(account.Id, accessToken);
                    activeAds.AddRange(ads?.data ?? new());
                }
            }
            if (activeAds?.Any() ?? false)
            {
                List<GoogleAdsInfo> googleAds = new();
                activeAds.ForEach(async i =>
                {
                    googleAds.Add(new GoogleAdsInfo()
                    {
                        AdId = i?.id,
                        AdName = i?.name,
                        AdAccountId = i?.ad_account_id,
                        AdAccountName = i?.ad_account_name,
                        AdSetId = i?.adset_id,
                        AdSetName = i?.adset_name,
                        CampaignId = i?.campaign_id,
                        CampaignName = i?.campaign_name,
                        Status = i?.effective_status,
                        GoogleAuthResponseId = googleAccountId,
                        IsSubscribed = true,
                        CountryCode = i?.countryCode,
                        CurrencyCode = i?.CurrencyCode,
                        CustomerId = i?.CustomerId

                    });
                });
                List<GoogleCampaign> googleCampaigns = googleAds
                    .GroupBy(a => a.CampaignId)
                    .Select(g => new GoogleCampaign
                    {
                        CampaignId = g.Key,
                        CampaignName = g.First().CampaignName,
                        GoogleAuthResponseId = g.First().GoogleAuthResponseId
                    })
                    .ToList();
                await _googleCampaignsRepo.UpdateRangeAsync(googleCampaigns);
                await _googleAdsRepo.UpdateRangeAsync(googleAds);
            }
        }
    }
}
