﻿using Dapper;
using Npgsql;
using PlayGround;
using PlayGround.ProjectMigration;
using PlayGround.SeedLeadStatusRolePermission;
using PlayGround.SubscriptionMigration;


//string connectionString = "Host=lrb-prd.postgres.database.azure.com;Port=5432;Database=leadratBlackApp;Username=dbmasteruser;Password=********************;Pooling=true;MinPoolSize=3;MaxPoolSize=500;";

string connectionString = "Host=qa-lrb.postgres.database.azure.com;Port=5432;Database=leadratBlackApp;Username=dbmasteruser;Password=********************************;";

//string connectionString = $"Host=lrb-prd.postgres.database.azure.com;Port=5432;Database=leadratBlackApp;Username=dbmasteruser;Password=********************;Pooling=true;MinPoolSize=3;MaxPoolSize=5000;";

var conn = new NpgsqlConnection(connectionString);
try
{
    string innerQuery = "SELECT * From \"MultiTenancy\".\"Tenants\"";
    await conn.OpenAsync();
    var tenants = (await conn.QueryAsync<Tenants>(innerQuery)).ToList();
    await conn.CloseAsync();
    CustomFiltersMigration dataPermision = new CustomFiltersMigration();
    foreach (var tenant in tenants)
    {
        if (tenant.Id == "blackrock")
        {
            if (!string.IsNullOrEmpty(tenant.ConnectionString) && !CanConnectToServer(tenant.ConnectionString))
            {
                // TODO: Need to check why database is not connected 
            }
            var res = await dataPermision.MigrateAsync(string.IsNullOrWhiteSpace(tenant.ConnectionString) ? connectionString : tenant.ConnectionString, tenant.Id);
            Console.WriteLine(res);
        }
    }
}
catch (Exception ex)
{

}


static bool CanConnectToServer(string connectionString)
{
    try
    {
        using (var connection = new NpgsqlConnection(connectionString))
        {
            connection.Open(); // Attempt to open the connection
            connection.Close();
            return true; // Connection successful
        }
    }
    catch (Exception ex)
    {
        Console.WriteLine($"Failed to connect: {ex.Message}");
        return false; // Connection failed
    }
}

















//var query = "Select * from \"MultiTenancy\".\"Tenants\"";

//var conn = new NpgsqlConnection(connectionString);
//try
//{
//    CustomProspectStatus customProspectStatus = new CustomProspectStatus();
//    //CustomProspectSource customProspectSource = new CustomProspectSource();
//    var tenants = (await conn.QueryAsync<string>(query)).ToList();
//    //var masterSources = await customProspectSource.GetMasterProspectSources(connectionString);
//    var masterStatus = await customProspectStatus.GetMasterProspectStatuses(connectionString);

//    /* foreach(var tenant in tenants)
//     {
//         Console.WriteLine(tenant);
//         var res = await customProspectSource.MigrateAsync(masterSources, connectionString, tenant);
//         Console.WriteLine(res);
//     }*/


//    foreach (var tenant in tenants)
//    {
//        Console.WriteLine(tenant);
//        var res = await customProspectStatus.MigrateAsync(masterStatus, connectionString, tenant);
//        Console.WriteLine(res);
//    }

//}
//catch(Exception ex)
//{

//}



////See https://aka.ms/new-console-template for more information
//using PlayGround;

//Console.WriteLine("Hello, World!");
//LeadAppointmentMigrator leadAppointmentMigrator = new LeadAppointmentMigrator();
//var connectionString = "Host=lrb-prd.postgres.database.azure.com;Port=5432;Database=leadratBlackApp;Username=dbmasteruser;Password=********************;Pooling=true;MinPoolSize=3;MaxPoolSize=500;";
//CustomLeadStatusMigration customLeadStatusMigration = new CustomLeadStatusMigration();
////UpdateLeadHistory leadHistory = new UpdateLeadHistory();
////await leadHistory.GetLeadHistories(connectionString);
//var tenants = await leadAppointmentMigrator.GetAllTenantsAsync(connectionString);
//var masterStatus = await customLeadStatusMigration.GetMasterLeadStatuses(connectionString);
//foreach (var tenant in tenants)
//{
//    Console.WriteLine(tenant);
//    var res = await customLeadStatusMigration.MigrateAsync(masterStatus,connectionString, tenant);
//    Console.WriteLine(res);
//}
