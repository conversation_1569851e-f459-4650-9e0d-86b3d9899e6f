﻿using Lrb.Application.Automation.Dtos;
using Lrb.Application.Automation.Helpers;
using Lrb.Application.Common.Atomation;
using Lrb.Application.Common.LeadRotation;
using Lrb.Application.Common.PushNotification;
using Lrb.Application.CustomStatus.Web;
using Lrb.Application.GlobalSettings.Web;
using Lrb.Application.Identity.Users;
using Lrb.Application.Integration.Web.Automation;
using Lrb.Application.Integration.Web.Helpers;
using Lrb.Application.Lead.Web;
using Lrb.Application.Lead.Web.Dtos;
using Lrb.Application.Lead.Web.Mappings;
using Lrb.Application.Lead.Web.Specs;
using Lrb.Application.Project.Web.Specs;
using Lrb.Application.TempProject.Specs;
using Lrb.Application.UserDetails.Web.Specs;
using Lrb.Domain.Entities;
using Lrb.Domain.Entities.ErrorModule;
using Lrb.Domain.Entities.Integration;
using Lrb.Domain.Entities.MasterData;
using Newtonsoft.Json;
using Serilog;
using System.Text.Json.Serialization;
using static Lrb.Application.CustomMasterLeadSubStatus.Web.Request.MasterLeadSubStatusByLevelAndBaseIdSpec;

namespace Lrb.Application.Integration.Web.Requests.ListingSites
{
    public class RealStateIndiaListingSiteIntegrationRequest : IRequest<Response<bool>>
    {
        public string? Name { get; set; }
        public string? Mobile { get; set; }
        public string? Email { get; set; }
        [JsonProperty("enquiry_id")]
        [JsonPropertyName("enquiry_id")]
        public string? EnquiryId { get; set; }
        public string? Subject { get; set; }
        public string? Details { get; set; }
        [JsonProperty("property_id")]
        [JsonPropertyName("property_id")]
        public string? PropertyId { get; set; }
        [JsonProperty("recv_date")]
        [JsonPropertyName("recv_date")]
        public string? RecvDate { get; set; }
        [JsonProperty("lookinf_for")]
        [JsonPropertyName("lookinf_for")]
        public string? LookingFor { get; set; }
        [JsonProperty("address")]
        [JsonPropertyName("address")]
        public string? Address {  get; set; }
        public Guid AccountId { get; set; }
        public string? ApiKey { get; set; }
        public string? Currency { get; set; }
        public LeadSource LeadSource {  get; set; }
        public string? PrimaryUser { get; set; }
        public string? SecondaryUser { get; set; }
    }

    public class RealStateIndiaListingSiteIntegrationRequestHandler : IRequestHandler<RealStateIndiaListingSiteIntegrationRequest, Response<bool>>
    {
        private readonly IRepositoryWithEvents<IntegrationAccountInfo> _integrationAccRepo;
        private readonly IRepositoryWithEvents<IntegrationLeadInfo> _integrationLeadInfoRepo;
        private readonly IRepositoryWithEvents<Domain.Entities.Lead> _leadRepo;
        private readonly IRepositoryWithEvents<LeadEnquiry> _leadEnquiryRepo;
        private readonly IRepositoryWithEvents<Address> _addressRepo;
        //private readonly IRepositoryWithEvents<MasterLeadStatus> _leadStatusRepo;
        private readonly IRepositoryWithEvents<MasterPropertyType> _masterPropertytypeRepo;
        private readonly IRepositoryWithEvents<Lrb.Domain.Entities.Project> _projectsRepo;
        private readonly INotificationSenderService _notificationSenderService;
        private readonly IUserService _userService;
        private readonly IRepositoryWithEvents<Domain.Entities.UserDetails> _userDetailsRepo;
        private readonly ILogger _logger;
        private readonly IRepositoryWithEvents<CustomMasterLeadStatus> _customLeadStatusRepo;
        private readonly IRepositoryWithEvents<Domain.Entities.GlobalSettings> _globalSettingsRepo;
        private readonly IRepositoryWithEvents<LeadHistory> _leadHistoryRepo;
        private readonly IRepositoryWithEvents<DuplicateLeadFeatureInfo> _duplicateInfoRepo;
        private bool _isDupicateUnassigned = false;
        private readonly INpgsqlRepository _npgsqlRepo;
        private readonly ILeadRepositoryAsync _leadRepositoryAsync;
        private readonly IRepositoryWithEvents<IntegrationAssignment> _integrationAssignmentRepo;
        private readonly IRepositoryWithEvents<AssignmentModule> _assignmentModuleRepo;
        private readonly IRepositoryWithEvents<UserAssignment> _userAssignmentRepo;
        private readonly ILeadRotationService _leadRotationService;
        private readonly IRepositoryWithEvents<UserView> _userViewRepo;
        private readonly IRepositoryWithEvents<LeadAssignment> _leadAssignmentRepo;
        private readonly IUserAssignmentMetricsService _userAssignmentMetricsService;
        public RealStateIndiaListingSiteIntegrationRequestHandler(
            IRepositoryWithEvents<IntegrationAccountInfo> integrationAccRepo,
            IRepositoryWithEvents<IntegrationLeadInfo> integrationLeadInfoRepo,
            IRepositoryWithEvents<Domain.Entities.Lead> leadRepo,
            IRepositoryWithEvents<LeadEnquiry> leadEnquiryRepo,
            IRepositoryWithEvents<Address> addressRepo,
            //IRepositoryWithEvents<MasterLeadStatus> leadStatusRepo,
            IRepositoryWithEvents<MasterPropertyType> masterPropertytypeRepo,
            IRepositoryWithEvents<Lrb.Domain.Entities.Project> projectsRepo,
            INotificationSenderService notificationSenderService,
            IUserService userService,
            IRepositoryWithEvents<Domain.Entities.UserDetails> userDetailsRepo,
            ILogger logger,
            IRepositoryWithEvents<CustomMasterLeadStatus> customLeadStatusRepo,
            IRepositoryWithEvents<Domain.Entities.GlobalSettings> globalSettingsRepo,
            IRepositoryWithEvents<LeadHistory> leadHistoryRepo,
            IRepositoryWithEvents<DuplicateLeadFeatureInfo> duplicateInfoRepo,
            INpgsqlRepository npgsqlRepo,
            ILeadRepositoryAsync leadRepositoryAsync,
            IRepositoryWithEvents<IntegrationAssignment> integrationAssignmentRepo,
            IRepositoryWithEvents<AssignmentModule> assignmentModuleRepo,
            IRepositoryWithEvents<UserAssignment> userAssignmentRepo,
            ILeadRotationService leadRotationService,
            IRepositoryWithEvents<UserView> userViewRepo,
            IRepositoryWithEvents<Location> locationRepo,
            IRepositoryWithEvents<LeadAssignment> leadAssignmentRepo,
            IUserAssignmentMetricsService userAssignmentMetricsService
            )
        {
            _integrationAccRepo = integrationAccRepo;
            _integrationLeadInfoRepo = integrationLeadInfoRepo;
            _leadRepo = leadRepo;
            _leadEnquiryRepo = leadEnquiryRepo;
            _addressRepo = addressRepo;
            //_leadStatusRepo = leadStatusRepo;
            _masterPropertytypeRepo = masterPropertytypeRepo;
            _projectsRepo = projectsRepo;
            _notificationSenderService = notificationSenderService;
            _userService = userService;
            _userDetailsRepo = userDetailsRepo;
            _logger = logger;
            _customLeadStatusRepo = customLeadStatusRepo;
            _globalSettingsRepo = globalSettingsRepo;
            _leadHistoryRepo = leadHistoryRepo;
            _duplicateInfoRepo = duplicateInfoRepo;
            _npgsqlRepo = npgsqlRepo;
            _leadRepositoryAsync = leadRepositoryAsync;
            _integrationAssignmentRepo = integrationAssignmentRepo;
            _assignmentModuleRepo = assignmentModuleRepo;
            _userAssignmentRepo = userAssignmentRepo;
            _leadRotationService = leadRotationService;
            _userViewRepo = userViewRepo;
            _leadAssignmentRepo = leadAssignmentRepo;
            _userAssignmentMetricsService = userAssignmentMetricsService;
        }

        public async Task<Response<bool>> Handle(RealStateIndiaListingSiteIntegrationRequest request, CancellationToken cancellationToken)
       {
            if (string.IsNullOrWhiteSpace(request.Mobile))
            {
                throw new Exception("Invalid Mobile number ");
            }
            Domain.Entities.GlobalSettings? globalSettings = await _globalSettingsRepo.FirstOrDefaultAsync(new GetGlobalSettingsSpec(), cancellationToken);
            request.AccountId = AccountIdHelper.GetAccountId(request.ApiKey);
            var integrationAccountInfo = await _integrationAccRepo.FirstOrDefaultAsync(new GetIntegrationAccInfoWithAgencySpec(request.AccountId), cancellationToken);
            var countryCode =string.Empty;
            var contact = ListingSitesHelper.ConcatenatePhoneNumberV2(countryCode, request.Mobile, globalSettings, integrationAccountInfo?.CountryCode ??string.Empty);

            if (string.IsNullOrWhiteSpace(contact))
            {
                throw new Exception("Invalid Mobile number ");
            }
            //var integrationAccountInfo = await _integrationAccRepo.GetByIdAsync(request.AccountId);
            if (integrationAccountInfo == null) { return new(false); }
            List<Domain.Entities.Lead> duplicateLeads = null;
            var duplicateFeatureInfo = (await _duplicateInfoRepo.ListAsync(cancellationToken)).FirstOrDefault();
            var globalSetting = (await _globalSettingsRepo.ListAsync(new GetGlobalSettingsSpec(), cancellationToken)).FirstOrDefault();
            var mobileWithCountryCode = ListingSitesHelper.ConcatenatePhoneNumberV2(countryCode, request.Mobile, globalSettings, integrationAccountInfo?.CountryCode ?? string.Empty);
            if (duplicateFeatureInfo != null && duplicateFeatureInfo.IsFeatureAdded)
            {
                if (!duplicateFeatureInfo.AllowAllDuplicates)
                {
                    var duplicateLeadSpecDto = request.Adapt<DuplicateLeadSpecDto>();
                    duplicateLeadSpecDto.SubSource = integrationAccountInfo.AccountName?.ToLower() ?? string.Empty;
                    duplicateLeads = await _leadRepo.ListAsync(new DuplicateFeatureSpec(duplicateFeatureInfo, duplicateLeadSpecDto, mobileWithoutCountryCode: mobileWithCountryCode), cancellationToken);
                }
            }
            else
            {
                duplicateLeads ??= new();
                var duplicateLead = await _leadRepo.FirstOrDefaultAsync(new LeadByContactNoSpec((mobileWithCountryCode?.Length >= 1 ? mobileWithCountryCode : "invalid ContactNo") ?? "invalid ContactNo", request.Mobile), cancellationToken);
                if (duplicateLead != null)
                {
                    duplicateLeads.Add(duplicateLead);
                }
            }
            if (!duplicateLeads?.Any() ?? true)
            {
                #region Assignment Address
                Address address = null;
                var integrationAssignmentDetails = await IntegrationAssignmentHelper.GetIntegrationAssignmentDetails(integrationAccountInfo.Id, request.LeadSource, _integrationAssignmentRepo, integrationAccRepo: _integrationAccRepo);
                var assignedLocation = integrationAssignmentDetails?.Location;
                if (assignedLocation != null)
                {
                    var existingAddress = await _addressRepo.FirstOrDefaultAsync(new GetAddressByLocaitonIdSpec(assignedLocation.Id), cancellationToken);
                    if (existingAddress != null)
                    {
                        address = existingAddress;
                    }
                    else
                    {
                        address = assignedLocation.MapToAddress();
                        address.Location = assignedLocation;
                        await _addressRepo.AddAsync(address);
                    }
                }
                #endregion


                /*if (request?.Mobile?.Length == 10)
                {
                    request.Mobile = $"+91{request.Mobile.Trim()}";
                }*/
                var leadInfo = request.Adapt<IntegrationLeadInfo>();
                leadInfo.SubmittedDate = request.RecvDate ?? string.Empty;
                leadInfo.Notes = request.Details;
                var lead = leadInfo.Adapt<Domain.Entities.Lead>();
                if (!string.IsNullOrWhiteSpace(leadInfo.PrimaryUser))
                {
                    var primaryUserDetails = await _userViewRepo.FirstOrDefaultAsync(new GetUserByNameSpec(leadInfo.PrimaryUser), cancellationToken);
                    lead.AssignTo = primaryUserDetails?.Id ?? Guid.Empty;
                }
                var enquiry = leadInfo.Adapt<LeadEnquiry>();
                try
                {
                    await _integrationLeadInfoRepo.AddAsync(leadInfo);
                }
                catch (Exception ex)
                {
                    throw ex;
                }
                if (!string.IsNullOrWhiteSpace(request?.Address?.Trim()))
                {
                    Address addressDto = new();
                    addressDto.Locality = request.Address?.Trim();
                    enquiry.Addresses = new List<Address>() { addressDto };
                }
                var customStatus = await _customLeadStatusRepo.FirstOrDefaultAsync(new GetDefaultStatusSpec(), cancellationToken);
                string name = lead.Name.Trim();
                lead.ContactNo = ListingSitesHelper.ConcatenatePhoneNumberV2(countryCode, request.Mobile, globalSettings, integrationAccountInfo?.CountryCode ?? string.Empty);
                lead.CreatedOnPortal = ListingSitesHelper.GetUtcDateTime(leadInfo.SubmittedDate, leadInfo.SubmittedTime);
                lead.LeadNumber = name[0].ToString().ToUpper() + new Random().Next(1000, 9999).ToString() + DateTime.UtcNow.ToString("FFFFFFF");
                lead.AccountId = integrationAccountInfo.Id;
                lead.TagInfo = new();
                // lead.CreatedBy = integrationAccountInfo.CreatedBy;
                // lead.LastModifiedBy = integrationAccountInfo.LastModifiedBy;
                lead.CountryCode = countryCode ?? integrationAccountInfo?.CountryCode;
                lead.CustomLeadStatus = customStatus ?? (await _customLeadStatusRepo.FirstOrDefaultAsync(new MasterLeadSubStatusByNameSpec(new List<string>() { "new" }), cancellationToken));
                #region Notes 
                lead.Notes = !string.IsNullOrEmpty(lead.Notes) ? "Note - " + lead.Notes + "\n" : string.Empty;
                lead.Notes += !string.IsNullOrEmpty(request.RecvDate) ? "Received Date - " + request.RecvDate + ", \n" : string.Empty;
                lead.Notes += !string.IsNullOrEmpty(request.PropertyId) ? "PropertyId - " + request.PropertyId + ", \n" : string.Empty;
                lead.Notes += !string.IsNullOrEmpty(request.EnquiryId) ? "EnquiryId - " + request.EnquiryId + ", \n" : string.Empty;
                lead.Notes += !string.IsNullOrEmpty(request.Subject) ? "Subject - " + request.Subject + ", \n" : string.Empty;
                lead.Notes += !string.IsNullOrEmpty(request.LookingFor) ? "LookingFor - " + request.LookingFor + ", \n" : string.Empty;
                lead.Notes += !string.IsNullOrEmpty(request.Address) ? "Address - " + request.Address + ", \n" : string.Empty;

                #endregion

                #region Automation


                (UserAssignment? UserAssignment, Lrb.Domain.Entities.Project? Project, int? Priority) userAssignmentAndProject = new();

                var project = await _projectsRepo.FirstOrDefaultAsync(new GetProjectByIdSpecs(lead.Projects?.FirstOrDefault(i => i != null)?.Id ?? Guid.Empty), cancellationToken);

                userAssignmentAndProject = await IntegrationAssignmentHelper.GetMostPrioritizedUserAssignmentAndPriorityAsync(integrationAccountInfo.Id, request.LeadSource, _integrationAssignmentRepo, _assignmentModuleRepo, globalSettings, integrationAccRepo: _integrationAccRepo, projectWithAssignment: project);
                var existingLead = await _leadRepo.FirstOrDefaultAsync(new GetRootLeadSpec(lead.ContactNo, lead.AlternateContactNo), cancellationToken);
                UserDetailsDto? assignedUser = null;
                if (existingLead != null && existingLead.AssignTo != Guid.Empty)
                {
                    try
                    {
                        assignedUser = await _userService.GetAsync(existingLead?.AssignTo.ToString() ?? Guid.Empty.ToString(), cancellationToken);
                    }
                    catch (Exception ex)
                    {
                    }
                }

                if ((globalSettings?.IsStickyAgentEnabled ?? false) && existingLead != null && existingLead.AssignTo != default && assignedUser?.IsActive == true)
                {
                    lead.AssignTo = existingLead.AssignTo;
                }
                else
                {
                    List<Domain.Entities.Lead> existingLeads = await _leadRepo.ListAsync(new LeadByContactNoSpec(new List<string>() { lead.ContactNo ?? "Invalid Number" })) ?? new();
                    (Guid AssignTo, bool IsDupicateUnassigned) assignToRes = userAssignmentAndProject.UserAssignment != null ? await userAssignmentAndProject.UserAssignment.GetUserIdAsync(_userAssignmentRepo, _userDetailsRepo, _userService, existingLeads) : (Guid.Empty, false);
                    if (userAssignmentAndProject.UserAssignment?.CategoryType == AssignmentCategoryType.PercentageBased && integrationAccountInfo?.UserAssignment != null)
                    {
                        try
                        {
                            integrationAccountInfo.UserAssignment.TotalLeadsCount = (integrationAccountInfo?.UserAssignment?.TotalLeadsCount ?? 0) + 1;
                            var assignTo = await _userAssignmentMetricsService.DetermineUserAndSaveInfoAsync(integrationAccountInfo.Adapt<AccountInfoDto>());
                            lead.AssignTo = lead.AssignTo == Guid.Empty ? assignTo ?? lead.AssignTo : lead.AssignTo;
                        }
                        catch (Exception ex) { }
                    }
                    else
                    {
                        var assignmentModules = (await _assignmentModuleRepo.ListAsync(default)).OrderBy(i => i.Priority).LastOrDefault();
                        if (assignToRes.AssignTo == Guid.Empty && userAssignmentAndProject.Priority < assignmentModules?.Priority)
                        {
                            bool isAssigned = true;
                            while (isAssigned)
                            {
                                userAssignmentAndProject = await IntegrationAssignmentHelper.GetMostPrioritizedUserAssignmentAndPriorityAsync(integrationAccountInfo.Id, request.LeadSource, _integrationAssignmentRepo, _assignmentModuleRepo, globalSettings, integrationAccRepo: _integrationAccRepo, projectWithAssignment: project, priority: userAssignmentAndProject.Priority);

                                assignToRes = userAssignmentAndProject.UserAssignment != null ? await userAssignmentAndProject.UserAssignment?.GetUserIdAsync(_userAssignmentRepo, _userDetailsRepo, _userService, existingLeads) : (Guid.Empty, false);

                                if (assignToRes.AssignTo != Guid.Empty)
                                {
                                    isAssigned = false;
                                }
                                else if (assignToRes.AssignTo == Guid.Empty && userAssignmentAndProject.Priority < assignmentModules?.Priority && userAssignmentAndProject.Priority != null)
                                {
                                    userAssignmentAndProject.Priority = userAssignmentAndProject.Priority;
                                }
                                else
                                {
                                    isAssigned = false;
                                }
                            }

                        }

                        lead.AssignTo = lead.AssignTo == Guid.Empty ? assignToRes.AssignTo : lead.AssignTo;
                    }
                    // Set OriginalOwner to the assigned user when first assigned
                    if (lead.AssignTo != Guid.Empty && lead.OriginalOwner == null)
                    {
                        lead.OriginalOwner = lead.AssignTo;
                    }
                    var contactWithCode = ListingSitesHelper.ConcatenatePhoneNumber(globalSetting?.Countries?.FirstOrDefault()?.DefaultCallingCode, request.Mobile);
                    if ((globalSettings?.IsDualOwnershipEnabled ?? false) && (userAssignmentAndProject.UserAssignment?.IsDualAssignmentEnabled ?? false))
                    {
                        if (!string.IsNullOrWhiteSpace(leadInfo.SecondaryUser))
                        {
                            var secondaryUserDetails = await _userViewRepo.FirstOrDefaultAsync(new GetUserByNameSpec(leadInfo.SecondaryUser), cancellationToken);
                            lead.SecondaryUserId = secondaryUserDetails?.Id ?? Guid.Empty;
                        }
                        (Guid AssignTo, bool IsDupicateUnassigned) secondaryAssignTo = userAssignmentAndProject.UserAssignment != null ? await userAssignmentAndProject.UserAssignment?.GetSecondaryUserIdAsync(_userAssignmentRepo, _userDetailsRepo, _userService, _leadRepo, lead, contactWithCode) : (Guid.Empty, false);
                        lead.SecondaryUserId = lead.SecondaryUserId == null || lead.SecondaryUserId == Guid.Empty ? secondaryAssignTo.AssignTo : lead.SecondaryUserId;
                    }
                    _logger.Information("ProcessFacebookWebhookRequestHandler -> Mapped Lead after assignment : " + JsonConvert.SerializeObject(lead, new JsonSerializerSettings() { Formatting = Formatting.Indented, ReferenceLoopHandling = ReferenceLoopHandling.Ignore }));
                    _isDupicateUnassigned = assignToRes.IsDupicateUnassigned;
                }
                try
                {
                    IntegrationAssignment integrationAssignment = await IntegrationAssignmentHelper.GetAssignedProjLocAsyncV1(source: LeadSource.RealEstateIndia, intgrAccId: integrationAccountInfo.Id, integrationAccRepo: _integrationAccRepo);

                    //(Lrb.Domain.Entities.Project? AssignedProject, Location? AssignedLocation) = await IntegrationAssignmentHelper.GetAssignedProjLocAsync(source: LeadSource.RealEstateIndia, intgrAccId: integrationAccountInfo.Id, integrationAccRepo: _integrationAccRepo);
                    var projectToAssign = userAssignmentAndProject.Project ?? integrationAssignment?.Project;
                    if (lead.Projects != null && projectToAssign != null && projectToAssign?.IsArchived == false && projectToAssign?.IsDeleted == false)
                    {
                        lead.Projects.Add(projectToAssign);
                    }
                    else if (projectToAssign != null && projectToAssign?.IsArchived == false && projectToAssign?.IsDeleted == false)
                    {
                        lead.Projects ??= new List<Lrb.Domain.Entities.Project>() { projectToAssign };
                    }

                    #endregion
                    if (lead.Agencies != null && integrationAssignment?.Agency != null && integrationAssignment?.Agency?.IsDeleted == false)
                    {
                        lead.Agencies.Add(integrationAssignment?.Agency);
                    }
                    else if (integrationAssignment?.Agency != null && integrationAssignment?.Agency?.IsDeleted == false)
                    {
                        lead.Agencies ??= new List<Lrb.Domain.Entities.Agency>() { integrationAssignment?.Agency };
                    }

                    if (lead.Campaigns != null && integrationAssignment?.Campaign != null && integrationAssignment?.Campaign?.IsDeleted == false)
                    {
                        lead.Campaigns.Add(integrationAssignment?.Campaign);
                    }
                    else if (integrationAssignment?.Campaign != null && integrationAssignment?.Campaign?.IsDeleted == false)
                    {
                        lead.Campaigns ??= new List<Lrb.Domain.Entities.Campaign>() { integrationAssignment?.Campaign };
                    }
                    if (lead.ChannelPartners != null && integrationAssignment?.ChannelPartner != null && integrationAssignment?.ChannelPartner?.IsDeleted == false)
                    {
                        lead.ChannelPartners.Add(integrationAssignment?.ChannelPartner);
                    }
                    else if (integrationAssignment?.ChannelPartner != null && integrationAssignment?.ChannelPartner?.IsDeleted == false)
                    {
                        lead.ChannelPartners ??= new List<Lrb.Domain.Entities.ChannelPartner>() { integrationAssignment?.ChannelPartner };
                    }

                    if (lead.Properties != null && integrationAssignment?.Property != null && integrationAssignment?.Property?.IsDeleted == false)
                    {
                        lead.Properties.Add(integrationAssignment?.Property);
                    }
                    else if (integrationAssignment?.Property != null && integrationAssignment?.Property?.IsDeleted == false && integrationAssignment?.Property?.IsArchived == false)
                    {
                        lead.Properties ??= new List<Lrb.Domain.Entities.Property>() { integrationAssignment?.Property };
                    }
                }
                catch { }

                #region DuplicateDetails
                var parentLead = await _leadRepo.FirstOrDefaultAsync(new Lead.Mobile.Specs.GetParentLeadSpec(lead.ContactNo ?? string.Empty), cancellationToken);
                if (parentLead != null)
                {
                    lead = lead.AddDuplicateDetail(parentLead.ChildLeadsCount, parentLead.Id);
                    parentLead.ChildLeadsCount += 1;
                    try
                    {
                        await _leadRepo.UpdateAsync(parentLead);
                    }
                    catch (Exception ex)
                    {
                        var error = new LrbError()
                        {
                            ErrorMessage = ex.Message,
                            ErrorSource = ex.Source,
                            StackTrace = ex.StackTrace,
                            ErrorModule = "ListingSitesIntegrationRequestHandler -> Handle() -> UpdateAsync()"
                        };
                        await _leadRepositoryAsync.AddErrorAsync(error);
                    }

                }
                #endregion
                if (lead.AssignTo != Guid.Empty)
                {
                    lead.AssignDate = DateTime.UtcNow;
                }
                lead.ApiKey = request.ApiKey;
                await _leadRepo.AddAsync(lead);

                //var enquiryDetails = await ListingSitesHelper.CreateEnquiryFromIntegration(request.LookingFor, _masterPropertytypeRepo);
                var enquiryDetails = await ListingSitesHelper.CreateEnquiryFromIntegrationV1(request.LookingFor, _masterPropertytypeRepo);
                //if (enquiryDetails.EnquiredFor != null)
                //{
                //    enquiry.EnquiredFor = (EnquiryType)enquiryDetails.EnquiredFor;
                //}
                if (enquiryDetails.EnquiryTypes?.Any() ?? false)
                {
                    enquiry.EnquiryTypes = enquiryDetails.EnquiryTypes;
                }

                if (enquiryDetails.propetyInfo != null)
                {
                    //enquiry.BHKType = enquiryDetails.propetyInfo.BHKType;
                    //enquiry.NoOfBHKs = enquiryDetails.propetyInfo.NoOfBHK;
                    enquiry.BHKTypes = enquiryDetails.propetyInfo.BHKTypes;
                    enquiry.BHKs = enquiryDetails.propetyInfo.BHKs;
                    enquiry.PropertyType = enquiryDetails.propetyInfo.PropertyType;
                    enquiry.PropertyTypes = enquiryDetails.propetyInfo.PropertyTypes;

                }
                if (string.IsNullOrWhiteSpace(enquiry?.Currency))
                {
                    enquiry.Currency = request.Currency ?? globalSettings?.Countries?.FirstOrDefault()?.DefaultCurrency ?? "INR";
                }

                enquiry.LeadId = lead.Id;
                enquiry.SubSource = integrationAccountInfo?.AccountName?.ToLower() ?? string.Empty;
                enquiry.IsPrimary = true;
                //enquiry.Address = address;
                if (address != null)
                {
                    enquiry.Addresses = new List<Address> { address };
                }
                await _leadEnquiryRepo.AddAsync(enquiry);
                #region CreateDuplicateLead
                var mobileWithCode = ListingSitesHelper.ConcatenatePhoneNumber(globalSetting?.Countries?.FirstOrDefault()?.DefaultCallingCode, request.Mobile);
                var totalLeadsCount = 0;
                try
                {
                    userAssignmentAndProject = await IntegrationAssignmentHelper.GetMostPrioritizedUserAssignmentAndPriorityAsync(integrationAccountInfo.Id, request.LeadSource, _integrationAssignmentRepo, _assignmentModuleRepo, globalSettings, integrationAccRepo: _integrationAccRepo, projectWithAssignment: project);
                    if ((duplicateFeatureInfo?.IsFeatureAdded ?? false) && (!globalSettings?.IsStickyAgentEnabled ?? false) && (userAssignmentAndProject.UserAssignment?.IsDuplicateAssignmentEnabled ?? false))
                    {
                        var duplicateLeadAssignmentsIds = userAssignmentAndProject.UserAssignment?.DuplicateUserIds != null ? await userAssignmentAndProject.UserAssignment?.GetUserIdListAsync(_userAssignmentRepo, _userDetailsRepo, _userService, lead) : (new List<Guid>());
                        if (duplicateLeadAssignmentsIds?.Any() ?? false)
                        {
                            if (userAssignmentAndProject.UserAssignment?.ShouldCreateMultipleDuplicates ?? false)
                            {
                                totalLeadsCount = await DuplicateLeadHelper.CreateDuplicateLeadsAsync(lead, lead.Id, _leadRepo, _leadEnquiryRepo, _leadRepositoryAsync, duplicateLeadAssignmentsIds, cancellationToken: cancellationToken, mobileWithCode);
                            }
                            else
                            {
                                totalLeadsCount = await DuplicateLeadHelper.CreateDuplicateLeadAsync(userAssignmentAndProject.UserAssignment, lead, lead.Id, _leadRepo, _leadEnquiryRepo, _leadRepositoryAsync, duplicateLeadAssignmentsIds, cancellationToken: cancellationToken, mobileWithCode);
                                await _userAssignmentRepo.UpdateAsync(userAssignmentAndProject.UserAssignment);
                            }
                        }

                    }
                }
                catch (Exception ex)
                {
                    var error = new LrbError()
                    {
                        ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                        ErrorSource = ex?.Source,
                        StackTrace = ex?.StackTrace,
                        InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                        ErrorModule = "RealStateIndiaListingSiteIntegrationRequestHandler -> Handle() -> AddAsync()"
                    };
                    await _leadRepositoryAsync.AddErrorAsync(error);
                }
                try
                {
                    if ((duplicateFeatureInfo?.IsFeatureAdded ?? false) && (!globalSettings?.IsStickyAgentEnabled ?? true) && (globalSettings?.IsDualOwnershipEnabled ?? false) && (userAssignmentAndProject.UserAssignment?.IsDualAssignmentEnabled ?? false))
                    {
                        var replicatedLeads = await _leadRepo.ListAsync(new GetDuplicateLeadSpec(lead.Id), cancellationToken);
                        try
                        {
                            if (replicatedLeads?.Any() ?? false && userAssignmentAndProject.UserAssignment != null)
                            {
                                await UserAssignmentHelper.AssignSecondaryUserIdsToDuplicateLeadsAsync(userAssignmentAndProject.UserAssignment, _userAssignmentRepo, _userDetailsRepo, _userService, _leadRepo, replicatedLeads, mobileWithCode);
                                await _leadRepo.UpdateRangeAsync(replicatedLeads);
                            }
                        }
                        catch (Exception ex)
                        {

                        }
                    }
                }
                catch (Exception ex)
                {
                    var error = new LrbError()
                    {
                        ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                        ErrorSource = ex?.Source,
                        StackTrace = ex?.StackTrace,
                        InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                        ErrorModule = "RealStateIndiaListingSiteIntegrationRequestHandler -> Handle() -> AddAsync()"
                    };
                    await _leadRepositoryAsync.AddErrorAsync(error);
                }
                #endregion
                var fullLead = (await _leadRepo.ListAsync(new LeadByIdSpec(lead.Id), cancellationToken))?[0];
                var leadDto = fullLead?.Adapt<ViewLeadDto>();
                integrationAccountInfo.TotalLeadCount = integrationAccountInfo?.TotalLeadCount + totalLeadsCount + 1;
                integrationAccountInfo.LeadCount++;
                await _integrationAccRepo.UpdateAsync(integrationAccountInfo);
                await leadDto.SetUsersInViewLeadDtoAsync(_userService, cancellationToken , source:leadInfo.LeadSource);
                var leadHsitory = LeadHistoryHelper.LeadHistoryMapper(leadDto);
                try
                {
                    await _leadHistoryRepo.AddAsync(leadHsitory);
                }
                catch (Exception ex)
                {
                    var error = new LrbError()
                    {
                        ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                        ErrorSource = ex?.Source,
                        StackTrace = ex?.StackTrace,
                        InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                        ErrorModule = "RealStateIndiaListingSiteIntegrationRequestHandler -> Handle() -> AddAsync()"
                    };
                    await _leadRepositoryAsync.AddErrorAsync(error);
                }
                #region DuplicateLead History
                try
                {
                    if ((duplicateFeatureInfo?.IsFeatureAdded ?? false) && (!globalSettings?.IsStickyAgentEnabled ?? true)  && (userAssignmentAndProject.UserAssignment?.IsDuplicateAssignmentEnabled ?? false))
                    {
                        var totalDuplicateLeads = await _leadRepo.ListAsync(new GetDuplicateLeadSpec(lead.Id), cancellationToken);
                        if (totalDuplicateLeads?.Any() ?? false)
                        {
                            await DuplicateLeadHelper.CreateDuplicateLeadHistoryAsync(totalDuplicateLeads, _leadHistoryRepo, _leadRepositoryAsync, _userService, cancellationToken);
                        }
                    }
                }
                catch (Exception ex) { }
                #endregion

                #region Assignment History
                try
                {
                    if (fullLead?.AssignTo != Guid.Empty)
                    {
                        await ListingSitesHelper.CreateLeadAssignmentHistory(lead, _leadAssignmentRepo, cancellationToken);
                    }
                }
                catch(Exception ex) { }
                #endregion

                #region Push Notification
                //Domain.Entities.GlobalSettings? globalSettings = await _globalSettingsRepo.FirstOrDefaultAsync(new GetGlobalSettingsSpec(), cancellationToken);
                try
                {
                    NotificationSettings? notificationSettings = JsonConvert.DeserializeObject<NotificationSettings>(globalSettings?.NotificationSettings ?? string.Empty);
                    List<string> notificationResponses = new();
                    string? tenantId = await _npgsqlRepo.GetTenantId(request.AccountId);
                    List<Guid> adminIds = await _npgsqlRepo.GetAdminIdsAsync(tenantId ?? string.Empty);
                    if (lead.AssignTo == default || lead.AssignTo == Guid.Empty)
                    {
                        _logger.Information($"RealStateIndiaListingSiteIntegrationRequestHandler -> tenantId : {tenantId} , adminIds : " + JsonConvert.SerializeObject(adminIds));
                        if (adminIds.Any())
                        {

                            List<string> notificationSchduleResponse = new();
                            if (_isDupicateUnassigned)
                            {
                                notificationSchduleResponse = await _notificationSenderService.ScheduleNotificationsAsync(Domain.Enums.Event.DuplicateUnAssigment, lead, null, null, topics: new List<string> { lead.CreatedBy.ToString(), lead.LastModifiedBy.ToString() }, null, null, adminIds);
                            }
                            else
                            {
                                notificationSchduleResponse = await _notificationSenderService.ScheduleNotificationsAsync(Domain.Enums.Event.LeadFromIntegration, lead, null, null, topics: new List<string> { lead.CreatedBy.ToString(), lead.LastModifiedBy.ToString() }, null, null, adminIds);
                            }
                            notificationResponses.AddRange(notificationSchduleResponse);
                        }
                    }
                    else if (lead.AssignTo != Guid.Empty)
                    {
                        var user = await _userService.GetAsync(lead.AssignTo.ToString(), cancellationToken);
                        if (user != null)
                        {
                            List<string> notificationSchduleResponse = await _notificationSenderService.ScheduleNotificationsAsync(Domain.Enums.Event.LeadFromIntegration, lead, lead.AssignTo, user.FirstName + " " + user.LastName, topics: new List<string> { lead.CreatedBy.ToString(), lead.LastModifiedBy.ToString() });
                            notificationResponses.AddRange(notificationSchduleResponse);
                        }
                        List<Guid> userWithManagerIds = new();
                        if (notificationSettings?.IsManagerEnabled ?? false)
                        {
                            List<Guid> managerIds = await _npgsqlRepo.GetReportingManagerUserIdsAsync(new List<Guid> { lead.AssignTo });
                            userWithManagerIds.AddRange(managerIds);
                        }
                        if (notificationSettings?.IsAdminEnabled ?? false)
                        {
                            userWithManagerIds.AddRange(adminIds);
                        }
                        if (user != null && userWithManagerIds.Any())
                        {
                            userWithManagerIds = userWithManagerIds.Distinct().ToList();
                            userWithManagerIds.Remove(lead.AssignTo);
                            List<string> notificationSchduleResponse = await _notificationSenderService.ScheduleNotificationsAsync(Domain.Enums.Event.NotifiyManagerAndAdmins, lead, null, null, topics: new List<string> { lead.CreatedBy.ToString(), lead.LastModifiedBy.ToString() }, userIds: userWithManagerIds);
                            notificationResponses.AddRange(notificationSchduleResponse);
                        }
                    }
                    _logger.Information($"ListingSitesIntegrationRequest -> NotificationSchedulingResponses JobIds : " + JsonConvert.SerializeObject(notificationResponses));
                    if ((duplicateFeatureInfo?.IsFeatureAdded ?? false) && (!globalSettings?.IsStickyAgentEnabled ?? true) && (userAssignmentAndProject.UserAssignment?.IsDuplicateAssignmentEnabled ?? false))
                    {
                        var allduplicateLeads = await _leadRepo.ListAsync(new GetDuplicateLeadSpec(lead.Id), cancellationToken);
                        if (allduplicateLeads?.Any() ?? false)
                        {
                            foreach (var duplicatelead in allduplicateLeads)
                            {
                                try
                                {
                                    if (duplicatelead.AssignTo != Guid.Empty && duplicatelead.AssignTo != null)
                                    {
                                        var user = await _userService.GetAsync(lead.AssignTo.ToString(), cancellationToken);
                                        if (user != null)
                                        {
                                            List<string> notificationSchduleResponse = await _notificationSenderService.ScheduleNotificationsAsync(Domain.Enums.Event.LeadFromIntegration, duplicatelead, duplicatelead.AssignTo, user.FirstName + " " + user.LastName, topics: new List<string> { duplicatelead.CreatedBy.ToString(), lead.LastModifiedBy.ToString() });
                                            notificationResponses.AddRange(notificationSchduleResponse);
                                        }
                                        List<Guid> userWithManagerIds = new();
                                        if (notificationSettings?.IsManagerEnabled ?? false)
                                        {
                                            List<Guid> managerIds = await _npgsqlRepo.GetReportingManagerUserIdsAsync(new List<Guid> { duplicatelead.AssignTo });
                                            userWithManagerIds.AddRange(managerIds);
                                        }
                                        if (notificationSettings?.IsAdminEnabled ?? false)
                                        {
                                            userWithManagerIds.AddRange(adminIds);
                                        }
                                        if (user != null && userWithManagerIds.Any())
                                        {
                                            userWithManagerIds = userWithManagerIds.Distinct().ToList();
                                            userWithManagerIds.Remove(duplicatelead.AssignTo);
                                            List<string> notificationSchduleResponse = await _notificationSenderService.ScheduleNotificationsAsync(Domain.Enums.Event.NotifiyManagerAndAdmins, duplicatelead, null, null, topics: new List<string> { duplicatelead.CreatedBy.ToString(), duplicatelead.LastModifiedBy.ToString() }, userIds: userWithManagerIds);
                                            notificationResponses.AddRange(notificationSchduleResponse);
                                        }
                                    }
                                    _logger.Information($"RealStateIndiaListingSiteIntegrationRequestHandler -> NotificationSchedulingResponses JobIds : " + JsonConvert.SerializeObject(notificationResponses));


                                }
                                catch (Exception ex)
                                {
                                    _logger.Information($"RealStateIndiaListingSiteIntegrationRequestHandler -> Exception -> PushNotification : " + JsonConvert.SerializeObject(ex, new JsonSerializerSettings() { Formatting = Formatting.Indented, ReferenceLoopHandling = ReferenceLoopHandling.Ignore }));
                                    var error = new LrbError()
                                    {
                                        ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                                        ErrorSource = ex?.Source,
                                        StackTrace = ex?.StackTrace,
                                        InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                                    };
                                    await _leadRepositoryAsync.AddErrorAsync(error);

                                }
                            }
                        }
                    }
                }
                catch (Exception ex)
                {
                    _logger.Information($"RealStateIndiaListingSiteIntegrationRequestHandler -> Exception -> PushNotification : " + JsonConvert.SerializeObject(ex, new JsonSerializerSettings() { Formatting = Formatting.Indented, ReferenceLoopHandling = ReferenceLoopHandling.Ignore }));
                    var error = new LrbError()
                    {
                        ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                        ErrorSource = ex?.Source,
                        StackTrace = ex?.StackTrace,
                        InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                    };
                    await _leadRepositoryAsync.AddErrorAsync(error);
                }
                #endregion

                #region Lead Rotation
                try
                {
                    if ((leadInfo?.LeadSource != LeadSource.PropertyMicrosite) && (leadInfo?.LeadSource != LeadSource.ProjectMicrosite))
                    {
                        if ((existingLead != null && existingLead.AssignTo == lead.AssignTo) && (globalSettings?.IsStickyAgentOverriddenEnabled ?? false) && (globalSettings?.IsLeadRotationEnabled ?? false))
                        {
                            if (lead.AssignTo != Guid.Empty && ((userAssignmentAndProject.UserAssignment != null) && userAssignmentAndProject.UserAssignment?.UserAssignmentType == UserAssignmentType.Team))
                            {
                                await _leadRotationService.ScheduleTeamLeadRotation(lead.Id, accountId: integrationAccountInfo?.Id);
                            }
                        }
                        else if ((globalSettings != null && globalSettings.IsLeadRotationEnabled) && existingLead == null && ((userAssignmentAndProject.UserAssignment != null) && userAssignmentAndProject.UserAssignment?.UserAssignmentType == UserAssignmentType.Team))
                        {
                            if (lead.AssignTo != Guid.Empty)
                            {
                                await _leadRotationService.ScheduleTeamLeadRotation(lead.Id, accountId: integrationAccountInfo?.Id);
                            }
                        }
                    }
                }
                catch (Exception ex) { }
                #endregion
            }
            GetInvalidItemsModel InvalidData = new();
            if (duplicateLeads?.Any(i => i != null) ?? false)
            {
                List<DuplicateItem> duplicateItems = new();
                duplicateLeads.ToList().ForEach(i => duplicateItems.Add(new DuplicateItem(i.Name, i.ContactNo, DuplicateItemType.Lead)));
                InvalidData.DuplicateItems.DuplicateItems.AddRange(duplicateItems);
                InvalidData.DuplicateItems.LeadCount = duplicateItems.Count;
            }
            if (InvalidData.DuplicateItems.DuplicateItems.Any())
            {
                return new Response<bool>(true, JsonConvert.SerializeObject(InvalidData));
            }
            return new(true);
        }


    }
}
