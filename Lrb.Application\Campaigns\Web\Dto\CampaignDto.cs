﻿using Lrb.Application.Property.Web;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Lrb.Application
{


    public class ViewCampaignDto : CampaignDto
    {
        public int LeadsCount { get; set; }
        public int ProspectCount { get; set; }
        public List<ContactType>? ContactTypes { get; set; }
        public List<string>? TemplateNames { get; set; }
    }
    public class CreateOrUpdateCampaignDto : CampaignDto
    {
    }
    public class CampaignDto :IDto
    {
        public Guid CreatedBy { get; set; }
        public DateTime CreatedOn { get; set; }
        public Guid LastModifiedBy { get; set; }
        public DateTime? LastModifiedOn { get; set; }
        public string? Name { get; set; }
        public Guid Id { get; set; }
        public List<string>? FlagNames { get; set; }
    }
    public class CampaignsDto : IDto
    {
        public string? Name { get; set; }
        public List<Guid>? Flags { get; set; }
    }
    public class MarketingCampaignFormattedDto : IDto
    {
        public string SlNo { get; set; } = default!;
        public string Name { get; set; }
        public int LeadsCount { get; set; }
        public int ProspectCount { get; set; }
        public Guid CreatedBy { get; set; }
        public DateTime CreatedOn { get; set; }
        public Guid LastModifiedBy { get; set; }
        public DateTime? LastModifiedOn { get; set; }

    }
    public class MarketingCampaignV2FormattedDto : IDto
    {
        public string SlNo { get; set; } = default!;
        public string Name { get; set; }
        public long LeadsCount { get; set; }
        public long ProspectCount { get; set; }
        public string CreatedBy { get; set; }
        public DateTime CreatedOn { get; set; }
        public string LastModifiedBy { get; set; }
        public DateTime LastModifiedOn { get; set; }

    }
    public class CampaignDapperDto
    {
        public Guid Id { get; set; }
        public string? Name { get; set; }
        public bool IsDeleted { get; set; }
        public DateTime? DeletedOn { get; set; }
        public Guid? DeletedBy { get; set; }
        public Guid CreatedBy { get; set; }
        public DateTime CreatedOn { get; set; }
        public Guid LastModifiedBy { get; set; }
        public DateTime? LastModifiedOn { get; set; }

    }
    public class CampaignLeadDto
    {
        public Guid CamapignsId { get; set; }
        public Guid LeadsId { get; set; }
    }
    public class CampaignsDtoV1 : IDto
    {
        public string? Name { get; set; }
    }


}
