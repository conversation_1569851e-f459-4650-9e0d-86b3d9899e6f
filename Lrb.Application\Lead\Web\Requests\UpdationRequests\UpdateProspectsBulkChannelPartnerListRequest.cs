﻿using Lrb.Application.Common.Interfaces;
using Lrb.Application.DataManagement.Web;
using Lrb.Application.DataManagement.Web.Dtos;
using Lrb.Application.DataManagement.Web.Dtos.CustomData;
using Lrb.Application.DataManagement.Web.Mapping;
using Lrb.Application.DataManagement.Web.Request.CommonHandler;
using Lrb.Application.DataManagement.Web.Specs;
using Lrb.Application.Identity.Users;
using Lrb.Domain.Entities.MasterData;
using Lrb.Shared.Utils;

namespace Lrb.Application.Lead.Web.Requests.UpdationRequests
{
    public class UpdateProspectsBulkChannelPartnerListRequest : IRequest<Response<int>>
    {
        public List<string>? channelPartnerNames { get; set; }
        public List<Guid> Ids { get; set; } = default!;
        public bool? ShouldRemoveExistingChannelPartener { get; set; }
        public Guid? CurrentUserId { get; set; }
        public string? TenantId { get; set; }


    }
    public class UpdateProspectsBulkChannelPartnerListRequestHandler : DataCommonRequestHandler, IRequestHandler<UpdateProspectsBulkChannelPartnerListRequest, Response<int>>
    {
        protected readonly ICurrentUser _currentUser;
        private readonly IRepositoryWithEvents<CustomProspectStatus> _prospectStatusRepo;
        private readonly IRepositoryWithEvents<MasterProspectSource> _prospectSourceRepo;
        private readonly IUserService _userService;
        private readonly IRepositoryWithEvents<ProspectHistory> _prospectHistoryRepo;
        private readonly IDapperRepository _dapperRepository;



        public UpdateProspectsBulkChannelPartnerListRequestHandler(
            IServiceProvider serviceProvider,
            ICurrentUser currentUser, 
            IRepositoryWithEvents<CustomProspectStatus> prospectStatusRepo,
            IRepositoryWithEvents<MasterProspectSource> prospectSourceRepo,
            IUserService userService, 
            IRepositoryWithEvents<ProspectHistory> prospectHistoryRepo,
            IDapperRepository dapperRepository) : base(serviceProvider)
        {
            _currentUser = currentUser;
            _userService = userService;
            _prospectStatusRepo = prospectStatusRepo;
            _prospectSourceRepo = prospectSourceRepo;
            _prospectHistoryRepo = prospectHistoryRepo;
            _dapperRepository = dapperRepository;
        }

        public async Task<Response<int>> Handle(UpdateProspectsBulkChannelPartnerListRequest request, CancellationToken cancellationToken)
        {
            try
            {
                int totalUpdatedCount = 0;
                var existingProspects = await _prospectRepo.ListAsync(new GetProspectByIdsSpecs(request.Ids), cancellationToken);
                var currentUserId = request.CurrentUserId ?? _currentUser.GetUserId();
                var updatedProspects = new List<Prospect>();
                var prospectHistorys = new List<ProspectHistory>();
                List<Domain.Entities.ChannelPartner>? channelPartener = null;
                MasterProspectSource? source = null;
                if (request.channelPartnerNames !=null)
                {
                    channelPartener = await _channelPartnerRepo.ListAsync(new GetAllChannelPartenerSpec(request?.channelPartnerNames?.ConvertAll(i => i.ToLower()) ?? new()), cancellationToken);

                    if (channelPartener == null)
                    {
                        throw new NotFoundException("No channelPartener found by this Id");
                    }
                }

                if (existingProspects?.Any() ?? false)
                {
                    var statuses = await _prospectStatusRepo.ListAsync();
                    var propertyTypes = await _propertyTypeRepo.ListAsync();
                    var sources = await _prospectSourceRepo.ListAsync();
                    var userIds = new List<string?>();
                    existingProspects.ForEach(i => {
                        userIds.AddRange(new[] { i.AssignedFrom?.ToString(), i.AssignTo.ToString(), i.LastModifiedBy.ToString(), i.SourcingManager?.ToString(), i.ClosingManager?.ToString() });
                    });
                    userIds.Add(currentUserId.ToString());
                    userIds = userIds.Where(i => i != null && i != string.Empty)?.DistinctBy(i => i).ToList();
                    var users = await _userService.GetListOfUsersByIdsAsync(userIds, cancellationToken);
                    foreach (var existingProspect in existingProspects)
                    {
                        var oldProspect = existingProspect.Adapt<ViewProspectDto>();
                        var prospect = request.Adapt(existingProspect);
                        if ((prospect.ChannelPartners?.Any() ?? false) && request?.ShouldRemoveExistingChannelPartener == false)
                        {
                            List<Lrb.Domain.Entities.ChannelPartner> newCp = new();
                            newCp.AddRange(prospect.ChannelPartners);
                            newCp.AddRange(channelPartener);
                            newCp = newCp.DistinctBy(p => p.Id).ToList();
                            prospect.ChannelPartners = newCp;
                        }
                        else
                        {
                            prospect.ChannelPartners = channelPartener;
                        }


                        prospect.LastModifiedBy = currentUserId;
                        prospect.LastModifiedOn = DateTime.UtcNow;
                        totalUpdatedCount++;
                        updatedProspects.Add(prospect);

                        var prospectVM = prospect.Adapt<ViewProspectDto>();
                        prospectVM = await ProspectHistoryHelper.SetUserViewForProspect(prospectVM, _userService, cancellationToken, currentUserId: currentUserId, userDetails: users);
                        oldProspect = await ProspectHistoryHelper.SetUserViewForProspect(oldProspect, _userService, cancellationToken, currentUserId: currentUserId, userDetails: users);
                        var histories = await ProspectHistoryHelper.UpdateProspectHistoryForVM(prospectVM, oldProspect, currentUserId, 1, statuses, propertyTypes, sources, _userService, cancellationToken);
                        prospectHistorys.AddRange(histories);
                    }
                    if (updatedProspects.Any())
                    {

                        var prospectCps = updatedProspects.Where(prospect => prospect.ChannelPartners != null && prospect.ChannelPartners.Any()).SelectMany(prospect => prospect.ChannelPartners.Select(i => new ChannelPartnerProspectDto
                        {
                            ProspectsId = prospect.Id,
                            ChannelPartnersId = i.Id
                        })).ToList();
                        if (prospectCps.Any())
                        {
                            if (request?.ShouldRemoveExistingChannelPartener == true)
                            {
                                var prospectIdsToClear = updatedProspects.Select(l => l.Id).Distinct().ToList();
                                if (prospectIdsToClear.Any())
                                {
                                    var prospectIdList = string.Join(", ", prospectIdsToClear.Select(id => $"'{id}'"));
                                    var deleteQuery = $"DELETE FROM \"{DataBaseDetails.LRBSchema}\".\"ChannelPartnerProspect\" WHERE \"ProspectsId\" IN ({prospectIdList});";
                                    await _dapperRepository.ExecuteQueryAsync(deleteQuery);
                                }
                            }
                            List<string> columnNames = new List<string> { "ChannelPartnersId", "ProspectsId" };
                            var ChannelInsertQuery = QueryGenerator.GenerateInsertQueryV1(null, DataBaseDetails.LRBSchema, "ChannelPartnerProspect", columnNames, prospectCps);
                            await _dapperRepository.ExecuteQueryAsync(ChannelInsertQuery);
                        }
                        var addHistories = prospectHistorys.Adapt<List<ProspectHistoryDapperDto>>();
                        var columns = QueryGenerator.GetMappedProperties<ProspectHistoryDapperDto>();
                        var insertQuery = QueryGenerator.GenerateInsertQuery(request?.TenantId, DataBaseDetails.LRBSchema, "ProspectHistories", columns, addHistories);
                        await _dapperRepository.ExecuteQueryAsync(insertQuery);
                    }
                }
                
                return new Response<int>(totalUpdatedCount);
            }
            catch (Exception ex)
            {
                throw new ApplicationException("Error updating prospect sources", ex);
            }

            //try
            //{
            //    List<Domain.Entities.ChannelPartner> channelPartener = await _channelPartnerRepo.ListAsync(new GetAllChannelPartenerSpec(request?.ChannelPartenerNames?.ConvertAll(i => i.ToLower()) ?? new()), cancellationToken);

            //    List<Domain.Entities.Prospect> existingProspects = await _prospectRepo.ListAsync(new GetProspectByIdsSpecs(request?.Ids ?? new()), cancellationToken);

            //    if (channelPartener.Count >= 0 && existingProspects.Count > 0)
            //    {
            //        await UpdateChannelPartnerForMultipleProspectsAsync(existingProspects, channelPartener, request?.ShouldRemoveExistingChannelPartener ?? false, cancellationToken, request?.CurrentUserId, request?.TenantId);

            //        return new(existingProspects.Count());
            //    }
            //    else
            //    {
            //        return new(existingProspects.Count());
            //    }
            //}
            //catch (Exception ex)
            //{
            //    throw;
            //}
        }
    }
}
