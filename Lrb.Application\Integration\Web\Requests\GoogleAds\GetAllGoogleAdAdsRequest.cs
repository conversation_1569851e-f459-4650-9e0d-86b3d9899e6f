﻿using Lrb.Application.Common.Facebook;
using Lrb.Application.Common.GoogleAds;
using Lrb.Application.Identity.Users;
using Lrb.Application.Integration.Web.Requests.Facebook;
using Lrb.Application.Integration.Web.Specs;
using Lrb.Domain.Entities.Integration;

namespace Lrb.Application.Integration.Web.Requests.GoogleAds
{

    public class GetAllGoogleAdAdsRequest : PaginationFilter, IRequest<PagedResponse<GoogleAdsInfoDto, string>>
    {
        public Guid AccountId { get; set; }
    }
    public class GetAllGoogleAdAdsRequestHandler : <PERSON><PERSON><PERSON><PERSON><PERSON>andler, IRequestHandler<GetAllGoogleAdAdsRequest, PagedResponse<GoogleAdsInfoDto, string>>
    {
        private readonly IRepositoryWithEvents<IntegrationAssignmentInfo> _integrationAssignmentInforepo;
        private readonly IRepositoryWithEvents<Lrb.Domain.Entities.Project> _projectRepo;
        private readonly IUserService _userService;

        public GetAllGoogleAdAdsRequestHandler(
           IRepositoryWithEvents<FacebookAuthResponse> facebookAuthResponseRepo,
           IRepositoryWithEvents<FacebookConnectedPageAccount> facebookConnectedPageAccountRepo,
           IRepositoryWithEvents<FacebookLeadGenForm> facebookLeadGenFormRepo,
           IFacebookService facebookService,
           IJobService hangfireService,
           ITenantIndependentRepository repository,
           ICurrentUser currentUser,
           IRepositoryWithEvents<IntegrationAccountInfo> integrationAccInfoRepo,
           IRepositoryWithEvents<IntegrationAssignmentInfo> integrationAssignmentInforepo,
           IRepositoryWithEvents<FacebookAdsInfo> fbAdsRepo,
           IRepositoryWithEvents<Lrb.Domain.Entities.Project> projectRepo,
           IUserService userService,
           ILeadRepositoryAsync leadRepositoryAsync, IGoogleAdsService googleAdsService,
           IRepositoryWithEvents<GoogleAdsAuthResponse> googleAdsAuthResponseRepo,
           IRepositoryWithEvents<GoogleAdsInfo> googleAdsRepo,
           IRepositoryWithEvents<GoogleCampaign> googleCampaignsRepo)
           : base(facebookAuthResponseRepo,
                 facebookConnectedPageAccountRepo,
                 facebookLeadGenFormRepo,
                 facebookService,
                 hangfireService,
                 repository,
                 currentUser,
                 integrationAccInfoRepo,
                 fbAdsRepo,
                 leadRepositoryAsync, googleAdsService, googleAdsAuthResponseRepo,
                 googleAdsRepo, googleCampaignsRepo)
        {
            _integrationAssignmentInforepo = integrationAssignmentInforepo;
            _projectRepo = projectRepo;
            _userService = userService;
        }
        public async Task<PagedResponse<GoogleAdsInfoDto, string>> Handle(GetAllGoogleAdAdsRequest request, CancellationToken cancellationToken)
        {
            var allStoredAds = await _googleAdsRepo.ListAsync(new GoogleAdAdsByAccountIdSpec(request.AccountId), cancellationToken);
            var adsCount = await _googleAdsRepo.CountAsync(new GoogleAdAdsByAccountIdSpec(request.AccountId), cancellationToken);
            var ads = allStoredAds.Adapt<List<GoogleAdsInfoDto>>();
            return new PagedResponse<GoogleAdsInfoDto, string>(ads, adsCount);
        }
    }
}