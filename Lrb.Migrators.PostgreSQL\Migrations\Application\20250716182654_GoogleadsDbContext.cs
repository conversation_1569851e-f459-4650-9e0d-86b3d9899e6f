﻿using System;
using System.Collections.Generic;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Lrb.Migrators.PostgreSQL.Migrations.Application
{
    public partial class GoogleadsDbContext : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<IDictionary<string, string>>(
                name: "GoogleAdsProperties",
                schema: "LeadratBlack",
                table: "Leads",
                type: "jsonb",
                nullable: true);

            migrationBuilder.CreateTable(
                name: "GoogleAdsAuthResponses",
                schema: "LeadratBlack",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    AccessToken = table.Column<string>(type: "text", nullable: false),
                    RefreshToken = table.Column<string>(type: "text", nullable: false),
                    AccountName = table.Column<string>(type: "text", nullable: false),
                    LongLivedUserAccessToken = table.Column<string>(type: "text", nullable: false),
                    CustomerId = table.Column<string>(type: "text", nullable: true),
                    IsActive = table.Column<bool>(type: "boolean", nullable: false),
                    TenantId = table.Column<string>(type: "character varying(64)", maxLength: 64, nullable: false),
                    IsDeleted = table.Column<bool>(type: "boolean", nullable: false),
                    CreatedBy = table.Column<Guid>(type: "uuid", nullable: false),
                    CreatedOn = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    LastModifiedBy = table.Column<Guid>(type: "uuid", nullable: false),
                    LastModifiedOn = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    DeletedOn = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    DeletedBy = table.Column<Guid>(type: "uuid", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_GoogleAdsAuthResponses", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "GoogleAdsBulkLeadFetchTrackers",
                schema: "LeadratBlack",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    FromDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    ToDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    ActiveAdsCount = table.Column<int>(type: "integer", nullable: false),
                    ActiveFormsCount = table.Column<int>(type: "integer", nullable: false),
                    FetchedLeadsCount = table.Column<int>(type: "integer", nullable: false),
                    UniqueLeadsCount = table.Column<int>(type: "integer", nullable: false),
                    StoredLeadsCount = table.Column<int>(type: "integer", nullable: false),
                    Status = table.Column<int>(type: "integer", nullable: false),
                    Error = table.Column<string>(type: "text", nullable: true),
                    TenantId = table.Column<string>(type: "character varying(64)", maxLength: 64, nullable: false),
                    IsDeleted = table.Column<bool>(type: "boolean", nullable: false),
                    CreatedBy = table.Column<Guid>(type: "uuid", nullable: false),
                    CreatedOn = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    LastModifiedBy = table.Column<Guid>(type: "uuid", nullable: false),
                    LastModifiedOn = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    DeletedOn = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    DeletedBy = table.Column<Guid>(type: "uuid", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_GoogleAdsBulkLeadFetchTrackers", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "GoogleCampaigns",
                schema: "LeadratBlack",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    CampaignId = table.Column<string>(type: "text", nullable: false),
                    CampaignName = table.Column<string>(type: "text", nullable: false),
                    IsActive = table.Column<bool>(type: "boolean", nullable: false),
                    GoogleAuthResponseId = table.Column<Guid>(type: "uuid", nullable: false),
                    TenantId = table.Column<string>(type: "character varying(64)", maxLength: 64, nullable: false),
                    IsDeleted = table.Column<bool>(type: "boolean", nullable: false),
                    CreatedBy = table.Column<Guid>(type: "uuid", nullable: false),
                    CreatedOn = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    LastModifiedBy = table.Column<Guid>(type: "uuid", nullable: false),
                    LastModifiedOn = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    DeletedOn = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    DeletedBy = table.Column<Guid>(type: "uuid", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_GoogleCampaigns", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "GoogleAdsAccounts",
                schema: "LeadratBlack",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    AccountId = table.Column<string>(type: "text", nullable: false),
                    AccountType = table.Column<string>(type: "text", nullable: false),
                    AccessToken = table.Column<string>(type: "text", nullable: false),
                    RefreshToken = table.Column<string>(type: "text", nullable: false),
                    GoogleAuthResponseId = table.Column<Guid>(type: "uuid", nullable: false),
                    UserAssignmentId = table.Column<Guid>(type: "uuid", nullable: true),
                    TenantId = table.Column<string>(type: "character varying(64)", maxLength: 64, nullable: false),
                    IsDeleted = table.Column<bool>(type: "boolean", nullable: false),
                    CreatedBy = table.Column<Guid>(type: "uuid", nullable: false),
                    CreatedOn = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    LastModifiedBy = table.Column<Guid>(type: "uuid", nullable: false),
                    LastModifiedOn = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    DeletedOn = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    DeletedBy = table.Column<Guid>(type: "uuid", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_GoogleAdsAccounts", x => x.Id);
                    table.ForeignKey(
                        name: "FK_GoogleAdsAccounts_GoogleAdsAuthResponses_GoogleAuthResponse~",
                        column: x => x.GoogleAuthResponseId,
                        principalSchema: "LeadratBlack",
                        principalTable: "GoogleAdsAuthResponses",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_GoogleAdsAccounts_UserAssignments_UserAssignmentId",
                        column: x => x.UserAssignmentId,
                        principalSchema: "LeadratBlack",
                        principalTable: "UserAssignments",
                        principalColumn: "Id");
                });

            migrationBuilder.CreateTable(
                name: "GoogleAdsInfos",
                schema: "LeadratBlack",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    AdId = table.Column<string>(type: "text", nullable: true),
                    AdName = table.Column<string>(type: "text", nullable: true),
                    Status = table.Column<string>(type: "text", nullable: true),
                    CampaignName = table.Column<string>(type: "text", nullable: true),
                    CampaignId = table.Column<string>(type: "text", nullable: true),
                    AdSetName = table.Column<string>(type: "text", nullable: true),
                    AdSetId = table.Column<string>(type: "text", nullable: true),
                    AdAccountName = table.Column<string>(type: "text", nullable: true),
                    AdAccountId = table.Column<string>(type: "text", nullable: true),
                    CustomerId = table.Column<string>(type: "text", nullable: true),
                    GoogleAuthResponseId = table.Column<Guid>(type: "uuid", nullable: false),
                    IsSubscribed = table.Column<bool>(type: "boolean", nullable: false),
                    LeadsCount = table.Column<int>(type: "integer", nullable: false),
                    CountryCode = table.Column<string>(type: "text", nullable: true),
                    CurrencyCode = table.Column<string>(type: "text", nullable: true),
                    GoogleAdsAccountId = table.Column<Guid>(type: "uuid", nullable: true),
                    TenantId = table.Column<string>(type: "character varying(64)", maxLength: 64, nullable: false),
                    IsDeleted = table.Column<bool>(type: "boolean", nullable: false),
                    CreatedBy = table.Column<Guid>(type: "uuid", nullable: false),
                    CreatedOn = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    LastModifiedBy = table.Column<Guid>(type: "uuid", nullable: false),
                    LastModifiedOn = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    DeletedOn = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    DeletedBy = table.Column<Guid>(type: "uuid", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_GoogleAdsInfos", x => x.Id);
                    table.ForeignKey(
                        name: "FK_GoogleAdsInfos_GoogleAdsAccounts_GoogleAdsAccountId",
                        column: x => x.GoogleAdsAccountId,
                        principalSchema: "LeadratBlack",
                        principalTable: "GoogleAdsAccounts",
                        principalColumn: "Id");
                });

            migrationBuilder.CreateIndex(
                name: "IX_GoogleAdsAccounts_GoogleAuthResponseId",
                schema: "LeadratBlack",
                table: "GoogleAdsAccounts",
                column: "GoogleAuthResponseId");

            migrationBuilder.CreateIndex(
                name: "IX_GoogleAdsAccounts_UserAssignmentId",
                schema: "LeadratBlack",
                table: "GoogleAdsAccounts",
                column: "UserAssignmentId");

            migrationBuilder.CreateIndex(
                name: "IX_GoogleAdsInfos_GoogleAdsAccountId",
                schema: "LeadratBlack",
                table: "GoogleAdsInfos",
                column: "GoogleAdsAccountId");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "GoogleAdsBulkLeadFetchTrackers",
                schema: "LeadratBlack");

            migrationBuilder.DropTable(
                name: "GoogleAdsInfos",
                schema: "LeadratBlack");

            migrationBuilder.DropTable(
                name: "GoogleCampaigns",
                schema: "LeadratBlack");

            migrationBuilder.DropTable(
                name: "GoogleAdsAccounts",
                schema: "LeadratBlack");

            migrationBuilder.DropTable(
                name: "GoogleAdsAuthResponses",
                schema: "LeadratBlack");

            migrationBuilder.DropColumn(
                name: "GoogleAdsProperties",
                schema: "LeadratBlack",
                table: "Leads");
        }
    }
}
