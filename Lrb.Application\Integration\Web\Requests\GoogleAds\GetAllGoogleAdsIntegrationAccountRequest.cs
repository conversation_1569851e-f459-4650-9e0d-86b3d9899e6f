﻿using Lrb.Application.Common.GoogleAds;
using Lrb.Application.Integration.Web.Specs;
using Lrb.Domain.Entities.Integration;

namespace Lrb.Application.Integration.Web.Requests.GoogleAds
{
    public class GetAllGoogleAdsIntegrationAccountRequest : PaginationFilter, IRequest<PagedResponse<GoogleAdsAuthResponseDto, string>>
    {
        public LeadSource? LeadSource { get; set; }
    }
    public class GetAllGoogleAdsIntegrationAccountRequestHandler : IRequestHandler<GetAllGoogleAdsIntegrationAccountRequest, PagedResponse<GoogleAdsAuthResponseDto, string>>
    {
        private readonly IRepositoryWithEvents<IntegrationAccountInfo> _integrationRepo;
        private readonly IRepositoryWithEvents<GoogleAdsAuthResponse> _googleAdsAuthResponseRepo;
        public GetAllGoogleAdsIntegrationAccountRequestHandler(IRepositoryWithEvents<IntegrationAccountInfo> integrationRepo, IRepositoryWithEvents<GoogleAdsAuthResponse> googleAdsAuthResponseRepo)
        {
            _integrationRepo = integrationRepo;
            _googleAdsAuthResponseRepo = googleAdsAuthResponseRepo;
        }
        public async Task<PagedResponse<GoogleAdsAuthResponseDto, string>> Handle(GetAllGoogleAdsIntegrationAccountRequest request, CancellationToken cancellationToken)
        {
            var spec = new GoogleAdsAccountsSpec(request.LeadSource ?? LeadSource.GoogleAdsCampaign);
            var accounts = await _integrationRepo.ListAsync(spec, cancellationToken);
            var accountIds = accounts?.Select(x => x.GoogleadLeadFormId).ToList();
            if (accountIds?.Any() == true)
            {
                var googleAccounts = await _googleAdsAuthResponseRepo.ListAsync(new GetGoogleAdsAuthResponseByIdSpec(accountIds),cancellationToken);
                var count = await _integrationRepo.CountAsync(spec, cancellationToken);
                var dtos = googleAccounts.Adapt<List<GoogleAdsAuthResponseDto>>();
                var accountMap = accounts.ToDictionary(x => x.GoogleadLeadFormId, x => x.LeadCount);
                dtos = dtos.Select(dto =>
                {
                    if (dto.Id != null && accountMap.TryGetValue(dto.Id, out var leadCount))
                    {
                        dto.LeadCount = leadCount;
                    }
                    dto.Status = dto.LeadCount > 0 ? "Completed" : "InComplete";
                    return dto;
                }).ToList();
                return new PagedResponse<GoogleAdsAuthResponseDto, string>(dtos.Adapt<List<GoogleAdsAuthResponseDto>>(), count);
            }

            return new PagedResponse<GoogleAdsAuthResponseDto, string>(new List<GoogleAdsAuthResponseDto>(), 0);
        }

    }
}
