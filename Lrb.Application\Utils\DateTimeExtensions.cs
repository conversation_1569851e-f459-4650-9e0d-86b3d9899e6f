﻿using Azure.Core;
using Microsoft.VisualBasic;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Lrb.Application.Utils
{
    public static class DateTimeExtensions 
    {
        public static DateTime ToLocalTime(this DateTime utcDate, string timeZoneId)
        {
            TimeZoneInfo tz = TimeZoneInfo.FindSystemTimeZoneById(timeZoneId);
            return TimeZoneInfo.ConvertTimeFromUtc(utcDate, tz);
        }

        public static DateTime ToIndianStandardTime(this DateTime utcDate)
        {
            TimeZoneInfo ist = TimeZoneInfo.FindSystemTimeZoneById("India Standard Time");
            return TimeZoneInfo.ConvertTimeFromUtc(utcDate, ist);
        }
        public static DateTime ConvertFromDateToUtc(this DateTime date)
        {
            return date;
        }
        public static DateTime ConvertToDateToUtc(this DateTime date)
        {
            return date.AddDays(1).AddSeconds(-1);
        }
        public static DateTime ConvertAndSetKindAsUtc(this DateTime localDateTime)
        {
            if (localDateTime.Kind == DateTimeKind.Local || localDateTime.Kind == DateTimeKind.Unspecified)
            {
                DateTime utcDateTime = localDateTime.AddMinutes(-330);
                utcDateTime = DateTime.SpecifyKind(utcDateTime, DateTimeKind.Utc);
                return utcDateTime;
            }
            else if (localDateTime.Kind == DateTimeKind.Utc)
            {
                return localDateTime;
            }
            else
            {
                return DateTime.SpecifyKind(localDateTime, DateTimeKind.Utc);
            }
        }
        public static DateTime ToParticularTimeZone(this DateTime utcDate, string? timeZoneId, TimeSpan baseUTcOffset = default)
        {
            if (timeZoneId != null && baseUTcOffset != default)
            {
                return utcDate + baseUTcOffset;
            }
            else
            {
                return utcDate;
            }
        }
        public static DateTime ConvertDateTime(this DateTime time, string? timeZoneId, TimeSpan baseUTcOffset = default)
        {
            if (timeZoneId != null && baseUTcOffset != default)
            {
                var timeZone = TimeZoneInfo.FindSystemTimeZoneById(timeZoneId);
                DateTime adjustedTime;
                adjustedTime = time - baseUTcOffset;
                return DateTime.SpecifyKind(adjustedTime, DateTimeKind.Utc);
            }
            else
            {
                return DateTime.SpecifyKind(time, DateTimeKind.Utc);
            }
        }
        public static DateTime? ConvertDateAndTimeInUtc(string dateStr, string timeStr, string timeZoneId, string baseUtcOffset)
        {
            try
            {
                if (!DateTime.TryParse(dateStr, out var date))
                {
                    return null;
                }
                TimeSpan time;
                if (!TimeSpan.TryParse(timeStr, out time))
                {
                    if (DateTime.TryParse(timeStr, out var parsedTime))
                        time = parsedTime.TimeOfDay;
                    else
                        return null;
                }
                var localDateTime = date.Date + time;
                if (!TimeSpan.TryParse(baseUtcOffset, out var offset)) return null;
                var customTimeZone = TimeZoneInfo.CreateCustomTimeZone(
                    id: timeZoneId,
                    baseUtcOffset: offset,
                    displayName: timeZoneId,
                    standardDisplayName: timeZoneId
                );
                return TimeZoneInfo.ConvertTimeToUtc(localDateTime, customTimeZone);
            }
            catch (Exception ex)
            {
                return null;
            }
        }

        public static DateTime ToUtcFromTimeZone(this DateTime localDateTime, string? timeZoneId, TimeSpan baseUtcOffset = default)
        {
            if (!string.IsNullOrWhiteSpace(timeZoneId) && baseUtcOffset != default)
            {
                var unspecified = DateTime.SpecifyKind(localDateTime, DateTimeKind.Unspecified);
                var customTimeZone = TimeZoneInfo.CreateCustomTimeZone(
                    id: timeZoneId,
                    baseUtcOffset: baseUtcOffset,
                    displayName: timeZoneId,
                    standardDisplayName: timeZoneId);

                return TimeZoneInfo.ConvertTimeToUtc(unspecified, customTimeZone);
            }
            return DateTime.SpecifyKind(localDateTime, DateTimeKind.Utc);
        }
    }
}
