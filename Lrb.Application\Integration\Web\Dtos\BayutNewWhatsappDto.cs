﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Lrb.Application.Integration.Web.Dtos
{
    public class BayutNewWhatsappDto : IDto
    {
        public string? id { get; set; }
        public AgentClass? agent { get; set; }
        public StoryClass? story { get; set; }
        public string? messaage { get; set; }
        public string? recived_at { get; set; }
        public EnquirClass? enquirer { get; set; }
        public string? wamid { get; set; }
    }
    public class AgentClass : IDto
    {
        public string? id { get; set; }
        public string? name { get; set; }
        public string? email { get; set; }
        public string? url { get; set; }
    }
    public class StoryClass : IDto
    {
        public string? id { get; set; }
        public string type { get; set; }
        public string? listing_url { get; set; }
        public string? listing_title { get; set; }
        public string? project_title { get; set; }
        public string? listing_reference { get; set; }
        public string? project_url { get; set; }
        public string? loc3_name { get; set; }
        public string? purpose { get; set; }
    }
    public class EnquirClass : IDto
    {
        public string? name { get; set; }
        public string? contact_link { get; set; }
        public string? phone_number { get; set; }
    }
}
