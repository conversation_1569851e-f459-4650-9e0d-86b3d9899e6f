﻿using Lrb.Application.PushNotification.Mobile.Dtos;
using Lrb.Application.PushNotification.Mobile.Requests;
using MediatR;

namespace Lrb.MobileApi.Host.Controllers
{
    [Authorize]
    public class NotificationController : VersionedApiController
    {
        private readonly IMediator _mediator;
        public NotificationController(IMediator mediator)
        {
            _mediator = mediator;
        }
        [HttpPut("updateDeliveryStatus")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.Update, LrbResource.Leads)]
        [OpenApiOperation("Update Delivery Status of Push Notification.", "")]
        public async Task<Response<bool>> UpdateRecordAsync(UpdatePushNotificationRecordRequest request)
        {
            return await Mediator.Send(request);
        }
        [HttpPut("updateOpenedState")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.Update, LrbResource.Leads)]
        [OpenApiOperation("Update Opened State of Push Notification.", "")]
        public async Task<Response<bool>> UpdateOpenedStateAsync(UpdatePushNotificationOpenedStateRequest request)
        {
            return await Mediator.Send(request);
        }
        [HttpGet("Notification")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Leads)]
        [OpenApiOperation("Get All Notifications By Device.", "")]
        public async Task<Response<PushNotificationWrapperDto>> GetAllNotifications([FromQuery]GetAllPushNotificationsRequest request)
        {
            return await Mediator.Send(request);
        }
        [HttpGet]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Leads)]
        [OpenApiOperation("Get All Notifications By User.", "")]
        public async Task<Response<PushNotificationWrapperDto>> GetAllNotifications([FromQuery] GetAllPushNotificationsByUserRequest request)
        {
            return await Mediator.Send(request);
        }
        [HttpGet("all")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Leads)]
        [OpenApiOperation("Get All Notifications By User.", "")]
        public async Task<Response<PushNotificationWrapperDto>> GetAllNotifications([FromQuery] GetAllPushNotificationsByUserWithoutCountRequest request)
        {
            return await Mediator.Send(request);
        }
        [HttpGet("count/total")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Leads)]
        [OpenApiOperation("Get All Notifications Count By User.", "")]
        public async Task<Response<long>> GetAllNotificationsCount()
        {
            return await Mediator.Send(new GetAllPushNotificationsByUserCountRequest());
        }
        [HttpGet("count/un-opened")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.Leads)]
        [OpenApiOperation("Get All UnOpened Notifications Count By User.", "")]
        public async Task<Response<long>> GetAllUnOpenedNotificationsCount()
        {
            return await Mediator.Send(new GetAllUnOpenedPushNotificationsCountByUserRequest());
        }
        //[HttpPost("local-notification")]
        //[AllowAnonymous]
        //[TenantIdHeader]
        //[OpenApiOperation("Create a Local Notification", "")]
        //public async Task<Response<bool>> CreateLocalNotification(CreateLocalNotificationRequest request)
        //{
        //    return await Mediator.Send(request);
        //}
    }
}
