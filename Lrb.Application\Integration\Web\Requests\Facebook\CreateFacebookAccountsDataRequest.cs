﻿using Lrb.Application.Common.AWS_Batch;
using Lrb.Application.Common.Facebook;
using Lrb.Application.Common.GoogleAds;
using Lrb.Application.Common.ServiceBus;
using Lrb.Application.Integration.Web.Dtos;
using Lrb.Application.Integration.Web.Requests.Facebook;
using Lrb.Application.Integration.Web.Specs;
using Lrb.Domain.Entities.ErrorModule;
using Lrb.Domain.Entities.Integration;
using Newtonsoft.Json;

namespace Lrb.Application.Integration.Web.Requests
{
    public class CreateFacebookAccountsDataRequest : IRequest<Response<bool>>
    {
        public List<FacebookAccountDataDto>? Accounts { get; set; }
    }
    public class CreateFacebookAccountsDataRequestHandler : IRequestHandler<CreateFacebookAccountsDataRequest, Response<bool>>
    {
        private readonly IRepositoryWithEvents<FacebookAccountsInfo> _facebookAccountRepo;
        private readonly IFacebookService _facebookService;
        private readonly ICurrentUser _currentUser;
        private readonly ILeadRepositoryAsync _leadRepositoryAsync;

        public CreateFacebookAccountsDataRequestHandler(
            IRepositoryWithEvents<FacebookAccountsInfo> facebookAccountRepo,
            IFacebookService facebookService,
            ICurrentUser currentUser,
            ILeadRepositoryAsync leadRepositoryAsync)
        {
            _facebookAccountRepo = facebookAccountRepo;
            _facebookService = facebookService;
            _currentUser = currentUser;
            _leadRepositoryAsync = leadRepositoryAsync;
        }
        public async Task<Response<bool>> Handle(CreateFacebookAccountsDataRequest request, CancellationToken cancellationToken)
        {
            var tenantId = _currentUser.GetTenant();
            if (request.Accounts == null || request.Accounts.Count <= 0) { return new(false, "Please enter some account data."); }
            foreach (var account in request.Accounts)
            {
                var fbAccount = (await _facebookAccountRepo.ListAsync(new FacebookAccountByPageIdSpec(account?.PageId ?? string.Empty)))?.FirstOrDefault();
                if (fbAccount == null)
                {
                    fbAccount = account.Adapt<FacebookAccountsInfo>();
                    try
                    {
                        var tokenData = await _facebookService.GetLongLivedTokenAsync(account?.AccessToken ?? string.Empty);
                        fbAccount.LongLivedAccessToken = tokenData?.access_token;
                    }
                    catch (Exception ex)
                    {
                        fbAccount.LongLivedAccessToken = account.AccessToken;
                        var error = new LrbError()
                        {
                            ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                            ErrorSource = ex?.Source,
                            StackTrace = ex?.StackTrace,
                            InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                            ErrorModule = "CreateFacebookAccountsDataRequestHandler -> Handle()"
                        };
                        await _leadRepositoryAsync.AddErrorAsync(error);
                    }
                    await _facebookAccountRepo.AddAsync(fbAccount);
                    var fbData = new FacebookData
                    {
                        Id = Guid.NewGuid(),
                        CreatedOn = DateTime.UtcNow,
                        LastModifiedOn = DateTime.UtcNow,
                        FacebookIntegrationId = fbAccount.Id,
                        PageId = fbAccount.PageId,
                        TenantId = tenantId ?? "root"
                    };
                    await _facebookService.StoreFacebookAccountAsync(fbData);
                }
                else
                {
                    var tokenData = await _facebookService.GetLongLivedTokenAsync(account?.AccessToken ?? string.Empty);
                    fbAccount = account.Adapt(fbAccount);
                    fbAccount.LongLivedAccessToken = tokenData?.access_token;

                    await _facebookAccountRepo.UpdateAsync(fbAccount);
                }
            }
            return new(true, "Successfully stored account details");
        }
    }

    //New Implementation
    public class CreateFacebookIntegrationRequest : IRequest<Response<bool>>
    {
        public string AccessToken { get; set; } = default!;
        public string FacebookUserId { get; set; } = default!;
        public string? FacebookAccountName { get; set; }
    }
    public class CreateFacebookIntegrationRequestHandler : FBCommonHandler, IRequestHandler<CreateFacebookIntegrationRequest, Response<bool>>
    {
        public CreateFacebookIntegrationRequestHandler(
            IRepositoryWithEvents<FacebookAuthResponse> facebookAuthResponseRepo,
            IRepositoryWithEvents<FacebookConnectedPageAccount> facebookConnectedPageAccountRepo,
            IRepositoryWithEvents<FacebookLeadGenForm> facebookLeadGenFormRepo,
            IFacebookService facebookService,
            IJobService hangfireService,
            ITenantIndependentRepository repository,
            ICurrentUser currentUser,
            IRepositoryWithEvents<IntegrationAccountInfo> integrationAccInfoRepo,
            IRepositoryWithEvents<FacebookAdsInfo> fbAdsRepo,
            ILeadRepositoryAsync leadRepositoryAsync,
            IGoogleAdsService googleAdsService, IRepositoryWithEvents<GoogleAdsAuthResponse> googleAdsAuthResponseRepo,
            IRepositoryWithEvents<GoogleAdsInfo> googleAdsRepo,
            IRepositoryWithEvents<GoogleCampaign> googleCampaignsRepo)
            : base(facebookAuthResponseRepo,
                  facebookConnectedPageAccountRepo,
                  facebookLeadGenFormRepo,
                  facebookService,
                  hangfireService,
                  repository,
                  currentUser,
                  integrationAccInfoRepo,
                  fbAdsRepo, leadRepositoryAsync, googleAdsService, googleAdsAuthResponseRepo, googleAdsRepo, googleCampaignsRepo)
        { }
        public async Task<Response<bool>> Handle(CreateFacebookIntegrationRequest request, CancellationToken cancellationToken)
        {
            //TODO: RUN via Job
            var tenantId = _currentUser.GetTenant();
            if (!string.IsNullOrEmpty(tenantId))
            {
                var tInfo = await _repository.GetTenantInfoAsync(tenantId);
                var dto = new FacebookIntegrationDto()
                {
                    AccessToken = request.AccessToken,
                    FacebookUserId = request.FacebookUserId,
                    TenantInfoDto = tInfo,
                    CurrentUserId = _currentUser.GetUserId()
                };
                if (!IsValidRequest(dto)) return new(false, "Invalid Request!");

                if (await IsAccountExistsInDifferentTenant(dto)) return new(false, "Account Already Exists in a Different Tenant!");

                if (await IsAccountExistsInSameTenant(dto))
                {
                    await UpdateFacebookAuthResponse(dto);
                    return new(false, "Integration Account Updated Successfully!");
                }
                else
                {
                    await CreateFacebookAuthResponse(dto);
                    return new(true, "Integration Account Created Successfully!");
                }
                //_hangfireService.Enqueue(() => Execute(dto));
            }

            return new(true, "Integration Account Created Successfully!");
        }

    }

    public class CreateFacebookIntegrationUsingconsoleRequest : IRequest<Response<bool>>
    {
        public string AccessToken { get; set; } = default!;
        public string FacebookUserId { get; set; } = default!;
        public string? FacebookAccountName { get; set; }
    }
    public class CreateFacebookIntegrationUsingconsoleRequestHandler : IRequestHandler<CreateFacebookIntegrationUsingconsoleRequest, Response<bool>>
    {
        private readonly IAWSBatchService _batchService;
        private readonly ICurrentUser _currentUser;
        private readonly IServiceBus _serviceBus;

        public CreateFacebookIntegrationUsingconsoleRequestHandler(IAWSBatchService batchService, ICurrentUser currentUser,
            IServiceBus serviceBus)
        {
            _batchService = batchService;
            _currentUser = currentUser;
            _serviceBus = serviceBus;
        }
        public async Task<Response<bool>> Handle(CreateFacebookIntegrationUsingconsoleRequest request, CancellationToken cancellationToken)
        {
            //TODO: RUN via Job
            var tenantId = _currentUser.GetTenant();
            if (!string.IsNullOrEmpty(tenantId))
            {
                try
                {
                    Lead.Web.Export.InputPayload input = new(TrackerId: Guid.Empty, TenantId: tenantId, CurrentUserId: _currentUser.GetUserId(), Type: "fblogin", JsonData: JsonConvert.SerializeObject(request));
                    var stringArgument = JsonConvert.SerializeObject(input);
                    var cmdArgs = new List<string>() { stringArgument };
                    await _serviceBus.RunExcelUploadJobAsync(cmdArgs);
                    return new(true, "Integration with Facebook account has begun..., this may take a few minutes! ");
                }
                catch (Exception ex)
                {
                    return new(false, "Invalid request!");
                }
            }
            return new(false, "Invalid request!");
        }

    }
}


