﻿using DocumentFormat.OpenXml.InkML;
using Lrb.Application.Common.AWS_Batch;
using Lrb.Application.Common.ServiceBus;
using Lrb.Application.Common.TimeZone;
using Lrb.Application.Lead.Web.Dtos;
using Lrb.Domain.Constants;
using Newtonsoft.Json;
using System.Text.RegularExpressions;

namespace Lrb.Application.Lead.Web.Export
{
    public class RunAWSBatchForExportLeadsRequest : GetAllLeadsParametersNewFilters, IRequest<Response<Guid>>
    {
        public List<string>? ToRecipients { get; set; } = new();
        public List<string>? CcRecipients { get; set; } = new();
        public List<string>? BccRecipients { get; set; } = new();
        public Guid? ExportTemplateId { get; set; }
        public List<string>? ReferralNames { get; set; }
        public List<string>? ReferralContactNos { get; set; }
        public List<string>? ReferralEmail { get; set; }
        public string? FileName {  get; set; }
    }
    public class RunAWSBatchExportLeadsRequestHandler : IRequestHandler<RunAWSBatchForExportLeadsRequest, Response<Guid>>
    {
        private readonly IAWSBatchService _batchService;
        private readonly ICurrentUser _currentUser;
        private readonly IRepositoryWithEvents<ExportLeadTracker> _exportLeadRepo;
        public const string TYPE = "exportleads";
        private readonly IServiceBus _serviceBus;

        public RunAWSBatchExportLeadsRequestHandler(IAWSBatchService batchService,
            ICurrentUser currentUser,
            IRepositoryWithEvents<ExportLeadTracker> exportLeadRepo,
            IServiceBus serviceBus
            )
        {
            _batchService = batchService;
            _currentUser = currentUser;
            _exportLeadRepo = exportLeadRepo;
            _serviceBus = serviceBus;
        }

        public async Task<Response<Guid>> Handle(RunAWSBatchForExportLeadsRequest request, CancellationToken cancellationToken)
        {
            try
            {
                ExportLeadTracker exportTracker = new();
                exportTracker.Request = JsonConvert.SerializeObject(request);
                exportTracker.TemplateId = request.ExportTemplateId;
                exportTracker.ToRecipients = request.ToRecipients;
                exportTracker.CcRecipients = request.CcRecipients;
                exportTracker.BccRecipients = request.BccRecipients;
                var commonTimeZoneDto = request.Adapt<CommonTimeZoneDto>();
                var serializedData = JsonConvert.SerializeObject(commonTimeZoneDto);
                var tenantId = _currentUser.GetTenant();
                var currentUserId = _currentUser.GetUserId();
                await _exportLeadRepo.AddAsync(exportTracker, cancellationToken);
                //Submit a job in AWS Batch
                InputPayload input = new(exportTracker.Id, tenantId ?? string.Empty, currentUserId, TYPE, serializedData);
                var stringArgument = JsonConvert.SerializeObject(input);
                var cmdArgs = new List<string>() { stringArgument };
                await _serviceBus.RunExcelExportJobAsync(cmdArgs);
                return new(exportTracker.Id);
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException("Something went wrong.");
            }
        }
    }
    public record InputPayload(Guid TrackerId, string TenantId, Guid CurrentUserId, string Type, string? JsonData);
}
