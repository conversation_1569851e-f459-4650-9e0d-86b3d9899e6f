﻿using Lrb.Application.Utils;
using Newtonsoft.Json;
using Lrb.Shared.Extensions;

namespace Lrb.Application.Reports.Web
{
    public class V2GetLeadStatusReportByUsersRequest : IRequest<PagedResponse<LeadsReportByUserDto, string>>
    {
        public DateTime? FromDate { get; set; }
        public DateTime? ToDate { get; set; }
        public DateType? DateType { get; set; }
        public bool IsWithTeam { get; set; }
        public List<Guid>? UserIds { get; set; }
        public string? SearchText { get; set; }
        public List<LeadSource>? Sources { get; set; }
        public List<string>? SubSources { get; set; }
        public List<string>? Projects { get; set; }
        public UserStatus? UserStatus { get; set; }
        public List<string>? Localites { get; set; }
        public List<string>? States { get; set; }
        public List<string>? Cities { get; set; }
        public ReportPermission? ReportPermission { get; set; }
        public int PageNumber { get; set; }
        public int PageSize { get; set; } = int.MaxValue;
        public bool? IsWithGeneralManager { get; set; }
        public List<string>? Countries { get; set; }
    }
    public class V2GetLeadStatusReportByUsersRequestHandler : IRequestHandler<V2GetLeadStatusReportByUsersRequest, PagedResponse<LeadsReportByUserDto, string>>
    {
        private readonly IDapperRepository _dapperRepository;
        private readonly ICurrentUser _currentUser;

        public V2GetLeadStatusReportByUsersRequestHandler(IDapperRepository dapperRepository, ICurrentUser currentUser)
        {
            _dapperRepository = dapperRepository;
            _currentUser = currentUser;
        }

        public async Task<PagedResponse<LeadsReportByUserDto, string>> Handle(V2GetLeadStatusReportByUsersRequest request, CancellationToken cancellationToken)
        {
            var tenantId = _currentUser.GetTenant();
            var userId = _currentUser.GetUserId();
            List<Guid> teamUserIds = new();
            List<Guid> permittedUserIds = new();
            var isAdmin = await _dapperRepository.IsAdminAsync(userId, tenantId ?? string.Empty);
            if (isAdmin)
            {
                permittedUserIds = (await _dapperRepository.GetAllUserIdsAsync(tenantId ?? string.Empty)).ToList();
            }
            else if (request.ReportPermission != null)
            {
                switch (request.ReportPermission)
                {
                    case ReportPermission.All:
                        permittedUserIds = (await _dapperRepository.GetAllUserIdsAsync(tenantId ?? string.Empty)).ToList();
                        break;
                    case ReportPermission.Reportees:
                        permittedUserIds = (await _dapperRepository.GetSubordinateIdsWithOnlyReporteesAsync(new List<Guid>() { userId }, tenantId ?? string.Empty)).ToList();
                        break;
                }
            }
            if (request?.UserIds?.Any() ?? false)
            {
                if (request?.IsWithTeam ?? false)
                {
                    teamUserIds = (await _dapperRepository.GetSubordinateIdsWithOnlyReporteesAsync(request.UserIds, tenantId ?? string.Empty)).ToList();
                }
                else if (request?.IsWithGeneralManager ?? false)
                {
                    teamUserIds = (await _dapperRepository.GetSubordinateIdsWithColumnNameAsync(request.UserIds, tenantId ?? string.Empty, "GeneralManager")).ToList();
                }
                else
                {
                    teamUserIds = request?.UserIds ?? new List<Guid>();
                }
            }
            else
            {
                if (!isAdmin)
                {
                    teamUserIds = permittedUserIds;
                }

            }
            if (teamUserIds.Any())
            {
                teamUserIds = teamUserIds.Where(userId => permittedUserIds.Contains(userId)).ToList();
            }
            else
            {
                teamUserIds = permittedUserIds;
            }
            List<LeadsReportByUserV2Dto> leadsReportByUserDtos = new List<LeadsReportByUserV2Dto>();
            List<LeadsReportByUserDto> newReportByUserDtos = new List<LeadsReportByUserDto>();
            request.FromDate = request.FromDate.HasValue ? request.FromDate.Value.ConvertFromDateToUtc() : null;
            request.ToDate = request.ToDate.HasValue ? request.ToDate.Value.ConvertToDateToUtc() : null;
            var res = (await _dapperRepository.QueryStoredProcedureFromReadReplicaAsync<LeadsReportDto>("LeadratBlack", "GetLeadReportByUser", new
            {
                fromdate = request.FromDate,
                todate = request.ToDate,
                datetype = request.DateType,
                tenantid = tenantId,
                userids = teamUserIds,
                searchtext = string.IsNullOrEmpty(request.SearchText) ? null : request.SearchText.Replace(" ", "").ToLower(),
                sources = request?.Sources?.ConvertAll(i => (int)i),
                projects = request?.Projects?.ConvertAll<string>(i => i.Replace(" ", "").ToLower()),
                userstatus = (request?.UserStatus ?? 0),
                subsources = request?.SubSources?.ConvertAll<string>(i => i.Replace(" ", "").ToLower()),
                pagesize = request.PageSize,
                pagenumber = request.PageNumber,
                localites = request?.Localites?.ConvertAll<string>(i => i.LrbNormalize()),
                cities = request?.Cities?.ConvertAll<string>(i => i.LrbNormalize()),
                states = request?.States?.ConvertAll<string>(i => i.LrbNormalize()),
                countries = request?.Countries?.ConvertAll<string>(i => i.Replace(" ", "").ToLower())
            })).ToList();
            res.ForEach(i => leadsReportByUserDtos.Add(JsonConvert.DeserializeObject<LeadsReportByUserV2Dto>(i.Report ?? string.Empty) ?? new LeadsReportByUserV2Dto()));
            var groupedResult = leadsReportByUserDtos.GroupBy(i => i?.User ?? new User()).ToDictionary(i => i.Key, j => j.SelectMany(i => i?.Status).ToList());
            foreach (var group in groupedResult)
            {
                float totalCount = ((float)(group.Value?.Sum(i => i.Count) ?? 1));
                totalCount = totalCount == 0 ? 1 : totalCount;
                newReportByUserDtos.Add(new()
                {
                    UserId = group.Key.UserId,
                    FirstName = group.Key.FirstName,
                    LastName = group.Key.LastName,
                    GeneralManager = group.Key.GeneralManager,
                    ReportingManager = group.Key.ReportingManager,
                    AllCount = group.Value?.Sum(i => i.Count) ?? 0,
                    ActiveCount = group.Value?.Where(i => i.BaseStatus != "dropped" && i.BaseStatus != "not_interested" && i.BaseStatus != "booked" && i.BaseStatus != "booking_cancel" && i.BaseStatus != "invoiced").Sum(i => i.Count) ?? 0,
                    MeetingDoneCount = group.Value?.Sum(i => i.MeetingDoneCount) ?? 0,
                    OverdueCount = group.Value?.Sum(i => i.OverdueCount) ?? 0,
                    OverdueCountPercentage = GetRoundValue(((group.Value?.Sum(i => i.OverdueCount) ?? 0) / totalCount) * 100),
                    MeetingNotDoneCount = group.Value?.Sum(i => i.MeetingNotDoneCount) ?? 0,
                    SiteVisitDoneCount = group.Value?.Sum(i => i.SiteVisitDoneCount) ?? 0,
                    SiteVisitNotDoneCount = group.Value?.Sum(i => i.SiteVisitNotDoneCount) ?? 0,
                    CallbackCount = group.Value?.Where(i => i.BaseStatus == "callback" && i.SubStatus != "callback")?.Sum(i => i.Count) ?? 0,
                    CallbackCountPercentage = GetRoundValue(((group.Value?.Where(i => i.BaseStatus == "callback" && i.SubStatus != "callback")?.Sum(i => i.Count) ?? 0) / totalCount) * 100),
                    BookedCount = group.Value?.Where(i => i.BaseStatus == "booked")?.Sum(i => i.Count) ?? 0,
                    BookedCountPercentage = GetRoundValue(((group.Value?.Where(i => i.BaseStatus == "booked")?.Sum(i => i.Count) ?? 0) / totalCount) * 100),
                    NewCount = group.Value?.FirstOrDefault(i => i.BaseStatus == "new")?.Count ?? 0,
                    NewCountPercentage = GetRoundValue(((group.Value?.FirstOrDefault(i => i.BaseStatus == "new")?.Count ?? 0) / totalCount) * 100),
                    DroppedCount = group.Value?.Where(i => i.BaseStatus == "dropped" && i.SubStatus != "dropped")?.Sum(i => i.Count) ?? 0,
                    DroppedCountPercentage = GetRoundValue(((group.Value?.Where(i => i.BaseStatus == "dropped" && i.SubStatus != "dropped")?.Sum(i => i.Count) ?? 0) / totalCount) * 100),
                    MeetingScheduledCount = group.Value?.Where(i => i.BaseStatus == "meeting_scheduled" && i.SubStatus != "meeting_scheduled")?.Sum(i => i.Count) ?? 0,
                    MeetingScheduledCountPercentage = GetRoundValue(((group.Value?.Where(i => i.BaseStatus == "meeting_scheduled" && i.SubStatus != "meeting_scheduled")?.Sum(i => i.Count) ?? 0) / totalCount) * 100),
                    SiteVisitScheduledCount = group.Value?.Where(i => i.BaseStatus == "site_visit_scheduled" && i.SubStatus != "site_visit_scheduled")?.Sum(i => i.Count) ?? 0,
                    SiteVisitScheduledCountPercentage = GetRoundValue(((group.Value?.Where(i => i.BaseStatus == "site_visit_scheduled" && i.SubStatus != "site_visit_scheduled")?.Sum(i => i.Count) ?? 0) / totalCount) * 100),
                    NotInterestedCount = group.Value?.Where(i => i.BaseStatus == "not_interested" && i.SubStatus != "not_interested")?.Sum(i => i.Count) ?? 0,
                    NotInterestedCountPercentage = GetRoundValue(((group.Value?.Where(i => i.BaseStatus == "not_interested" && i.SubStatus != "not_interested")?.Sum(i => i.Count) ?? 0) / totalCount) * 100),
                    PendingCount = group.Value?.FirstOrDefault(i => i.BaseStatus == "pending")?.Count ?? 0,
                    PendingCountPercentage = GetRoundValue(((group.Value?.FirstOrDefault(i => i.BaseStatus == "pending")?.Count ?? 0) / totalCount) * 100),
                    MeetingDoneUniqueCount = group.Value?.Sum(i => i.MeetingDoneUniqueCount) ?? 0,
                    MeetingDoneUniqueCountPercentage = GetRoundValue(((group.Value?.Sum(i => i.MeetingDoneUniqueCount) ?? 0) / totalCount) * 100),
                    MeetingNotDoneUniqueCount = group.Value?.Sum(i => i.MeetingNotDoneUniqueCount) ?? 0,
                    MeetingNotDoneUniqueCountPercentage = GetRoundValue(((group.Value?.Sum(i => i.MeetingNotDoneUniqueCount) ?? 0) / totalCount) * 100),
                    SiteVisitDoneUniqueCount = group.Value?.Sum(i => i.SiteVisitDoneUniqueCount) ?? 0,
                    SiteVisitDoneUniqueCountPercentage = GetRoundValue(((group.Value?.Sum(i => i.SiteVisitDoneUniqueCount) ?? 0) / totalCount) * 100),
                    SiteVisitNotDoneUniqueCount = group.Value?.Sum(i => i.SiteVisitNotDoneUniqueCount) ?? 0,
                    SiteVisitNotDoneUniqueCountPercentage = GetRoundValue(((group.Value?.Sum(i => i.SiteVisitNotDoneUniqueCount) ?? 0) / totalCount) * 100),
                    BookingCancelCount = group.Value?.Where(i => i.BaseStatus == "booking_cancel")?.Sum(i => i.Count) ?? 0,
                    BookingCancelCountPercentage = GetRoundValue(((group.Value?.Where(i => i.BaseStatus == "booking_cancel")?.Sum(i => i.Count) ?? 0) / totalCount) * 100),
                    ExpressionOfInterestLeadCount = group.Value?.Where(i => i.BaseStatus == "expression_of_interest")?.Sum(i => i.Count) ?? 0,
                    ExpressionOfInterestLeadCountPercentage = GetRoundValue(((group.Value?.Where(i => i.BaseStatus == "expression_of_interest")?.Sum(i => i.Count) ?? 0) / totalCount) * 100),
                    InvoicedLeadsCount = group.Value?.Where(i => i.BaseStatus == "invoiced")?.Sum(i => i.Count) ?? 0,
                    InvoicedLeadsCountPercentage = GetRoundValue(((group.Value?.Where(i => i.BaseStatus == "invoiced")?.Sum(i => i.Count) ?? 0) / totalCount) * 100)
                });
            }

            return new(newReportByUserDtos, 0);
        }
        private string GetRoundValue(float percentage)
        {
            if (percentage > 0)
            {
                return (float)Math.Round(percentage, 2) + " %";
            }
            return percentage + " %";
        }
    }
}
