﻿using Lrb.Application.Common.Persistence;
using Lrb.Application.DataManagement.Web.Dtos;
using Lrb.Application.Identity.Users;
using Lrb.Domain.Entities;
using Lrb.Domain.Entities.MasterData;
using Lrb.Shared.Extensions;
using Microsoft.Extensions.DependencyInjection;
using System.Collections.Concurrent;

namespace Lrb.Application.DataManagement.Web.Request
{
    public class ProcessProspectSourceUpdateRequest : IRequest<Response<bool>>
    {
        public Guid ProspectSourceId { get; set; }
        public List<Guid> ProspectIds { get; set; } = default!;
        public Guid? TrackerId { get; set; }
        public Guid? CurrentUserId { get; set; }
        public string? TenantId { get; set; }
        public string? SubSource { get; set; }
    }

    public class ProcessProspectSourceUpdateRequestHandler : IRequestHandler<ProcessProspectSourceUpdateRequest, Response<bool>>
    {
        protected readonly IRepositoryWithEvents<BulkCommonTracker> _bulkCommonTracker;
        protected readonly ICurrentUser _currentUser;
        protected readonly IServiceProvider _serviceProvider;
        private readonly IRepositoryWithEvents<CustomProspectStatus> _prospectStatusRepo;
        private readonly IRepositoryWithEvents<MasterProspectSource> _prospectSourceRepo;
        private readonly IRepositoryWithEvents<MasterPropertyType> _propertyTypeRepo;
        private readonly IRepositoryWithEvents<Prospect> _prospectRepo;
        private readonly IUserService _userService;

        public ProcessProspectSourceUpdateRequestHandler(
            IRepositoryWithEvents<BulkCommonTracker> bulkCommonTracker,
            ICurrentUser currentUser,
            IServiceProvider serviceProvider,
            IRepositoryWithEvents<CustomProspectStatus> prospectStatusRepo,
            IRepositoryWithEvents<MasterProspectSource> prospectSourceRepo,
            IRepositoryWithEvents<MasterPropertyType> propertyTypeRepo,
            IRepositoryWithEvents<Prospect> prospectRepo,
            IUserService userService)
        {
            _bulkCommonTracker = bulkCommonTracker;
            _currentUser = currentUser;
            _serviceProvider = serviceProvider;
            _prospectStatusRepo = prospectStatusRepo;
            _prospectSourceRepo = prospectSourceRepo;
            _propertyTypeRepo = propertyTypeRepo;
            _prospectRepo = prospectRepo;
            _userService = userService;
        }

        public async Task<Response<bool>> Handle(ProcessProspectSourceUpdateRequest request, CancellationToken cancellationToken)
        {
            BulkCommonTracker? tracker = null;
            try
            {
                int totalProspects = 0;
                int prospectsPerChunk = request.ProspectIds.Count > 50 ? 50 : request.ProspectIds.Count;
                var currentUser = request.CurrentUserId ?? _currentUser.GetUserId();
                string tenantId = request.TenantId ?? _currentUser.GetTenant();

                List<CustomProspectStatus>? CustomProspectStatuses = await _prospectStatusRepo.ListAsync(cancellationToken);
                List<MasterPropertyType>? MasterPropertyType = await _propertyTypeRepo.ListAsync();
                List<MasterProspectSource>? MasterPropertyTypeSources = await _prospectSourceRepo.ListAsync();

                if (request.ProspectIds.Any() && request.ProspectIds.Count >= 50)
                {
                    tracker = new BulkCommonTracker()
                    {
                        TotalCount = request.ProspectIds.Count(),
                        Status = UploadStatus.InProgress,
                        RawJson = request.Serialize(),
                        ClassType = "BulkProspectSourceUpdate",
                        Module = "prospect",
                        CreatedBy = currentUser,
                        LastModifiedBy = currentUser,
                    };
                    tracker = await _bulkCommonTracker.AddAsync(tracker);
                    request.TrackerId = tracker.Id;
                }

                request.CurrentUserId = request.CurrentUserId ?? currentUser;
                var chunks = request.ProspectIds.Chunk(prospectsPerChunk).Select(i => new ConcurrentBag<Guid>(i));
                List<Task> tasks = new List<Task>();
                try
                {
                    foreach (var chunk in chunks.ToList())
                    {
                        var newRequest = request.Adapt<UpdateBulkProspectSourceRequest>();
                        newRequest.CustomProspectStatuses = CustomProspectStatuses;
                        newRequest.MasterPropertyType = MasterPropertyType;
                        newRequest.MasterPropertyTypeSources = MasterPropertyTypeSources;
                        newRequest.ProspectIds = chunk.ToList();

                        var task = Task.Run(async () =>
                        {
                            using var scope = _serviceProvider.CreateScope();
                            var mediator = scope.ServiceProvider.GetRequiredService<IMediator>();
                            var count = await mediator.Send(newRequest, cancellationToken);
                            Interlocked.Add(ref totalProspects, count.Data);
                        });
                        tasks.Add(task);
                    }
                }
                catch (Exception ex) { }

                await Task.WhenAll(tasks);

                #region Update Common Bulk Upload Tracker
                if (request.TrackerId != null && request.TrackerId != Guid.Empty)
                {
                    tracker.TotalUploadedCount = tracker.TotalUploadedCount + totalProspects;
                    tracker.Status = tracker.TotalUploadedCount < tracker.TotalCount ? UploadStatus.InProgress : UploadStatus.Completed;
                    tracker.DistinctCount = tracker.DistinctCount + totalProspects;
                    tracker.UpdatedCount = tracker.UpdatedCount + totalProspects;
                    await _bulkCommonTracker.UpdateAsync(tracker);
                }
                #endregion
            }
            catch (Exception ex) { }
            return new();
        }
    }
}