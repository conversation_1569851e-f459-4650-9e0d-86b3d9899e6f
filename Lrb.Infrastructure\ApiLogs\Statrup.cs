﻿using Lrb.Application.Common.GoogleAds;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Serilog;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Lrb.Infrastructure.ApiResposeTimeInfo
{
    public static class Statrup
    {
        public static IServiceCollection ConfigureServices(this IServiceCollection services)
        {
            services.AddControllers(options =>
            {
                // Create an object of the ExecutionTimeFilter
                var executionTimeFilter = new ExecutionTimeFilter();
                // Add the custom action filter globally to all APIs in the controller
                options.Filters.Add(executionTimeFilter);
            });
            return services;
        }

    }
}
