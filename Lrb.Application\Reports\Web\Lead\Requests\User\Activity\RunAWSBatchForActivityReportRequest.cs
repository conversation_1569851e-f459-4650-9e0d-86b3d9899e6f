﻿using Lrb.Application.Common.AWS_Batch;
using Lrb.Application.Common.ServiceBus;
using Newtonsoft.Json;

namespace Lrb.Application.Reports.Web
{
    public class RunAWSBatchForActivityReportRequest : IRequest<Response<Guid>>
    {
        public string? SearchText { get; set; }
        public DateTime? FromDate { get; set; }
        public DateTime? ToDate { get; set; }
        public bool IsWithTeam { get; set; }
        public List<Guid>? UserIds { get; set; }
        public UserStatus? UserStatus { get; set; }
        public ReportPermission? ReportPermission { get; set; }
        public int PageNumber { get; set; } = 1;
        public int PageSize { get; set; } = int.MaxValue;
        public List<string>? ToRecipients { get; set; } = new();
        public List<string>? CcRecipients { get; set; } = new();
        public List<string>? BccRecipients { get; set; } = new();
        public string? FileName { get; set; }
        public bool? ShouldShowDataReport { get; set; }
        public string? TimeZoneId { get; set; }
        public TimeSpan BaseUTcOffset { get; set; }
        public string? S3BucketKey { get; set; }

    }
    public class RunAWSBatchForActivityReportRequestHandler : IRequestHandler<RunAWSBatchForActivityReportRequest, Response<Guid>>
    {
        private readonly IAWSBatchService _batchService;
        private readonly ICurrentUser _currentUser;
        private readonly IRepositoryWithEvents<ExportReportsTracker> _exportRepo;

        private readonly IServiceBus _serviceBus;
        public const string TYPE1 = "useractivityreport";
        public const string TYPE2 = "leadanddatauseractivityreport";
        public RunAWSBatchForActivityReportRequestHandler(IAWSBatchService batchService,
            ICurrentUser currentUser,
            IRepositoryWithEvents<ExportReportsTracker> exportRepo,
            IServiceBus serviceBus)
        {
            _batchService = batchService;
            _currentUser = currentUser;
            _exportRepo = exportRepo;
            _serviceBus = serviceBus;
        }

        public async Task<Response<Guid>> Handle(RunAWSBatchForActivityReportRequest request, CancellationToken cancellationToken)
        {
            try 
            {
                ExportReportsTracker tracker = new();
                tracker.Request = JsonConvert.SerializeObject(request);
                tracker.ToRecipients = request.ToRecipients;
                tracker.CcRecipients = request.CcRecipients;
                tracker.BccRecipients = request.BccRecipients;
                tracker.Type = request.ShouldShowDataReport == true ? TYPE2 : TYPE1;
                var tenantId = _currentUser.GetTenant();
                var currentUserId = _currentUser.GetUserId();
                tracker.S3BucketKey = request?.S3BucketKey ?? string.Empty;
                await _exportRepo.AddAsync(tracker, cancellationToken);
                string TYPE = tracker.Type;
                //Submit a job in AWS Batch
                if (string.IsNullOrWhiteSpace(request?.S3BucketKey))
                {
                    InputPayload input = new(tracker.Id, tenantId ?? string.Empty, currentUserId, TYPE);
                    var stringArgument = JsonConvert.SerializeObject(input);
                    var cmdArgs = new List<string>() { stringArgument };
                    await _serviceBus.RunExcelExportJobAsync(cmdArgs);
                }
                return new(tracker.Id);

            }
            catch (Exception ex)
            {
                throw new InvalidOperationException("Something went wrong.");
            }
        }
    }
}
