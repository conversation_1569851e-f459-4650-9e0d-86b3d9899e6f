using Lrb.Application.Common.Interfaces;
using Lrb.Application.DataManagement.Web.Dtos;
using Lrb.Application.DataManagement.Web.Mapping;
using Lrb.Application.DataManagement.Web.Specs;
using Lrb.Application.Identity.Users;
using Lrb.Domain.Entities;
using Lrb.Domain.Entities.MasterData;

namespace Lrb.Application.DataManagement.Web.Request
{
    public class UpdateBulkProspectSourceRequest : IRequest<Response<int>>
    {
        public Guid ProspectSourceId { get; set; }
        public List<Guid> ProspectIds { get; set; } = default!;
        public Guid? TrackerId { get; set; }
        public Guid? CurrentUserId { get; set; }
        public string? TenantId { get; set; }
        public string? SubSource { get; set; }
        public List<CustomProspectStatus>? CustomProspectStatuses { get; set; }
        public List<MasterPropertyType>? MasterPropertyType { get; set; }
        public List<MasterProspectSource>? MasterPropertyTypeSources { get; set; }
    }

    public class UpdateBulkProspectSourceRequestHandler : IRequestHandler<UpdateBulkProspectSourceRequest, Response<int>>
    {
        private readonly IRepositoryWithEvents<Prospect> _prospectRepo;
        private readonly IRepositoryWithEvents<MasterProspectSource> _prospectSourceRepo;
        private readonly IRepositoryWithEvents<ProspectHistory> _prospectHistoryRepo;
        private readonly IRepositoryWithEvents<CustomProspectStatus> _prospectStatusRepo;
        private readonly IRepositoryWithEvents<MasterPropertyType> _propertyTypeRepo;
        private readonly ICurrentUser _currentUser;
        private readonly IUserService _userService;

        public UpdateBulkProspectSourceRequestHandler(
            IRepositoryWithEvents<Prospect> prospectRepo,
            IRepositoryWithEvents<MasterProspectSource> prospectSourceRepo,
            IRepositoryWithEvents<ProspectHistory> prospectHistoryRepo,
            IRepositoryWithEvents<CustomProspectStatus> prospectStatusRepo,
            IRepositoryWithEvents<MasterPropertyType> propertyTypeRepo,
            ICurrentUser currentUser,
            IUserService userService)
        {
            _prospectRepo = prospectRepo;
            _prospectSourceRepo = prospectSourceRepo;
            _prospectHistoryRepo = prospectHistoryRepo;
            _prospectStatusRepo = prospectStatusRepo;
            _propertyTypeRepo = propertyTypeRepo;
            _currentUser = currentUser;
            _userService = userService;
        }

        public async Task<Response<int>> Handle(UpdateBulkProspectSourceRequest request, CancellationToken cancellationToken)
        {
            try
            {
                int totalUpdatedCount = 0;
                var existingProspects = await _prospectRepo.ListAsync(new GetProspectByIdsSpecs(request.ProspectIds), cancellationToken);
                var currentUserId = request.CurrentUserId ?? _currentUser.GetUserId();
                var updatedProspects = new List<Prospect>();
                var prospectHistorys = new List<ProspectHistory>();
                MasterProspectSource? source = null;
                if (request.ProspectSourceId != Guid.Empty)
                {
                    source = await _prospectSourceRepo.GetByIdAsync(request.ProspectSourceId, cancellationToken);
                    if (source == null)
                    {
                        throw new NotFoundException("No Source found by this Id");
                    }
                }
                else
                {
                    source = await _prospectSourceRepo.FirstOrDefaultAsync(new GetDefaultProspectSourceSpecs());
                }
                if (existingProspects?.Any() ?? false)
                {
                    var statuses = request.CustomProspectStatuses ?? await _prospectStatusRepo.ListAsync();
                    var propertyTypes = request.MasterPropertyType ?? await _propertyTypeRepo.ListAsync();
                    var sources = request.MasterPropertyTypeSources ?? await _prospectSourceRepo.ListAsync();
                    var userIds = new List<string?>();
                    existingProspects.ForEach(i => {
                        userIds.AddRange(new[] { i.AssignedFrom?.ToString(), i.AssignTo.ToString(), i.LastModifiedBy.ToString(), i.SourcingManager?.ToString(), i.ClosingManager?.ToString() });
                    });
                    userIds.Add(currentUserId.ToString());
                    userIds = userIds.Where(i => i != null && i != string.Empty)?.DistinctBy(i => i).ToList();
                    var users = await _userService.GetListOfUsersByIdsAsync(userIds, cancellationToken);
                    foreach (var existingProspect in existingProspects)
                    {
                        var oldProspect = existingProspect.Adapt<ViewProspectDto>();
                        var prospect = request.Adapt(existingProspect);

                        ProspectEnquiry existingEnquiry = prospect.Enquiries.FirstOrDefault() ?? new();
                        if (source != null &&
                           (existingEnquiry.Source.Id != source.Id ||
                           (existingEnquiry.Source.Id == source.Id  && existingEnquiry.SubSource != request.SubSource)))
                        {
                            existingEnquiry.Source = source;
                            if (existingEnquiry.SubSource != request.SubSource)
                            {
                                existingEnquiry.SubSource = request.SubSource;
                            }
                            else
                            {
                                existingEnquiry.SubSource = null;
                            }
                        }

                        prospect.LastModifiedBy = currentUserId;
                        prospect.LastModifiedOn = DateTime.UtcNow;
                        totalUpdatedCount++;
                        updatedProspects.Add(prospect);

                        var prospectVM = prospect.Adapt<ViewProspectDto>();
                        prospectVM = await ProspectHistoryHelper.SetUserViewForProspect(prospectVM, _userService, cancellationToken, currentUserId: currentUserId, userDetails: users);
                        oldProspect = await ProspectHistoryHelper.SetUserViewForProspect(oldProspect, _userService, cancellationToken, currentUserId: currentUserId, userDetails: users);
                        var histories = await ProspectHistoryHelper.UpdateProspectHistoryForVM(prospectVM, oldProspect, currentUserId, 1, statuses, propertyTypes, sources, _userService, cancellationToken);
                        prospectHistorys.AddRange(histories);
                    }
                    if (updatedProspects.Any())
                    {
                        await _prospectRepo.UpdateRangeAsync(updatedProspects);
                        await _prospectHistoryRepo.AddRangeAsync(prospectHistorys);
                    }
                }
                return new Response<int>(totalUpdatedCount);
            }
            catch (Exception ex)
            {
                throw new ApplicationException("Error updating prospect sources", ex);
            }
        }
    }
}